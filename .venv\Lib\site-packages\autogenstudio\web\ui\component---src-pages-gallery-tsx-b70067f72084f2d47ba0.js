/*! For license information please see component---src-pages-gallery-tsx-b70067f72084f2d47ba0.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[355],{1610:function(e){e.exports=JSON.parse('{"id":"gallery_default","name":"Default Component Gallery","url":null,"metadata":{"author":"AutoGen Team","created_at":"2025-02-21T20:43:06.400850","updated_at":"2025-02-21T20:43:07.501068","version":"1.0.0","description":"A default gallery containing basic components for human-in-loop conversations","tags":["human-in-loop","assistant","web agents"],"license":"MIT","homepage":null,"category":"conversation","last_synced":null},"components":{"agents":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with ability to use tools.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_ext.agents.web_surfer.MultimodalWebSurfer","component_type":"agent","version":1,"component_version":1,"description":"An agent that solves tasks by browsing the web using a headless browser.","label":"Web Surfer Agent","config":{"name":"websurfer_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"description":"an agent that solves tasks by browsing the web","headless":true,"start_page":"https://www.bing.com/","animate_actions":false,"to_save_screenshots":false,"use_ocr":false,"to_resize_viewport":true}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"an agent that verifies and summarizes information","label":"Verification Assistant","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that verifies and summarizes information","system_message":"You are a task verification assistant who is working with a web surfer agent to solve tasks. At each point, check if the task has been completed as requested by the user. If the websurfer_agent responds and the task has not yet been completed, respond with what is left to do and then say \'keep going\'. If and only when the task has been completed, summarize and present a final answer that directly addresses the user task in detail and then respond with TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_agentchat.agents.UserProxyAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that can represent a human user through an input function.","label":"UserProxyAgent","config":{"name":"user_proxy","description":"a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}],"models":[{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"OpenAI GPT-4o-mini","label":"OpenAI GPT-4o Mini","config":{"model":"gpt-4o-mini"}},{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Local Mistral-7B model client for instruction-based generation (Ollama, LMStudio).","label":"Mistral-7B Local","config":{"model":"TheBloke/Mistral-7B-Instruct-v0.2-GGUF","model_info":{"vision":false,"function_calling":true,"json_output":false,"family":"unknown"},"base_url":"http://localhost:1234/v1"}},{"provider":"autogen_ext.models.openai.AzureOpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"GPT-4o Mini Azure OpenAI model client.","label":"AzureOpenAI GPT-4o-mini","config":{"model":"gpt-4o-mini","api_key":"sk-...","azure_endpoint":"https://{your-custom-endpoint}.openai.azure.com/","azure_deployment":"{your-azure-deployment}","api_version":"2024-06-01"}}],"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs basic arithmetic operations (addition, subtraction, multiplication, division).","label":"Calculator Tool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that generates images based on a text description using OpenAI\'s DALL-E model. Note: Requires OpenAI API key to function.","label":"Image Generation Tool","config":{"source_code":"async def generate_image(\\n    query: str, output_dir: Optional[Path] = None, image_size: Literal[\\"1024x1024\\", \\"512x512\\", \\"256x256\\"] = \\"1024x1024\\"\\n) -> List[str]:\\n    \\"\\"\\"\\n    Generate images using OpenAI\'s DALL-E model based on a text description.\\n\\n    Args:\\n        query: Natural language description of the desired image\\n        output_dir: Directory to save generated images (default: current directory)\\n        image_size: Size of generated image (1024x1024, 512x512, or 256x256)\\n\\n    Returns:\\n        List[str]: Paths to the generated image files\\n    \\"\\"\\"\\n    # Initialize the OpenAI client\\n    client = OpenAI()\\n\\n    # Generate images using DALL-E 3\\n    response = client.images.generate(model=\\"dall-e-3\\", prompt=query, n=1, response_format=\\"b64_json\\", size=image_size)\\n\\n    saved_files = []\\n\\n    # Process the response\\n    if response.data:\\n        for image_data in response.data:\\n            # Generate a unique filename\\n            file_name = f\\"{uuid.uuid4()}.png\\"\\n\\n            # Use output_dir if provided, otherwise use current directory\\n            file_path = Path(output_dir) / file_name if output_dir else Path(file_name)\\n\\n            base64_str = image_data.b64_json\\n            img = Image.open(io.BytesIO(base64.decodebytes(bytes(base64_str, \\"utf-8\\"))))\\n\\n            # Save the image to a file\\n            img.save(file_path)\\n\\n            saved_files.append(str(file_path))\\n\\n    return saved_files\\n","name":"generate_image","description":"Generate images using DALL-E based on text descriptions.","global_imports":["io","uuid","base64",{"module":"typing","imports":["List","Optional","Literal"]},{"module":"pathlib","imports":["Path"]},{"module":"openai","imports":["OpenAI"]},{"module":"PIL","imports":["Image"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that generates a PDF file from a list of images.Requires the PyFPDF and pillow library to function.","label":"PDF Generation Tool","config":{"source_code":"async def generate_pdf(\\n    sections: List[Dict[str, Optional[str]]], output_file: str = \\"report.pdf\\", report_title: str = \\"PDF Report\\"\\n) -> str:\\n    \\"\\"\\"\\n    Generate a PDF report with formatted sections including text and images.\\n\\n    Args:\\n        sections: List of dictionaries containing section details with keys:\\n            - title: Section title\\n            - level: Heading level (title, h1, h2)\\n            - content: Section text content\\n            - image: Optional image URL or file path\\n        output_file: Name of output PDF file\\n        report_title: Title shown at top of report\\n\\n    Returns:\\n        str: Path to the generated PDF file\\n    \\"\\"\\"\\n\\n    def normalize_text(text: str) -> str:\\n        \\"\\"\\"Normalize Unicode text to ASCII.\\"\\"\\"\\n        return unicodedata.normalize(\\"NFKD\\", text).encode(\\"ascii\\", \\"ignore\\").decode(\\"ascii\\")\\n\\n    def get_image(image_url_or_path):\\n        \\"\\"\\"Fetch image from URL or local path.\\"\\"\\"\\n        if image_url_or_path.startswith((\\"http://\\", \\"https://\\")):\\n            response = requests.get(image_url_or_path)\\n            if response.status_code == 200:\\n                return BytesIO(response.content)\\n        elif Path(image_url_or_path).is_file():\\n            return open(image_url_or_path, \\"rb\\")\\n        return None\\n\\n    def add_rounded_corners(img, radius=6):\\n        \\"\\"\\"Add rounded corners to an image.\\"\\"\\"\\n        mask = Image.new(\\"L\\", img.size, 0)\\n        draw = ImageDraw.Draw(mask)\\n        draw.rounded_rectangle([(0, 0), img.size], radius, fill=255)\\n        img = ImageOps.fit(img, mask.size, centering=(0.5, 0.5))\\n        img.putalpha(mask)\\n        return img\\n\\n    class PDF(FPDF):\\n        \\"\\"\\"Custom PDF class with header and content formatting.\\"\\"\\"\\n\\n        def header(self):\\n            self.set_font(\\"Arial\\", \\"B\\", 12)\\n            normalized_title = normalize_text(report_title)\\n            self.cell(0, 10, normalized_title, 0, 1, \\"C\\")\\n\\n        def chapter_title(self, txt):\\n            self.set_font(\\"Arial\\", \\"B\\", 12)\\n            normalized_txt = normalize_text(txt)\\n            self.cell(0, 10, normalized_txt, 0, 1, \\"L\\")\\n            self.ln(2)\\n\\n        def chapter_body(self, body):\\n            self.set_font(\\"Arial\\", \\"\\", 12)\\n            normalized_body = normalize_text(body)\\n            self.multi_cell(0, 10, normalized_body)\\n            self.ln()\\n\\n        def add_image(self, img_data):\\n            img = Image.open(img_data)\\n            img = add_rounded_corners(img)\\n            img_path = Path(f\\"temp_{uuid.uuid4().hex}.png\\")\\n            img.save(img_path, format=\\"PNG\\")\\n            self.image(str(img_path), x=None, y=None, w=190 if img.width > 190 else img.width)\\n            self.ln(10)\\n            img_path.unlink()\\n\\n    # Initialize PDF\\n    pdf = PDF()\\n    pdf.add_page()\\n    font_size = {\\"title\\": 16, \\"h1\\": 14, \\"h2\\": 12, \\"body\\": 12}\\n\\n    # Add sections\\n    for section in sections:\\n        title = section.get(\\"title\\", \\"\\")\\n        level = section.get(\\"level\\", \\"h1\\")\\n        content = section.get(\\"content\\", \\"\\")\\n        image = section.get(\\"image\\")\\n\\n        pdf.set_font(\\"Arial\\", \\"B\\" if level in font_size else \\"\\", font_size.get(level, font_size[\\"body\\"]))\\n        pdf.chapter_title(title)\\n\\n        if content:\\n            pdf.chapter_body(content)\\n\\n        if image:\\n            img_data = get_image(image)\\n            if img_data:\\n                pdf.add_image(img_data)\\n                if isinstance(img_data, BytesIO):\\n                    img_data.close()\\n\\n    pdf.output(output_file)\\n    return output_file\\n","name":"generate_pdf","description":"Generate PDF reports with formatted sections containing text and images","global_imports":["uuid","requests","unicodedata",{"module":"typing","imports":["List","Dict","Optional"]},{"module":"pathlib","imports":["Path"]},{"module":"fpdf","imports":["FPDF"]},{"module":"PIL","imports":["Image","ImageDraw","ImageOps"]},{"module":"io","imports":["BytesIO"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that fetches the content of a webpage and converts it to markdown. Requires the requests and beautifulsoup4 library to function.","label":"Fetch Webpage Tool","config":{"source_code":"async def fetch_webpage(\\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\\n) -> str:\\n    \\"\\"\\"Fetch a webpage and convert it to markdown format.\\n\\n    Args:\\n        url: The URL of the webpage to fetch\\n        include_images: Whether to include image references in the markdown\\n        max_length: Maximum length of the output markdown (if None, no limit)\\n        headers: Optional HTTP headers for the request\\n\\n    Returns:\\n        str: Markdown version of the webpage content\\n\\n    Raises:\\n        ValueError: If the URL is invalid or the page can\'t be fetched\\n    \\"\\"\\"\\n    # Use default headers if none provided\\n    if headers is None:\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n    try:\\n        # Fetch the webpage\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(url, headers=headers, timeout=10)\\n            response.raise_for_status()\\n\\n            # Parse HTML\\n            soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n            # Remove script and style elements\\n            for script in soup([\\"script\\", \\"style\\"]):\\n                script.decompose()\\n\\n            # Convert relative URLs to absolute\\n            for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                if tag.get(\\"href\\"):\\n                    tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                if tag.get(\\"src\\"):\\n                    tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n            # Configure HTML to Markdown converter\\n            h2t = html2text.HTML2Text()\\n            h2t.body_width = 0  # No line wrapping\\n            h2t.ignore_images = not include_images\\n            h2t.ignore_emphasis = False\\n            h2t.ignore_links = False\\n            h2t.ignore_tables = False\\n\\n            # Convert to markdown\\n            markdown = h2t.handle(str(soup))\\n\\n            # Trim if max_length is specified\\n            if max_length and len(markdown) > max_length:\\n                markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n            return markdown.strip()\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to fetch webpage: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error processing webpage: {str(e)}\\") from e\\n","name":"fetch_webpage","description":"Fetch a webpage and convert it to markdown format, with options for including images and limiting length","global_imports":["os","html2text",{"module":"typing","imports":["Optional","Dict"]},"httpx",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"html2text","imports":["HTML2Text"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs Bing searches using the Bing Web Search API. Requires the requests library, BING_SEARCH_KEY env variable to function.","label":"Bing Search Tool","config":{"source_code":"async def bing_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: str = \\"moderate\\",\\n    response_filter: str = \\"webpages\\",\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Bing search using the Bing Web Search API.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 50)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., \'en\', \'es\', \'fr\')\\n        country: Optional market code for search results (e.g., \'us\', \'uk\')\\n        safe_search: SafeSearch setting (\'off\', \'moderate\', or \'strict\')\\n        response_filter: Type of results (\'webpages\', \'news\', \'images\', or \'videos\')\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results\\n\\n    Raises:\\n        ValueError: If API credentials are invalid or request fails\\n    \\"\\"\\"\\n    # Get and validate API key\\n    api_key = os.getenv(\\"BING_SEARCH_KEY\\", \\"\\").strip()\\n\\n    if not api_key:\\n        raise ValueError(\\n            \\"BING_SEARCH_KEY environment variable is not set. \\" \\"Please obtain an API key from Azure Portal.\\"\\n        )\\n\\n    # Validate safe_search parameter\\n    valid_safe_search = [\\"off\\", \\"moderate\\", \\"strict\\"]\\n    if safe_search.lower() not in valid_safe_search:\\n        raise ValueError(f\\"Invalid safe_search value. Must be one of: {\', \'.join(valid_safe_search)}\\")\\n\\n    # Validate response_filter parameter\\n    valid_filters = [\\"webpages\\", \\"news\\", \\"images\\", \\"videos\\"]\\n    if response_filter.lower() not in valid_filters:\\n        raise ValueError(f\\"Invalid response_filter value. Must be one of: {\', \'.join(valid_filters)}\\")\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    # Build request headers and parameters\\n    headers = {\\"Ocp-Apim-Subscription-Key\\": api_key, \\"Accept\\": \\"application/json\\"}\\n\\n    params = {\\n        \\"q\\": query,\\n        \\"count\\": min(max(1, num_results), 50),\\n        \\"mkt\\": f\\"{language}-{country.upper()}\\" if country else language,\\n        \\"safeSearch\\": safe_search.capitalize(),\\n        \\"responseFilter\\": response_filter,\\n        \\"textFormat\\": \\"raw\\",\\n    }\\n\\n    # Make the request\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\n                \\"https://api.bing.microsoft.com/v7.0/search\\", headers=headers, params=params, timeout=10\\n            )\\n\\n            # Handle common error cases\\n            if response.status_code == 401:\\n                raise ValueError(\\"Authentication failed. Please verify your Bing Search API key.\\")\\n            elif response.status_code == 403:\\n                raise ValueError(\\n                    \\"Access forbidden. This could mean:\\\\n\\"\\n                    \\"1. The API key is invalid\\\\n\\"\\n                    \\"2. The API key has expired\\\\n\\"\\n                    \\"3. You\'ve exceeded your API quota\\"\\n                )\\n            elif response.status_code == 429:\\n                raise ValueError(\\"API quota exceeded. Please try again later.\\")\\n\\n            response.raise_for_status()\\n            data = response.json()\\n\\n        # Process results based on response_filter\\n        results = []\\n        if response_filter == \\"webpages\\" and \\"webPages\\" in data:\\n            items = data[\\"webPages\\"][\\"value\\"]\\n        elif response_filter == \\"news\\" and \\"news\\" in data:\\n            items = data[\\"news\\"][\\"value\\"]\\n        elif response_filter == \\"images\\" and \\"images\\" in data:\\n            items = data[\\"images\\"][\\"value\\"]\\n        elif response_filter == \\"videos\\" and \\"videos\\" in data:\\n            items = data[\\"videos\\"][\\"value\\"]\\n        else:\\n            if not any(key in data for key in [\\"webPages\\", \\"news\\", \\"images\\", \\"videos\\"]):\\n                return []  # No results found\\n            raise ValueError(f\\"No {response_filter} results found in API response\\")\\n\\n        # Extract relevant information based on result type\\n        for item in items:\\n            result = {\\"title\\": item.get(\\"name\\", \\"\\")}\\n\\n            if response_filter == \\"webpages\\":\\n                result[\\"link\\"] = item.get(\\"url\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n                if include_content:\\n                    result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n            elif response_filter == \\"news\\":\\n                result[\\"link\\"] = item.get(\\"url\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n                result[\\"date\\"] = item.get(\\"datePublished\\", \\"\\")\\n                if include_content:\\n                    result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n            elif response_filter == \\"images\\":\\n                result[\\"link\\"] = item.get(\\"contentUrl\\", \\"\\")\\n                result[\\"thumbnail\\"] = item.get(\\"thumbnailUrl\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n\\n            elif response_filter == \\"videos\\":\\n                result[\\"link\\"] = item.get(\\"contentUrl\\", \\"\\")\\n                result[\\"thumbnail\\"] = item.get(\\"thumbnailUrl\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n                result[\\"duration\\"] = item.get(\\"duration\\", \\"\\")\\n\\n            results.append(result)\\n\\n        return results[:num_results]\\n\\n    except httpx.RequestException as e:\\n        error_msg = str(e)\\n        if \\"InvalidApiKey\\" in error_msg:\\n            raise ValueError(\\"Invalid API key. Please check your BING_SEARCH_KEY environment variable.\\") from e\\n        elif \\"KeyExpired\\" in error_msg:\\n            raise ValueError(\\"API key has expired. Please generate a new key.\\") from e\\n        else:\\n            raise ValueError(f\\"Search request failed: {error_msg}\\") from e\\n    except json.JSONDecodeError:\\n        raise ValueError(\\"Failed to parse API response. \\" \\"Please verify your API credentials and try again.\\") from None\\n    except Exception as e:\\n        raise ValueError(f\\"Unexpected error during search: {str(e)}\\") from e\\n","name":"bing_search","description":"\\n    Perform Bing searches using the Bing Web Search API. Requires BING_SEARCH_KEY environment variable.\\n    Supports web, news, image, and video searches.\\n    See function documentation for detailed setup instructions.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","json","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs Google searches using the Google Custom Search API. Requires the requests library, [GOOGLE_API_KEY, GOOGLE_CSE_ID] to be set,  env variable to function.","label":"Google Search Tool","config":{"source_code":"async def google_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: bool = True,\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 10)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., en, es, fr)\\n        country: Optional country code for search results (e.g., us, uk)\\n        safe_search: Enable safe search filtering\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results, each containing:\\n            - title: Result title\\n            - link: Result URL\\n            - snippet: Result description (if include_snippets=True)\\n            - content: Webpage content in markdown (if include_content=True)\\n    \\"\\"\\"\\n    api_key = os.getenv(\\"GOOGLE_API_KEY\\")\\n    cse_id = os.getenv(\\"GOOGLE_CSE_ID\\")\\n\\n    if not api_key or not cse_id:\\n        raise ValueError(\\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\\")\\n\\n    num_results = min(max(1, num_results), 10)\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    params = {\\n        \\"key\\": api_key,\\n        \\"cx\\": cse_id,\\n        \\"q\\": query,\\n        \\"num\\": num_results,\\n        \\"hl\\": language,\\n        \\"safe\\": \\"active\\" if safe_search else \\"off\\",\\n    }\\n\\n    if country:\\n        params[\\"gl\\"] = country\\n\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\"https://www.googleapis.com/customsearch/v1\\", params=params, timeout=10)\\n            response.raise_for_status()\\n            data = response.json()\\n\\n            results = []\\n            if \\"items\\" in data:\\n                for item in data[\\"items\\"]:\\n                    result = {\\"title\\": item.get(\\"title\\", \\"\\"), \\"link\\": item.get(\\"link\\", \\"\\")}\\n                    if include_snippets:\\n                        result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n\\n                    if include_content:\\n                        result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n                    results.append(result)\\n\\n            return results\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to perform search: {str(e)}\\") from e\\n    except KeyError as e:\\n        raise ValueError(f\\"Invalid API response format: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error during search: {str(e)}\\") from e\\n","name":"google_search","description":"\\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}}],"terminations":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}},{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"description":"Termination condition that ends the conversation when either a message contains \'TERMINATE\' or the maximum number of messages is reached.","label":"OR Termination","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}}],"teams":[{"provider":"autogen_agentchat.teams.RoundRobinGroupChat","component_type":"team","version":1,"component_version":1,"description":"A single AssistantAgent (with a calculator tool) in a RoundRobinGroupChat team. ","label":"RoundRobin Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}}],"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}}}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 2 agents - an AssistantAgent (with a calculator tool) and a CriticAgent in a SelectorGroupChat team.","label":"Selector Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"critic_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that critiques and improves the assistant\'s output","system_message":"You are a helpful assistant. Critique the assistant\'s output and suggest improvements.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}},"selector_prompt":"You are in a role play game. The following roles are available:\\n{roles}.\\nRead the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n{history}\\n\\nRead the above conversation. Then select the next role from {participants} to play. Only return the role.\\n","allow_repeated_speaker":false,"max_selector_attempts":3}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 3 agents - a Web Surfer agent that can browse the web, a Verification Assistant that verifies and summarizes information, and a User Proxy that provides human feedback when needed.","label":"Web Agent Team (Operator)","config":{"participants":[{"provider":"autogen_ext.agents.web_surfer.MultimodalWebSurfer","component_type":"agent","version":1,"component_version":1,"description":"MultimodalWebSurfer is a multimodal agent that acts as a web surfer that can search the web and visit web pages.","label":"MultimodalWebSurfer","config":{"name":"websurfer_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"description":"an agent that solves tasks by browsing the web","headless":true,"start_page":"https://www.bing.com/","animate_actions":false,"to_save_screenshots":false,"use_ocr":false,"to_resize_viewport":true}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"tools":[],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that verifies and summarizes information","system_message":"You are a task verification assistant who is working with a web surfer agent to solve tasks. At each point, check if the task has been completed as requested by the user. If the websurfer_agent responds and the task has not yet been completed, respond with what is left to do and then say \'keep going\'. If and only when the task has been completed, summarize and present a final answer that directly addresses the user task in detail and then respond with TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_agentchat.agents.UserProxyAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that can represent a human user through an input function.","label":"UserProxyAgent","config":{"name":"user_proxy","description":"a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":20,"include_agent_event":false}},{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}}]}},"selector_prompt":"You are the cordinator of role play game. The following roles are available:\\n{roles}. Given a task, the websurfer_agent will be tasked to address it by browsing the web and providing information.  The assistant_agent will be tasked with verifying the information provided by the websurfer_agent and summarizing the information to present a final answer to the user. If the task  needs assistance from a human user (e.g., providing feedback, preferences, or the task is stalled), you should select the user_proxy role to provide the necessary information.\\n\\nRead the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n{history}\\n\\nRead the above conversation. Then select the next role from {participants} to play. Only return the role.","allow_repeated_speaker":false,"max_selector_attempts":3}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 3 agents - a Research Assistant that performs web searches and analyzes information, a Verifier that ensures research quality and completeness, and a Summary Agent that provides a detailed markdown summary of the research as a report to the user.","label":"Deep Research Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"research_assistant","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"async def google_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: bool = True,\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 10)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., en, es, fr)\\n        country: Optional country code for search results (e.g., us, uk)\\n        safe_search: Enable safe search filtering\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results, each containing:\\n            - title: Result title\\n            - link: Result URL\\n            - snippet: Result description (if include_snippets=True)\\n            - content: Webpage content in markdown (if include_content=True)\\n    \\"\\"\\"\\n    api_key = os.getenv(\\"GOOGLE_API_KEY\\")\\n    cse_id = os.getenv(\\"GOOGLE_CSE_ID\\")\\n\\n    if not api_key or not cse_id:\\n        raise ValueError(\\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\\")\\n\\n    num_results = min(max(1, num_results), 10)\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    params = {\\n        \\"key\\": api_key,\\n        \\"cx\\": cse_id,\\n        \\"q\\": query,\\n        \\"num\\": num_results,\\n        \\"hl\\": language,\\n        \\"safe\\": \\"active\\" if safe_search else \\"off\\",\\n    }\\n\\n    if country:\\n        params[\\"gl\\"] = country\\n\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\"https://www.googleapis.com/customsearch/v1\\", params=params, timeout=10)\\n            response.raise_for_status()\\n            data = response.json()\\n\\n            results = []\\n            if \\"items\\" in data:\\n                for item in data[\\"items\\"]:\\n                    result = {\\"title\\": item.get(\\"title\\", \\"\\"), \\"link\\": item.get(\\"link\\", \\"\\")}\\n                    if include_snippets:\\n                        result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n\\n                    if include_content:\\n                        result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n                    results.append(result)\\n\\n            return results\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to perform search: {str(e)}\\") from e\\n    except KeyError as e:\\n        raise ValueError(f\\"Invalid API response format: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error during search: {str(e)}\\") from e\\n","name":"google_search","description":"\\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"async def fetch_webpage(\\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\\n) -> str:\\n    \\"\\"\\"Fetch a webpage and convert it to markdown format.\\n\\n    Args:\\n        url: The URL of the webpage to fetch\\n        include_images: Whether to include image references in the markdown\\n        max_length: Maximum length of the output markdown (if None, no limit)\\n        headers: Optional HTTP headers for the request\\n\\n    Returns:\\n        str: Markdown version of the webpage content\\n\\n    Raises:\\n        ValueError: If the URL is invalid or the page can\'t be fetched\\n    \\"\\"\\"\\n    # Use default headers if none provided\\n    if headers is None:\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n    try:\\n        # Fetch the webpage\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(url, headers=headers, timeout=10)\\n            response.raise_for_status()\\n\\n            # Parse HTML\\n            soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n            # Remove script and style elements\\n            for script in soup([\\"script\\", \\"style\\"]):\\n                script.decompose()\\n\\n            # Convert relative URLs to absolute\\n            for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                if tag.get(\\"href\\"):\\n                    tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                if tag.get(\\"src\\"):\\n                    tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n            # Configure HTML to Markdown converter\\n            h2t = html2text.HTML2Text()\\n            h2t.body_width = 0  # No line wrapping\\n            h2t.ignore_images = not include_images\\n            h2t.ignore_emphasis = False\\n            h2t.ignore_links = False\\n            h2t.ignore_tables = False\\n\\n            # Convert to markdown\\n            markdown = h2t.handle(str(soup))\\n\\n            # Trim if max_length is specified\\n            if max_length and len(markdown) > max_length:\\n                markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n            return markdown.strip()\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to fetch webpage: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error processing webpage: {str(e)}\\") from e\\n","name":"fetch_webpage","description":"Fetch a webpage and convert it to markdown format, with options for including images and limiting length","global_imports":["os","html2text",{"module":"typing","imports":["Optional","Dict"]},"httpx",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"html2text","imports":["HTML2Text"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}}],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A research assistant that performs web searches and analyzes information","system_message":"You are a research assistant focused on finding accurate information.\\n        Use the google_search tool to find relevant information.\\n        Break down complex queries into specific search terms.\\n        Always verify information across multiple sources when possible.\\n        When you find relevant information, explain why it\'s relevant and how it connects to the query. When you get feedback from the a verifier agent, use your tools to act on the feedback and make progress.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"verifier","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"tools":[],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A verification specialist who ensures research quality and completeness","system_message":"You are a research verification specialist.\\n        Your role is to:\\n        1. Verify that search queries are effective and suggest improvements if needed\\n        2. Explore drill downs where needed e.g, if the answer is likely in a link in the returned search results, suggest clicking on the link\\n        3. Suggest additional angles or perspectives to explore. Be judicious in suggesting new paths to avoid scope creep or wasting resources, if the task appears to be addressed and we can provide a report, do this and respond with \\"TERMINATE\\".\\n        4. Track progress toward answering the original question\\n        5. When the research is complete, provide a detailed summary in markdown format. For incomplete research, end your message with \\"CONTINUE RESEARCH\\". For complete research, end your message with APPROVED.\\n        Your responses should be structured as:\\n        - Progress Assessment\\n        - Gaps/Issues (if any)\\n        - Suggestions (if needed)\\n        - Next Steps or Final Summary","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"summary_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"tools":[],"handoffs":[],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A summary agent that provides a detailed markdown summary of the research as a report to the user.","system_message":"You are a summary agent. Your role is to provide a detailed markdown summary of the research as a report to the user. Your report should have a reasonable title that matches the research question and should summarize the key details in the results found in natural an actionable manner. The main results/answer should be in the first paragraph. Where reasonable, your report should have clear comparison tables that drive critical insights. Most importantly, you should have a reference section and cite the key sources (where available) for facts obtained INSIDE THE MAIN REPORT. Also, where appropriate, you may add images if available that illustrate concepts needed for the summary.\\n        Your report should end with the word \\"TERMINATE\\" to signal the end of the conversation.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}"}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":30,"include_agent_event":false}}]}},"selector_prompt":"You are coordinating a research team by selecting the team member to speak/act next. The following team member roles are available:\\n    {roles}.\\n    The research_assistant performs searches and analyzes information.\\n    The verifier evaluates progress and ensures completeness.\\n    The summary_agent provides a detailed markdown summary of the research as a report to the user.\\n\\n    Given the current context, select the most appropriate next speaker.\\n    The research_assistant should search and analyze.\\n    The verifier should evaluate progress and guide the research (select this role is there is a need to verify/evaluate progress). You should ONLY select the summary_agent role if the research is complete and it is time to generate a report.\\n\\n    Base your selection on:\\n    1. Current stage of research\\n    2. Last speaker\'s findings or suggestions\\n    3. Need for verification vs need for new information\\n    Read the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n    {history}\\n\\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.","allow_repeated_speaker":true,"max_selector_attempts":3}}]}}')},4377:function(e,t,n){n.r(t),n.d(t,{default:function(){return X}});var o=n(6540),a=n(5312),r=n(436),s=n(9036),i=n(8458),l=n(7677),c=n(2744),m=n(2571),p=n(955),d=n(2941),u=n(9910),g=n(697),h=n(9644),f=n(1788);const _=(0,f.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var y=n(7213);const v=(0,f.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var b=n(2708);const x=(0,f.A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var w=n(2206);const A=e=>{let{isOpen:t,galleries:n,currentGallery:a,onToggle:r,onSelectGallery:s,onCreateGallery:i,onDeleteGallery:l,onSyncGallery:c,isLoading:m=!1}=e;return t?o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-primary font-medium"},"Galleries"),o.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},n.length)),o.createElement(p.A,{title:"Close Sidebar"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(h.A,{strokeWidth:1.5,className:"h-6 w-6"})))),o.createElement("div",{className:"my-4 flex text-sm"},o.createElement("div",{className:"mr-2 w-full"},o.createElement(p.A,{title:"Create new gallery"},o.createElement(d.Ay,{type:"primary",className:"w-full",icon:o.createElement(g.A,{className:"w-4 h-4"}),onClick:i},"New Gallery")))),o.createElement("div",{className:"py-2 flex text-sm text-secondary"},o.createElement("div",{className:"flex"},"All Galleries"),m&&o.createElement(_,{className:"w-4 h-4 ml-2 animate-spin"})),!m&&0===n.length&&o.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},o.createElement(y.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No galleries found"),o.createElement("div",{className:"scroll overflow-y-auto h-[calc(100%-170px)]"},n.map((e=>o.createElement("div",{key:e.id,className:"relative border-secondary"},o.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded "+((null==a?void 0:a.id)===e.id?"bg-accent":"bg-tertiary")}),e&&e.config&&e.config.components&&o.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary "+((null==a?void 0:a.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>s(e)},o.createElement("div",{className:"flex items-center justify-between min-w-0"},o.createElement("div",{className:"flex items-center gap-2 min-w-0 flex-1"},o.createElement("div",{className:"truncate flex-1"},o.createElement("span",{className:"font-medium"},e.config.name)),e.config.url&&o.createElement(p.A,{title:"Remote Gallery"},o.createElement(v,{className:"w-3 h-3 text-secondary flex-shrink-0"}))),o.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0"},e.config.url&&o.createElement(p.A,{title:"Sync gallery"},o.createElement(d.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",icon:o.createElement(_,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation(),c(e.id)}})),o.createElement(p.A,{title:1===n.length?"Cannot delete the last gallery":"Delete gallery"},o.createElement(d.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",danger:!0,disabled:1===n.length,icon:o.createElement(b.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),l(e.id)}})))),o.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},o.createElement("span",{className:"bg-secondary/20 truncate rounded px-1"},"v",e.config.metadata.version),o.createElement("div",{className:"flex items-center gap-1"},o.createElement(x,{className:"w-3 h-3"}),o.createElement("span",null,Object.values(e.config.components).reduce(((e,t)=>e+t.length),0)," ","components"))),e.updated_at&&o.createElement("div",{className:"mt-1 flex items-center gap-1 text-xs text-secondary"},o.createElement("span",null,(0,w.vq)(e.updated_at))))))))):o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"p-2 -ml-2"},o.createElement(p.A,{title:`Galleries (${n.length})`},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(u.A,{strokeWidth:1.5,className:"h-6 w-6"})))),o.createElement("div",{className:"mt-4 px-2 -ml-1"},o.createElement(p.A,{title:"Create new gallery"},o.createElement(d.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:i,icon:o.createElement(g.A,{className:"w-4 h-4"})}))))};var E=n(9957),C=n(6161),k=n(6143);const N=(0,f.A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);var T=n(5404),I=n(8188),O=n(6274),S=n(2640),M=n(6816),P=n(7073),R=n(5680),L=n(8309),G=n(7199),F=n(5144);const D=e=>{let{item:t,onEdit:n,onDuplicate:a,onDelete:r,index:s,allowDelete:i}=e;return o.createElement("div",{className:"bg-secondary rounded overflow-hidden group h-full cursor-pointer",onClick:()=>n(t,s)},o.createElement("div",{className:"px-4 py-3 flex items-center justify-between border-b border-tertiary"},o.createElement("div",{className:"text-xs text-secondary truncate flex-1"},t.provider),o.createElement("div",{className:"flex gap-0"},i&&o.createElement(d.Ay,{title:"Delete",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600",icon:o.createElement(N,{className:"w-3.5 h-3.5"}),onClick:e=>{e.stopPropagation(),r(t,s)}}),o.createElement(d.Ay,{title:"Duplicate",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity",icon:o.createElement(T.A,{className:"w-3.5 h-3.5"}),onClick:e=>{e.stopPropagation(),a(t,s)}}),o.createElement(d.Ay,{title:"Edit",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity",icon:o.createElement(I.A,{className:"w-3.5 h-3.5"}),onClick:e=>{e.stopPropagation(),n(t,s)}}))),o.createElement("div",{className:"p-4 pb-0 pt-3"},o.createElement("div",{className:"text-base font-medium mb-2"},t.label),o.createElement("div",{className:"text-sm text-secondary line-clamp-2 mb-3 min-h-[40px]"},o.createElement(w.PA,{content:t.description||"",showFullscreen:!1,textThreshold:70}))))},z=e=>{let{items:t,title:n,...a}=e;return o.createElement("div",null,o.createElement("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr"},t.map(((e,n)=>o.createElement(D,Object.assign({key:n,item:e,index:n,allowDelete:t.length>1},a))))))},j={team:O.A,agent:S.A,tool:M.A,model:P.A,termination:R.A},U={team:{selector_prompt:"Default selector prompt",participants:[]},agent:{name:"New Agent",description:""},model:{model:"gpt-3.5",api_key:""},tool:{source_code:"",name:"New Tool",description:"A new tool",global_imports:[],has_cancellation_support:!1},termination:{max_messages:1}},$=e=>{let{gallery:t,onSave:n,onDirtyStateChange:a}=e;if(!t.config.components)return o.createElement("div",{className:"text-secondary"},"No components found");const{0:s,1:i}=(0,o.useState)(null),{0:l,1:c}=(0,o.useState)("team"),{0:m,1:u}=(0,o.useState)(!1),{0:h,1:f}=(0,o.useState)(t.config.name),{0:_,1:y}=(0,o.useState)(t.config.metadata.description);(0,o.useEffect)((()=>{f(t.config.name),y(t.config.metadata.description),c("team"),i(null)}),[t.id]);const b=(e,o)=>{const r={...t,config:{...t.config,components:{...t.config.components,[e]:o(t.config.components[e])}}};n(r),a(!0)},w={onEdit:(e,t)=>{i({component:e,category:`${l}s`,index:t})},onDuplicate:(e,n)=>{var o;const a=`${l}s`,s=null===(o=e.label)||void 0===o?void 0:o.replace(/_\d+$/,""),i=t.config.components[a],c=Math.max.apply(Math,(0,r.A)(i.map((e=>{var t;const n=null===(t=e.label)||void 0===t?void 0:t.match(new RegExp(`^${s}_?(\\d+)?$`));return n?parseInt(n[1]||"0"):0})).filter((e=>!isNaN(e)))).concat([0]))+1;b(a,(t=>[].concat((0,r.A)(t),[{...e,label:`${s}_${c}`}])))},onDelete:(e,t)=>{b(`${l}s`,(e=>e.filter(((e,n)=>n!==t))))}},A=Object.entries(j).map((e=>{let[n,a]=e;return{key:n,label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(a,{className:"w-5 h-5"}),n.charAt(0).toUpperCase()+n.slice(1),"s",o.createElement("span",{className:"text-xs font-light text-secondary"},"(",t.config.components[`${n}s`].length,")")),children:o.createElement("div",null,o.createElement("div",{className:"flex justify-between items-center mb-4"},o.createElement("h3",{className:"text-base font-medium"},t.config.components[`${n}s`].length," ",1===t.config.components[`${n}s`].length?n.charAt(0).toUpperCase()+n.slice(1):n.charAt(0).toUpperCase()+n.slice(1)+"s"),o.createElement(d.Ay,{type:"primary",icon:o.createElement(g.A,{className:"w-4 h-4"}),onClick:()=>{c(n),(()=>{const e=`${l}s`,n=t.config.components[e];let o;const a=`New ${l.charAt(0).toUpperCase()+l.slice(1)}`;o=n.length>0?{...n[0],label:a}:{provider:"new",component_type:l,config:U[l],label:a},b(e,(t=>{const n=[].concat((0,r.A)(t),[o]);return i({component:o,category:e,index:n.length-1}),n}))})()}},`Add ${n.charAt(0).toUpperCase()+n.slice(1)}`)),o.createElement(z,Object.assign({items:t.config.components[`${n}s`],title:n},w)))}}));return o.createElement("div",{className:"max-w-7xl mx-auto px-4"},o.createElement("div",{className:"relative h-64 rounded bg-secondary overflow-hidden mb-8"},o.createElement("img",{src:"/images/bg/layeredbg.svg",alt:"Gallery Banner",className:"absolute w-full h-full object-cover"}),o.createElement("div",{className:"relative z-10 p-6 h-full flex flex-col justify-between"},o.createElement("div",null,o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center gap-2"},m?o.createElement(E.A,{value:h,onChange:e=>f(e.target.value),className:"text-2xl font-medium bg-background/50 backdrop-blur px-2 py-1 rounded w-[400px]"}):o.createElement("h1",{className:"text-2xl font-medium text-primary"},t.config.name),t.config.url&&o.createElement(p.A,{title:"Remote Gallery"},o.createElement(v,{className:"w-5 h-5 text-secondary"})))),m?o.createElement(F.A,{value:_,onChange:e=>y(e.target.value),className:"w-1/2 bg-background/50 backdrop-blur px-2 py-1 rounded mt-2",rows:2}):o.createElement("div",{className:"flex flex-col gap-2"},o.createElement("p",{className:"text-secondary w-1/2 mt-2 line-clamp-2"},t.config.metadata.description),o.createElement("div",{className:"flex gap-0"},o.createElement(p.A,{title:"Edit Gallery"},o.createElement(d.Ay,{icon:o.createElement(I.A,{className:"w-4 h-4"}),onClick:()=>u(!0),type:"text",className:"text-white hover:text-white/80"})),o.createElement(p.A,{title:"Download Gallery"},o.createElement(d.Ay,{icon:o.createElement(L.A,{className:"w-4 h-4"}),onClick:()=>{const e=JSON.stringify(t,null,2),n=new Blob([e],{type:"application/json"}),o=URL.createObjectURL(n),a=document.createElement("a");a.href=o,a.download=`${t.config.name.toLowerCase().replace(/\s+/g,"_")}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(o)},type:"text",className:"text-white hover:text-white/80"})))),m&&o.createElement("div",{className:"flex gap-2 mt-2"},o.createElement(d.Ay,{onClick:()=>u(!1)},"Cancel"),o.createElement(d.Ay,{type:"primary",onClick:()=>{const e={...t,config:{...t.config,name:h,metadata:{...t.config.metadata,description:_}}};n(e),a(!0),u(!1)}},"Save"))),o.createElement("div",{className:"flex gap-2"},o.createElement("div",{className:"bg-tertiary backdrop-blur rounded p-2 flex items-center gap-2"},o.createElement(x,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-sm"},Object.values(t.config.components).reduce(((e,t)=>e+t.length),0)," ","components")),o.createElement("div",{className:"bg-tertiary backdrop-blur rounded p-2 text-sm"},"v",t.config.metadata.version)))),o.createElement(C.A,{items:A,className:"gallery-tabs",size:"large",onChange:e=>c(e)}),o.createElement(k.A,{title:"Edit Component",placement:"right",size:"large",onClose:()=>i(null),open:!!s,className:"component-editor-drawer"},s&&o.createElement(G.L,{component:s.component,onChange:e=>{s&&(b(s.category,(t=>t.map(((t,n)=>n===s.index?e:t)))),i(null))},onClose:()=>i(null),navigationDepth:!0})))};var q=n(8355),B=n(7260),W=n(4796),H=n(3164),V=n(9872);const Y=(()=>{try{return n(1610)}catch(e){throw console.error("Error loading gallery JSON:",e),e}})(),K=e=>{let{open:t,onCancel:n,onCreateGallery:a}=e;const{0:r,1:s}=(0,o.useState)("url"),{0:l,1:c}=(0,o.useState)(""),{0:m,1:p}=(0,o.useState)(JSON.stringify(Y,null,2)),{0:u,1:g}=(0,o.useState)(""),{0:h,1:f}=(0,o.useState)(!1),_=(0,o.useRef)(null),y={accept:".json",showUploadList:!1,customRequest:e=>{let{file:t,onSuccess:n}=e;setTimeout((()=>{n&&n("ok")}),0)},onChange:e=>{const{status:t,originFileObj:o}=e.file;if("done"===t&&o instanceof File){const e=new FileReader;e.onload=e=>{try{var t;const o=JSON.parse(null===(t=e.target)||void 0===t?void 0:t.result);a({config:o}),n()}catch(o){g("Invalid JSON file")}},e.readAsText(o)}else"error"===t&&g("File upload failed")}},b=(0,o.useRef)(null),x=[{key:"url",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(v,{className:"w-4 h-4"})," URL Import"),children:o.createElement("div",{className:"space-y-4"},o.createElement(E.A,{ref:b,placeholder:"Enter gallery URL...",value:l,onChange:e=>c(e.target.value)}),o.createElement("div",{className:"text-xs"},"Sample",o.createElement("a",{role:"button",onClick:e=>{c("https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json"),e.preventDefault()},href:"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json",target:"_blank",rel:"noreferrer",className:"text-accent"}," ","gallery.json"," ")),o.createElement(d.Ay,{type:"primary",onClick:async()=>{f(!0),g("");try{const e=await fetch(l),t=await e.json();a({config:t}),n()}catch(e){g("Failed to fetch or parse gallery from URL")}finally{f(!1)}},disabled:!l||h,block:!0},"Import from URL"))},{key:"file",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(W.A,{className:"w-4 h-4"})," File Upload"),children:o.createElement("div",{className:"border-2 border-dashed rounded-lg p-8 text-center space-y-4"},o.createElement(q.A.Dragger,y,o.createElement("p",{className:"ant-upload-drag-icon"},o.createElement(W.A,{className:"w-8 h-8 mx-auto text-secondary"})),o.createElement("p",{className:"ant-upload-text"},"Click or drag JSON file to this area")))},{key:"paste",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(H.A,{className:"w-4 h-4"})," Paste JSON"),children:o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"h-64"},o.createElement(V.T,{value:m,onChange:p,editorRef:_,language:"json",minimap:!1})),o.createElement(d.Ay,{type:"primary",onClick:()=>{try{const e=JSON.parse(m);a({config:e}),n()}catch(e){g("Invalid JSON format")}},block:!0},"Import JSON"))}];return o.createElement(i.A,{title:"Create New Gallery",open:t,onCancel:n,footer:null,width:800},o.createElement("div",{className:"mt-4"},o.createElement(C.A,{activeKey:r,onChange:s,items:x}),u&&o.createElement(B.A,{message:u,type:"error",showIcon:!0,className:"mt-4"})))};var J=()=>{const{0:e,1:t}=(0,o.useState)(!1),{0:n,1:a}=(0,o.useState)([]),{0:p,1:d}=(0,o.useState)(null),{0:u,1:g}=(0,o.useState)(!1),{0:h,1:f}=(0,o.useState)(!1),{0:_,1:y}=(0,o.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("gallerySidebar");return null===e||JSON.parse(e)}return!0})),{user:v}=(0,o.useContext)(c.v),[b,x]=s.Ay.useMessage();(0,o.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("gallerySidebar",JSON.stringify(_))}),[_]);const w=(0,o.useCallback)((async()=>{if(null!=v&&v.id)try{t(!0);const e=await m.f.listGalleries(v.id);a(e),!p&&e.length>0&&d(e[0])}catch(e){console.error("Error fetching galleries:",e),b.error("Failed to fetch galleries")}finally{t(!1)}}),[null==v?void 0:v.id,p,b]);(0,o.useEffect)((()=>{w()}),[w]),(0,o.useEffect)((()=>{const e=new URLSearchParams(window.location.search).get("galleryId");if(e&&!p){const t=parseInt(e,10);isNaN(t)||E(t)}}),[]),(0,o.useEffect)((()=>{null!=p&&p.id&&window.history.pushState({},"",`?galleryId=${p.id.toString()}`)}),[null==p?void 0:p.id]);const E=async e=>{null!=v&&v.id&&(h?i.A.confirm({title:"Unsaved Changes",content:"You have unsaved changes. Do you want to discard them?",okText:"Discard",cancelText:"Go Back",onOk:()=>{C(e),f(!1)}}):await C(e))},C=async e=>{if(null!=v&&v.id){t(!0);try{const t=await m.f.getGallery(e,v.id);d(t)}catch(n){console.error("Error loading gallery:",n),b.error("Failed to load gallery")}finally{t(!1)}}},k=async e=>{if(null!=v&&v.id&&null!=p&&p.id)try{const t={...e,created_at:void 0,updated_at:void 0},o=await m.f.updateGallery(p.id,t,v.id);a(n.map((e=>e.id===o.id?o:e))),d(o),f(!1),b.success("Gallery updated successfully")}catch(t){console.error("Error updating gallery:",t),b.error("Failed to update gallery")}};return null!=v&&v.id?o.createElement("div",{className:"relative flex h-full w-full"},x,o.createElement(K,{open:u,onCancel:()=>g(!1),onCreateGallery:async e=>{if(null!=v&&v.id){e.user_id=v.id;try{const t=await m.f.createGallery(e,v.id);a([t].concat((0,r.A)(n))),d(t),g(!1),b.success("Gallery created successfully")}catch(t){console.error("Error creating gallery:",t),b.error("Failed to create gallery")}}}}),o.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(_?"w-64":"w-12")},o.createElement(A,{isOpen:_,galleries:n,currentGallery:p,onToggle:()=>y(!_),onSelectGallery:e=>E(e.id),onCreateGallery:()=>g(!0),onDeleteGallery:async e=>{if(null!=v&&v.id)try{await m.f.deleteGallery(e,v.id),a(n.filter((t=>t.id!==e))),(null==p?void 0:p.id)===e&&d(null),b.success("Gallery deleted successfully")}catch(t){console.error("Error deleting gallery:",t),b.error("Failed to delete gallery")}},onSyncGallery:async e=>{if(null!=v&&v.id)try{t(!0);const o=n.find((t=>t.id===e));if(null==o||!o.config.url)return;const a=await m.f.syncGallery(o.config.url);await k({...a,id:e,config:{...a.config,metadata:{...a.config.metadata,lastSynced:(new Date).toISOString()}}}),b.success("Gallery synced successfully")}catch(o){console.error("Error syncing gallery:",o),b.error("Failed to sync gallery")}finally{t(!1)}},isLoading:e})),o.createElement("div",{className:"flex-1 transition-all -mr-6 duration-200 "+(_?"ml-64":"ml-12")},o.createElement("div",{className:"p-4 pt-2"},o.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},o.createElement("span",{className:"text-primary font-medium"},"Galleries"),p&&o.createElement(o.Fragment,null,o.createElement(l.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-secondary"},p.config.name))),e&&!p?o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Loading galleries..."):p?o.createElement($,{gallery:p,onSave:k,onDirtyStateChange:f}):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Select a gallery from the sidebar or create a new one")))):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Please log in to view galleries")};var X=e=>{let{data:t}=e;return o.createElement(a.A,{meta:t.site.siteMetadata,title:"Home",link:"/gallery"},o.createElement("main",{style:{height:"100%"},className:" h-full "},o.createElement(J,null)))}},5404:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6274:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},6816:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},7260:function(e,t,n){n.d(t,{A:function(){return z}});var o=n(6540),a=n(8811),r=n(6029),s=n(7852),i=n(7541),l=n(7850),c=n(6942),m=n.n(c),p=n(754),d=n(2065),u=n(8719),g=n(682),h=n(2279),f=n(2187),_=n(5905),y=n(7358);const v=(e,t,n,o,a)=>({background:e,border:`${(0,f.zA)(o.lineWidth)} ${o.lineType} ${t}`,[`${a}-icon`]:{color:n}}),b=e=>{const{componentCls:t,motionDurationSlow:n,marginXS:o,marginSM:a,fontSize:r,fontSizeLG:s,lineHeight:i,borderRadiusLG:l,motionEaseInOutCirc:c,withDescriptionIconSize:m,colorText:p,colorTextHeading:d,withDescriptionPadding:u,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,_.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:r,lineHeight:i},"&-message":{color:d},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${c}, opacity ${n} ${c},\n        padding-top ${n} ${c}, padding-bottom ${n} ${c},\n        margin-bottom ${n} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:u,[`${t}-icon`]:{marginInlineEnd:a,fontSize:m,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:d,fontSize:s},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},x=e=>{const{componentCls:t,colorSuccess:n,colorSuccessBorder:o,colorSuccessBg:a,colorWarning:r,colorWarningBorder:s,colorWarningBg:i,colorError:l,colorErrorBorder:c,colorErrorBg:m,colorInfo:p,colorInfoBorder:d,colorInfoBg:u}=e;return{[t]:{"&-success":v(a,o,n,e,t),"&-info":v(u,d,p,e,t),"&-warning":v(i,s,r,e,t),"&-error":Object.assign(Object.assign({},v(m,c,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},w=e=>{const{componentCls:t,iconCls:n,motionDurationMid:o,marginXS:a,fontSizeIcon:r,colorIcon:s,colorIconHover:i}=e;return{[t]:{"&-action":{marginInlineStart:a},[`${t}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:r,lineHeight:(0,f.zA)(r),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:s,transition:`color ${o}`,"&:hover":{color:i}}},"&-close-text":{color:s,transition:`color ${o}`,"&:hover":{color:i}}}}};var A=(0,y.OF)("Alert",(e=>[b(e),x(e),w(e)]),(e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}))),E=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]])}return n};const C={success:a.A,info:l.A,error:r.A,warning:i.A},k=e=>{const{icon:t,prefixCls:n,type:a}=e,r=C[a]||null;return t?(0,g.fx)(t,o.createElement("span",{className:`${n}-icon`},t),(()=>({className:m()(`${n}-icon`,t.props.className)}))):o.createElement(r,{className:`${n}-icon`})},N=e=>{const{isClosable:t,prefixCls:n,closeIcon:a,handleClose:r,ariaProps:i}=e,l=!0===a||void 0===a?o.createElement(s.A,null):a;return t?o.createElement("button",Object.assign({type:"button",onClick:r,className:`${n}-close-icon`,tabIndex:0},i),l):null},T=o.forwardRef(((e,t)=>{const{description:n,prefixCls:a,message:r,banner:s,className:i,rootClassName:l,style:c,onMouseEnter:g,onMouseLeave:f,onClick:_,afterClose:y,showIcon:v,closable:b,closeText:x,closeIcon:w,action:C,id:T}=e,I=E(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,S]=o.useState(!1);const M=o.useRef(null);o.useImperativeHandle(t,(()=>({nativeElement:M.current})));const{getPrefixCls:P,direction:R,closable:L,closeIcon:G,className:F,style:D}=(0,h.TP)("alert"),z=P("alert",a),[j,U,$]=A(z),q=t=>{var n;S(!0),null===(n=e.onClose)||void 0===n||n.call(e,t)},B=o.useMemo((()=>void 0!==e.type?e.type:s?"warning":"info"),[e.type,s]),W=o.useMemo((()=>!("object"!=typeof b||!b.closeIcon)||(!!x||("boolean"==typeof b?b:!1!==w&&null!=w||!!L))),[x,w,b,L]),H=!(!s||void 0!==v)||v,V=m()(z,`${z}-${B}`,{[`${z}-with-description`]:!!n,[`${z}-no-icon`]:!H,[`${z}-banner`]:!!s,[`${z}-rtl`]:"rtl"===R},F,i,l,$,U),Y=(0,d.A)(I,{aria:!0,data:!0}),K=o.useMemo((()=>"object"==typeof b&&b.closeIcon?b.closeIcon:x||(void 0!==w?w:"object"==typeof L&&L.closeIcon?L.closeIcon:G)),[w,b,x,G]),J=o.useMemo((()=>{const e=null!=b?b:L;if("object"==typeof e){const{closeIcon:t}=e;return E(e,["closeIcon"])}return{}}),[b,L]);return j(o.createElement(p.Ay,{visible:!O,motionName:`${z}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},((t,a)=>{let{className:s,style:i}=t;return o.createElement("div",Object.assign({id:T,ref:(0,u.K4)(M,a),"data-show":!O,className:m()(V,s),style:Object.assign(Object.assign(Object.assign({},D),c),i),onMouseEnter:g,onMouseLeave:f,onClick:_,role:"alert"},Y),H?o.createElement(k,{description:n,icon:e.icon,prefixCls:z,type:B}):null,o.createElement("div",{className:`${z}-content`},r?o.createElement("div",{className:`${z}-message`},r):null,n?o.createElement("div",{className:`${z}-description`},n):null),C?o.createElement("div",{className:`${z}-action`},C):null,o.createElement(N,{isClosable:W,prefixCls:z,closeIcon:K,handleClose:q,ariaProps:J}))})))}));var I=T,O=n(3029),S=n(2901),M=n(3954),P=n(2176),R=n(6822);var L=n(5501);let G=function(e){function t(){var e,n,o,a;return(0,O.A)(this,t),n=this,o=t,a=arguments,o=(0,M.A)(o),(e=(0,R.A)(n,(0,P.A)()?Reflect.construct(o,a||[],(0,M.A)(n).constructor):o.apply(n,a))).state={error:void 0,info:{componentStack:""}},e}return(0,L.A)(t,e),(0,S.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){const{message:e,description:t,id:n,children:a}=this.props,{error:r,info:s}=this.state,i=(null==s?void 0:s.componentStack)||null,l=void 0===e?(r||"").toString():e,c=void 0===t?i:t;return r?o.createElement(I,{id:n,type:"error",message:l,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},c)}):a}}])}(o.Component);var F=G;const D=I;D.ErrorBoundary=F;var z=D},8309:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}}]);
//# sourceMappingURL=component---src-pages-gallery-tsx-b70067f72084f2d47ba0.js.map