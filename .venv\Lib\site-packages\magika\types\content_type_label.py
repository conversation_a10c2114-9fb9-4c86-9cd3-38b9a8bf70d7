# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from magika.types.strenum import StrEnum

# NOTE: DO NOT EDIT --- This file is automatically generated.


# This is the list of all possible content types we know about; however, models
# support a smaller subset of them. See model's README.md for details.
class ContentTypeLabel(StrEnum):
    _3DS = "3ds"
    _3DSM = "3dsm"
    _3DSX = "3dsx"
    _3GP = "3gp"
    _3MF = "3mf"
    ABNF = "abnf"
    ACE = "ace"
    ADA = "ada"
    AFF = "aff"
    AI = "ai"
    AIDL = "aidl"
    ALGOL68 = "algol68"
    ANI = "ani"
    APK = "apk"
    APPLEBPLIST = "applebplist"
    APPLEDOUBLE = "appledouble"
    APPLEPLIST = "appleplist"
    APPLESINGLE = "applesingle"
    AR = "ar"
    ARC = "arc"
    ARJ = "arj"
    ARROW = "arrow"
    ASC = "asc"
    ASD = "asd"
    ASF = "asf"
    ASM = "asm"
    ASP = "asp"
    AU = "au"
    AUTOHOTKEY = "autohotkey"
    AUTOIT = "autoit"
    AVI = "avi"
    AVIF = "avif"
    AVRO = "avro"
    AWK = "awk"
    AX = "ax"
    BATCH = "batch"
    BAZEL = "bazel"
    BCAD = "bcad"
    BIB = "bib"
    BMP = "bmp"
    BPG = "bpg"
    BPL = "bpl"
    BRAINFUCK = "brainfuck"
    BRF = "brf"
    BZIP = "bzip"
    BZIP3 = "bzip3"
    C = "c"
    CAB = "cab"
    CAD = "cad"
    CAT = "cat"
    CDF = "cdf"
    CHM = "chm"
    CLOJURE = "clojure"
    CMAKE = "cmake"
    COBOL = "cobol"
    COFF = "coff"
    COFFEESCRIPT = "coffeescript"
    COM = "com"
    CPL = "cpl"
    CPP = "cpp"
    CRT = "crt"
    CRX = "crx"
    CS = "cs"
    CSPROJ = "csproj"
    CSS = "css"
    CSV = "csv"
    CTL = "ctl"
    DART = "dart"
    DEB = "deb"
    DEX = "dex"
    DEY = "dey"
    DICOM = "dicom"
    DIFF = "diff"
    DIRECTORY = "directory"
    DJANGO = "django"
    DLL = "dll"
    DM = "dm"
    DMG = "dmg"
    DMIGD = "dmigd"
    DMSCRIPT = "dmscript"
    DOC = "doc"
    DOCKERFILE = "dockerfile"
    DOCX = "docx"
    DOSMBR = "dosmbr"
    DOTX = "dotx"
    DSSTORE = "dsstore"
    DWG = "dwg"
    DXF = "dxf"
    DYLIB = "dylib"
    EBML = "ebml"
    ELF = "elf"
    ELIXIR = "elixir"
    EMF = "emf"
    EML = "eml"
    EMPTY = "empty"
    EPUB = "epub"
    ERB = "erb"
    ERLANG = "erlang"
    ESE = "ese"
    EXE = "exe"
    EXP = "exp"
    FLAC = "flac"
    FLUTTER = "flutter"
    FLV = "flv"
    FORTRAN = "fortran"
    FPX = "fpx"
    GEMFILE = "gemfile"
    GEMSPEC = "gemspec"
    GIF = "gif"
    GITATTRIBUTES = "gitattributes"
    GITMODULES = "gitmodules"
    GLEAM = "gleam"
    GO = "go"
    GPX = "gpx"
    GRADLE = "gradle"
    GROOVY = "groovy"
    GZIP = "gzip"
    H = "h"
    H5 = "h5"
    HANDLEBARS = "handlebars"
    HASKELL = "haskell"
    HCL = "hcl"
    HEIF = "heif"
    HFS = "hfs"
    HLP = "hlp"
    HPP = "hpp"
    HTA = "hta"
    HTACCESS = "htaccess"
    HTML = "html"
    HVE = "hve"
    HWP = "hwp"
    ICC = "icc"
    ICNS = "icns"
    ICO = "ico"
    ICS = "ics"
    IGNOREFILE = "ignorefile"
    IMG = "img"
    INI = "ini"
    INTERNETSHORTCUT = "internetshortcut"
    IOSAPP = "iosapp"
    IPYNB = "ipynb"
    ISO = "iso"
    JAR = "jar"
    JAVA = "java"
    JAVABYTECODE = "javabytecode"
    JAVASCRIPT = "javascript"
    JINJA = "jinja"
    JNG = "jng"
    JNLP = "jnlp"
    JP2 = "jp2"
    JPEG = "jpeg"
    JSON = "json"
    JSONC = "jsonc"
    JSONL = "jsonl"
    JSX = "jsx"
    JULIA = "julia"
    JXL = "jxl"
    KO = "ko"
    KOTLIN = "kotlin"
    KS = "ks"
    LATEX = "latex"
    LATEXAUX = "latexaux"
    LESS = "less"
    LHA = "lha"
    LICENSE = "license"
    LISP = "lisp"
    LITCS = "litcs"
    LNK = "lnk"
    LOCK = "lock"
    LRZ = "lrz"
    LUA = "lua"
    LZ = "lz"
    LZ4 = "lz4"
    LZX = "lzx"
    M3U = "m3u"
    M4 = "m4"
    MACHO = "macho"
    MAFF = "maff"
    MAKEFILE = "makefile"
    MARKDOWN = "markdown"
    MATLAB = "matlab"
    MHT = "mht"
    MIDI = "midi"
    MKV = "mkv"
    MP2 = "mp2"
    MP3 = "mp3"
    MP4 = "mp4"
    MPEGTS = "mpegts"
    MSCOMPRESS = "mscompress"
    MSI = "msi"
    MSIX = "msix"
    MST = "mst"
    MUI = "mui"
    MUM = "mum"
    MUN = "mun"
    NIM = "nim"
    NPY = "npy"
    NPZ = "npz"
    NULL = "null"
    NUPKG = "nupkg"
    OBJECT = "object"
    OBJECTIVEC = "objectivec"
    OCAML = "ocaml"
    OCX = "ocx"
    ODEX = "odex"
    ODIN = "odin"
    ODP = "odp"
    ODS = "ods"
    ODT = "odt"
    OGG = "ogg"
    OLE = "ole"
    ONE = "one"
    ONNX = "onnx"
    OOXML = "ooxml"
    OTF = "otf"
    OUTLOOK = "outlook"
    PALMOS = "palmos"
    PARQUET = "parquet"
    PASCAL = "pascal"
    PBM = "pbm"
    PCAP = "pcap"
    PDB = "pdb"
    PDF = "pdf"
    PEBIN = "pebin"
    PEM = "pem"
    PERL = "perl"
    PGP = "pgp"
    PHP = "php"
    PICKLE = "pickle"
    PNG = "png"
    PO = "po"
    POSTSCRIPT = "postscript"
    POWERSHELL = "powershell"
    PPT = "ppt"
    PPTX = "pptx"
    PRINTFOX = "printfox"
    PROLOG = "prolog"
    PROTEINDB = "proteindb"
    PROTO = "proto"
    PROTOBUF = "protobuf"
    PSD = "psd"
    PUB = "pub"
    PYTHON = "python"
    PYTHONBYTECODE = "pythonbytecode"
    PYTHONPAR = "pythonpar"
    PYTORCH = "pytorch"
    QOI = "qoi"
    QT = "qt"
    R = "r"
    RANDOMASCII = "randomascii"
    RANDOMBYTES = "randombytes"
    RANDOMTXT = "randomtxt"
    RAR = "rar"
    RDF = "rdf"
    RDP = "rdp"
    RIFF = "riff"
    RLIB = "rlib"
    RLL = "rll"
    RPM = "rpm"
    RST = "rst"
    RTF = "rtf"
    RUBY = "ruby"
    RUST = "rust"
    RZIP = "rzip"
    SCALA = "scala"
    SCHEME = "scheme"
    SCR = "scr"
    SCRIPTWSF = "scriptwsf"
    SCSS = "scss"
    SEVENZIP = "sevenzip"
    SGML = "sgml"
    SH3D = "sh3d"
    SHELL = "shell"
    SMALI = "smali"
    SNAP = "snap"
    SO = "so"
    SOLIDITY = "solidity"
    SQL = "sql"
    SQLITE = "sqlite"
    SQUASHFS = "squashfs"
    SRT = "srt"
    STLBINARY = "stlbinary"
    STLTEXT = "stltext"
    SUM = "sum"
    SVD = "svd"
    SVG = "svg"
    SWF = "swf"
    SWIFT = "swift"
    SYMLINK = "symlink"
    SYMLINKTEXT = "symlinktext"
    SYS = "sys"
    TAR = "tar"
    TCL = "tcl"
    TEXTPROTO = "textproto"
    TGA = "tga"
    THUMBSDB = "thumbsdb"
    TIFF = "tiff"
    TMDX = "tmdx"
    TOML = "toml"
    TORRENT = "torrent"
    TROFF = "troff"
    TSV = "tsv"
    TSX = "tsx"
    TTF = "ttf"
    TWIG = "twig"
    TXT = "txt"
    TXTASCII = "txtascii"
    TXTUTF16 = "txtutf16"
    TXTUTF8 = "txtutf8"
    TYPESCRIPT = "typescript"
    UDF = "udf"
    UNDEFINED = "undefined"
    UNIXCOMPRESS = "unixcompress"
    UNKNOWN = "unknown"
    VBA = "vba"
    VBE = "vbe"
    VCARD = "vcard"
    VCS = "vcs"
    VCXPROJ = "vcxproj"
    VERILOG = "verilog"
    VHD = "vhd"
    VHDL = "vhdl"
    VISIO = "visio"
    VTT = "vtt"
    VUE = "vue"
    WAD = "wad"
    WASM = "wasm"
    WAV = "wav"
    WEBM = "webm"
    WEBP = "webp"
    WEBTEMPLATE = "webtemplate"
    WIM = "wim"
    WINREGISTRY = "winregistry"
    WMA = "wma"
    WMF = "wmf"
    WMV = "wmv"
    WOFF = "woff"
    WOFF2 = "woff2"
    XAR = "xar"
    XCF = "xcf"
    XLS = "xls"
    XLSB = "xlsb"
    XLSX = "xlsx"
    XML = "xml"
    XPI = "xpi"
    XSD = "xsd"
    XZ = "xz"
    YAML = "yaml"
    YARA = "yara"
    ZIG = "zig"
    ZIP = "zip"
    ZLIBSTREAM = "zlibstream"
    ZST = "zst"

    def __repr__(self) -> str:
        return str(self)
