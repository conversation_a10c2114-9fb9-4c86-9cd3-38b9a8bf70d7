/*! For license information please see fea25e403f7cf4f6c7352c9a59c3f8f4372c2770-673333103db2a927e4c0.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[46],{181:function(e,t,n){var a=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,l=/^0o[0-7]+$/i,i=parseInt,c="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,s="object"==typeof self&&self&&self.Object===Object&&self,u=c||s||Function("return this")(),m=Object.prototype.toString,d=Math.max,p=Math.min,f=function(){return u.Date.now()};function g(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function h(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==m.call(e)}(e))return NaN;if(g(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=g(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(a,"");var n=o.test(e);return n||l.test(e)?i(e.slice(2),n?2:8):r.test(e)?NaN:+e}e.exports=function(e,t,n){var a,r,o,l,i,c,s=0,u=!1,m=!1,b=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function v(t){var n=a,o=r;return a=r=void 0,s=t,l=e.apply(o,n)}function y(e){var n=e-c;return void 0===c||n>=t||n<0||m&&e-s>=o}function E(){var e=f();if(y(e))return N(e);i=setTimeout(E,function(e){var n=t-(e-c);return m?p(n,o-(e-s)):n}(e))}function N(e){return i=void 0,b&&a?v(e):(a=r=void 0,l)}function x(){var e=f(),n=y(e);if(a=arguments,r=this,c=e,n){if(void 0===i)return function(e){return s=e,i=setTimeout(E,t),u?v(e):l}(c);if(m)return i=setTimeout(E,t),v(c)}return void 0===i&&(i=setTimeout(E,t)),l}return t=h(t)||0,g(n)&&(u=!!n.leading,o=(m="maxWait"in n)?d(h(n.maxWait)||0,t):o,b="trailing"in n?!!n.trailing:b),x.cancel=function(){void 0!==i&&clearTimeout(i),s=0,a=c=r=i=void 0},x.flush=function(){return void 0===i?l:N(f())},x}},2609:function(e,t,n){"use strict";n.d(t,{A:function(){return R}});var a=n(6540),r=n(3567),o=n(6942),l=n.n(o),i=n(8168),c=n(4467),s=n(5544),u=n(3986),m=n(2533),d=n(6928),p=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],f=a.forwardRef((function(e,t){var n,r=e.prefixCls,o=void 0===r?"rc-switch":r,f=e.className,g=e.checked,h=e.defaultChecked,b=e.disabled,v=e.loadingIcon,y=e.checkedChildren,E=e.unCheckedChildren,N=e.onClick,x=e.onChange,w=e.onKeyDown,A=(0,u.A)(e,p),C=(0,m.A)(!1,{value:g,defaultValue:h}),k=(0,s.A)(C,2),S=k[0],$=k[1];function O(e,t){var n=S;return b||($(n=e),null==x||x(n,t)),n}var I=l()(o,f,(n={},(0,c.A)(n,"".concat(o,"-checked"),S),(0,c.A)(n,"".concat(o,"-disabled"),b),n));return a.createElement("button",(0,i.A)({},A,{type:"button",role:"switch","aria-checked":S,disabled:b,className:I,ref:t,onKeyDown:function(e){e.which===d.A.LEFT?O(!1,e):e.which===d.A.RIGHT&&O(!0,e),null==w||w(e)},onClick:function(e){var t=O(!S,e);null==N||N(t,e)}}),v,a.createElement("span",{className:"".concat(o,"-inner")},a.createElement("span",{className:"".concat(o,"-inner-checked")},y),a.createElement("span",{className:"".concat(o,"-inner-unchecked")},E)))}));f.displayName="Switch";var g=f,h=n(57),b=n(2279),v=n(8119),y=n(829),E=n(2187),N=n(2616),x=n(5905),w=n(7358),A=n(4277);const C=e=>{const{componentCls:t,trackHeightSM:n,trackPadding:a,trackMinWidthSM:r,innerMinMarginSM:o,innerMaxMarginSM:l,handleSizeSM:i,calc:c}=e,s=`${t}-inner`,u=(0,E.zA)(c(i).add(c(a).mul(2)).equal()),m=(0,E.zA)(c(l).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:r,height:n,lineHeight:(0,E.zA)(n),[`${t}-inner`]:{paddingInlineStart:l,paddingInlineEnd:o,[`${s}-checked, ${s}-unchecked`]:{minHeight:n},[`${s}-checked`]:{marginInlineStart:`calc(-100% + ${u} - ${m})`,marginInlineEnd:`calc(100% - ${u} + ${m})`},[`${s}-unchecked`]:{marginTop:c(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:i,height:i},[`${t}-loading-icon`]:{top:c(c(i).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:o,paddingInlineEnd:l,[`${s}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${s}-unchecked`]:{marginInlineStart:`calc(100% - ${u} + ${m})`,marginInlineEnd:`calc(-100% + ${u} - ${m})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${(0,E.zA)(c(i).add(a).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${s}`]:{[`${s}-unchecked`]:{marginInlineStart:c(e.marginXXS).div(2).equal(),marginInlineEnd:c(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${s}`]:{[`${s}-checked`]:{marginInlineStart:c(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:c(e.marginXXS).div(2).equal()}}}}}}},k=e=>{const{componentCls:t,handleSize:n,calc:a}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:a(a(n).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},S=e=>{const{componentCls:t,trackPadding:n,handleBg:a,handleShadow:r,handleSize:o,calc:l}=e,i=`${t}-handle`;return{[t]:{[i]:{position:"absolute",top:n,insetInlineStart:n,width:o,height:o,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:l(o).div(2).equal(),boxShadow:r,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${i}`]:{insetInlineStart:`calc(100% - ${(0,E.zA)(l(o).add(n).equal())})`},[`&:not(${t}-disabled):active`]:{[`${i}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${i}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},$=e=>{const{componentCls:t,trackHeight:n,trackPadding:a,innerMinMargin:r,innerMaxMargin:o,handleSize:l,calc:i}=e,c=`${t}-inner`,s=(0,E.zA)(i(l).add(i(a).mul(2)).equal()),u=(0,E.zA)(i(o).mul(2).equal());return{[t]:{[c]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:o,paddingInlineEnd:r,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${c}-checked, ${c}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:n},[`${c}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${u})`,marginInlineEnd:`calc(100% - ${s} + ${u})`},[`${c}-unchecked`]:{marginTop:i(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${c}`]:{paddingInlineStart:r,paddingInlineEnd:o,[`${c}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${c}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${u})`,marginInlineEnd:`calc(-100% + ${s} - ${u})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${c}`]:{[`${c}-unchecked`]:{marginInlineStart:i(a).mul(2).equal(),marginInlineEnd:i(a).mul(-1).mul(2).equal()}},[`&${t}-checked ${c}`]:{[`${c}-checked`]:{marginInlineStart:i(a).mul(-1).mul(2).equal(),marginInlineEnd:i(a).mul(2).equal()}}}}}},O=e=>{const{componentCls:t,trackHeight:n,trackMinWidth:a}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,x.dF)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:n,lineHeight:(0,E.zA)(n),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),(0,x.K8)(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}};var I=(0,w.OF)("Switch",(e=>{const t=(0,A.oX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[O(t),$(t),S(t),k(t),C(t)]}),(e=>{const{fontSize:t,lineHeight:n,controlHeight:a,colorWhite:r}=e,o=t*n,l=a/2,i=o-4,c=l-4;return{trackHeight:o,trackHeightSM:l,trackMinWidth:2*i+8,trackMinWidthSM:2*c+4,trackPadding:2,handleBg:r,handleSize:i,handleSizeSM:c,handleShadow:`0 2px 4px 0 ${new N.Y("#00230b").setA(.2).toRgbString()}`,innerMinMargin:i/2,innerMaxMargin:i+2+4,innerMinMarginSM:c/2,innerMaxMarginSM:c+2+4}})),_=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const M=a.forwardRef(((e,t)=>{const{prefixCls:n,size:o,disabled:i,loading:c,className:s,rootClassName:u,style:d,checked:p,value:f,defaultChecked:E,defaultValue:N,onChange:x}=e,w=_(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[A,C]=(0,m.A)(!1,{value:null!=p?p:f,defaultValue:null!=E?E:N}),{getPrefixCls:k,direction:S,switch:$}=a.useContext(b.QO),O=a.useContext(v.A),M=(null!=i?i:O)||c,j=k("switch",n),R=a.createElement("div",{className:`${j}-handle`},c&&a.createElement(r.A,{className:`${j}-loading-icon`})),[T,z,D]=I(j),P=(0,y.A)(o),q=l()(null==$?void 0:$.className,{[`${j}-small`]:"small"===P,[`${j}-loading`]:c,[`${j}-rtl`]:"rtl"===S},s,u,z,D),F=Object.assign(Object.assign({},null==$?void 0:$.style),d);return T(a.createElement(h.A,{component:"Switch"},a.createElement(g,Object.assign({},w,{checked:A,onChange:function(){C(arguments.length<=0?void 0:arguments[0]),null==x||x.apply(void 0,arguments)},prefixCls:j,className:q,style:F,disabled:M,ref:t,loadingIcon:R}))))})),j=M;j.__ANT_SWITCH=!0;var R=j},3164:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},4279:function(e,t,n){"use strict";n.d(t,{Ed:function(){return g},HX:function(){return m},Il:function(){return u},Jz:function(){return h},K1:function(){return N},O6:function(){return d},U9:function(){return s},VZ:function(){return o},WR:function(){return E},Zj:function(){return f},d9:function(){return v},fF:function(){return l},fk:function(){return p},gG:function(){return c},nL:function(){return i},qt:function(){return y},wx:function(){return b},xq:function(){return a}});const a={ROUND_ROBIN_TEAM:"autogen_agentchat.teams.RoundRobinGroupChat",SELECTOR_TEAM:"autogen_agentchat.teams.SelectorGroupChat",ASSISTANT_AGENT:"autogen_agentchat.agents.AssistantAgent",USER_PROXY:"autogen_agentchat.agents.UserProxyAgent",WEB_SURFER:"autogen_ext.agents.web_surfer.MultimodalWebSurfer",OPENAI:"autogen_ext.models.openai.OpenAIChatCompletionClient",AZURE_OPENAI:"autogen_ext.models.openai.AzureOpenAIChatCompletionClient",ANTHROPIC:"autogen_ext.models.anthropic.AnthropicChatCompletionClient",FUNCTION_TOOL:"autogen_core.tools.FunctionTool",OR_TERMINATION:"autogen_agentchat.base.OrTerminationCondition",AND_TERMINATION:"autogen_agentchat.base.AndTerminationCondition",MAX_MESSAGE:"autogen_agentchat.conditions.MaxMessageTermination",TEXT_MENTION:"autogen_agentchat.conditions.TextMentionTermination",UNBOUNDED_CONTEXT:"autogen_core.model_context.UnboundedChatCompletionContext"};function r(e,t){return e.provider===t}function o(e){return"team"===e.component_type}function l(e){return"agent"===e.component_type}function i(e){return"model"===e.component_type}function c(e){return"tool"===e.component_type}function s(e){return"termination"===e.component_type}function u(e){return r(e,a.ROUND_ROBIN_TEAM)}function m(e){return r(e,a.SELECTOR_TEAM)}function d(e){return r(e,a.ASSISTANT_AGENT)}function p(e){return r(e,a.USER_PROXY)}function f(e){return r(e,a.WEB_SURFER)}function g(e){return r(e,a.OPENAI)}function h(e){return r(e,a.AZURE_OPENAI)}function b(e){return e.provider===a.ANTHROPIC}function v(e){return r(e,a.FUNCTION_TOOL)}function y(e){return r(e,a.OR_TERMINATION)||r(e,a.AND_TERMINATION)}function E(e){return r(e,a.MAX_MESSAGE)}function N(e){return r(e,a.TEXT_MENTION)}},5680:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},6143:function(e,t,n){"use strict";n.d(t,{A:function(){return Y}});var a=n(6540),r=n(6942),o=n.n(r),l=n(9379),i=n(5544),c=n(5062),s=n(981),u=a.createContext(null),m=a.createContext({}),d=u,p=n(4467),f=n(8168),g=n(754),h=n(6928),b=n(2065),v=n(3986),y=n(8719),E=["prefixCls","className","containerRef"];var N=function(e){var t=e.prefixCls,n=e.className,r=e.containerRef,l=(0,v.A)(e,E),i=a.useContext(m).panel,c=(0,y.xK)(i,r);return a.createElement("div",(0,f.A)({className:o()("".concat(t,"-content"),n),role:"dialog",ref:c},(0,b.A)(e,{aria:!0}),{"aria-modal":"true"},l))},x=n(8210);function w(e){return"string"==typeof e&&String(Number(e))===e?((0,x.Ay)(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var A={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function C(e,t){var n,r,c,s=e.prefixCls,u=e.open,m=e.placement,v=e.inline,y=e.push,E=e.forceRender,x=e.autoFocus,C=e.keyboard,k=e.classNames,S=e.rootClassName,$=e.rootStyle,O=e.zIndex,I=e.className,_=e.id,M=e.style,j=e.motion,R=e.width,T=e.height,z=e.children,D=e.mask,P=e.maskClosable,q=e.maskMotion,F=e.maskClassName,B=e.maskStyle,H=e.afterOpenChange,L=e.onClose,X=e.onMouseEnter,W=e.onMouseOver,G=e.onMouseLeave,U=e.onClick,K=e.onKeyDown,V=e.onKeyUp,J=e.styles,Y=e.drawerRender,Q=a.useRef(),Z=a.useRef(),ee=a.useRef();a.useImperativeHandle(t,(function(){return Q.current}));a.useEffect((function(){var e;u&&x&&(null===(e=Q.current)||void 0===e||e.focus({preventScroll:!0}))}),[u]);var te=a.useState(!1),ne=(0,i.A)(te,2),ae=ne[0],re=ne[1],oe=a.useContext(d),le=null!==(n=null!==(r=null===(c="boolean"==typeof y?y?{}:{distance:0}:y||{})||void 0===c?void 0:c.distance)&&void 0!==r?r:null==oe?void 0:oe.pushDistance)&&void 0!==n?n:180,ie=a.useMemo((function(){return{pushDistance:le,push:function(){re(!0)},pull:function(){re(!1)}}}),[le]);a.useEffect((function(){var e,t;u?null==oe||null===(e=oe.push)||void 0===e||e.call(oe):null==oe||null===(t=oe.pull)||void 0===t||t.call(oe)}),[u]),a.useEffect((function(){return function(){var e;null==oe||null===(e=oe.pull)||void 0===e||e.call(oe)}}),[]);var ce=D&&a.createElement(g.Ay,(0,f.A)({key:"mask"},q,{visible:u}),(function(e,t){var n=e.className,r=e.style;return a.createElement("div",{className:o()("".concat(s,"-mask"),n,null==k?void 0:k.mask,F),style:(0,l.A)((0,l.A)((0,l.A)({},r),B),null==J?void 0:J.mask),onClick:P&&u?L:void 0,ref:t})})),se="function"==typeof j?j(m):j,ue={};if(ae&&le)switch(m){case"top":ue.transform="translateY(".concat(le,"px)");break;case"bottom":ue.transform="translateY(".concat(-le,"px)");break;case"left":ue.transform="translateX(".concat(le,"px)");break;default:ue.transform="translateX(".concat(-le,"px)")}"left"===m||"right"===m?ue.width=w(R):ue.height=w(T);var me={onMouseEnter:X,onMouseOver:W,onMouseLeave:G,onClick:U,onKeyDown:K,onKeyUp:V},de=a.createElement(g.Ay,(0,f.A)({key:"panel"},se,{visible:u,forceRender:E,onVisibleChanged:function(e){null==H||H(e)},removeOnLeave:!1,leavedClassName:"".concat(s,"-content-wrapper-hidden")}),(function(t,n){var r=t.className,i=t.style,c=a.createElement(N,(0,f.A)({id:_,containerRef:n,prefixCls:s,className:o()(I,null==k?void 0:k.content),style:(0,l.A)((0,l.A)({},M),null==J?void 0:J.content)},(0,b.A)(e,{aria:!0}),me),z);return a.createElement("div",(0,f.A)({className:o()("".concat(s,"-content-wrapper"),null==k?void 0:k.wrapper,r),style:(0,l.A)((0,l.A)((0,l.A)({},ue),i),null==J?void 0:J.wrapper)},(0,b.A)(e,{data:!0})),Y?Y(c):c)})),pe=(0,l.A)({},$);return O&&(pe.zIndex=O),a.createElement(d.Provider,{value:ie},a.createElement("div",{className:o()(s,"".concat(s,"-").concat(m),S,(0,p.A)((0,p.A)({},"".concat(s,"-open"),u),"".concat(s,"-inline"),v)),style:pe,tabIndex:-1,ref:Q,onKeyDown:function(e){var t=e.keyCode,n=e.shiftKey;switch(t){case h.A.TAB:var a;if(t===h.A.TAB)if(n||document.activeElement!==ee.current){if(n&&document.activeElement===Z.current){var r;null===(r=ee.current)||void 0===r||r.focus({preventScroll:!0})}}else null===(a=Z.current)||void 0===a||a.focus({preventScroll:!0});break;case h.A.ESC:L&&C&&(e.stopPropagation(),L(e))}}},ce,a.createElement("div",{tabIndex:0,ref:Z,style:A,"aria-hidden":"true","data-sentinel":"start"}),de,a.createElement("div",{tabIndex:0,ref:ee,style:A,"aria-hidden":"true","data-sentinel":"end"})))}var k=a.forwardRef(C);var S=function(e){var t=e.open,n=void 0!==t&&t,r=e.prefixCls,o=void 0===r?"rc-drawer":r,u=e.placement,d=void 0===u?"right":u,p=e.autoFocus,f=void 0===p||p,g=e.keyboard,h=void 0===g||g,b=e.width,v=void 0===b?378:b,y=e.mask,E=void 0===y||y,N=e.maskClosable,x=void 0===N||N,w=e.getContainer,A=e.forceRender,C=e.afterOpenChange,S=e.destroyOnClose,$=e.onMouseEnter,O=e.onMouseOver,I=e.onMouseLeave,_=e.onClick,M=e.onKeyDown,j=e.onKeyUp,R=e.panelRef,T=a.useState(!1),z=(0,i.A)(T,2),D=z[0],P=z[1];var q=a.useState(!1),F=(0,i.A)(q,2),B=F[0],H=F[1];(0,s.A)((function(){H(!0)}),[]);var L=!!B&&n,X=a.useRef(),W=a.useRef();(0,s.A)((function(){L&&(W.current=document.activeElement)}),[L]);var G=a.useMemo((function(){return{panel:R}}),[R]);if(!A&&!D&&!L&&S)return null;var U={onMouseEnter:$,onMouseOver:O,onMouseLeave:I,onClick:_,onKeyDown:M,onKeyUp:j},K=(0,l.A)((0,l.A)({},e),{},{open:L,prefixCls:o,placement:d,autoFocus:f,keyboard:h,width:v,mask:E,maskClosable:x,inline:!1===w,afterOpenChange:function(e){var t,n;(P(e),null==C||C(e),e||!W.current||null!==(t=X.current)&&void 0!==t&&t.contains(W.current))||(null===(n=W.current)||void 0===n||n.focus({preventScroll:!0}))},ref:X},U);return a.createElement(m.Provider,{value:G},a.createElement(c.A,{open:L||A||D,autoDestroy:!1,getContainer:w,autoLock:E&&(L||D)},a.createElement(k,K)))},$=n(2897),O=n(275),I=n(3723),_=n(235),M=n(2279),j=n(8557),R=n(64),T=n(7072);var z=e=>{var t,n;const{prefixCls:r,title:l,footer:i,extra:c,loading:s,onClose:u,headerStyle:m,bodyStyle:d,footerStyle:p,children:f,classNames:g,styles:h}=e,b=(0,M.TP)("drawer"),v=a.useCallback((e=>a.createElement("button",{type:"button",onClick:u,"aria-label":"Close",className:`${r}-close`},e)),[u]),[y,E]=(0,R.A)((0,R.d)(e),(0,R.d)(b),{closable:!0,closeIconRender:v}),N=a.useMemo((()=>{var e,t;return l||y?a.createElement("div",{style:Object.assign(Object.assign(Object.assign({},null===(e=b.styles)||void 0===e?void 0:e.header),m),null==h?void 0:h.header),className:o()(`${r}-header`,{[`${r}-header-close-only`]:y&&!l&&!c},null===(t=b.classNames)||void 0===t?void 0:t.header,null==g?void 0:g.header)},a.createElement("div",{className:`${r}-header-title`},E,l&&a.createElement("div",{className:`${r}-title`},l)),c&&a.createElement("div",{className:`${r}-extra`},c)):null}),[y,E,c,m,r,l]),x=a.useMemo((()=>{var e,t;if(!i)return null;const n=`${r}-footer`;return a.createElement("div",{className:o()(n,null===(e=b.classNames)||void 0===e?void 0:e.footer,null==g?void 0:g.footer),style:Object.assign(Object.assign(Object.assign({},null===(t=b.styles)||void 0===t?void 0:t.footer),p),null==h?void 0:h.footer)},i)}),[i,p,r]);return a.createElement(a.Fragment,null,N,a.createElement("div",{className:o()(`${r}-body`,null==g?void 0:g.body,null===(t=b.classNames)||void 0===t?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},null===(n=b.styles)||void 0===n?void 0:n.body),d),null==h?void 0:h.body)},s?a.createElement(T.A,{active:!0,title:!1,paragraph:{rows:5},className:`${r}-body-skeleton`}):f),x)},D=n(2187),P=n(5905),q=n(7358),F=n(4277);const B=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},H=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),L=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},H({opacity:e},{opacity:1})),X=(e,t)=>[L(.7,t),H({transform:B(e)},{transform:"none"})];var W=e=>{const{componentCls:t,motionDurationSlow:n}=e;return{[t]:{[`${t}-mask-motion`]:L(0,n),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce(((e,t)=>Object.assign(Object.assign({},e),{[`&-${t}`]:X(t,n)})),{})}}};const G=e=>{const{borderRadiusSM:t,componentCls:n,zIndexPopup:a,colorBgMask:r,colorBgElevated:o,motionDurationSlow:l,motionDurationMid:i,paddingXS:c,padding:s,paddingLG:u,fontSizeLG:m,lineHeightLG:d,lineWidth:p,lineType:f,colorSplit:g,marginXS:h,colorIcon:b,colorIconHover:v,colorBgTextHover:y,colorBgTextActive:E,colorText:N,fontWeightStrong:x,footerPaddingBlock:w,footerPaddingInline:A,calc:C}=e,k=`${n}-content-wrapper`;return{[n]:{position:"fixed",inset:0,zIndex:a,pointerEvents:"none",color:N,"&-pure":{position:"relative",background:o,display:"flex",flexDirection:"column",[`&${n}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${n}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${n}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${n}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${n}-mask`]:{position:"absolute",inset:0,zIndex:a,background:r,pointerEvents:"auto"},[k]:{position:"absolute",zIndex:a,maxWidth:"100vw",transition:`all ${l}`,"&-hidden":{display:"none"}},[`&-left > ${k}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${k}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${k}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${k}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${n}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:o,pointerEvents:"auto"},[`${n}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${(0,D.zA)(s)} ${(0,D.zA)(u)}`,fontSize:m,lineHeight:d,borderBottom:`${(0,D.zA)(p)} ${f} ${g}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${n}-extra`]:{flex:"none"},[`${n}-close`]:Object.assign({display:"inline-flex",width:C(m).add(c).equal(),height:C(m).add(c).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:h,color:b,fontWeight:x,fontSize:m,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${i}`,textRendering:"auto","&:hover":{color:v,backgroundColor:y,textDecoration:"none"},"&:active":{backgroundColor:E}},(0,P.K8)(e)),[`${n}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:m,lineHeight:d},[`${n}-body`]:{flex:1,minWidth:0,minHeight:0,padding:u,overflow:"auto",[`${n}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${n}-footer`]:{flexShrink:0,padding:`${(0,D.zA)(w)} ${(0,D.zA)(A)}`,borderTop:`${(0,D.zA)(p)} ${f} ${g}`},"&-rtl":{direction:"rtl"}}}};var U=(0,q.OF)("Drawer",(e=>{const t=(0,F.oX)(e,{});return[G(t),W(t)]}),(e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding}))),K=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const V={distance:180},J=e=>{const{rootClassName:t,width:n,height:r,size:l="default",mask:i=!0,push:c=V,open:s,afterOpenChange:u,onClose:m,prefixCls:d,getContainer:p,style:f,className:g,visible:h,afterVisibleChange:b,maskStyle:v,drawerStyle:y,contentWrapperStyle:E}=e,N=K(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle"]),{getPopupContainer:x,getPrefixCls:w,direction:A,className:C,style:k,classNames:R,styles:T}=(0,M.TP)("drawer"),D=w("drawer",d),[P,q,F]=U(D),B=void 0===p&&x?()=>x(document.body):p,H=o()({"no-mask":!i,[`${D}-rtl`]:"rtl"===A},t,q,F);const L=a.useMemo((()=>null!=n?n:"large"===l?736:378),[n,l]),X=a.useMemo((()=>null!=r?r:"large"===l?736:378),[r,l]),W={motionName:(0,I.b)(D,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},G=(0,j.f)(),[J,Y]=(0,O.YK)("Drawer",N.zIndex),{classNames:Q={},styles:Z={}}=N;return P(a.createElement($.A,{form:!0,space:!0},a.createElement(_.A.Provider,{value:Y},a.createElement(S,Object.assign({prefixCls:D,onClose:m,maskMotion:W,motion:e=>({motionName:(0,I.b)(D,`panel-motion-${e}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500})},N,{classNames:{mask:o()(Q.mask,R.mask),content:o()(Q.content,R.content),wrapper:o()(Q.wrapper,R.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},Z.mask),v),T.mask),content:Object.assign(Object.assign(Object.assign({},Z.content),y),T.content),wrapper:Object.assign(Object.assign(Object.assign({},Z.wrapper),E),T.wrapper)},open:null!=s?s:h,mask:i,push:c,width:L,height:X,style:Object.assign(Object.assign({},k),f),className:o()(C,g),rootClassName:H,getContainer:B,afterOpenChange:null!=u?u:b,panelRef:G,zIndex:J}),a.createElement(z,Object.assign({prefixCls:D},N,{onClose:m}))))))};J._InternalPanelDoNotUseOrYouWillBeFired=e=>{const{prefixCls:t,style:n,className:r,placement:l="right"}=e,i=K(e,["prefixCls","style","className","placement"]),{getPrefixCls:c}=a.useContext(M.QO),s=c("drawer",t),[u,m,d]=U(s),p=o()(s,`${s}-pure`,`${s}-${l}`,m,d,r);return u(a.createElement("div",{className:p,style:n},a.createElement(z,Object.assign({prefixCls:s},i))))};var Y=J},6808:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]])},7073:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},7199:function(e,t,n){"use strict";n.d(t,{L:function(){return jt},A:function(){return Rt}});var a=n(436),r=n(6540),o=n(9036),l=n(2941),i=n(6942),c=n.n(i),s=n(2546),u=n(2065),m=n(682),d=n(2279),p=n(4103),f=n(7550);const g=e=>{let{children:t}=e;const{getPrefixCls:n}=r.useContext(d.QO),a=n("breadcrumb");return r.createElement("li",{className:`${a}-separator`,"aria-hidden":"true"},""===t?t:t||"/")};g.__ANT_BREADCRUMB_SEPARATOR=!0;var h=g,b=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};function v(e,t,n,a){if(null==n)return null;const{className:o,onClick:l}=t,i=b(t,["className","onClick"]),s=Object.assign(Object.assign({},(0,u.A)(i,{data:!0,aria:!0})),{onClick:l});return void 0!==a?r.createElement("a",Object.assign({},s,{className:c()(`${e}-link`,o),href:a}),n):r.createElement("span",Object.assign({},s,{className:c()(`${e}-link`,o)}),n)}function y(e,t){return(n,a,r,o,l)=>{if(t)return t(n,a,r,o);const i=function(e,t){if(void 0===e.title||null===e.title)return null;const n=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(new RegExp(`:(${n})`,"g"),((e,n)=>t[n]||e))}(n,a);return v(e,n,i,l)}}var E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const N=e=>{const{prefixCls:t,separator:n="/",children:a,menu:o,overlay:l,dropdownProps:i,href:c}=e;const s=(e=>{if(o||l){const n=Object.assign({},i);if(o){const e=o||{},{items:t}=e,a=E(e,["items"]);n.menu=Object.assign(Object.assign({},a),{items:null==t?void 0:t.map(((e,t)=>{var{key:n,title:a,label:o,path:l}=e,i=E(e,["key","title","label","path"]);let s=null!=o?o:a;return l&&(s=r.createElement("a",{href:`${c}${l}`},s)),Object.assign(Object.assign({},i),{key:null!=n?n:t,label:s})}))})}else l&&(n.overlay=l);return r.createElement(f.A,Object.assign({placement:"bottom"},n),r.createElement("span",{className:`${t}-overlay-link`},e,r.createElement(p.A,null)))}return e})(a);return null!=s?r.createElement(r.Fragment,null,r.createElement("li",null,s),n&&r.createElement(h,null,n)):null},x=e=>{const{prefixCls:t,children:n,href:a}=e,o=E(e,["prefixCls","children","href"]),{getPrefixCls:l}=r.useContext(d.QO),i=l("breadcrumb",t);return r.createElement(N,Object.assign({},o,{prefixCls:i}),v(i,o,n,a))};x.__ANT_BREADCRUMB_ITEM=!0;var w=x,A=n(2187),C=n(5905),k=n(7358),S=n(4277);var $=(0,k.OF)("Breadcrumb",(e=>(e=>{const{componentCls:t,iconCls:n,calc:a}=e;return{[t]:Object.assign(Object.assign({},(0,C.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[n]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,A.zA)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:a(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,C.K8)(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`\n          > ${n} + span,\n          > ${n} + a\n        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,A.zA)(e.paddingXXS)}`,marginInline:a(e.marginXXS).mul(-1).equal(),[`> ${n}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}})((0,S.oX)(e,{}))),(e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}))),O=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};function I(e){const{breadcrumbName:t,children:n}=e,a=O(e,["breadcrumbName","children"]),r=Object.assign({title:t},a);return n&&(r.menu={items:n.map((e=>{var{breadcrumbName:t}=e,n=O(e,["breadcrumbName"]);return Object.assign(Object.assign({},n),{title:t})}))}),r}var _=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const M=e=>{const{prefixCls:t,separator:n="/",style:a,className:o,rootClassName:l,routes:i,items:p,children:f,itemRender:g,params:b={}}=e,v=_(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:E,direction:x,breadcrumb:w}=r.useContext(d.QO);let A;const C=E("breadcrumb",t),[k,S,O]=$(C),M=function(e,t){return(0,r.useMemo)((()=>e||(t?t.map(I):null)),[e,t])}(p,i);const j=y(C,g);if(M&&M.length>0){const e=[],t=p||i;A=M.map(((a,o)=>{const{path:l,key:i,type:c,menu:s,overlay:m,onClick:d,className:p,separator:f,dropdownProps:g}=a,v=((e,t)=>{if(void 0===t)return t;let n=(t||"").replace(/^\//,"");return Object.keys(e).forEach((t=>{n=n.replace(`:${t}`,e[t])})),n})(b,l);void 0!==v&&e.push(v);const y=null!=i?i:o;if("separator"===c)return r.createElement(h,{key:y},f);const E={},x=o===M.length-1;s?E.menu=s:m&&(E.overlay=m);let{href:w}=a;return e.length&&void 0!==v&&(w=`#/${e.join("/")}`),r.createElement(N,Object.assign({key:y},E,(0,u.A)(a,{data:!0,aria:!0}),{className:p,dropdownProps:g,href:w,separator:x?"":n,onClick:d,prefixCls:C}),j(a,b,t,e,w))}))}else if(f){const e=(0,s.A)(f).length;A=(0,s.A)(f).map(((t,a)=>{if(!t)return t;const r=a===e-1;return(0,m.Ob)(t,{separator:r?"":n,key:a})}))}const R=c()(C,null==w?void 0:w.className,{[`${C}-rtl`]:"rtl"===x},o,l,S,O),T=Object.assign(Object.assign({},null==w?void 0:w.style),a);return k(r.createElement("nav",Object.assign({className:R,style:T},v),r.createElement("ol",null,A)))};M.Item=w,M.Separator=h;var j=M,R=n(955),T=n(1788);const z=(0,T.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var D=n(6808);const P=(0,T.A)("RectangleEllipsis",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M17 12h.01",key:"1m0b6t"}],["path",{d:"M7 12h.01",key:"eqddd0"}]]);var q=n(3164),F=n(4279),B=n(9957),H=n(2609);const L=(0,T.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var X=n(8188);const W=(0,T.A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var G=n(2708);var U=e=>{let{title:t,children:n}=e;return r.createElement("div",{className:"relative mt-2 mb-4"},r.createElement("div",{className:"border border-secondary rounded-lg p-2 px-3 pt-6"},r.createElement("div",{className:"absolute -top-3 left-3 px-2 bg-primary"},r.createElement("span",{className:"text-xs text-primary"},t)),r.createElement("div",null,n)))};const{TextArea:K}=B.A,V=e=>{let{label:t,tooltip:n,required:a,children:o}=e;return r.createElement("label",{className:"block"},r.createElement("div",{className:"flex items-center gap-2 mb-1"},r.createElement("span",{className:"text-sm font-medium text-primary"},t," ",a&&r.createElement("span",{className:"text-red-500"},"*")),r.createElement(R.A,{title:n},r.createElement(L,{className:"w-4 h-4 text-secondary"}))),o)},J=e=>{var t;let{component:n,onChange:o,onNavigate:i,workingCopy:c,setWorkingCopy:s,editPath:u,updateComponentAtPath:m,getCurrentComponent:d}=e;if(!n)return null;const p=(0,r.useCallback)((e=>{o({...n,...e,config:{...n.config,...e.config||{}}})}),[n,o]),f=(0,r.useCallback)(((e,t)=>{p({config:{...n.config,[e]:t}})}),[n,p]),g=(0,r.useCallback)((e=>{if(!(0,F.O6)(n))return;const t=(0,a.A)(n.config.tools||[]);t.splice(e,1),f("tools",t)}),[n,f]),h=(0,r.useCallback)((()=>{if(!(0,F.O6)(n))return;const e=n.config.tools||[],t=[].concat((0,a.A)(e),[{provider:"autogen_core.tools.FunctionTool",component_type:"tool",version:1,component_version:1,description:"Create custom tools by wrapping standard Python functions.",label:"New Tool",config:{source_code:"def new_function():\n    pass",name:"new_function",description:"Description of the new function",global_imports:[],has_cancellation_support:!1}}]);if(f("tools",t),c&&s&&m&&d&&u){var r;const e=m(c,u,{config:{...null===(r=d(c))||void 0===r?void 0:r.config,tools:t}});s(e)}}),[n,f,c,s,m,d,u]);return r.createElement("div",{className:"space-y-6"},r.createElement(U,{title:"Component Details"},r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),r.createElement(B.A,{value:n.label||"",onChange:e=>p({label:e.target.value}),placeholder:"Component name",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),r.createElement(K,{value:n.description||"",onChange:e=>p({description:e.target.value}),placeholder:"Component description",rows:4,className:"mt-1"})))),r.createElement(U,{title:"Configuration"},r.createElement("div",{className:"space-y-4"},(0,F.O6)(n)&&r.createElement(r.Fragment,null,r.createElement(V,{label:"Name",tooltip:"Name of the assistant agent",required:!0},r.createElement(B.A,{value:n.config.name,onChange:e=>f("name",e.target.value)})),r.createElement("div",{className:"space-y-2"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Model Client"),n.config.model_client?r.createElement("div",{className:"bg-secondary p-1 px-2 rounded-md"},r.createElement("div",{className:"flex items-center justify-between"}," ",r.createElement("span",{className:"text-sm"},n.config.model_client.config.model),r.createElement("div",{className:"flex items-center justify-between"},n.config.model_client&&i&&r.createElement(l.Ay,{type:"text",icon:r.createElement(X.A,{className:"w-4 h-4"}),onClick:()=>{var e;return i("model",(null===(e=n.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}},"Configure Model")))):r.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No model configured")),r.createElement(V,{label:"System Message",tooltip:"System message for the agent"},r.createElement(K,{rows:4,value:n.config.system_message,onChange:e=>f("system_message",e.target.value)})),r.createElement("div",{className:"space-y-2"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Tools"),r.createElement(l.Ay,{type:"dashed",size:"small",onClick:h,icon:r.createElement(W,{className:"w-4 h-4"})},"Add Tool")),r.createElement("div",{className:"space-y-2"},null===(t=n.config.tools)||void 0===t?void 0:t.map(((e,t)=>r.createElement("div",{key:(e.label||"")+t,className:"bg-secondary p-1 px-2 rounded-md"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm"},e.config.name||e.label||""),r.createElement("div",{className:"flex items-center gap-2"},i&&r.createElement(l.Ay,{type:"text",icon:r.createElement(X.A,{className:"w-4 h-4"}),onClick:()=>i("tool",e.config.name||e.label||"","tools")}),r.createElement(l.Ay,{type:"text",danger:!0,icon:r.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>g(t)})))))),(!n.config.tools||0===n.config.tools.length)&&r.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No tools configured"))),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Reflect on Tool Use"),r.createElement(H.A,{checked:n.config.reflect_on_tool_use,onChange:e=>f("reflect_on_tool_use",e)})),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Stream Model Client"),r.createElement(H.A,{checked:n.config.model_client_stream,onChange:e=>f("model_client_stream",e)})),r.createElement(V,{label:"Tool Call Summary Format",tooltip:"Format for tool call summaries"},r.createElement(B.A,{value:n.config.tool_call_summary_format,onChange:e=>f("tool_call_summary_format",e.target.value)}))),(0,F.fk)(n)&&r.createElement(V,{label:"Name",tooltip:"Name of the user proxy agent",required:!0},r.createElement(B.A,{value:n.config.name,onChange:e=>f("name",e.target.value)})),(0,F.Zj)(n)&&r.createElement(r.Fragment,null,r.createElement(V,{label:"Name",tooltip:"Name of the web surfer agent",required:!0},r.createElement(B.A,{value:n.config.name,onChange:e=>f("name",e.target.value)})),r.createElement(V,{label:"Start Page",tooltip:"URL to start browsing from"},r.createElement(B.A,{value:n.config.start_page||"",onChange:e=>f("start_page",e.target.value)})),r.createElement(V,{label:"Downloads Folder",tooltip:"Folder path to save downloads"},r.createElement(B.A,{value:n.config.downloads_folder||"",onChange:e=>f("downloads_folder",e.target.value)})),r.createElement(V,{label:"Debug Directory",tooltip:"Directory for debugging logs"},r.createElement(B.A,{value:n.config.debug_dir||"",onChange:e=>f("debug_dir",e.target.value)})),r.createElement("div",{className:"space-y-2"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Model Client"),n.config.model_client?r.createElement("div",{className:"bg-secondary p-1 px-2 rounded-md"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm"},n.config.model_client.config.model),r.createElement("div",{className:"flex items-center justify-between"},i&&r.createElement(l.Ay,{type:"text",icon:r.createElement(X.A,{className:"w-4 h-4"}),onClick:()=>{var e;return i("model",(null===(e=n.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}},"Configure Model")))):r.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No model configured")),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Headless"),r.createElement(H.A,{checked:n.config.headless||!1,onChange:e=>f("headless",e)})),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Animate Actions"),r.createElement(H.A,{checked:n.config.animate_actions||!1,onChange:e=>f("animate_actions",e)})),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Save Screenshots"),r.createElement(H.A,{checked:n.config.to_save_screenshots||!1,onChange:e=>f("to_save_screenshots",e)})),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Use OCR"),r.createElement(H.A,{checked:n.config.use_ocr||!1,onChange:e=>f("use_ocr",e)})),r.createElement(V,{label:"Browser Channel",tooltip:"Channel for the browser (e.g. beta, stable)"},r.createElement(B.A,{value:n.config.browser_channel||"",onChange:e=>f("browser_channel",e.target.value)})),r.createElement(V,{label:"Browser Data Directory",tooltip:"Directory for browser profile data"},r.createElement(B.A,{value:n.config.browser_data_dir||"",onChange:e=>f("browser_data_dir",e.target.value)})),r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Resize Viewport"),r.createElement(H.A,{checked:n.config.to_resize_viewport||!1,onChange:e=>f("to_resize_viewport",e)}))))))};var Y=n(8168),Q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},Z=n(7064),ee=function(e,t){return r.createElement(Z.A,(0,Y.A)({},e,{ref:t,icon:Q}))};var te=r.forwardRef(ee),ne=n(4467),ae=n(2284),re=n(5544),oe=n(3986),le=n(3029),ie=n(2901);function ce(){return"function"==typeof BigInt}function se(e){return!e&&0!==e&&!Number.isNaN(e)||!String(e).trim()}function ue(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),(t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,"")).startsWith(".")&&(t="0".concat(t));var a=t||"0",r=a.split("."),o=r[0]||"0",l=r[1]||"0";"0"===o&&"0"===l&&(n=!1);var i=n?"-":"";return{negative:n,negativeStr:i,trimStr:a,integerStr:o,decimalStr:l,fullStr:"".concat(i).concat(a)}}function me(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function de(e){var t=String(e);if(me(e)){var n=Number(t.slice(t.indexOf("e-")+2)),a=t.match(/\.(\d+)/);return null!=a&&a[1]&&(n+=a[1].length),n}return t.includes(".")&&fe(t)?t.length-t.indexOf(".")-1:0}function pe(e){var t=String(e);if(me(e)){if(e>Number.MAX_SAFE_INTEGER)return String(ce()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(ce()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(de(t))}return ue(t).fullStr}function fe(e){return"number"==typeof e?!Number.isNaN(e):!!e&&(/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e))}var ge=function(){function e(t){if((0,le.A)(this,e),(0,ne.A)(this,"origin",""),(0,ne.A)(this,"negative",void 0),(0,ne.A)(this,"integer",void 0),(0,ne.A)(this,"decimal",void 0),(0,ne.A)(this,"decimalLen",void 0),(0,ne.A)(this,"empty",void 0),(0,ne.A)(this,"nan",void 0),se(t))this.empty=!0;else if(this.origin=String(t),"-"===t||Number.isNaN(t))this.nan=!0;else{var n=t;if(me(n)&&(n=Number(n)),fe(n="string"==typeof n?n:pe(n))){var a=ue(n);this.negative=a.negative;var r=a.trimStr.split(".");this.integer=BigInt(r[0]);var o=r[1]||"0";this.decimal=BigInt(o),this.decimalLen=o.length}else this.nan=!0}}return(0,ie.A)(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(e){var t="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(e,"0"));return BigInt(t)}},{key:"negate",value:function(){var t=new e(this.toString());return t.negative=!t.negative,t}},{key:"cal",value:function(t,n,a){var r=Math.max(this.getDecimalStr().length,t.getDecimalStr().length),o=n(this.alignDecimal(r),t.alignDecimal(r)).toString(),l=a(r),i=ue(o),c=i.negativeStr,s=i.trimStr,u="".concat(c).concat(s.padStart(l+1,"0"));return new e("".concat(u.slice(0,-l),".").concat(u.slice(-l)))}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=new e(t);return n.isInvalidate()?this:this.cal(n,(function(e,t){return e+t}),(function(e){return e}))}},{key:"multi",value:function(t){var n=new e(t);return this.isInvalidate()||n.isInvalidate()?new e(NaN):this.cal(n,(function(e,t){return e*t}),(function(e){return 2*e}))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toString()===(null==e?void 0:e.toString())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":ue("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),he=function(){function e(t){(0,le.A)(this,e),(0,ne.A)(this,"origin",""),(0,ne.A)(this,"number",void 0),(0,ne.A)(this,"empty",void 0),se(t)?this.empty=!0:(this.origin=String(t),this.number=Number(t))}return(0,ie.A)(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=Number(t);if(Number.isNaN(n))return this;var a=this.number+n;if(a>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(a<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var r=Math.max(de(this.number),de(n));return new e(a.toFixed(r))}},{key:"multi",value:function(t){var n=Number(t);if(this.isInvalidate()||Number.isNaN(n))return new e(NaN);var a=this.number*n;if(a>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(a<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var r=Math.max(de(this.number),de(n));return new e(a.toFixed(r))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toNumber()===(null==e?void 0:e.toNumber())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":pe(this.number):this.origin}}]),e}();function be(e){return ce()?new ge(e):new he(e)}function ve(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(""===e)return"";var r=ue(e),o=r.negativeStr,l=r.integerStr,i=r.decimalStr,c="".concat(t).concat(i),s="".concat(o).concat(l);if(n>=0){var u=Number(i[n]);return u>=5&&!a?ve(be(e).add("".concat(o,"0.").concat("0".repeat(n)).concat(10-u)).toString(),t,n,a):0===n?s:"".concat(s).concat(t).concat(i.padEnd(n,"0").slice(0,n))}return".0"===c?s:"".concat(s).concat(c)}var ye=be,Ee=n(8491),Ne=n(981);var xe=n(8719),we=n(8210);var Ae=n(8430),Ce=function(){var e=(0,r.useState)(!1),t=(0,re.A)(e,2),n=t[0],a=t[1];return(0,Ne.A)((function(){a((0,Ae.A)())}),[]),n},ke=n(5371);function Se(e){var t=e.prefixCls,n=e.upNode,a=e.downNode,o=e.upDisabled,l=e.downDisabled,i=e.onStep,s=r.useRef(),u=r.useRef([]),m=r.useRef();m.current=i;var d=function(){clearTimeout(s.current)},p=function(e,t){e.preventDefault(),d(),m.current(t),s.current=setTimeout((function e(){m.current(t),s.current=setTimeout(e,200)}),600)};if(r.useEffect((function(){return function(){d(),u.current.forEach((function(e){return ke.A.cancel(e)}))}}),[]),Ce())return null;var f="".concat(t,"-handler"),g=c()(f,"".concat(f,"-up"),(0,ne.A)({},"".concat(f,"-up-disabled"),o)),h=c()(f,"".concat(f,"-down"),(0,ne.A)({},"".concat(f,"-down-disabled"),l)),b=function(){return u.current.push((0,ke.A)(d))},v={unselectable:"on",role:"button",onMouseUp:b,onMouseLeave:b};return r.createElement("div",{className:"".concat(f,"-wrap")},r.createElement("span",(0,Y.A)({},v,{onMouseDown:function(e){p(e,!0)},"aria-label":"Increase Value","aria-disabled":o,className:g}),n||r.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),r.createElement("span",(0,Y.A)({},v,{onMouseDown:function(e){p(e,!1)},"aria-label":"Decrease Value","aria-disabled":l,className:h}),a||r.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function $e(e){var t="number"==typeof e?pe(e):ue(e).fullStr;return t.includes(".")?ue(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}var Oe=n(1980),Ie=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],_e=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],Me=function(e,t){return e||t.isEmpty()?t.toString():t.toNumber()},je=function(e){var t=ye(e);return t.isInvalidate()?null:t},Re=r.forwardRef((function(e,t){var n=e.prefixCls,a=e.className,o=e.style,l=e.min,i=e.max,s=e.step,u=void 0===s?1:s,m=e.defaultValue,d=e.value,p=e.disabled,f=e.readOnly,g=e.upHandler,h=e.downHandler,b=e.keyboard,v=e.changeOnWheel,y=void 0!==v&&v,E=e.controls,N=void 0===E||E,x=(e.classNames,e.stringMode),w=e.parser,A=e.formatter,C=e.precision,k=e.decimalSeparator,S=e.onChange,$=e.onInput,O=e.onPressEnter,I=e.onStep,_=e.changeOnBlur,M=void 0===_||_,j=e.domRef,R=(0,oe.A)(e,Ie),T="".concat(n,"-input"),z=r.useRef(null),D=r.useState(!1),P=(0,re.A)(D,2),q=P[0],F=P[1],B=r.useRef(!1),H=r.useRef(!1),L=r.useRef(!1),X=r.useState((function(){return ye(null!=d?d:m)})),W=(0,re.A)(X,2),G=W[0],U=W[1];var K=r.useCallback((function(e,t){if(!t)return C>=0?C:Math.max(de(e),de(u))}),[C,u]),V=r.useCallback((function(e){var t=String(e);if(w)return w(t);var n=t;return k&&(n=n.replace(k,".")),n.replace(/[^\w.-]+/g,"")}),[w,k]),J=r.useRef(""),Q=r.useCallback((function(e,t){if(A)return A(e,{userTyping:t,input:String(J.current)});var n="number"==typeof e?pe(e):e;if(!t){var a=K(n,t);if(fe(n)&&(k||a>=0))n=ve(n,k||".",a)}return n}),[A,K,k]),Z=r.useState((function(){var e=null!=m?m:d;return G.isInvalidate()&&["string","number"].includes((0,ae.A)(e))?Number.isNaN(e)?"":e:Q(G.toString(),!1)})),ee=(0,re.A)(Z,2),te=ee[0],le=ee[1];function ie(e,t){le(Q(e.isInvalidate()?e.toString(!1):e.toString(!t),t))}J.current=te;var ce,se,ue=r.useMemo((function(){return je(i)}),[i,C]),me=r.useMemo((function(){return je(l)}),[l,C]),ge=r.useMemo((function(){return!(!ue||!G||G.isInvalidate())&&ue.lessEquals(G)}),[ue,G]),he=r.useMemo((function(){return!(!me||!G||G.isInvalidate())&&G.lessEquals(me)}),[me,G]),be=function(e,t){var n=(0,r.useRef)(null);return[function(){try{var t=e.selectionStart,a=e.selectionEnd,r=e.value,o=r.substring(0,t),l=r.substring(a);n.current={start:t,end:a,value:r,beforeTxt:o,afterTxt:l}}catch(i){}},function(){if(e&&n.current&&t)try{var a=e.value,r=n.current,o=r.beforeTxt,l=r.afterTxt,i=r.start,c=a.length;if(a.startsWith(o))c=o.length;else if(a.endsWith(l))c=a.length-n.current.afterTxt.length;else{var s=o[i-1],u=a.indexOf(s,i-1);-1!==u&&(c=u+1)}e.setSelectionRange(c,c)}catch(m){(0,we.Ay)(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(m.message))}}]}(z.current,q),Ee=(0,re.A)(be,2),Ae=Ee[0],Ce=Ee[1],Oe=function(e){return ue&&!e.lessEquals(ue)?ue:me&&!me.lessEquals(e)?me:null},_e=function(e){return!Oe(e)},Re=function(e,t){var n,a=e,r=_e(a)||a.isEmpty();if(a.isEmpty()||t||(a=Oe(a)||a,r=!0),!f&&!p&&r){var o=a.toString(),l=K(o,t);return l>=0&&(a=ye(ve(o,".",l)),_e(a)||(a=ye(ve(o,".",l,!0)))),a.equals(G)||(n=a,void 0===d&&U(n),null==S||S(a.isEmpty()?null:Me(x,a)),void 0===d&&ie(a,t)),a}return G},Te=(ce=(0,r.useRef)(0),se=function(){ke.A.cancel(ce.current)},(0,r.useEffect)((function(){return se}),[]),function(e){se(),ce.current=(0,ke.A)((function(){e()}))}),ze=function e(t){if(Ae(),J.current=t,le(t),!H.current){var n=V(t),a=ye(n);a.isNaN()||Re(a,!0)}null==$||$(t),Te((function(){var n=t;w||(n=t.replace(/。/g,".")),n!==t&&e(n)}))},De=function(e){var t;if(!(e&&ge||!e&&he)){B.current=!1;var n=ye(L.current?$e(u):u);e||(n=n.negate());var a=(G||ye(0)).add(n.toString()),r=Re(a,!1);null==I||I(Me(x,r),{offset:L.current?$e(u):u,type:e?"up":"down"}),null===(t=z.current)||void 0===t||t.focus()}},Pe=function(e){var t,n=ye(V(te));t=n.isNaN()?Re(G,e):Re(n,e),void 0!==d?ie(G,!1):t.isNaN()||ie(t,!1)};r.useEffect((function(){if(y&&q){var e=function(e){De(e.deltaY<0),e.preventDefault()},t=z.current;if(t)return t.addEventListener("wheel",e,{passive:!1}),function(){return t.removeEventListener("wheel",e)}}}));return(0,Ne.o)((function(){G.isInvalidate()||ie(G,!1)}),[C,A]),(0,Ne.o)((function(){var e=ye(d);U(e);var t=ye(V(te));e.equals(t)&&B.current&&!A||ie(e,B.current)}),[d]),(0,Ne.o)((function(){A&&Ce()}),[te]),r.createElement("div",{ref:j,className:c()(n,a,(0,ne.A)((0,ne.A)((0,ne.A)((0,ne.A)((0,ne.A)({},"".concat(n,"-focused"),q),"".concat(n,"-disabled"),p),"".concat(n,"-readonly"),f),"".concat(n,"-not-a-number"),G.isNaN()),"".concat(n,"-out-of-range"),!G.isInvalidate()&&!_e(G))),style:o,onFocus:function(){F(!0)},onBlur:function(){M&&Pe(!1),F(!1),B.current=!1},onKeyDown:function(e){var t=e.key,n=e.shiftKey;B.current=!0,L.current=n,"Enter"===t&&(H.current||(B.current=!1),Pe(!1),null==O||O(e)),!1!==b&&!H.current&&["Up","ArrowUp","Down","ArrowDown"].includes(t)&&(De("Up"===t||"ArrowUp"===t),e.preventDefault())},onKeyUp:function(){B.current=!1,L.current=!1},onCompositionStart:function(){H.current=!0},onCompositionEnd:function(){H.current=!1,ze(z.current.value)},onBeforeInput:function(){B.current=!0}},N&&r.createElement(Se,{prefixCls:n,upNode:g,downNode:h,upDisabled:ge,downDisabled:he,onStep:De}),r.createElement("div",{className:"".concat(T,"-wrap")},r.createElement("input",(0,Y.A)({autoComplete:"off",role:"spinbutton","aria-valuemin":l,"aria-valuemax":i,"aria-valuenow":G.isInvalidate()?null:G.toString(),step:u},R,{ref:(0,xe.K4)(z,t),className:T,value:te,onChange:function(e){ze(e.target.value)},disabled:p,readOnly:f}))))})),Te=r.forwardRef((function(e,t){var n=e.disabled,a=e.style,o=e.prefixCls,l=void 0===o?"rc-input-number":o,i=e.value,c=e.prefix,s=e.suffix,u=e.addonBefore,m=e.addonAfter,d=e.className,p=e.classNames,f=(0,oe.A)(e,_e),g=r.useRef(null),h=r.useRef(null),b=r.useRef(null),v=function(e){b.current&&(0,Oe.F4)(b.current,e)};return r.useImperativeHandle(t,(function(){return e=b.current,t={focus:v,nativeElement:g.current.nativeElement||h.current},"undefined"!=typeof Proxy&&e?new Proxy(e,{get:function(e,n){if(t[n])return t[n];var a=e[n];return"function"==typeof a?a.bind(e):a}}):e;var e,t})),r.createElement(Ee.a,{className:d,triggerFocus:v,prefixCls:l,value:i,disabled:n,style:a,prefix:c,suffix:s,addonAfter:m,addonBefore:u,classNames:p,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:g},r.createElement(Re,(0,Y.A)({prefixCls:l,disabled:n,ref:b,domRef:h,className:null==p?void 0:p.input},f)))}));var ze=Te,De=n(2897),Pe=n(8182),qe=n(867),Fe=n(8119),Be=n(934),He=n(829),Le=n(4241),Xe=n(124),We=n(6327),Ge=n(1594),Ue=n(6716),Ke=n(9222),Ve=n(5974),Je=n(2616);const Ye=(e,t)=>{let{componentCls:n,borderRadiusSM:a,borderRadiusLG:r}=e;const o="lg"===t?r:a;return{[`&-${t}`]:{[`${n}-handler-wrap`]:{borderStartEndRadius:o,borderEndEndRadius:o},[`${n}-handler-up`]:{borderStartEndRadius:o},[`${n}-handler-down`]:{borderEndEndRadius:o}}}},Qe=e=>{const{componentCls:t,lineWidth:n,lineType:a,borderRadius:r,inputFontSizeSM:o,inputFontSizeLG:l,controlHeightLG:i,controlHeightSM:c,colorError:s,paddingInlineSM:u,paddingBlockSM:m,paddingBlockLG:d,paddingInlineLG:p,colorTextDescription:f,motionDurationMid:g,handleHoverColor:h,handleOpacity:b,paddingInline:v,paddingBlock:y,handleBg:E,handleActiveBg:N,colorTextDisabled:x,borderRadiusSM:w,borderRadiusLG:k,controlWidth:S,handleBorderColor:$,filledHandleBg:O,lineHeightLG:I,calc:_}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,C.dF)(e)),(0,Ge.wj)(e)),{display:"inline-block",width:S,margin:0,padding:0,borderRadius:r}),(0,Ke.Eb)(e,{[`${t}-handler-wrap`]:{background:E,[`${t}-handler-down`]:{borderBlockStart:`${(0,A.zA)(n)} ${a} ${$}`}}})),(0,Ke.sA)(e,{[`${t}-handler-wrap`]:{background:O,[`${t}-handler-down`]:{borderBlockStart:`${(0,A.zA)(n)} ${a} ${$}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:E}}})),(0,Ke.aP)(e,{[`${t}-handler-wrap`]:{background:E,[`${t}-handler-down`]:{borderBlockStart:`${(0,A.zA)(n)} ${a} ${$}`}}})),(0,Ke.lB)(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:l,lineHeight:I,borderRadius:k,[`input${t}-input`]:{height:_(i).sub(_(n).mul(2)).equal(),padding:`${(0,A.zA)(d)} ${(0,A.zA)(p)}`}},"&-sm":{padding:0,fontSize:o,borderRadius:w,[`input${t}-input`]:{height:_(c).sub(_(n).mul(2)).equal(),padding:`${(0,A.zA)(m)} ${(0,A.zA)(u)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:s}}},"&-group":Object.assign(Object.assign(Object.assign({},(0,C.dF)(e)),(0,Ge.XM)(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:k,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:w}}},(0,Ke.nm)(e)),(0,Ke.Vy)(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},(0,C.dF)(e)),{width:"100%",padding:`${(0,A.zA)(y)} ${(0,A.zA)(v)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:r,outline:0,transition:`all ${g} linear`,appearance:"textfield",fontSize:"inherit"}),(0,Ge.j_)(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,webkitAppearance:"none",appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:b,height:"100%",borderStartStartRadius:0,borderStartEndRadius:r,borderEndEndRadius:r,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${g}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:f,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${(0,A.zA)(n)} ${a} ${$}`,transition:`all ${g} linear`,"&:active":{background:N},"&:hover":{height:"60%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{color:h}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},(0,C.Nk)()),{color:f,transition:`all ${g} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:r},[`${t}-handler-down`]:{borderEndEndRadius:r}},Ye(e,"lg")),Ye(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`\n          ${t}-handler-up-disabled,\n          ${t}-handler-down-disabled\n        `]:{cursor:"not-allowed"},[`\n          ${t}-handler-up-disabled:hover &-handler-up-inner,\n          ${t}-handler-down-disabled:hover &-handler-down-inner\n        `]:{color:x}})}]},Ze=e=>{const{componentCls:t,paddingBlock:n,paddingInline:a,inputAffixPadding:r,controlWidth:o,borderRadiusLG:l,borderRadiusSM:i,paddingInlineLG:c,paddingInlineSM:s,paddingBlockLG:u,paddingBlockSM:m,motionDurationMid:d}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${(0,A.zA)(n)} 0`}},(0,Ge.wj)(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:o,padding:0,paddingInlineStart:a,"&-lg":{borderRadius:l,paddingInlineStart:c,[`input${t}-input`]:{padding:`${(0,A.zA)(u)} 0`}},"&-sm":{borderRadius:i,paddingInlineStart:s,[`input${t}-input`]:{padding:`${(0,A.zA)(m)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:r},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:a,marginInlineStart:r,transition:`margin ${d}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(a).equal()}})}};var et=(0,k.OF)("InputNumber",(e=>{const t=(0,S.oX)(e,(0,Ue.C)(e));return[Qe(t),Ze(t),(0,Ve.G)(t)]}),(e=>{var t;const n=null!==(t=e.handleVisible)&&void 0!==t?t:"auto",a=e.controlHeightSM-2*e.lineWidth;return Object.assign(Object.assign({},(0,Ue.b)(e)),{controlWidth:90,handleWidth:a,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new Je.Y(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:!0===n?1:0,handleVisibleWidth:!0===n?a:0})}),{unitless:{handleOpacity:!0}}),tt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const nt=r.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:a}=r.useContext(d.QO),o=r.useRef(null);r.useImperativeHandle(t,(()=>o.current));const{className:l,rootClassName:i,size:s,disabled:u,prefixCls:m,addonBefore:f,addonAfter:g,prefix:h,suffix:b,bordered:v,readOnly:y,status:E,controls:N,variant:x}=e,w=tt(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),A=n("input-number",m),C=(0,Be.A)(A),[k,S,$]=et(A,C),{compactSize:O,compactItemClassnames:I}=(0,We.RQ)(A,a);let _=r.createElement(te,{className:`${A}-handler-up-inner`}),M=r.createElement(p.A,{className:`${A}-handler-down-inner`});const j="boolean"==typeof N?N:void 0;"object"==typeof N&&(_=void 0===N.upIcon?_:r.createElement("span",{className:`${A}-handler-up-inner`},N.upIcon),M=void 0===N.downIcon?M:r.createElement("span",{className:`${A}-handler-down-inner`},N.downIcon));const{hasFeedback:R,status:T,isFormItemInput:z,feedbackIcon:D}=r.useContext(Le.$W),P=(0,Pe.v)(T,E),q=(0,He.A)((e=>{var t;return null!==(t=null!=s?s:O)&&void 0!==t?t:e})),F=r.useContext(Fe.A),B=null!=u?u:F,[H,L]=(0,Xe.A)("inputNumber",x,v),X=R&&r.createElement(r.Fragment,null,D),W=c()({[`${A}-lg`]:"large"===q,[`${A}-sm`]:"small"===q,[`${A}-rtl`]:"rtl"===a,[`${A}-in-form-item`]:z},S),G=`${A}-group`;return k(r.createElement(ze,Object.assign({ref:o,disabled:B,className:c()($,C,l,i,I),upHandler:_,downHandler:M,prefixCls:A,readOnly:y,controls:j,prefix:h,suffix:X||b,addonBefore:f&&r.createElement(De.A,{form:!0,space:!0},f),addonAfter:g&&r.createElement(De.A,{form:!0,space:!0},g),classNames:{input:W,variant:c()({[`${A}-${H}`]:L},(0,Pe.L)(A,P,R)),affixWrapper:c()({[`${A}-affix-wrapper-sm`]:"small"===q,[`${A}-affix-wrapper-lg`]:"large"===q,[`${A}-affix-wrapper-rtl`]:"rtl"===a,[`${A}-affix-wrapper-without-controls`]:!1===N},S),wrapper:c()({[`${G}-rtl`]:"rtl"===a},S),groupWrapper:c()({[`${A}-group-wrapper-sm`]:"small"===q,[`${A}-group-wrapper-lg`]:"large"===q,[`${A}-group-wrapper-rtl`]:"rtl"===a,[`${A}-group-wrapper-${H}`]:L},(0,Pe.L)(`${A}-group-wrapper`,P,R),S)}},w)))})),at=nt;at._InternalPanelDoNotUseOrYouWillBeFired=e=>r.createElement(qe.Ay,{theme:{components:{InputNumber:{handleVisible:!0}}}},r.createElement(nt,Object.assign({},e)));var rt=at,ot=n(5319),lt=n(5144);const it=e=>{let{label:t,tooltip:n,children:a}=e;return r.createElement("label",{className:"block"},r.createElement("div",{className:"flex items-center gap-2 mb-1"},r.createElement("span",{className:"text-sm font-medium text-primary"},t),r.createElement(R.A,{title:n},r.createElement(L,{className:"w-4 h-4 text-secondary"}))),a)},ct={temperature:{label:"Temperature",tooltip:"Controls randomness in the model's output. Higher values make output more random, lower values make it more focused.",component:rt,props:{min:0,max:2,step:.1,className:"w-full"}},max_tokens:{label:"Max Tokens",tooltip:"Maximum length of the model's output in tokens",component:rt,props:{min:1,className:"w-full"}},top_p:{label:"Top P",tooltip:"Controls diversity via nucleus sampling. Lower values make output more focused, higher values make it more diverse.",component:rt,props:{min:0,max:1,step:.1,className:"w-full"}},top_k:{label:"Top K",tooltip:"Limits the next token selection to the K most likely tokens. Only used by some models.",component:rt,props:{min:0,className:"w-full"}},frequency_penalty:{label:"Frequency Penalty",tooltip:"Decreases the model's likelihood to repeat the same information. Values range from -2.0 to 2.0.",component:rt,props:{min:-2,max:2,step:.1,className:"w-full"}},presence_penalty:{label:"Presence Penalty",tooltip:"Increases the model's likelihood to talk about new topics. Values range from -2.0 to 2.0.",component:rt,props:{min:-2,max:2,step:.1,className:"w-full"}},stop:{label:"Stop Sequences",tooltip:"Sequences where the model will stop generating further tokens",component:ot.A,props:{mode:"tags",placeholder:"Enter stop sequences",className:"w-full"}},stop_sequences:{label:"Stop Sequences",tooltip:"Sequences where the model will stop generating further tokens",component:ot.A,props:{mode:"tags",placeholder:"Enter stop sequences",className:"w-full"}},model:{label:"Model",tooltip:"The name of the model to use",component:B.A,props:{required:!0}},api_key:{label:"API Key",tooltip:"Your API key",component:B.A.Password,props:{}},organization:{label:"Organization",tooltip:"Optional: Your OpenAI organization ID",component:B.A,props:{}},base_url:{label:"Base URL",tooltip:"Optional: Custom base URL for API requests",component:B.A,props:{}},timeout:{label:"Timeout",tooltip:"Request timeout in seconds",component:rt,props:{min:1,className:"w-full"}},max_retries:{label:"Max Retries",tooltip:"Maximum number of retry attempts for failed requests",component:rt,props:{min:0,className:"w-full"}},azure_endpoint:{label:"Azure Endpoint",tooltip:"Your Azure OpenAI service endpoint URL",component:B.A,props:{required:!0}},azure_deployment:{label:"Azure Deployment",tooltip:"The name of your Azure OpenAI model deployment",component:B.A,props:{}},api_version:{label:"API Version",tooltip:"Azure OpenAI API version (e.g., 2023-05-15)",component:B.A,props:{required:!0}},azure_ad_token:{label:"Azure AD Token",tooltip:"Optional: Azure Active Directory token for authentication",component:B.A.Password,props:{}},tools:{label:"Tools",tooltip:"JSON definition of tools the model can use",component:lt.A,props:{rows:4,placeholder:"Enter tools JSON definition"},transform:{fromConfig:e=>e?JSON.stringify(e,null,2):"",toConfig:e=>{try{return e?JSON.parse(e):null}catch(t){return e}}}},tool_choice:{label:"Tool Choice",tooltip:"Controls whether the model uses tools ('auto', 'any', 'none', or JSON object)",component:ot.A,props:{options:[{label:"Auto",value:"auto"},{label:"Any",value:"any"},{label:"None",value:"none"},{label:"Custom",value:"custom"}],className:"w-full"},transform:{fromConfig:e=>"object"==typeof e?"custom":e||"auto",toConfig:(e,t)=>"custom"!==e?e:"object"==typeof t?t:{type:"function"}}},metadata:{label:"Metadata",tooltip:"Optional: Custom metadata to include with the request",component:lt.A,props:{rows:2,placeholder:"Enter metadata as JSON"},transform:{fromConfig:e=>e?JSON.stringify(e,null,2):"",toConfig:e=>{try{return e?JSON.parse(e):null}catch(t){return e}}}}},st={openai:{modelConfig:["model","api_key","organization","base_url","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","frequency_penalty","presence_penalty","stop"]},azure:{modelConfig:["model","api_key","azure_endpoint","azure_deployment","api_version","azure_ad_token","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","frequency_penalty","presence_penalty","stop"]},anthropic:{modelConfig:["model","api_key","base_url","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","top_k","stop_sequences","tools","tool_choice","metadata"]}},ut=e=>{let{component:t,onChange:n}=e,a=null;if((0,F.Ed)(t)?a="openai":(0,F.Jz)(t)?a="azure":(0,F.wx)(t)&&(a="anthropic"),!a)return null;const o=(0,r.useCallback)((e=>{n({...t,...e,config:{...t.config,...e.config||{}}})}),[t,n]),l=(0,r.useCallback)(((e,n)=>{var a;const r=ct[e],l=null!==(a=r.transform)&&void 0!==a&&a.toConfig?r.transform.toConfig(n,t.config[e]):n;o({config:{...t.config,[e]:l}})}),[t,o]),i=e=>r.createElement("div",{className:"space-y-4"},e.map((e=>(e=>{var n;const a=ct[e];if(!a)return null;const o=null!==(n=a.transform)&&void 0!==n&&n.fromConfig?a.transform.fromConfig(t.config[e]):t.config[e];return r.createElement(it,{key:e,label:a.label,tooltip:a.tooltip},r.createElement(a.component,Object.assign({},a.props,{value:o,onChange:t=>{const n=t&&t.target?t.target.value:t;l(e,n)}})))})(e))));return r.createElement("div",{className:"space-y-6"},r.createElement(U,{title:"Component Details"},r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),r.createElement(B.A,{value:t.label||"",onChange:e=>o({label:e.target.value}),placeholder:"Model name",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),r.createElement(lt.A,{value:t.description||"",onChange:e=>o({description:e.target.value}),placeholder:"Model description",rows:4,className:"mt-1"})))),r.createElement(U,{title:"azure"===a?"Azure Configuration":"Model Configuration"},i(st[a].modelConfig)),r.createElement(U,{title:"Model Parameters"},i(st[a].modelParams)),"anthropic"===a&&"custom"===t.config.tool_choice&&r.createElement(U,{title:"Custom Tool Choice"},r.createElement("div",{className:"space-y-4"},r.createElement(lt.A,{value:JSON.stringify(t.config.tool_choice,null,2),onChange:e=>{try{const t=JSON.parse(e.target.value);l("tool_choice",t)}catch(t){console.error("Invalid JSON for tool_choice")}},placeholder:"Enter tool choice configuration as JSON",rows:4}))))};var mt=n(5680);const{TextArea:dt}=B.A,pt=e=>{let{component:t,onChange:n,onNavigate:a}=e;if(!(0,F.HX)(t)&&!(0,F.Il)(t))return null;const o=(0,r.useCallback)((e=>{n({...t,...e,config:{...t.config,...e.config||{}}})}),[t,n]),i=(0,r.useCallback)(((e,n)=>{(0,F.HX)(t)?o({config:{...t.config,[e]:n}}):(0,F.Il)(t)&&o({config:{...t.config,[e]:n}})}),[t,o]);return r.createElement("div",{className:" "},r.createElement(U,{title:"Component Details"},r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),r.createElement(B.A,{value:t.label||"",onChange:e=>o({label:e.target.value}),placeholder:"Team name",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),r.createElement(dt,{value:t.description||"",onChange:e=>o({description:e.target.value}),placeholder:"Team description",rows:4,className:"mt-1"})))),r.createElement(U,{title:"Configuration"},(0,F.HX)(t)&&r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Selector Prompt"),r.createElement(dt,{value:t.config.selector_prompt||"",onChange:e=>i("selector_prompt",e.target.value),placeholder:"Prompt for the selector",rows:4,className:"mt-1"})),r.createElement("div",{className:"space-y-2"},r.createElement("h3",{className:"text-sm font-medium text-primary"},"Model"),r.createElement("div",{className:"bg-secondary p-4 rounded-md"},t.config.model_client?r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm"},t.config.model_client.config.model),a&&r.createElement(l.Ay,{type:"text",icon:r.createElement(X.A,{className:"w-4 h-4"}),onClick:()=>{var e;return a("model",(null===(e=t.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}})):r.createElement("div",{className:"text-sm text-secondary text-center"},"No model configured")))),r.createElement("div",{className:"space-y-2 mt-4"},r.createElement("h3",{className:"text-sm font-medium text-primary"},"Termination Condition"),r.createElement("div",{className:"bg-secondary p-4 rounded-md"},t.config.termination_condition?r.createElement("div",{className:"flex items-center justify-between"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement(mt.A,{className:"w-4 h-4 text-secondary"}),r.createElement("span",{className:"text-sm"},t.config.termination_condition.label||t.config.termination_condition.component_type)),a&&r.createElement(l.Ay,{type:"text",icon:r.createElement(X.A,{className:"w-4 h-4"}),onClick:()=>{var e;return a("termination",(null===(e=t.config.termination_condition)||void 0===e?void 0:e.label)||"","termination_condition")}})):r.createElement("div",{className:"text-sm text-secondary text-center"},"No termination condition configured")))))};var ft=n(2702);const gt=(0,T.A)("CircleMinus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);var ht=n(9872);const{TextArea:bt}=B.A,{Option:vt}=ot.A,yt=e=>{let{component:t,onChange:n}=e;if(!(0,F.d9)(t))return null;const o=(0,r.useRef)(null),{0:i,1:c}=(0,r.useState)(!1),{0:s,1:u}=(0,r.useState)("direct"),{0:m,1:d}=(0,r.useState)(""),{0:p,1:f}=(0,r.useState)({module:"",imports:""}),g=(0,r.useCallback)((e=>{n({...t,...e,config:{...t.config,...e.config||{}}})}),[t,n]),h=()=>{const e=(0,a.A)(t.config.global_imports||[]);"direct"===s&&m?(e.push(m),d("")):"fromModule"===s&&p.module&&p.imports&&(e.push({module:p.module,imports:p.imports.split(",").map((e=>e.trim())).filter((e=>e))}),f({module:"",imports:""})),g({config:{...t.config,global_imports:e}}),c(!1)};return r.createElement("div",{className:"space-y-6"},r.createElement(U,{title:"Component Details"},r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Name"),r.createElement(B.A,{value:t.label||"",onChange:e=>g({label:e.target.value}),placeholder:"Tool name",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Description"),r.createElement(bt,{value:t.description||"",onChange:e=>g({description:e.target.value}),placeholder:"Tool description",rows:4,className:"mt-1"})))),r.createElement(U,{title:"Configuration"},r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Function Name"),r.createElement(B.A,{value:t.config.name||"",onChange:e=>g({config:{...t.config,name:e.target.value}}),placeholder:"Function name",className:"mt-1"})),r.createElement("div",{className:"space-y-2"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Global Imports"),r.createElement("div",{className:"flex flex-wrap gap-2 mt-2"},(t.config.global_imports||[]).map(((e,n)=>r.createElement("div",{key:n,className:"flex items-center gap-2 bg-tertiary rounded px-2 py-1"},r.createElement("span",{className:"text-sm"},(e=>e?"string"==typeof e?e:`from ${e.module} import ${e.imports.join(", ")}`:"")(e)),r.createElement(l.Ay,{type:"text",size:"small",className:"flex items-center justify-center h-6 w-6 p-0",onClick:()=>(e=>{const n=(0,a.A)(t.config.global_imports||[]);n.splice(e,1),g({config:{...t.config,global_imports:n}})})(n),icon:r.createElement(gt,{className:"h-4 w-4"})}))))),i?r.createElement("div",{className:"border rounded p-3 space-y-3"},r.createElement(ot.A,{value:s,onChange:u,style:{width:200}},r.createElement(vt,{value:"direct"},"Direct Import"),r.createElement(vt,{value:"fromModule"},"From Module Import")),"direct"===s?r.createElement(ft.A,null,r.createElement(B.A,{placeholder:"Package name (e.g., os)",className:"w-64",value:m,onChange:e=>d(e.target.value),onKeyDown:e=>{"Enter"===e.key&&m&&h()}}),r.createElement(l.Ay,{onClick:h,disabled:!m},"Add")):r.createElement(ft.A,{direction:"vertical",className:"w-full"},r.createElement(B.A,{placeholder:"Module name (e.g., typing)",className:"w-64",value:p.module,onChange:e=>f((t=>({...t,module:e.target.value})))}),r.createElement(ft.A,{className:"w-full"},r.createElement(B.A,{placeholder:"Import names (comma-separated)",className:"w-64",value:p.imports,onChange:e=>f((t=>({...t,imports:e.target.value})))}),r.createElement(l.Ay,{onClick:h,disabled:!p.module||!p.imports},"Add")))):r.createElement(l.Ay,{type:"dashed",onClick:()=>c(!0),className:"w-full"},r.createElement(W,{className:"h-4 w-4 mr-2"}),"Add Import")),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Source Code"),r.createElement("div",{className:"mt-1 h-96"},r.createElement(ht.T,{value:t.config.source_code||"",editorRef:o,language:"python",onChange:e=>g({config:{...t.config,source_code:e}})}))),r.createElement("div",{className:"flex items-center gap-2"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Has Cancellation Support"),r.createElement(H.A,{checked:t.config.has_cancellation_support||!1,onChange:e=>g({config:{...t.config,has_cancellation_support:e}})})))))};const Et={MAX_MESSAGE:{label:"Max Messages",provider:F.xq.MAX_MESSAGE,defaultConfig:{max_messages:10,include_agent_event:!1}},TEXT_MENTION:{label:"Text Mention",provider:F.xq.TEXT_MENTION,defaultConfig:{text:"TERMINATE"}}},Nt=e=>{let{label:t,tooltip:n,children:a}=e;return r.createElement("label",{className:"block"},r.createElement("div",{className:"flex items-center gap-2 mb-1"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},t),r.createElement(R.A,{title:n},r.createElement(L,{className:"w-4 h-4 text-gray-400"}))),a)},xt=e=>{let{component:t,onChange:n,onNavigate:o}=e;const{0:i,1:c}=(0,r.useState)(!1),{0:s,1:u}=(0,r.useState)("");if(!t)return null;const m=(0,r.useCallback)((e=>{n({...t,...e,config:{...t.config,...e.config||{}}})}),[t,n]),d=()=>{if(!s||!(0,F.qt)(t))return;const e=(e=>{const t=Et[e];return{provider:t.provider,component_type:"termination",version:1,component_version:1,description:`${t.label} termination condition`,label:t.label,config:t.defaultConfig}})(s),n=t.config.conditions||[];m({config:{conditions:[].concat((0,a.A)(n),[e])}}),c(!1),u("")};var p;return(0,F.qt)(t)?r.createElement(U,{title:"Termination Conditions"},r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"flex justify-between items-center"},r.createElement(l.Ay,{type:"dashed",onClick:()=>c(!0),icon:r.createElement(W,{className:"w-4 h-4"}),className:"w-full"},"Add Condition")),i&&r.createElement("div",{className:"border rounded p-4 space-y-4"},r.createElement(Nt,{label:"Condition Type",tooltip:"Select the type of termination condition to add"},r.createElement(ot.A,{value:s,onChange:u,className:"w-full"},Object.entries(Et).map((e=>{let[t,n]=e;return r.createElement(ot.A.Option,{key:t,value:t},n.label)})))),r.createElement(l.Ay,{onClick:d,disabled:!s,className:"w-full"},"Add")),r.createElement("div",{className:"space-y-2"},null===(p=t.config.conditions)||void 0===p?void 0:p.map(((e,n)=>r.createElement("div",{key:n,className:"flex items-center gap-2"},r.createElement(l.Ay,{onClick:()=>null==o?void 0:o(e.component_type,e.label||"","conditions"),className:"w-full flex justify-between items-center"},r.createElement("span",null,e.label||`Condition ${n+1}`),r.createElement(X.A,{className:"w-4 h-4"})),r.createElement(l.Ay,{type:"text",danger:!0,icon:r.createElement(gt,{className:"w-4 h-4"}),onClick:()=>(e=>{if(!(0,F.qt)(t))return;const n=(0,a.A)(t.config.conditions);n.splice(e,1),m({config:{conditions:n}})})(n)}))))))):(0,F.WR)(t)?r.createElement(U,{title:"Max Messages Configuration"},r.createElement(Nt,{label:"Max Messages",tooltip:"Maximum number of messages before termination"},r.createElement(rt,{min:1,value:t.config.max_messages,onChange:e=>m({config:{max_messages:e}}),className:"w-full"}))):(0,F.K1)(t)?r.createElement(U,{title:"Text Mention Configuration"},r.createElement(Nt,{label:"Termination Text",tooltip:"Text that triggers termination when mentioned"},r.createElement(B.A,{value:t.config.text,onChange:e=>m({config:{text:e.target.value}})}))):null};var wt=n(181),At=n.n(wt),Ct=n(2197),kt=n(4471);const St=(0,T.A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var $t=n(2102),Ot=n(5107),It=n(7799),_t=n(6813);var Mt=e=>{let{result:t,onClose:n}=e;const[a,o]=r.useState(!1),l=t.status?" border-green-200":"  border-red-200",i=t.status?"text-green-500":"text-red-500";return r.createElement("div",{className:`mb-6 rounded-lg border text-primary ${l} overflow-hidden`},r.createElement("div",{className:"p-4"},r.createElement("div",{className:"flex items-start justify-between"},r.createElement("div",{className:"flex items-center gap-2"},t.status?r.createElement(kt.A,{className:`w-5 h-5 ${i}`}):r.createElement(St,{className:`w-5 h-5 ${i}`}),r.createElement("span",{className:"font-medium text-primary"},t.message)),r.createElement("div",{className:"flex items-center gap-2"},r.createElement("button",{onClick:()=>o(!a),className:"p-1 hover:bg-black/5 rounded-md"},a?r.createElement($t.A,{className:"w-4 h-4"}):r.createElement(Ot.A,{className:"w-4 h-4"})),r.createElement("button",{onClick:n,className:"p-1 hover:bg-black/5 rounded-md"},r.createElement(It.A,{className:"w-4 h-4"})))),a&&t.logs&&t.logs.length>0&&r.createElement("div",{className:"mt-4"},r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(_t.A,{className:"w-4 h-4"}),r.createElement("span",{className:"text-sm font-medium"},"Execution Logs")),r.createElement("pre",{className:"bg-secondary text-primary p-4 rounded-md text-sm font-mono overflow-x-auto"},t.logs.join("\n"))),a&&t.data&&r.createElement("div",{className:"mt-4"},r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(_t.A,{className:"w-4 h-4"}),r.createElement("span",{className:"text-sm font-medium"},"Additional Data")),r.createElement("pre",{className:"bg-secondary text-primary p-4 rounded-md text-sm font-mono overflow-x-auto"},JSON.stringify(t.data,null,2)))))};const jt=e=>{let{component:t,onChange:n,onClose:i,navigationDepth:c=!1}=e;const{0:s,1:u}=(0,r.useState)([]),{0:m,1:d}=(0,r.useState)(Object.assign({},t)),{0:p,1:f}=(0,r.useState)(!1),{0:g,1:h}=(0,r.useState)(!1),{0:b,1:v}=(0,r.useState)(null),[y,E]=o.Ay.useMessage(),N=(0,r.useRef)(null);r.useEffect((()=>{d(t),u([]),v(null)}),[t]);const x=(0,r.useCallback)((e=>s.reduce(((e,t)=>{if(!e)return null;const n=e.config[t.parentField];return Array.isArray(n)?"number"==typeof t.index&&t.index>=0&&t.index<n.length?n[t.index]:n.find((e=>e.label===t.id||e.config&&"name"in e.config&&e.config.name===t.id))||null:n||null}),e)),[s]),w=(0,r.useCallback)(((e,t,n)=>{if(0===t.length)return{...e,...n,config:{...e.config,...n.config||{}}};const[a,...r]=t,o=e.config[a.parentField];return{...e,config:{...e.config,[a.parentField]:(l=o,Array.isArray(l)?"number"==typeof a.index&&a.index>=0&&a.index<l.length?l.map(((e,t)=>t===a.index?w(e,r,n):e)):l.map((e=>"component_type"in e&&(e.label===a.id||"name"in e.config&&e.config.name===a.id)?w(e,r,n):e)):l&&"component_type"in l?w(l,r,n):l)}};var l}),[]),A=(0,r.useCallback)((e=>{const t=w(m,s,e);d(t)}),[m,s,w]),C=(0,r.useCallback)(((e,t,n,r)=>{c&&u((o=>[].concat((0,a.A)(o),[{componentType:e,id:t,parentField:n,index:r}])))}),[c]),k=(0,r.useCallback)((()=>{u((e=>e.slice(0,-1)))}),[]),S=(0,r.useCallback)(At()((e=>{try{const t=JSON.parse(e);d(t)}catch(t){console.error("Invalid JSON",t)}}),500),[]),$=x(m)||m,O=(0,r.useCallback)((()=>{const e={component:$,onChange:A};return(0,F.VZ)($)?r.createElement(pt,{component:$,onChange:A,onNavigate:C}):(0,F.fF)($)?r.createElement(J,{component:$,onChange:A,onNavigate:C}):(0,F.nL)($)?r.createElement(ut,{component:$,onChange:A}):(0,F.gG)($)?r.createElement(yt,e):(0,F.U9)($)?r.createElement(xt,{component:$,onChange:A,onNavigate:C}):null}),[$,A,C]),I=r.useMemo((()=>[{title:m.label||"Root"}].concat((0,a.A)(s.map((e=>({title:e.id})))))),[m.label,s]),_=(0,r.useCallback)((()=>{console.log("working copy",m.config),n(m),null==i||i()}),[m,n,i]),M=(0,F.nL)($);return r.createElement("div",{className:"flex flex-col h-full"},E,r.createElement("div",{className:"flex items-center gap-4 mb-6"},c&&s.length>0&&r.createElement(l.Ay,{onClick:k,icon:r.createElement(z,{className:"w-4 h-4"}),type:"text"}),r.createElement("div",{className:"flex-1"},r.createElement(j,{items:I})),M&&r.createElement(R.A,{title:"Test Component"},r.createElement(l.Ay,{onClick:async()=>{h(!0),v(null);try{const e=await Ct.gw.testComponent($);v(e),e.status?y.success("Component test passed!"):y.error("Component test failed!")}catch(e){console.error("Test component error:",e),v({status:!1,message:e instanceof Error?e.message:"Test failed",logs:[]}),y.error("Failed to test component")}finally{h(!1)}},loading:g,type:"default",className:"flex items-center gap-2 text-xs mr-0",icon:r.createElement("div",{className:"relative"},r.createElement(D.A,{className:"w-4 h-4 text-accent"}),b&&r.createElement("div",{className:`absolute top-0 right-0 w-2 h-2 ${b.status?"bg-green-500":"bg-red-500"} rounded-full`}))},"Test")),r.createElement(l.Ay,{onClick:()=>f((e=>!e)),type:"default",className:"flex text-accent items-center gap-2 text-xs"},p?r.createElement(r.Fragment,null,r.createElement(P,{className:"w-4 text-accent h-4 mr-1 inline-block"}),"Form Editor"):r.createElement(r.Fragment,null,r.createElement(q.A,{className:"w-4 text-accent h-4 mr-1 inline-block"}),"JSON Editor"))),b&&r.createElement(Mt,{result:b,onClose:()=>v(null)}),p?r.createElement("div",{className:"flex-1 overflow-y-auto"},r.createElement(ht.T,{editorRef:N,value:JSON.stringify(m,null,2),onChange:S,language:"json",minimap:!0})):r.createElement("div",{className:"flex-1 overflow-y-auto"},O()),i&&r.createElement("div",{className:"flex justify-end gap-2 mt-6 pt-4 border-t border-secondary"},r.createElement(l.Ay,{onClick:i},"Cancel"),r.createElement(l.Ay,{type:"primary",onClick:_},"Save Changes")))};var Rt=jt},7799:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}}]);
//# sourceMappingURL=fea25e403f7cf4f6c7352c9a59c3f8f4372c2770-673333103db2a927e4c0.js.map