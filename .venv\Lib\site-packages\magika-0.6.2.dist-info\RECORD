../../Scripts/magika.exe,sha256=aav-Z7JIFhBOfu_9jEwIdi3bb1AyMa0i2z7hhAD-H0g,23007744
magika-0.6.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
magika-0.6.2.dist-info/METADATA,sha256=LBLsvpTCYeb5kOqlh3-0BqOpbfSKuqTwsGVRx8_RwPA,16126
magika-0.6.2.dist-info/RECORD,,
magika-0.6.2.dist-info/WHEEL,sha256=LLQFiH3PDiV0Fx2rdtwgTAjjdTZOsQg2uytkqCh5Fbg,93
magika/__init__.py,sha256=d2RblKy0-R9swW9Hbf6cWFhEO17sS9ZtWWPyuRa_onU,953
magika/__pycache__/__init__.cpython-312.pyc,,
magika/__pycache__/colors.cpython-312.pyc,,
magika/__pycache__/logger.cpython-312.pyc,,
magika/__pycache__/magika.cpython-312.pyc,,
magika/cli/__pycache__/magika_client.cpython-312.pyc,,
magika/cli/__pycache__/magika_rust_client_not_found_warning.cpython-312.pyc,,
magika/cli/magika_client.py,sha256=VP1i09B3o-_PV-MFYoG7La08paaqTXXECkw_2dyH8lA,12081
magika/cli/magika_rust_client_not_found_warning.py,sha256=DWj70K0qWAmr9eUL99MAfzAjCuWfqfPums_ynhXsWPs,1205
magika/colors.py,sha256=rm2aZIsi2fWojye46twmGd5-4oxOZOhJgBYUbXU7S5Y,1073
magika/config/content_types_kb.min.json,sha256=dSCK26abwFVkA7YrMu88z4tc5JRBF4CEWFLlLmWDoQ0,44768
magika/logger.py,sha256=xyJqGIrL9qnRLKj00fHmkvwezSjtutzw1rCLfD4neuQ,2995
magika/magika.py,sha256=rgEmYumUgmrbc6eyS729KzboLJvOuecvpur6WX_ZYqQ,32477
magika/models/standard_v3_3/README.md,sha256=NVgW-EfbpXLMZzpq-mxN7TERiWWrQVZrgXpnjfFrB3c,16899
magika/models/standard_v3_3/config.min.json,sha256=mRpvt2DAQx5cA1Ckh7pL2J3uQ7dNU7d8yIEGRp2ZYXo,2142
magika/models/standard_v3_3/metadata.json,sha256=TjHOiH-3sFyJQbyqNYMopHZ66KH_nGtuf4FKqacBuOg,20
magika/models/standard_v3_3/model.onnx,sha256=_i0utJxfiKngpsBI4V1v_fhiNVGcKvxTUETeQzFp7Iw,3163737
magika/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
magika/types/__init__.py,sha256=6ve2JI6ET9OrtZtdk0fZD2pZInC-ErooEAxaGUvKRN8,1585
magika/types/__pycache__/__init__.cpython-312.pyc,,
magika/types/__pycache__/content_type_info.cpython-312.pyc,,
magika/types/__pycache__/content_type_label.cpython-312.pyc,,
magika/types/__pycache__/magika_error.cpython-312.pyc,,
magika/types/__pycache__/magika_prediction.cpython-312.pyc,,
magika/types/__pycache__/magika_result.cpython-312.pyc,,
magika/types/__pycache__/model.cpython-312.pyc,,
magika/types/__pycache__/overwrite_reason.cpython-312.pyc,,
magika/types/__pycache__/prediction_mode.cpython-312.pyc,,
magika/types/__pycache__/seekable.cpython-312.pyc,,
magika/types/__pycache__/status.cpython-312.pyc,,
magika/types/__pycache__/strenum.cpython-312.pyc,,
magika/types/content_type_info.py,sha256=WLFANUu1LtqqEFV7YF2-rKAzJSDmPTahVZEQa4sw1O8,1386
magika/types/content_type_label.py,sha256=bPwEvtjkappe5rUcCXPNklLwVxO4c4kxzJ2Wj_pSuvQ,8099
magika/types/magika_error.py,sha256=IwEOO-3lRgfCIopWFf5tpNL0EpPcBQyfDZqa2pkGIHU,41
magika/types/magika_prediction.py,sha256=x4-mQ-j_TYx4p66PWG-qLV0oQKckIfpJhwAAMNV0ehg,948
magika/types/magika_result.py,sha256=K9i4xb36NCMV3fRQJ2ZqYUbiTBlL3VxJQZTWL2AeCxM,3359
magika/types/model.py,sha256=4VfZgAqoB-cUYUmKhPBR13-n_9tART1Cuf6dCTFimWk,1518
magika/types/overwrite_reason.py,sha256=3CFr4AevoI1He_IAqIpbLrHjCV_nHHFwJJVb8ADOgOk,794
magika/types/prediction_mode.py,sha256=4_arRpTHvmMe3BoDQ-Onxlxs6xom3WrMUKBSHCmOqqg,984
magika/types/seekable.py,sha256=XSLvrFWDZzujjVibnM2ZGEOeYvsROAwfk2vIUSBN2F0,1103
magika/types/status.py,sha256=fuHzxNb1TDhxqrDxxlEzAHeMNaF0XDjuUxE61tV-_dA,1023
magika/types/strenum.py,sha256=N6lpPQYYi8S3w1AHjqfqyAAXER5PzwGZfEn4ZhKrIas,1335
