{"version": 3, "file": "6be23114e43bcb7eaa5d44e1c459a9e00d37e5e0-aaf5056eca2868da3f54.js", "mappings": "kMAEA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8DAAiE,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,gEAAoE,KAAQ,OAAQ,MAAS,Y,UCMrV,EAAe,SAAsBA,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,yFCd5C,GAA4B,IAAAC,eAAc,M,iDCwE1C,EAtEmB,SAAsBC,GACvC,IAAIC,EAAkBD,EAAQC,gBAC5BC,EAAaF,EAAQE,WACrBC,EAAMH,EAAQG,IACdC,EAAqBJ,EAAQK,UAC7BA,OAAmC,IAAvBD,EAAgC,CAAC,EAAIA,EAC/CE,EAAOD,EAAUC,KACnBC,EAAmBF,EAAUG,MAC7BA,OAA6B,IAArBD,EAA8B,SAAWA,EAC/CE,GAAY,IAAAC,YACdC,GAAa,OAAeF,EAAW,GACvCG,EAAWD,EAAW,GACtBE,EAAcF,EAAW,GACvBG,GAAe,IAAAC,UACfC,EAAY,eAAkB,SAAUC,GAC1C,MAAoB,mBAATX,EACFA,EAAKW,GAEM,iBAATX,EACFA,EAEFW,CACT,GAAG,CAACX,IAGJ,SAASY,IACPC,EAAA,EAAIC,OAAON,EAAaO,QAC1B,CAuCA,OAtCA,IAAAC,YAAU,WACR,IAAIC,EAAc,CAAC,EACnB,GAAItB,EACF,GAAIC,EAAY,CACdqB,EAAYC,MAAQR,EAAUf,EAAgBuB,OAC9C,IAAIC,EAAMtB,EAAM,QAAU,OACZ,UAAVK,IACFe,EAAYE,GAAOxB,EAAgBwB,IAEvB,WAAVjB,IACFe,EAAYE,GAAOxB,EAAgBwB,GAAOxB,EAAgBuB,MAAQ,EAClED,EAAYG,UAAYvB,EAAM,kBAAoB,oBAEtC,QAAVK,IACFe,EAAYE,GAAOxB,EAAgBwB,GAAOxB,EAAgBuB,MAC1DD,EAAYG,UAAY,oBAE5B,MACEH,EAAYI,OAASX,EAAUf,EAAgB0B,QACjC,UAAVnB,IACFe,EAAYK,IAAM3B,EAAgB2B,KAEtB,WAAVpB,IACFe,EAAYK,IAAM3B,EAAgB2B,IAAM3B,EAAgB0B,OAAS,EACjEJ,EAAYG,UAAY,oBAEZ,QAAVlB,IACFe,EAAYK,IAAM3B,EAAgB2B,IAAM3B,EAAgB0B,OACxDJ,EAAYG,UAAY,qBAQ9B,OAJAR,IACAJ,EAAaO,SAAU,EAAAF,EAAA,IAAI,WACzBN,EAAYU,EACd,IACOL,CACT,GAAG,CAACjB,EAAiBC,EAAYC,EAAKK,EAAOQ,IACtC,CACLa,MAAOjB,EAEX,ECtEIkB,EAAe,CACjBN,MAAO,EACPG,OAAQ,EACRI,KAAM,EACNH,IAAK,GCJQ,SAASI,EAAaC,EAAcC,GACjD,IAAIC,EAAW,SAAaF,GACxBG,EAAkB,WAAe,CAAC,GAEpCC,GADmB,OAAeD,EAAiB,GACpB,GASjC,MAAO,CAACD,EAASd,QARjB,SAAkBiB,GAChB,IAAIC,EAA8B,mBAAZD,EAAyBA,EAAQH,EAASd,SAAWiB,EACvEC,IAAaJ,EAASd,SACxBa,EAASK,EAAUJ,EAASd,SAE9Bc,EAASd,QAAUkB,EACnBF,EAAY,CAAC,EACf,EAEF,CCbA,IAGIG,EAAqBC,KAAKC,IAAI,KADX,I,aCGR,SAASC,EAAUC,GAChC,IAAInC,GAAY,IAAAC,UAAS,GACvBC,GAAa,OAAeF,EAAW,GACvCoC,EAAQlC,EAAW,GACnBmC,EAAWnC,EAAW,GACpBoC,GAAY,IAAAhC,QAAO,GACnBiC,GAAc,IAAAjC,UAUlB,OATAiC,EAAY3B,QAAUuB,GAGtB,QAAsB,WACpB,IAAIK,EAC6C,QAAhDA,EAAuBD,EAAY3B,eAA8C,IAAzB4B,GAAmCA,EAAqBC,KAAKF,EACxH,GAAG,CAACH,IAGG,WACDE,EAAU1B,UAAYwB,IAG1BE,EAAU1B,SAAW,EACrByB,EAASC,EAAU1B,SACrB,CACF,CC9BA,IAAI,EAAe,CACjBG,MAAO,EACPG,OAAQ,EACRI,KAAM,EACNH,IAAK,EACLuB,MAAO,GCFF,SAASC,EAAUC,GACxB,IAAIC,EASJ,OARID,aAAeE,KACjBD,EAAM,CAAC,EACPD,EAAIG,SAAQ,SAAUC,EAAGC,GACvBJ,EAAII,GAAKD,CACX,KAEAH,EAAMD,EAEDM,KAAKP,UAAUE,EACxB,CAEO,SAASM,EAAenC,GAC7B,OAAOoC,OAAOpC,GAAKqC,QAAQ,KAFF,UAG3B,CACO,SAASC,EAAaC,EAAUC,EAAWC,EAAUC,GAC1D,SAECD,GAEDC,IAEa,IAAbH,QAEaI,IAAbJ,KAAyC,IAAdC,GAAqC,OAAdA,GAIpD,CChCA,IAAII,EAAyB,cAAiB,SAAU3E,EAAOC,GAC7D,IAAI2E,EAAY5E,EAAM4E,UACpBJ,EAAWxE,EAAMwE,SACjBK,EAAS7E,EAAM6E,OACf1C,EAAQnC,EAAMmC,MAChB,OAAKqC,IAAiC,IAArBA,EAASM,QAGN,gBAAoB,SAAU,CAChD7E,IAAKA,EACL8E,KAAM,SACNC,UAAW,GAAGC,OAAOL,EAAW,YAChCzC,MAAOA,EACP,cAAe0C,aAAuC,EAASA,EAAOK,eAAiB,UACvFC,QAAS,SAAiBC,GACxBZ,EAASa,OAAO,MAAO,CACrBD,MAAOA,GAEX,GACCZ,EAASc,SAAW,KAbd,IAcX,IACA,ICUA,MA9BgC,cAAiB,SAAUtF,EAAOC,GAChE,IAMIsF,EANAC,EAAWxF,EAAMwF,SACnBZ,EAAY5E,EAAM4E,UAClBa,EAAQzF,EAAMyF,MAChB,IAAKA,EACH,OAAO,KAKT,IAAIC,EAAc,CAAC,EAYnB,MAXuB,YAAnB,OAAQD,IAAsC,iBAAqBA,GAGrEC,EAAYjC,MAAQgC,EAFpBC,EAAcD,EAIC,UAAbD,IACFD,EAAUG,EAAYjC,OAEP,SAAb+B,IACFD,EAAUG,EAAYrD,MAEjBkD,EAAuB,gBAAoB,MAAO,CACvDP,UAAW,GAAGC,OAAOL,EAAW,kBAChC3E,IAAKA,GACJsF,GAAW,IAChB,I,8BCjBII,EAA6B,cAAiB,SAAU3F,EAAOC,GACjE,IAAI2E,EAAY5E,EAAM4E,UACpBgB,EAAK5F,EAAM4F,GACXC,EAAO7F,EAAM6F,KACbhB,EAAS7E,EAAM6E,OACfiB,EAAS9F,EAAM8F,OACfC,EAAc/F,EAAMgG,KACpBC,OAA4B,IAAhBF,EAAyB,CAAC,EAAIA,EAC1C5D,EAAQnC,EAAMmC,MACd6C,EAAYhF,EAAMgF,UAClBR,EAAWxE,EAAMwE,SACjB0B,EAAelG,EAAMkG,aACrBzF,EAAMT,EAAMS,IACZ0F,EAAkBnG,EAAMmG,gBACxBC,EAAapG,EAAMoG,WACnBC,EAAoBrG,EAAMqG,kBAC1BC,EAAiBtG,EAAMsG,eAErBvF,GAAY,IAAAC,WAAS,GACvBC,GAAa,OAAeF,EAAW,GACvCwF,EAAOtF,EAAW,GAClBuF,EAAUvF,EAAW,GACnBwF,GAAa,IAAAzF,UAAS,MACxB0F,GAAa,OAAeD,EAAY,GACxCE,EAAcD,EAAW,GACzBE,EAAiBF,EAAW,GAC1BG,EAAkBZ,EAAU7F,KAC9B0G,OAA+B,IAApBD,EAA6B,OAASA,EAC/CE,EAAU,GAAG9B,OAAOW,EAAI,eACxBoB,EAAiB,GAAG/B,OAAOL,EAAW,aACtCqC,EAAiC,OAAhBN,EAAuB,GAAG1B,OAAO8B,EAAS,KAAK9B,OAAO0B,GAAe,KACtFO,EAAoBrC,aAAuC,EAASA,EAAOqC,kBAS/E,IAAIC,EAAoB,gBAAoB,KAAM,CAChDhC,QAAS,SAAiBiC,GACxB,IAAIrF,EAAMqF,EAAKrF,IACbsF,EAAWD,EAAKC,SAClBjB,EAAWrE,EAAKsF,GAChBb,GAAQ,EACV,EACA5B,UAAW,GAAGK,OAAO+B,EAAgB,SACrCpB,GAAImB,EACJO,UAAW,EACXC,KAAM,UACN,wBAAyBN,EACzBO,aAAc,CAACb,GACf,kBAAoCjC,IAAtBwC,EAAkCA,EAAoB,qBACnErB,EAAK4B,KAAI,SAAUC,GACpB,IAAIpD,EAAWoD,EAAIpD,SACjBG,EAAWiD,EAAIjD,SACfF,EAAYmD,EAAInD,UAChBxC,EAAM2F,EAAI3F,IACV4F,EAAQD,EAAIC,MACVC,EAAYvD,EAAaC,EAAUC,EAAWC,EAAUC,GAC5D,OAAoB,gBAAoB,KAAU,CAChD1C,IAAKA,EACL6D,GAAI,GAAGX,OAAO8B,EAAS,KAAK9B,OAAOlD,GACnCwF,KAAM,SACN,gBAAiB3B,GAAM,GAAGX,OAAOW,EAAI,WAAWX,OAAOlD,GACvD0C,SAAUA,GACI,gBAAoB,OAAQ,KAAMkD,GAAQC,GAA0B,gBAAoB,SAAU,CAChH7C,KAAM,SACN,aAAcoB,GAAmB,SACjCmB,SAAU,EACVtC,UAAW,GAAGC,OAAO+B,EAAgB,qBACrC7B,QAAS,SAAiB0C,GACxBA,EAAEC,kBAzCR,SAAqB1C,EAAOrD,GAC1BqD,EAAM2C,iBACN3C,EAAM0C,kBACNtD,EAASa,OAAO,SAAU,CACxBtD,IAAKA,EACLqD,MAAOA,GAEX,CAmCM4C,CAAYH,EAAG9F,EACjB,GACCwC,GAAaC,EAASyD,YAAc,KACzC,KACA,SAASC,EAAaC,GAQpB,IAPA,IAAIC,EAAcvC,EAAKwC,QAAO,SAAUX,GACtC,OAAQA,EAAIjD,QACd,IACI6D,EAAgBF,EAAYG,WAAU,SAAUb,GAClD,OAAOA,EAAI3F,MAAQ4E,CACrB,KAAM,EACF6B,EAAMJ,EAAYK,OACbC,EAAI,EAAGA,EAAIF,EAAKE,GAAK,EAAG,CAE/B,IAAIhB,EAAMU,EADVE,GAAiBA,EAAgBH,EAASK,GAAOA,GAEjD,IAAKd,EAAIjD,SAEP,YADAmC,EAAec,EAAI3F,IAGvB,CACF,EAgCA,IAAAH,YAAU,WAER,IAAI+G,EAAMC,SAASC,eAAe5B,GAC9B0B,GAAOA,EAAIG,gBACbH,EAAIG,gBAAe,EAEvB,GAAG,CAACnC,KACJ,IAAA/E,YAAU,WACH2E,GACHK,EAAe,KAEnB,GAAG,CAACL,IAGJ,IAAIwC,GAAY,OAAgB,CAAC,EAAGtI,EAAM,cAAgB,aAAcyF,GACnEL,EAAK4C,SACRM,EAAUC,WAAa,SACvBD,EAAUE,MAAQ,GAEpB,IAAIC,EAAmB,KAAW,OAAgB,CAAC,EAAG,GAAGjE,OAAO+B,EAAgB,QAASvG,IACrF0I,EAAWrD,EAAS,KAAoB,gBAAoB,KAAU,OAAS,CACjFlB,UAAWoC,EACXoC,QAASjC,EACTkC,UAASxD,EAAK4C,QAASlC,EACvB+C,gBAAiB9C,EACjB0C,iBAAkB,IAAWA,EAAkB5C,GAC/CiD,gBAAiB,GACjBC,gBAAiB,GACjBnD,kBAAmBA,GAClBJ,GAAyB,gBAAoB,SAAU,CACxDlB,KAAM,SACNC,UAAW,GAAGC,OAAOL,EAAW,aAChCzC,MAAO4G,EACP,gBAAiB,UACjB,gBAAiBhC,EACjBnB,GAAI,GAAGX,OAAOW,EAAI,SAClB,gBAAiBW,EACjBkD,UApEF,SAAmB5B,GACjB,IAAI6B,EAAQ7B,EAAE6B,MACd,GAAKnD,EAOL,OAAQmD,GACN,KAAKC,EAAA,EAAQC,GACX1B,GAAc,GACdL,EAAEE,iBACF,MACF,KAAK4B,EAAA,EAAQE,KACX3B,EAAa,GACbL,EAAEE,iBACF,MACF,KAAK4B,EAAA,EAAQG,IACXtD,GAAQ,GACR,MACF,KAAKmD,EAAA,EAAQI,MACb,KAAKJ,EAAA,EAAQK,MACS,OAAhBrD,GACFP,EAAWO,EAAakB,OArBxB,CAAC8B,EAAA,EAAQE,KAAMF,EAAA,EAAQI,MAAOJ,EAAA,EAAQK,OAAOC,SAASP,KACxDlD,GAAQ,GACRqB,EAAEE,iBAuBR,GAyCGjB,IACH,OAAoB,gBAAoB,MAAO,CAC7C9B,UAAW,IAAW,GAAGC,OAAOL,EAAW,mBAAoBI,GAC/D7C,MAAOA,EACPlC,IAAKA,GACJkJ,EAAuB,gBAAoB,EAAW,CACvDvE,UAAWA,EACXC,OAAQA,EACRL,SAAUA,IAEd,IACA,EAA4B,OAAWmB,GAAe,SAAUuE,EAAGC,GACjE,OAGEA,EACF,SACF,IC3FA,EAjGc,SAAiBnK,GAC7B,IAAI4E,EAAY5E,EAAM4E,UACpBgB,EAAK5F,EAAM4F,GACXwE,EAASpK,EAAMoK,OACfC,EAAQrK,EAAMqK,MACdC,EAAatK,EAAM0H,IACnB3F,EAAMuI,EAAWvI,IACjB4F,EAAQ2C,EAAW3C,MACnBlD,EAAW6F,EAAW7F,SACtBF,EAAY+F,EAAW/F,UACvBnE,EAAOkK,EAAWlK,KAClBkE,EAAWtE,EAAMsE,SACjBiG,EAAgBvK,EAAMuK,cACtBpE,EAAkBnG,EAAMmG,gBACxB3B,EAAWxE,EAAMwE,SACjBW,EAAUnF,EAAMmF,QAChBqF,EAAUxK,EAAMwK,QAChBC,EAASzK,EAAMyK,OACfhB,EAAYzJ,EAAMyJ,UAClBiB,EAAc1K,EAAM0K,YACpBC,EAAY3K,EAAM2K,UAClBxI,EAAQnC,EAAMmC,MACdyI,EAAW5K,EAAM4K,SACjBC,EAAkB7K,EAAM6K,gBACtBC,EAAY,GAAG7F,OAAOL,EAAW,QACjCgD,EAAYvD,EAAaC,EAAUC,EAAWC,EAAUC,GAC5D,SAASsG,EAAgBlD,GACnBpD,GAGJU,EAAQ0C,EACV,CASA,IAAImD,EAAY,WAAc,WAC5B,OAAO5K,GAAyB,iBAAVuH,EAAkC,gBAAoB,OAAQ,KAAMA,GAASA,CACrG,GAAG,CAACA,EAAOvH,IACP6K,EAAS,SAAa,MAC1B,aAAgB,WACVZ,GAASY,EAAOtJ,SAClBsJ,EAAOtJ,QAAQ0I,OAEnB,GAAG,CAACA,IACJ,IAAIa,EAAoB,gBAAoB,MAAO,CACjDnJ,IAAKA,EACL,gBAAiBmC,EAAenC,GAChCiD,UAAW,IAAW8F,GAAW,QAAgB,QAAgB,QAAgB,OAAgB,CAAC,EAAG,GAAG7F,OAAO6F,EAAW,gBAAiBlD,GAAY,GAAG3C,OAAO6F,EAAW,WAAYV,GAAS,GAAGnF,OAAO6F,EAAW,aAAcrG,GAAW,GAAGQ,OAAO6F,EAAW,UAAWT,IAC/QlI,MAAOA,EACPgD,QAAS4F,GACK,gBAAoB,MAAO,CACzC9K,IAAKgL,EACL1D,KAAM,MACN,gBAAiB6C,EACjBxE,GAAIA,GAAM,GAAGX,OAAOW,EAAI,SAASX,OAAOlD,GACxCiD,UAAW,GAAGC,OAAO6F,EAAW,QAChC,gBAAiBlF,GAAM,GAAGX,OAAOW,EAAI,WAAWX,OAAOlD,GACvD,gBAAiB0C,EACjB6C,SAAU7C,EAAW,KAAO2F,EAAS,GAAK,EAC1CjF,QAAS,SAAiB0C,GACxBA,EAAEC,kBACFiD,EAAgBlD,EAClB,EACA4B,UAAWA,EACXiB,YAAaA,EACbC,UAAWA,EACXH,QAASA,EACTC,OAAQA,GACPJ,GAAsB,gBAAoB,MAAO,CAClD,YAAa,SACblI,MAAO,CACLL,MAAO,EACPG,OAAQ,EACRuD,SAAU,WACV2F,SAAU,SACVC,QAAS,IAEV,OAAOnG,OAAO4F,EAAiB,QAAQ5F,OAAO2F,IAAYxK,GAAqB,gBAAoB,OAAQ,CAC5G4E,UAAW,GAAGC,OAAO6F,EAAW,UAC/B1K,GAAOuH,GAASqD,GAAYpD,GAA0B,gBAAoB,SAAU,CACrF7C,KAAM,SACNwC,KAAM,MACN,aAAcpB,GAAmB,SACjCmB,SAAU8C,EAAS,GAAK,EACxBpF,UAAW,GAAGC,OAAO6F,EAAW,WAChC3F,QAAS,SAAiB0C,GA1D5B,IAAqBzC,EA2DjByC,EAAEC,mBA3De1C,EA4DLyC,GA3DRE,iBACN3C,EAAM0C,kBACNtD,EAASa,OAAO,SAAU,CACxBtD,IAAKA,EACLqD,MAAOA,GAwDT,GACCb,GAAaC,EAASyD,YAAc,MACvC,OAAOsC,EAAgBA,EAAcW,GAAQA,CAC/C,EC1DIG,EAAU,SAAiBC,GAC7B,IAAIlE,EAAOkE,EAAO3J,SAAW,CAAC,EAC5B4J,EAAmBnE,EAAKoE,YACxBA,OAAmC,IAArBD,EAA8B,EAAIA,EAChDE,EAAoBrE,EAAKsE,aACzBA,OAAqC,IAAtBD,EAA+B,EAAIA,EAGpD,GAAIH,EAAO3J,QAAS,CAClB,IAAIgK,EAAwBL,EAAO3J,QAAQiK,wBACzC9J,EAAQ6J,EAAsB7J,MAC9BG,EAAS0J,EAAsB1J,OACjC,GAAIc,KAAK8I,IAAI/J,EAAQ0J,GAAe,EAClC,MAAO,CAAC1J,EAAOG,EAEnB,CACA,MAAO,CAACuJ,EAAaE,EACvB,EAKII,EAAe,SAAsBlL,EAAMmL,GAC7C,OAAOnL,EAAKmL,EAAyB,EAAI,EAC3C,EACIC,EAA0B,cAAiB,SAAUhM,EAAOC,GAC9D,IAAI+E,EAAYhF,EAAMgF,UACpB7C,EAAQnC,EAAMmC,MACdyD,EAAK5F,EAAM4F,GACXqG,EAAWjM,EAAMiM,SACjBC,EAAYlM,EAAMkM,UAClBzL,EAAMT,EAAMS,IACZgF,EAAQzF,EAAMyF,MACdjB,EAAWxE,EAAMwE,SACjBK,EAAS7E,EAAM6E,OACfsH,EAAcnM,EAAMmM,YACpBjG,EAAelG,EAAMkG,aACrBkG,EAAWpM,EAAMoM,SACjBhG,EAAapG,EAAMoG,WACnBiG,EAAcrM,EAAMqM,YACpB1L,EAAYX,EAAMW,UAChB2L,EAAoB,aAAiBC,GACvC3H,EAAY0H,EAAkB1H,UAC9BiB,EAAOyG,EAAkBzG,KACvB2G,GAAe,IAAAnL,QAAO,MACtBoL,GAAe,IAAApL,QAAO,MACtBqL,GAAgB,IAAArL,QAAO,MACvBsL,GAAiB,IAAAtL,QAAO,MACxBuL,GAAa,IAAAvL,QAAO,MACpBwL,GAAgB,IAAAxL,QAAO,MACvByL,GAAoB,IAAAzL,QAAO,MAC3B0K,EAAyC,QAAhBI,GAAyC,WAAhBA,EAClDY,GAAgBzK,EAAa,GAAG,SAAU6H,EAAM6C,GAC5CjB,GAA0BM,GAC5BA,EAAY,CACVY,UAAW9C,EAAO6C,EAAO,OAAS,SAGxC,IACAE,IAAiB,OAAeH,GAAe,GAC/CI,GAAgBD,GAAe,GAC/BE,GAAmBF,GAAe,GAChCG,GAAiB/K,EAAa,GAAG,SAAU6H,EAAM6C,IAC5CjB,GAA0BM,GAC7BA,EAAY,CACVY,UAAW9C,EAAO6C,EAAO,MAAQ,UAGvC,IACAM,IAAiB,OAAeD,GAAgB,GAChDE,GAAeD,GAAe,GAC9BE,GAAkBF,GAAe,GAC/BvM,IAAY,IAAAC,UAAS,CAAC,EAAG,IAC3BC,IAAa,OAAeF,GAAW,GACvC0M,GAA4BxM,GAAW,GACvCyM,GAA+BzM,GAAW,GACxCwF,IAAa,IAAAzF,UAAS,CAAC,EAAG,IAC5B0F,IAAa,OAAeD,GAAY,GACxCkH,GAAiBjH,GAAW,GAC5BkH,GAAoBlH,GAAW,GAC7BmH,IAAa,IAAA7M,UAAS,CAAC,EAAG,IAC5B8M,IAAa,OAAeD,GAAY,GACxCE,GAAUD,GAAW,GACrBE,GAAaF,GAAW,GACtBG,IAAa,IAAAjN,UAAS,CAAC,EAAG,IAC5BkN,IAAa,OAAeD,GAAY,GACxCE,GAAgBD,GAAW,GAC3BE,GAAmBF,GAAW,GAC5BG,GPlGC,SAAwB9L,GAC7B,IAAI+L,GAAW,IAAAjN,QAAO,IAClBoF,GAAa,IAAAzF,UAAS,CAAC,GAEzB2B,GADa,OAAe8D,EAAY,GACf,GACvB8H,GAAQ,IAAAlN,QAA+B,mBAAjBkB,EAA8BA,IAAiBA,GACrEiM,EAAcvL,GAAU,WAC1B,IAAItB,EAAU4M,EAAM5M,QACpB2M,EAAS3M,QAAQmC,SAAQ,SAAUZ,GACjCvB,EAAUuB,EAASvB,EACrB,IACA2M,EAAS3M,QAAU,GACnB4M,EAAM5M,QAAUA,EAChBgB,EAAY,CAAC,EACf,IAKA,MAAO,CAAC4L,EAAM5M,QAJd,SAAiBuB,GACfoL,EAAS3M,QAAQ8M,KAAKvL,GACtBsL,GACF,EAEF,CO8EwBE,CAAe,IAAI7K,KACvC8K,IAAmB,OAAeN,GAAiB,GACnDO,GAAWD,GAAiB,GAC5BE,GAAcF,GAAiB,GAC7BG,GV9HS,SAAoBjJ,EAAM+I,EAAUG,GACjD,OAAO,IAAAC,UAAQ,WAKb,IAJA,IAAIC,EACAxH,EAAM,IAAI5D,IACVqL,EAAaN,EAASO,IAA2B,QAAtBF,EAASpJ,EAAK,UAA2B,IAAXoJ,OAAoB,EAASA,EAAOlN,MAAQK,EACrGgN,EAAcF,EAAW7M,KAAO6M,EAAWpN,MACtC4G,EAAI,EAAGA,EAAI7C,EAAK4C,OAAQC,GAAK,EAAG,CACvC,IAKM2G,EALFtN,EAAM8D,EAAK6C,GAAG3G,IACduN,EAAOV,EAASO,IAAIpN,GAGnBuN,IAEHA,EAAOV,EAASO,IAA8B,QAAzBE,EAAQxJ,EAAK6C,EAAI,UAA0B,IAAV2G,OAAmB,EAASA,EAAMtN,MAAQK,GAElG,IAAImN,EAAS9H,EAAI0H,IAAIpN,KAAQ,OAAc,CAAC,EAAGuN,GAG/CC,EAAO9L,MAAQ2L,EAAcG,EAAOlN,KAAOkN,EAAOzN,MAGlD2F,EAAI+H,IAAIzN,EAAKwN,EACf,CACA,OAAO9H,CACT,GAAG,CAAC5B,EAAK4B,KAAI,SAAUC,GACrB,OAAOA,EAAI3F,GACb,IAAG0N,KAAK,KAAMb,EAAUG,GAC1B,CUmGmBW,CAAW7J,EAAM+I,GAAUjB,GAAe,IAGvDgC,GAAiC7D,EAAa2B,GAA2B1B,GACzE6D,GAAsB9D,EAAa6B,GAAgB5B,GACnD8D,GAAe/D,EAAaiC,GAAShC,GACrC+D,GAAqBhE,EAAaqC,GAAepC,GACjDgE,GAAahN,KAAKiN,MAAML,IAAkC5M,KAAKiN,MAAMJ,GAAsBC,IAC3FI,GAAyBF,GAAaJ,GAAiCG,GAAqBH,GAAiCE,GAG7HK,GAA4B,GAAGjL,OAAOL,EAAW,0BACjDuL,GAAe,EACfC,GAAe,EAWnB,SAASC,GAAaC,GACpB,OAAIA,EAAQH,GACHA,GAELG,EAAQF,GACHA,GAEFE,CACT,CAlBKvE,GAGMtL,GACT0P,GAAe,EACfC,GAAerN,KAAKwN,IAAI,EAAGX,GAAsBK,MAJjDE,GAAepN,KAAKyN,IAAI,EAAGP,GAAyBL,IACpDQ,GAAe,GAmBjB,IAAIK,IAAiB,IAAApP,QAAO,MACxBqP,IAAa,IAAA1P,YACf2P,IAAc,OAAeD,GAAY,GACzCE,GAAgBD,GAAY,GAC5BE,GAAmBF,GAAY,GACjC,SAASG,KACPD,GAAiBE,KAAKC,MACxB,CACA,SAASC,KACHR,GAAe9O,SACjBuP,aAAaT,GAAe9O,QAEhC,ER5Ka,SAAsB1B,EAAKkR,GACxC,IAAIpQ,GAAY,IAAAC,YACdC,GAAa,OAAeF,EAAW,GACvCqQ,EAAgBnQ,EAAW,GAC3BoQ,EAAmBpQ,EAAW,GAC5BwF,GAAa,IAAAzF,UAAS,GACxB0F,GAAa,OAAeD,EAAY,GACxC6K,EAAgB5K,EAAW,GAC3B6K,EAAmB7K,EAAW,GAC5BmH,GAAa,IAAA7M,UAAS,GACxB8M,GAAa,OAAeD,EAAY,GACxC2D,EAAe1D,EAAW,GAC1B2D,EAAkB3D,EAAW,GAC3BG,GAAa,IAAAjN,YACfkN,GAAa,OAAeD,EAAY,GACxCiB,EAAahB,EAAW,GACxBwD,EAAgBxD,EAAW,GACzByD,GAAY,IAAAtQ,UAiEZuQ,GAAwB,IAAAvQ,UAwBxBwQ,GAAiB,IAAAxQ,QAAO,MAC5BwQ,EAAelQ,QAAU,CACvBmQ,aAvFF,SAAsBjK,GACpB,IAAIkK,EAAclK,EAAEmK,QAAQ,GAC1BC,EAAUF,EAAYE,QACtBC,EAAUH,EAAYG,QACxBb,EAAiB,CACfc,EAAGF,EACHG,EAAGF,IAELG,OAAOC,cAAcX,EAAUhQ,QACjC,EA+EE4Q,YA9EF,SAAqB1K,GACnB,GAAKuJ,EAAL,CAGA,IAAIoB,EAAe3K,EAAEmK,QAAQ,GAC3BC,EAAUO,EAAaP,QACvBC,EAAUM,EAAaN,QACzBb,EAAiB,CACfc,EAAGF,EACHG,EAAGF,IAEL,IAAIO,EAAUR,EAAUb,EAAce,EAClCO,EAAUR,EAAUd,EAAcgB,EACtCjB,EAASsB,EAASC,GAClB,IAAI1B,EAAMD,KAAKC,MACfO,EAAiBP,GACjBS,EAAgBT,EAAMM,GACtBI,EAAc,CACZS,EAAGM,EACHL,EAAGM,GAlBqB,CAoB5B,EA0DEC,WAzDF,WACE,GAAKvB,IACLC,EAAiB,MACjBK,EAAc,MAGVxC,GAAY,CACd,IAAI0D,EAAY1D,EAAWiD,EAAIX,EAC3BqB,EAAY3D,EAAWkD,EAAIZ,EAC3BsB,EAAO/P,KAAK8I,IAAI+G,GAChBG,EAAOhQ,KAAK8I,IAAIgH,GAGpB,GAAI9P,KAAKwN,IAAIuC,EAAMC,GAxEA,GAwE4B,OAC/C,IAAIC,EAAWJ,EACXK,EAAWJ,EACflB,EAAUhQ,QAAU0Q,OAAOa,aAAY,WACjCnQ,KAAK8I,IAAImH,GA3EK,KA2E8BjQ,KAAK8I,IAAIoH,GA3EvC,IA4EhBZ,OAAOC,cAAcX,EAAUhQ,SAKjCwP,EAhFe,IA8Ef6B,GAAYlQ,GA9EG,IA+EfmQ,GAAYnQ,GAEd,GAjFiB,GAkFnB,CACF,EAgCEqQ,QA5BF,SAAiBtL,GACf,IAAIuL,EAASvL,EAAEuL,OACbC,EAASxL,EAAEwL,OAGTC,EAAQ,EACRR,EAAO/P,KAAK8I,IAAIuH,GAChBL,EAAOhQ,KAAK8I,IAAIwH,GAChBP,IAASC,EACXO,EAA0C,MAAlC1B,EAAsBjQ,QAAkByR,EAASC,EAChDP,EAAOC,GAChBO,EAAQF,EACRxB,EAAsBjQ,QAAU,MAEhC2R,EAAQD,EACRzB,EAAsBjQ,QAAU,KAE9BwP,GAAUmC,GAAQA,IACpBzL,EAAEE,gBAEN,GAUA,aAAgB,WAId,SAASwL,EAAiB1L,GACxBgK,EAAelQ,QAAQ4Q,YAAY1K,EACrC,CACA,SAAS2L,EAAgB3L,GACvBgK,EAAelQ,QAAQgR,WAAW9K,EACpC,CAkBA,OAdAe,SAAS6K,iBAAiB,YAAaF,EAAkB,CACvDG,SAAS,IAEX9K,SAAS6K,iBAAiB,WAAYD,EAAiB,CACrDE,SAAS,IAIXzT,EAAI0B,QAAQ8R,iBAAiB,cApB7B,SAA2B5L,GACzBgK,EAAelQ,QAAQmQ,aAAajK,EACtC,GAkB8D,CAC5D6L,SAAS,IAEXzT,EAAI0B,QAAQ8R,iBAAiB,SAd7B,SAAsB5L,GACpBgK,EAAelQ,QAAQwR,QAAQtL,EACjC,GAYoD,CAClD6L,SAAS,IAEJ,WACL9K,SAAS+K,oBAAoB,YAAaJ,GAC1C3K,SAAS+K,oBAAoB,WAAYH,EAC3C,CACF,GAAG,GACL,CQ4BEI,CAAajH,GAAgB,SAAU8F,EAASC,GAC9C,SAASmB,EAAOC,EAAU3L,GACxB2L,GAAS,SAAUxD,GAEjB,OADeD,GAAaC,EAAQnI,EAEtC,GACF,CAGA,QAAK4H,KAGDhE,EACF8H,EAAOzG,GAAkBqF,GAEzBoB,EAAOrG,GAAiBkF,GAE1BzB,KACAH,MACO,EACT,KACA,IAAAlP,YAAU,WAOR,OANAqP,KACIL,KACFH,GAAe9O,QAAUoS,YAAW,WAClClD,GAAiB,EACnB,GAAG,MAEEI,EACT,GAAG,CAACL,KAIJ,IAAIoD,GN/MS,SAAyBlF,EAAYmB,EAAwBjO,EAAW4N,EAAqBqE,EAAkBC,EAAwB9M,GACpJ,IAGI+M,EACA3O,EACA4O,EALAvO,EAAOuB,EAAKvB,KACdsG,EAAc/E,EAAK+E,YACnB1L,EAAM2G,EAAK3G,IAab,MATI,CAAC,MAAO,UAAUwJ,SAASkC,IAC7BgI,EAAW,QACX3O,EAAW/E,EAAM,QAAU,OAC3B2T,EAAgBrR,KAAK8I,IAAI7J,KAEzBmS,EAAW,SACX3O,EAAW,MACX4O,GAAiBpS,IAEZ,IAAAgN,UAAQ,WACb,IAAKnJ,EAAK4C,OACR,MAAO,CAAC,EAAG,GAIb,IAFA,IAAID,EAAM3C,EAAK4C,OACX4L,EAAW7L,EACNE,EAAI,EAAGA,EAAIF,EAAKE,GAAK,EAAG,CAC/B,IAAIP,EAAS2G,EAAWK,IAAItJ,EAAK6C,GAAG3G,MAAQ,EAC5C,GAAIgB,KAAKiN,MAAM7H,EAAO3C,GAAY2C,EAAOgM,IAAapR,KAAKiN,MAAMoE,EAAgBnE,GAAyB,CACxGoE,EAAW3L,EAAI,EACf,KACF,CACF,CAEA,IADA,IAAI4L,EAAa,EACRC,EAAK/L,EAAM,EAAG+L,GAAM,EAAGA,GAAM,EAEpC,IADczF,EAAWK,IAAItJ,EAAK0O,GAAIxS,MAAQ,GAClCyD,GAAY4O,EAAe,CACrCE,EAAaC,EAAK,EAClB,KACF,CAEF,OAAOD,GAAcD,EAAW,CAAC,EAAG,GAAK,CAACC,EAAYD,EACxD,GAAG,CAACvF,EAAYmB,EAAwBL,EAAqBqE,EAAkBC,EAAwBE,EAAejI,EAAatG,EAAK4B,KAAI,SAAUC,GACpJ,OAAOA,EAAI3F,GACb,IAAG0N,KAAK,KAAMhP,GAChB,CMsKyB+T,CAAgB1F,GAErCmB,GAEAlE,EAAyBoB,GAAgBI,GAEzCqC,GAEAC,GAEAC,IAAoB,QAAc,OAAc,CAAC,EAAG9P,GAAQ,CAAC,EAAG,CAC9D6F,KAAMA,KAER4O,IAAoB,OAAeT,GAAkB,GACrDU,GAAeD,GAAkB,GACjCE,GAAaF,GAAkB,GAG7BG,IAAc,EAAAC,EAAA,IAAS,WACzB,IAAI9S,EAAM+S,UAAUrM,OAAS,QAAsB/D,IAAjBoQ,UAAU,GAAmBA,UAAU,GAAK5I,EAC1E6I,EAAYjG,GAAWK,IAAIpN,IAAQ,CACrCD,MAAO,EACPG,OAAQ,EACRI,KAAM,EACNoB,MAAO,EACPvB,IAAK,GAEP,GAAI6J,EAAwB,CAE1B,IAAIiJ,EAAe7H,GAGf1M,EACEsU,EAAUtR,MAAQ0J,GACpB6H,EAAeD,EAAUtR,MAChBsR,EAAUtR,MAAQsR,EAAUjT,MAAQqL,GAAgB8C,KAC7D+E,EAAeD,EAAUtR,MAAQsR,EAAUjT,MAAQmO,IAI9C8E,EAAU1S,MAAQ8K,GACzB6H,GAAgBD,EAAU1S,KACjB0S,EAAU1S,KAAO0S,EAAUjT,OAASqL,GAAgB8C,KAC7D+E,IAAiBD,EAAU1S,KAAO0S,EAAUjT,MAAQmO,KAEtDzC,GAAgB,GAChBJ,GAAiBiD,GAAa2E,GAChC,KAAO,CAEL,IAAIC,EAAgB1H,GAChBwH,EAAU7S,KAAOqL,GACnB0H,GAAiBF,EAAU7S,IAClB6S,EAAU7S,IAAM6S,EAAU9S,QAAUsL,GAAe0C,KAC5DgF,IAAkBF,EAAU7S,IAAM6S,EAAU9S,OAASgO,KAEvD7C,GAAiB,GACjBI,GAAgB6C,GAAa4E,GAC/B,CACF,IAGIC,IAAc,IAAAlU,YAChBmU,IAAc,OAAeD,GAAa,GAC1CE,GAAWD,GAAY,GACvBE,GAAcF,GAAY,GACxBG,IAAc,IAAAtU,WAAS,GACzBuU,IAAc,OAAeD,GAAa,GAC1CE,GAAUD,GAAY,GACtBE,GAAaF,GAAY,GACvBnN,GAAcvC,EAAKwC,QAAO,SAAUX,GACtC,OAAQA,EAAIjD,QACd,IAAGgD,KAAI,SAAUC,GACf,OAAOA,EAAI3F,GACb,IACIoP,GAAW,SAAkBhJ,GAC/B,IAAIuN,EAAetN,GAAYuN,QAAQP,IAAYlJ,GAC/C1D,EAAMJ,GAAYK,OAElBmN,EAASxN,IADIsN,EAAevN,EAASK,GAAOA,GAEhD6M,GAAYO,EACd,EACIC,GAAgB,SAAuBhO,GACzC,IAAIiO,EAAOjO,EAAEiO,KACTC,EAAQtV,GAAOsL,EACfiK,EAAkB5N,GAAY,GAC9B6N,EAAiB7N,GAAYA,GAAYK,OAAS,GACtD,OAAQqN,GAEN,IAAK,YAEG/J,GACFoF,GAAS4E,EAAQ,GAAK,GAExB,MAIJ,IAAK,aAEGhK,GACFoF,GAAS4E,GAAS,EAAI,GAExB,MAIJ,IAAK,UAEDlO,EAAEE,iBACGgE,GACHoF,IAAU,GAEZ,MAIJ,IAAK,YAEDtJ,EAAEE,iBACGgE,GACHoF,GAAS,GAEX,MAIJ,IAAK,OAEDtJ,EAAEE,iBACFsN,GAAYW,GACZ,MAIJ,IAAK,MAEDnO,EAAEE,iBACFsN,GAAYY,GACZ,MAIJ,IAAK,QACL,IAAK,QAEDpO,EAAEE,iBACF3B,EAAWgP,GAAUvN,GACrB,MAGJ,IAAK,YACL,IAAK,SAED,IAAIqO,EAAc9N,GAAYuN,QAAQP,IAClCe,EAAYtQ,EAAKuQ,MAAK,SAAU1O,GAClC,OAAOA,EAAI3F,MAAQqT,EACrB,IACgB/Q,EAAa8R,aAA6C,EAASA,EAAU7R,SAAU6R,aAA6C,EAASA,EAAU5R,UAAWC,EAAU2R,aAA6C,EAASA,EAAU1R,YAE1PoD,EAAEE,iBACFF,EAAEC,kBACFtD,EAASa,OAAO,SAAU,CACxBtD,IAAKqT,GACLhQ,MAAOyC,IAGLqO,IAAgB9N,GAAYK,OAAS,EACvC0I,IAAU,GAEVA,GAAS,IAMrB,EAGIkF,GAAe,CAAC,EAChBtK,EACFsK,GAAa5V,EAAM,cAAgB,cAAgByF,EAEnDmQ,GAAaC,UAAYpQ,EAE3B,IAAIqQ,GAAW1Q,EAAK4B,KAAI,SAAUC,EAAKgB,GACrC,IAAI3G,EAAM2F,EAAI3F,IACd,OAAoB,gBAAoB,EAAS,CAC/C6D,GAAIA,EACJhB,UAAWA,EACX7C,IAAKA,EACL2F,IAAKA,EAELvF,MAAa,IAANuG,OAAUhE,EAAY2R,GAC7B/R,SAAUoD,EAAIpD,SACdE,SAAUA,EACV4F,OAAQrI,IAAQmK,EAChB7B,MAAOtI,IAAQqT,GACf7K,cAAe6B,EACfjG,gBAAiBtB,aAAuC,EAASA,EAAOsB,gBACxEyE,SAAUxC,GAAYK,OACtBoC,gBAAiBnC,EAAI,EACrBvD,QAAS,SAAiB0C,GACxBzB,EAAWrE,EAAK8F,EAClB,EACA4B,UAAWoM,GACXrL,QAAS,WACFgL,IACHH,GAAYtT,GAEd6S,GAAY7S,GACZ+O,KACKnE,EAAehL,UAIflB,IACHkM,EAAehL,QAAQ6U,WAAa,GAEtC7J,EAAehL,QAAQ8U,UAAY,EACrC,EACAhM,OAAQ,WACN4K,QAAY3Q,EACd,EACAgG,YAAa,WACX+K,IAAW,EACb,EACA9K,UAAW,WACT8K,IAAW,EACb,GAEJ,IAGIiB,GAAiB,WACnB,OAAO7H,IAAY,WACjB,IAAI8H,EACAC,EAAW,IAAI/S,IACfgT,EAA0D,QAA9CF,EAAsB/J,EAAWjL,eAA6C,IAAxBgV,OAAiC,EAASA,EAAoB/K,wBAoBpI,OAnBA/F,EAAK/B,SAAQ,SAAUgT,GACrB,IAAIC,EACAhV,EAAM+U,EAAM/U,IACZiV,EAA0D,QAA/CD,EAAuBnK,EAAWjL,eAA8C,IAAzBoV,OAAkC,EAASA,EAAqBE,cAAc,mBAAoBhS,OAAOf,EAAenC,GAAM,OACpM,GAAIiV,EAAS,CACX,IAAIE,EAlbG,SAAoBxP,EAAKyP,GAExC,IAAI3L,EAAc9D,EAAI8D,YACpBE,EAAehE,EAAIgE,aACnB0L,EAAY1P,EAAI0P,UAChBC,EAAa3P,EAAI2P,WACfC,EAAwB5P,EAAIkE,wBAC9B9J,EAAQwV,EAAsBxV,MAC9BG,EAASqV,EAAsBrV,OAC/BI,EAAOiV,EAAsBjV,KAC7BH,EAAMoV,EAAsBpV,IAG9B,OAAIa,KAAK8I,IAAI/J,EAAQ0J,GAAe,EAC3B,CAAC1J,EAAOG,EAAQI,EAAO8U,EAAc9U,KAAMH,EAAMiV,EAAcjV,KAEjE,CAACsJ,EAAaE,EAAc2L,EAAYD,EACjD,CAia4BG,CAAWP,EAASH,GACpCW,GAAe,OAAeN,EAAa,GAC3CpV,EAAQ0V,EAAa,GACrBvV,EAASuV,EAAa,GACtBnV,EAAOmV,EAAa,GACpBtV,EAAMsV,EAAa,GACrBZ,EAASpH,IAAIzN,EAAK,CAChBD,MAAOA,EACPG,OAAQA,EACRI,KAAMA,EACNH,IAAKA,GAET,CACF,IACO0U,CACT,GACF,GACA,IAAAhV,YAAU,WACR8U,IACF,GAAG,CAAC7Q,EAAK4B,KAAI,SAAUC,GACrB,OAAOA,EAAI3F,GACb,IAAG0N,KAAK,OACR,IAAIgI,GAAqBxU,GAAU,WAEjC,IAAIyU,EAAgBrM,EAAQmB,GACxBmL,EAAgBtM,EAAQoB,GACxBmL,EAAiBvM,EAAQqB,GAC7BgB,GAA6B,CAACgK,EAAc,GAAKC,EAAc,GAAKC,EAAe,GAAIF,EAAc,GAAKC,EAAc,GAAKC,EAAe,KAC5I,IAAIC,EAAaxM,EAAQyB,GACzBkB,GAAW6J,GACX,IAAIC,EAAmBzM,EAAQwB,GAC/BuB,GAAiB0J,GAGjB,IAAIC,EAAqB1M,EAAQuB,GACjCgB,GAAkB,CAACmK,EAAmB,GAAKF,EAAW,GAAIE,EAAmB,GAAKF,EAAW,KAG7FnB,IACF,IAGIsB,GAAkBnS,EAAKoS,MAAM,EAAGvD,IAChCwD,GAAgBrS,EAAKoS,MAAMtD,GAAa,GACxCwD,GAAa,GAAGlT,QAAO,OAAmB+S,KAAkB,OAAmBE,KAG/E3X,GAAkBuO,GAAWK,IAAIjD,GAOnCkM,GANkB,EAAa,CAC7B7X,gBAAiBA,GACjBC,WAAYuL,EACZpL,UAAWA,EACXF,IAAKA,IAEwB0B,OAGjC,IAAAP,YAAU,WACRgT,IACF,GAAG,CAAC1I,EAAWiE,GAAcC,GAAc1M,EAAUnD,IAAkBmD,EAAUoL,IAAa/C,KAG9F,IAAAnK,YAAU,WACR6V,IAEF,GAAG,CAAChX,IAGJ,IAEI4X,GACAC,GACAC,GACAC,GALAC,KAAgBN,GAAW1P,OAC3BiQ,GAAa,GAAGzT,OAAOL,EAAW,aAiBtC,OAZImH,EACEtL,GACF6X,GAAYnL,GAAgB,EAC5BkL,GAAWlL,KAAkBiD,KAE7BiI,GAAWlL,GAAgB,EAC3BmL,GAAYnL,KAAkBgD,KAGhCoI,GAAUhL,GAAe,EACzBiL,GAAajL,KAAiB4C,IAEZ,gBAAoB,IAAgB,CACtDwI,SAAUlB,IACI,gBAAoB,MAAO,CACzCxX,KAAK,QAAcA,EAAKuM,GACxBjF,KAAM,UACN,mBAAoBwE,EAAyB,aAAe,WAC5D/G,UAAW,IAAW,GAAGC,OAAOL,EAAW,QAASI,GACpD7C,MAAOA,EACPsH,UAAW,WAETqH,IACF,GACc,gBAAoB,EAAc,CAChD7Q,IAAKwM,EACLjH,SAAU,OACVC,MAAOA,EACPb,UAAWA,IACI,gBAAoB,IAAgB,CACnD+T,SAAUlB,IACI,gBAAoB,MAAO,CACzCzS,UAAW,IAAW0T,IAAY,QAAgB,QAAgB,QAAgB,OAAgB,CAAC,EAAG,GAAGzT,OAAOyT,GAAY,cAAeL,IAAW,GAAGpT,OAAOyT,GAAY,eAAgBJ,IAAY,GAAGrT,OAAOyT,GAAY,aAAcH,IAAU,GAAGtT,OAAOyT,GAAY,gBAAiBF,KAC7RvY,IAAK0M,GACS,gBAAoB,IAAgB,CAClDgM,SAAUlB,IACI,gBAAoB,MAAO,CACzCxX,IAAK2M,EACL5H,UAAW,GAAGC,OAAOL,EAAW,aAChCzC,MAAO,CACLH,UAAW,aAAaiD,OAAOkI,GAAe,QAAQlI,OAAOsI,GAAc,OAC3EqL,WAAYhI,GAAgB,YAASlM,IAEtC6R,GAAuB,gBAAoB,EAAW,CACvDtW,IAAK6M,EACLlI,UAAWA,EACXC,OAAQA,EACRL,SAAUA,EACVrC,OAAO,QAAc,OAAc,CAAC,EAAuB,IAApBoU,GAAS9N,YAAe/D,EAAY2R,IAAe,CAAC,EAAG,CAC5FrN,WAAYyP,GAAc,SAAW,SAExB,gBAAoB,MAAO,CAC1CzT,UAAW,IAAW,GAAGC,OAAOL,EAAW,aAAa,OAAgB,CAAC,EAAG,GAAGK,OAAOL,EAAW,qBAAsBqH,EAAS4M,SAChI1W,MAAOiW,SACY,gBAAoB,GAAe,OAAS,CAAC,EAAGpY,EAAO,CAC1EmG,gBAAiBtB,aAAuC,EAASA,EAAOsB,gBACxElG,IAAK4M,EACLjI,UAAWA,EACXiB,KAAMsS,GACNnT,WAAYyT,IAAevI,GAC3B4I,YAAalI,MACG,gBAAoB,EAAc,CAClD3Q,IAAKyM,EACLlH,SAAU,QACVC,MAAOA,EACPb,UAAWA,KAGf,IACA,ICvlBImU,EAAuB,cAAiB,SAAU/Y,EAAOC,GAC3D,IAAI2E,EAAY5E,EAAM4E,UACpBI,EAAYhF,EAAMgF,UAClB7C,EAAQnC,EAAMmC,MACdyD,EAAK5F,EAAM4F,GACXwE,EAASpK,EAAMoK,OACf4O,EAAShZ,EAAMgZ,OACf5M,EAAWpM,EAAMoM,SACnB,OAAoB,gBAAoB,MAAO,CAC7CxG,GAAIA,GAAM,GAAGX,OAAOW,EAAI,WAAWX,OAAO+T,GAC1CzR,KAAM,WACND,SAAU8C,EAAS,GAAK,EACxB,kBAAmBxE,GAAM,GAAGX,OAAOW,EAAI,SAASX,OAAO+T,GACvD,eAAgB5O,EAChBjI,MAAOA,EACP6C,UAAW,IAAWJ,EAAWwF,GAAU,GAAGnF,OAAOL,EAAW,WAAYI,GAC5E/E,IAAKA,GACJmM,EACL,IAIA,QCrBI6M,EAAY,CAAC,gBACfC,EAAa,CAAC,QAAS,OAkCzB,MA1BwB,SAA2B9R,GACjD,IAAI+R,EAAe/R,EAAK+R,aACtBC,GAAY,OAAyBhS,EAAM6R,GAE3CpT,EADsB,aAAiB0G,GACd1G,KAC3B,OAAIsT,EAcKA,GAbc,QAAc,OAAc,CAAC,EAAGC,GAAY,CAAC,EAAG,CAEnEC,MAAOxT,EAAK4B,KAAI,SAAUqP,GACxB,IAAInP,EAAQmP,EAAMnP,MAChB5F,EAAM+U,EAAM/U,IACZuX,GAAe,OAAyBxC,EAAOoC,GACjD,OAAoB,gBAAoB,GAAS,OAAS,CACxDxR,IAAKC,EACL5F,IAAKA,EACLiX,OAAQjX,GACPuX,GACL,MAEkC,GAElB,gBAAoB,EAAYF,EACtD,E,UC9BI,GAAY,CAAC,MAAO,cAAe,QAAS,YAAa,0BAmD7D,GA7CmB,SAAsBpZ,GACvC,IAAI4F,EAAK5F,EAAM4F,GACbsG,EAAYlM,EAAMkM,UAClBD,EAAWjM,EAAMiM,SACjBE,EAAcnM,EAAMmM,YACpBoN,EAAyBvZ,EAAMuZ,uBAC7BjN,EAAoB,aAAiBC,GACvC3H,EAAY0H,EAAkB1H,UAC9BiB,EAAOyG,EAAkBzG,KACvB2T,EAAkBvN,EAASwN,QAC3BC,EAAmB,GAAGzU,OAAOL,EAAW,YAC5C,OAAoB,gBAAoB,MAAO,CAC7CI,UAAW,IAAW,GAAGC,OAAOL,EAAW,qBAC7B,gBAAoB,MAAO,CACzCI,UAAW,IAAW,GAAGC,OAAOL,EAAW,YAAa,GAAGK,OAAOL,EAAW,aAAaK,OAAOkH,IAAc,OAAgB,CAAC,EAAG,GAAGlH,OAAOL,EAAW,qBAAsB4U,KAC7K3T,EAAK4B,KAAI,SAAUkS,GACpB,IAAI5X,EAAM4X,EAAK5X,IACb6X,EAAcD,EAAKC,YACnBC,EAAYF,EAAKxX,MACjB2X,EAAgBH,EAAK3U,UACrB+U,EAA6BJ,EAAKJ,uBAClCD,GAAe,OAAyBK,EAAM,IAC5CvP,EAASrI,IAAQmK,EACrB,OAAoB,gBAAoB,OAAW,OAAS,CAC1DnK,IAAKA,EACLsH,QAASe,EACTwP,YAAaA,EACbI,iBAAkBT,IAA0BQ,GAC5CE,gBAAiB,GAAGhV,OAAOyU,EAAkB,YAC5CzN,EAASiO,gBAAgB,SAAU9S,EAAMnH,GAC1C,IAAIka,EAAc/S,EAAKjF,MACrBiY,EAAkBhT,EAAKpC,UACzB,OAAoB,gBAAoB,GAAS,OAAS,CAAC,EAAGsU,EAAc,CAC1E1U,UAAW8U,EACX9T,GAAIA,EACJoT,OAAQjX,EACRkK,SAAUuN,EACVpP,OAAQA,EACRjI,OAAO,QAAc,OAAc,CAAC,EAAG0X,GAAYM,GACnDnV,UAAW,IAAW8U,EAAeM,GACrCna,IAAKA,IAET,GACF,KACF,E,QChDA,IAAI,GAAY,CAAC,KAAM,YAAa,YAAa,QAAS,YAAa,YAAa,mBAAoB,WAAY,WAAY,cAAe,eAAgB,cAAe,qBAAsB,SAAU,OAAQ,yBAA0B,eAAgB,WAAY,aAAc,cAAe,oBAAqB,iBAAkB,aAsB5Uoa,GAAO,EACPC,GAAoB,cAAiB,SAAUta,EAAOC,GACxD,IAAI2F,EAAK5F,EAAM4F,GACb2U,EAAmBva,EAAM4E,UACzBA,OAAiC,IAArB2V,EAA8B,UAAYA,EACtDvV,EAAYhF,EAAMgF,UAClBwV,EAAQxa,EAAMwa,MACdvN,EAAYjN,EAAMiN,UAClBf,EAAYlM,EAAMkM,UAClBuO,EAAmBza,EAAMya,iBACzBjW,EAAWxE,EAAMwE,SACjByH,EAAWjM,EAAMiM,SACjByO,EAAqB1a,EAAMmM,YAC3BA,OAAqC,IAAvBuO,EAAgC,MAAQA,EACtDxU,EAAelG,EAAMkG,aACrByU,EAAc3a,EAAM2a,YACpBC,EAAqB5a,EAAM4a,mBAC3B/V,EAAS7E,EAAM6E,OACfmB,EAAOhG,EAAMgG,KACbuT,EAAyBvZ,EAAMuZ,uBAC/BJ,EAAenZ,EAAMmZ,aACrB3W,EAAWxC,EAAMwC,SACjB4D,EAAapG,EAAMoG,WACnBiG,EAAcrM,EAAMqM,YACpBhG,EAAoBrG,EAAMqG,kBAC1BC,EAAiBtG,EAAMsG,eACvB3F,EAAYX,EAAMW,UAClByY,GAAY,OAAyBpZ,EAAO,IAC1C6F,EAAO,WAAc,WACvB,OAAQ2U,GAAS,IAAInS,QAAO,SAAUsR,GACpC,OAAOA,GAA0B,YAAlB,OAAQA,IAAsB,QAASA,CACxD,GACF,GAAG,CAACa,IACA/Z,EAAoB,QAAdwM,EACN4N,EC3DS,WACb,IAIIA,EAJA5O,EAAW6I,UAAUrM,OAAS,QAAsB/D,IAAjBoQ,UAAU,GAAmBA,UAAU,GAAK,CACjF+D,QAAQ,EACRY,SAAS,GA6BX,OAzBEoB,GADe,IAAb5O,EACe,CACf4M,QAAQ,EACRY,SAAS,IAEW,IAAbxN,EACQ,CACf4M,QAAQ,EACRY,SAAS,IAGM,OAAc,CAC7BZ,QAAQ,GACe,YAAtB,OAAQ5M,GAAyBA,EAAW,CAAC,IAI/BiO,oBAA4CxV,IAA3BmW,EAAepB,UACjDoB,EAAepB,SAAU,IAEtBoB,EAAeX,eAAiBW,EAAepB,UAIlDoB,EAAepB,SAAU,GAEpBoB,CACT,CD0BuBC,CAAiB7O,GAGlClL,GAAY,IAAAC,WAAS,GACvBC,GAAa,OAAeF,EAAW,GACvC+E,EAAS7E,EAAW,GACpB8Z,EAAY9Z,EAAW,IACzB,IAAAW,YAAU,WAERmZ,GAAU,EAAAC,EAAA,KACZ,GAAG,IAGH,IAAIC,GAAkB,EAAAC,EAAA,IAAe,WACjC,IAAIjM,EACJ,OAA8B,QAAtBA,EAASpJ,EAAK,UAA2B,IAAXoJ,OAAoB,EAASA,EAAOlN,GAC5E,GAAG,CACDuO,MAAOpE,EACPiP,aAAcV,IAEhBW,GAAmB,OAAeH,EAAiB,GACnDI,EAAkBD,EAAiB,GACnCE,EAAqBF,EAAiB,GACpC3U,GAAa,IAAAzF,WAAS,WACtB,OAAO6E,EAAK0C,WAAU,SAAUb,GAC9B,OAAOA,EAAI3F,MAAQsZ,CACrB,GACF,IACA3U,GAAa,OAAeD,EAAY,GACxC8U,EAAc7U,EAAW,GACzB8U,GAAiB9U,EAAW,IAG9B,IAAA9E,YAAU,WACR,IAIM6Z,EAJFC,EAAiB7V,EAAK0C,WAAU,SAAUb,GAC5C,OAAOA,EAAI3F,MAAQsZ,CACrB,KACwB,IAApBK,IAEFA,EAAiB3Y,KAAKwN,IAAI,EAAGxN,KAAKyN,IAAI+K,EAAa1V,EAAK4C,OAAS,IACjE6S,EAAqE,QAAjDG,EAAuB5V,EAAK6V,UAAsD,IAAzBD,OAAkC,EAASA,EAAqB1Z,MAE/IyZ,GAAeE,EACjB,GAAG,CAAC7V,EAAK4B,KAAI,SAAUC,GACrB,OAAOA,EAAI3F,GACb,IAAG0N,KAAK,KAAM4L,EAAiBE,IAG/B,IAAII,IAAmB,EAAAT,EAAA,GAAe,KAAM,CACxC5K,MAAO1K,IAETgW,IAAmB,OAAeD,GAAkB,GACpDE,GAAWD,GAAiB,GAC5BE,GAAcF,GAAiB,IAGjC,IAAAha,YAAU,WACHgE,IACHkW,GAAY,WAAW7W,OAAkDoV,KACzEA,IAAQ,EAEZ,GAAG,IAaH,IAAI0B,GAAc,CAChBnW,GAAIiW,GACJ3P,UAAWmP,EACXpP,SAAU4O,EACV1O,YAAaA,EACb1L,IAAKA,EACLqF,OAAQA,GAENkW,IAAiB,QAAc,OAAc,CAAC,EAAGD,IAAc,CAAC,EAAG,CACrEvX,SAAUA,EACVK,OAAQA,EACRmB,KAAMA,EACNE,aAAcA,EACdE,WAvBF,SAA4BrE,EAAK8F,GAC/BzB,SAAgDA,EAAWrE,EAAK8F,GAChE,IAAIoU,EAAkBla,IAAQsZ,EAC9BC,EAAmBvZ,GACfka,IACFzZ,SAA4CA,EAAST,GAEzD,EAiBEsK,YAAaA,EACb5G,MAAOmV,EACPzY,MAAOwY,EACPtB,MAAO,KACPhT,kBAAmBA,EACnBC,eAAgBA,EAChB3F,UAAWA,IAEb,OAAoB,gBAAoB4L,EAAW2P,SAAU,CAC3D5L,MAAO,CACLzK,KAAMA,EACNjB,UAAWA,IAEC,gBAAoB,OAAO,OAAS,CAClD3E,IAAKA,EACL2F,GAAIA,EACJZ,UAAW,IAAWJ,EAAW,GAAGK,OAAOL,EAAW,KAAKK,OAAOkH,IAAc,QAAgB,QAAgB,OAAgB,CAAC,EAAG,GAAGlH,OAAOL,EAAW,WAAYkB,GAAS,GAAGb,OAAOL,EAAW,aAAcJ,GAAW,GAAGS,OAAOL,EAAW,QAASnE,GAAMuE,IAC/PoU,GAAyB,gBAAoB,GAAmB,OAAS,CAAC,EAAG4C,GAAgB,CAC9F7C,aAAcA,KACE,gBAAoB,IAAc,OAAS,CAC3DI,uBAAwBA,GACvBwC,GAAa,CACd9P,SAAU4O,MAEd,IAIA,IEjLA,GFiLA,G,0CGjLA,MAAM,GAAS,CACbsB,cAAc,EACdC,aAAa,EACbC,aAAa,G,eCJXC,GAAgC,SAAUC,EAAG1U,GAC/C,IAAI2U,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOG,OAAOC,UAAUC,eAAepZ,KAAK+Y,EAAGE,IAAM5U,EAAE8N,QAAQ8G,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCG,OAAOG,sBAA2C,KAAInU,EAAI,EAAb,IAAgB+T,EAAIC,OAAOG,sBAAsBN,GAAI7T,EAAI+T,EAAEhU,OAAQC,IAClIb,EAAE8N,QAAQ8G,EAAE/T,IAAM,GAAKgU,OAAOC,UAAUG,qBAAqBtZ,KAAK+Y,EAAGE,EAAE/T,MAAK8T,EAAEC,EAAE/T,IAAM6T,EAAEE,EAAE/T,IADuB,CAGvH,OAAO8T,CACT,E,2DC8BA,OApCuBO,IACrB,MAAM,aACJC,EAAY,mBACZC,GACEF,EACJ,MAAO,CAAC,CACN,CAACC,GAAe,CACd,CAAC,GAAGA,YAAwB,CAC1B,oBAAqB,CACnBpE,WAAY,OACZ,UAAW,CACTxN,QAAS,GAEX,WAAY,CACVA,QAAS,EACTwN,WAAY,WAAWqE,MAG3B,UAAW,CACTzX,SAAU,WACVoT,WAAY,OACZsE,MAAO,EACP,UAAW,CACT9R,QAAS,GAEX,WAAY,CACVA,QAAS,EACTwN,WAAY,WAAWqE,SAOjC,EAAC,SAAgBF,EAAO,aAAa,SAAgBA,EAAO,eAAe,EC/B7E,MAAMI,GAAeJ,IACnB,MAAM,aACJC,EAAY,gBACZI,EAAe,OACfC,EAAM,WACNC,EAAU,qBACVC,EAAoB,kBACpBC,GACET,EACJ,MAAO,CACL,CAAC,GAAGC,UAAsB,CACxB,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,SAAqB,CACvBS,OAAQ,EACRC,QAASN,EACTO,WAAYN,EACZO,OAAQ,IAAG,SAAKb,EAAMc,cAAcd,EAAMe,YAAYP,IACtD3E,WAAY,OAAOmE,EAAME,sBAAsBF,EAAMgB,mBAEvD,CAAC,GAAGf,gBAA4B,CAC9BgB,MAAOR,EACPG,WAAYZ,EAAMkB,kBAEpB,CAAC,GAAGjB,eAA2BN,OAAOwB,OAAO,CAAC,GAAG,SAAgBnB,GAAQ,IACzE,CAAC,GAAGC,aAAyB,CAC3BhU,WAAY,UAEd,CAAC,KAAKgU,QAAmBA,eAA0BA,aAAyB,CAC1EmB,QAAS,SAIb,CAAC,IAAInB,WAAsBA,YAAwB,CACjD,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,WAAsBA,SAAqB,CAC7CoB,WAAY,CACVC,cAAc,EACd/N,OAAO,SAAKgN,OAKpB,CAAC,IAAIN,SAAqB,CACxB,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,SAAqB,CACvBsB,aAAc,IAAG,SAAKvB,EAAMwB,oBAAmB,SAAKxB,EAAMwB,uBAE5D,CAAC,GAAGvB,gBAA4B,CAC9BwB,kBAAmBzB,EAAMkB,oBAI/B,CAAC,IAAIjB,YAAwB,CAC3B,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,SAAqB,CACvBsB,aAAc,QAAO,SAAKvB,EAAMwB,oBAAmB,SAAKxB,EAAMwB,mBAEhE,CAAC,GAAGvB,gBAA4B,CAC9ByB,eAAgB1B,EAAMkB,oBAK5B,CAAC,IAAIjB,YAAuBA,WAAuB,CACjD,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,WAAsBA,SAAqB,CAC7C1G,WAAW,SAAKgH,MAItB,CAAC,IAAIN,UAAsB,CACzB,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,SAAqB,CACvBsB,aAAc,CACZD,cAAc,EACd/N,MAAO,IAAG,SAAKyM,EAAMwB,wBAAuB,SAAKxB,EAAMwB,oBAG3D,CAAC,GAAGvB,gBAA4B,CAC9B0B,iBAAkB,CAChBL,cAAc,EACd/N,MAAOyM,EAAMkB,qBAKrB,CAAC,IAAIjB,WAAuB,CAC1B,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,SAAqB,CACvBsB,aAAc,CACZD,cAAc,EACd/N,MAAO,MAAK,SAAKyM,EAAMwB,oBAAmB,SAAKxB,EAAMwB,sBAGzD,CAAC,GAAGvB,gBAA4B,CAC9B2B,gBAAiB,CACfN,cAAc,EACd/N,MAAOyM,EAAMkB,sBAMxB,EAEGW,GAAmB7B,IACvB,MAAM,aACJC,EAAY,eACZ6B,EAAc,iCACdC,GACE/B,EACJ,MAAO,CACL,CAAC,GAAGC,cAA0BN,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,GAAG,SAAenB,IAAS,CACpFvX,SAAU,WACVtD,KAAM,KACNG,KAAM,CACJgc,cAAc,EACd/N,OAAQ,MAEVyO,OAAQhC,EAAMiC,YACdC,QAAS,QACT,WAAY,CACVA,QAAS,QAEX,CAAC,GAAGjC,mBAA+B,CACjCkC,UAAWnC,EAAMoC,mBACjB1B,OAAQ,EACRC,QAAS,IAAG,SAAKoB,OACjBM,UAAW,SACXC,UAAW,OACXC,UAAW,CACTjB,cAAc,EACd/N,MAAO,QAETiP,cAAe,OACfC,gBAAiBzC,EAAMkB,iBACvBwB,eAAgB,cAChBnB,aAAcvB,EAAMwB,eACpBJ,QAAS,OACTuB,UAAW3C,EAAM4C,mBACjB,SAAUjD,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,EAAG,OAAe,CACvDe,QAAS,OACTW,WAAY,SACZC,SAAU9C,EAAM+C,kBAChBrC,OAAQ,EACRC,QAAS,IAAG,SAAKX,EAAMgD,gBAAe,SAAKhD,EAAMiD,aACjDhC,MAAOjB,EAAMkD,UACbC,WAAY,SACZC,SAAUpD,EAAMoD,SAChBC,WAAYrD,EAAMqD,WAClBC,OAAQ,UACRzH,WAAY,OAAOmE,EAAME,qBACzB,SAAU,CACRqD,KAAM,EACNC,WAAY,UAEd,WAAY,CACVD,KAAM,OACNlC,WAAY,CACVC,cAAc,EACd/N,MAAOyM,EAAMyD,UAEfxC,MAAOjB,EAAM0D,qBACbN,SAAUpD,EAAM2D,WAChB/C,WAAY,cACZC,OAAQ,EACRyC,OAAQ,UACR,UAAW,CACTrC,MAAOa,IAGX,UAAW,CACTlB,WAAYZ,EAAM4D,oBAEpB,aAAc,CACZ,aAAc,CACZ3C,MAAOjB,EAAM6D,kBACbjD,WAAY,cACZ0C,OAAQ,qBAMnB,EAEGQ,GAAmB9D,IACvB,MAAM,aACJC,EAAY,OACZS,EAAM,qBACNF,EAAoB,iBACpBuD,EAAgB,oBAChBC,EAAmB,mBACnBC,EAAkB,KAClBC,GACElE,EACJ,MAAO,CAEL,CAAC,GAAGC,UAAqBA,YAAwB,CAC/CkE,cAAe,SACf,CAAC,KAAKlE,kBAA6BA,SAAqB,CACtDS,OAAQqD,EACR,YAAa,CACXtb,SAAU,WACV/B,MAAO,CACL4a,cAAc,EACd/N,MAAO,GAETjO,KAAM,CACJgc,cAAc,EACd/N,MAAO,GAET6Q,aAAc,IAAG,SAAKpE,EAAMc,cAAcd,EAAMe,YAAYP,IAC5DhY,QAAS,MAEX,CAAC,GAAGyX,aAAyB,CAC3B/a,OAAQ8a,EAAMqE,cACd,aAAc,CACZxI,WAAY,SAASmE,EAAME,4BAA4BF,EAAME,0CACrDF,EAAME,uBAGlB,CAAC,GAAGD,cAA0B,CAC5B,sBAAuB,CACrB9a,IAAK,EACLmf,OAAQ,EACRvf,MAAOib,EAAMuE,eAEf,YAAa,CACXjf,KAAM,CACJgc,cAAc,EACd/N,MAAO,GAEToP,UAAW3C,EAAMwE,2BAEnB,WAAY,CACV9d,MAAO,CACL4a,cAAc,EACd/N,MAAO,GAEToP,UAAW3C,EAAMyE,4BAEnB,CAAC,IAAIxE,gCAA4C,CAC/C5R,QAAS,GAEX,CAAC,IAAI4R,gCAA4C,CAC/C5R,QAAS,MAKjB,CAAC,GAAG4R,SAAqB,CACvB,CAAC,KAAKA,2BACMA,SAAqB,CAC/B,YAAa,CACXqE,OAAQ,GAEV,CAAC,GAAGrE,aAAyB,CAC3BqE,OAAQ,KAId,CAAC,GAAGrE,YAAwB,CAC1B,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD/T,MAAO,EACPqN,UAAWmH,EACXgE,aAAc,EACd,YAAa,CACXvf,IAAK,GAEP,CAAC,GAAG8a,aAAyB,CAC3B9a,IAAK,IAGT,CAAC,KAAK8a,6BAAwCA,oBAAgC,CAC5E/T,MAAO,IAIX,CAAC,GAAG+T,WAAsBA,WAAuB,CAC/C,CAAC,KAAKA,kBAA6BA,SAAqB,CACtDkE,cAAe,SACfrB,SAAUoB,EAAKlE,EAAMuE,eAAeI,IAAI,MAAMC,QAE9C,CAAC,GAAG3E,SAAqB,CACvBU,QAASqD,EACTzB,UAAW,UAEb,CAAC,GAAGtC,WAAsBA,SAAqB,CAC7CS,OAAQuD,GAGV,CAAC,GAAGhE,cAA0B,CAC5BkE,cAAe,SACf,sBAAuB,CACrBzd,MAAO,CACL4a,cAAc,EACd/N,MAAO,GAETjO,KAAM,CACJgc,cAAc,EACd/N,MAAO,GAETrO,OAAQ8a,EAAMuE,eAEhB,YAAa,CACXpf,IAAK,EACLwd,UAAW3C,EAAM6E,0BAEnB,WAAY,CACVP,OAAQ,EACR3B,UAAW3C,EAAM8E,6BAEnB,CAAC,IAAI7E,+BAA2C,CAC9C5R,QAAS,GAEX,CAAC,IAAI4R,iCAA6C,CAChD5R,QAAS,IAIb,CAAC,GAAG4R,aAAyB,CAC3Blb,MAAOib,EAAMqE,cACb,aAAc,CACZxI,WAAY,UAAUmE,EAAME,2BAA2BF,EAAME,uBAGjE,CAAC,GAAGD,eAA0BA,oBAAgC,CAC5DsD,KAAM,WAENY,cAAe,YAIrB,CAAC,GAAGlE,UAAsB,CACxB,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,aAAyB,CAC3BvZ,MAAO,CACL4a,cAAc,EACd/N,MAAO,KAIb,CAAC,KAAK0M,6BAAwCA,oBAAgC,CAC5EoB,WAAY,CACVC,cAAc,EACd/N,OAAO,SAAK2Q,EAAKlE,EAAMc,WAAW6D,KAAK,GAAGC,UAE5CG,WAAY,CACVzD,cAAc,EACd/N,MAAO,IAAG,SAAKyM,EAAMc,cAAcd,EAAMe,YAAYf,EAAMgF,eAE7D,CAAC,KAAK/E,eAA0BA,aAAyB,CACvDgF,YAAa,CACX3D,cAAc,EACd/N,MAAOyM,EAAMkF,cAKrB,CAAC,GAAGjF,WAAuB,CACzB,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD/T,MAAO,EACP,CAAC,GAAG+T,aAAyB,CAC3B3a,KAAM,CACJgc,cAAc,EACd/N,MAAO,KAIb,CAAC,KAAK0M,6BAAwCA,oBAAgC,CAC5E/T,MAAO,EACPiZ,YAAa,CACX7D,cAAc,EACd/N,MAAO2Q,EAAKlE,EAAMc,WAAW6D,KAAK,GAAGC,SAEvCQ,YAAa,CACX9D,cAAc,EACd/N,MAAO,IAAG,SAAKyM,EAAMc,cAAcd,EAAMe,YAAYf,EAAMgF,eAE7D,CAAC,KAAK/E,eAA0BA,aAAyB,CACvDoF,aAAc,CACZ/D,cAAc,EACd/N,MAAOyM,EAAMkF,cAKtB,EAEGI,GAAetF,IACnB,MAAM,aACJC,EAAY,cACZsF,EAAa,cACbC,EAAa,wBACbC,EAAuB,wBACvBC,GACE1F,EACJ,MAAO,CACL,CAACC,GAAe,CACd,UAAW,CACT,CAAC,KAAKA,SAAqB,CACzB,CAAC,GAAGA,SAAqB,CACvBU,QAAS8E,EACTrC,SAAUpD,EAAM2F,mBAItB,UAAW,CACT,CAAC,KAAK1F,SAAqB,CACzB,CAAC,GAAGA,SAAqB,CACvBU,QAAS+E,EACTtC,SAAUpD,EAAM4F,oBAKxB,CAAC,GAAG3F,UAAsB,CACxB,CAAC,IAAIA,WAAuB,CAC1B,CAAC,KAAKA,SAAqB,CACzB,CAAC,GAAGA,SAAqB,CACvBU,QAAS4E,IAGb,CAAC,IAAItF,YAAwB,CAC3B,CAAC,KAAKA,SAAoBA,SAAqB,CAC7CsB,aAAc,QAAO,SAAKvB,EAAMuB,kBAAiB,SAAKvB,EAAMuB,kBAGhE,CAAC,IAAItB,SAAqB,CACxB,CAAC,KAAKA,SAAoBA,SAAqB,CAC7CsB,aAAc,IAAG,SAAKvB,EAAMuB,kBAAiB,SAAKvB,EAAMuB,sBAG5D,CAAC,IAAItB,WAAuB,CAC1B,CAAC,KAAKA,SAAoBA,SAAqB,CAC7CsB,aAAc,CACZD,cAAc,EACd/N,MAAO,MAAK,SAAKyM,EAAMuB,kBAAiB,SAAKvB,EAAMuB,qBAIzD,CAAC,IAAItB,UAAsB,CACzB,CAAC,KAAKA,SAAoBA,SAAqB,CAC7CsB,aAAc,CACZD,cAAc,EACd/N,MAAO,IAAG,SAAKyM,EAAMuB,sBAAqB,SAAKvB,EAAMuB,oBAK7D,CAAC,IAAItB,WAAuB,CAC1B,CAAC,KAAKA,SAAqB,CACzB,CAAC,GAAGA,SAAqB,CACvBU,QAAS6E,MAKlB,EAEGK,GAAc7F,IAClB,MAAM,aACJC,EAAY,gBACZ6F,EAAe,eACfhE,EAAc,QACdiE,EAAO,yBACPC,EAAwB,sBACxBC,EAAqB,kBACrBxF,EAAiB,UACjByF,GACElG,EACEmG,EAAS,GAAGlG,QAClB,MAAO,CACL,CAACkG,GAAS,CACR1d,SAAU,WACV2d,mBAAoB,OACpBC,wBAAyB,cACzBnE,QAAS,cACTW,WAAY,SACZlC,QAASsF,EACT7C,SAAUpD,EAAMsG,cAChB1F,WAAY,cACZC,OAAQ,EACRO,QAAS,OACTkC,OAAQ,UACRrC,MAAOiF,EACP,kBAAmB,CACjB,wCAAyC,CACvCjF,MAAO6E,IAGX,QAAS,CACP1E,QAAS,OACTvF,WAAY,OAAOmE,EAAME,qBACzB,CAAC,GAAGiG,2BAAiC,CACnCI,gBAAiBvG,EAAMyD,WAG3B,WAAY9D,OAAOwB,OAAO,CACxBoC,KAAM,OACN4B,YAAa,CACX7D,cAAc,EACd/N,MAAOyM,EAAMkE,KAAKlE,EAAMwG,WAAW7B,KAAK,GAAGC,SAE7CvD,WAAY,CACVC,cAAc,EACd/N,MAAOyM,EAAMyG,UAEfxF,MAAOjB,EAAM0D,qBACbN,SAAUpD,EAAM2D,WAChB/C,WAAY,cACZC,OAAQ,OACRO,QAAS,OACTkC,OAAQ,UACRzH,WAAY,OAAOmE,EAAME,qBACzB,UAAW,CACTe,MAAOjB,EAAM0G,oBAEd,SAAc1G,IACjB,UAAW,CACTiB,MAAOa,GAET,CAAC,IAAIqE,YAAiBA,SAAe,CACnClF,MAAOR,EACPkG,WAAY3G,EAAM4G,sBAEpB,CAAC,IAAIT,WAAgBA,SAAexG,OAAOwB,OAAO,CAAC,GAAG,SAAgBnB,IACtE,CAAC,IAAImG,cAAoB,CACvBlF,MAAOjB,EAAM6D,kBACbP,OAAQ,eAEV,CAAC,IAAI6C,cAAmBA,WAAgBA,cAAmBlG,YAAwB,CACjF,oBAAqB,CACnBgB,MAAOjB,EAAM6D,oBAGjB,CAAC,KAAKsC,YAAiBJ,KAAY,CACjCrF,OAAQ,GAEV,CAAC,GAAGqF,sBAA6B,CAC/BZ,YAAa,CACX7D,cAAc,EACd/N,MAAOyM,EAAMyD,YAInB,CAAC,GAAG0C,OAAYA,KAAW,CACzBzF,OAAQ,CACNY,cAAc,EACd/N,MAAOyS,IAGZ,EAEGa,GAAc7G,IAClB,MAAM,aACJC,EAAY,4BACZ6G,EAA2B,QAC3Bf,EAAO,WACPxF,EAAU,KACV2D,GACElE,EACE+G,EAAS,GAAG9G,QAClB,MAAO,CACL,CAAC8G,GAAS,CACR7W,UAAW,MACX,CAAC,GAAG+P,SAAqB,CACvB,CAAC,GAAGA,SAAqB,CACvBS,OAAQ,CACNY,cAAc,EACd/N,MAAOuT,GAET,CAAC,GAAG7G,sBAAkC,CACpCoB,WAAY,CACVC,cAAc,EACd/N,MAAO,IAGX,CAACwS,GAAU,CACTZ,YAAa,CACX7D,cAAc,EACd/N,MAAO,GAET8N,WAAY,CACVC,cAAc,EACd/N,OAAO,SAAKyM,EAAMyD,YAGtB,CAAC,GAAGxD,gBAA4B,CAC9BkF,YAAa,CACX7D,cAAc,EACd/N,OAAO,SAAKyM,EAAMyG,WAEpBpF,WAAY,CACVC,cAAc,EACd/N,OAAO,SAAK2Q,EAAKlE,EAAMwG,WAAW7B,KAAK,GAAGC,UAE5C,CAACmB,GAAU,CACTrF,OAAQ,MAKhB,CAAC,IAAIT,UAAsB,CACzB,CAAC,KAAKA,SAAqB,CACzB/T,MAAO,GAET,CAAC,KAAK+T,oBAAgC,CACpC/T,MAAO,IAGX,CAAC,IAAI+T,WAAuB,CAC1B,CAAC,KAAKA,SAAqB,CACzB/T,MAAO,GAET,CAAC,KAAK+T,oBAAgC,CACpC/T,MAAO,IAIX,CAAC,IAAI+T,SAAoBA,WAAsBA,SAAoBA,YAAwB,CACzF,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,WAAsBA,SAAqB,CAC7CkF,YAAa,CACX7D,cAAc,EACd/N,MAAOgN,GAETc,WAAY,CACVC,cAAc,EACd/N,MAAO,OAMjB,CAAC,GAAG0M,kBAA8B,CAChC/P,UAAW,OAEb,CAAC,GAAG+P,eAA2B,CAC7B,CAAC,GAAGA,kBAA8B,CAChCsC,UAAW,CACTjB,cAAc,EACd/N,MAAO,WAId,EAEGyT,GAAehH,IACnB,MAAM,aACJC,EAAY,gBACZI,EAAe,WACf4G,EAAU,WACV1G,EAAU,eACVuB,EAAc,gBACdgE,EAAe,qBACftF,GACER,EACJ,MAAO,CACL,CAACC,GAAeN,OAAOwB,OAAOxB,OAAOwB,OAAOxB,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,GAAG,SAAenB,IAAS,CAClGkC,QAAS,OAET,CAAC,KAAKjC,kBAA6BA,SAAqB,CACtDxX,SAAU,WACVyZ,QAAS,OACTqB,KAAM,OACNV,WAAY,SACZ,CAAC,GAAG5C,cAA0B,CAC5BxX,SAAU,WACVyZ,QAAS,OACTqB,KAAM,OACN2D,UAAW,UACX9Y,SAAU,SACVoV,WAAY,SACZve,UAAW,eAGX,sBAAuB,CACrBwD,SAAU,WACVuZ,OAAQ,EACR3T,QAAS,EACTwN,WAAY,WAAWmE,EAAME,qBAC7B1X,QAAS,KACT2e,cAAe,SAGnB,CAAC,GAAGlH,cAA0B,CAC5BxX,SAAU,WACVyZ,QAAS,OACTrG,WAAY,WAAWmE,EAAME,sBAG/B,CAAC,GAAGD,oBAAgC,CAClCiC,QAAS,OACTgF,UAAW,WAEb,CAAC,GAAGjH,2BAAuC,CACzCxX,SAAU,WACVwD,WAAY,SACZkb,cAAe,QAEjB,CAAC,GAAGlH,cAA0B,CAC5BxX,SAAU,WACVkY,QAASN,EACTO,WAAY,cACZC,OAAQ,EACRI,MAAOjB,EAAMkD,UACb,WAAY,CACVza,SAAU,WACV/B,MAAO,CACL4a,cAAc,EACd/N,MAAO,GAET+Q,OAAQ,EACRhf,KAAM,CACJgc,cAAc,EACd/N,MAAO,GAETrO,OAAQ8a,EAAMkE,KAAKlE,EAAMoH,iBAAiBC,IAAI,GAAGzC,QACjD3f,UAAW,mBACXuD,QAAS,OAGb,CAAC,GAAGyX,aAAyBN,OAAOwB,OAAO,CACzC2B,SAAUmE,EACV5F,WAAY,CACVC,cAAc,EACd/N,MAAOgN,GAETI,SAAS,SAAKX,EAAMsH,WACpB1G,WAAY,cACZC,OAAQ,IAAG,SAAKb,EAAMc,cAAcd,EAAMe,YAAYP,IACtDe,aAAc,IAAG,SAAKvB,EAAMwB,oBAAmB,SAAKxB,EAAMwB,sBAC1DJ,QAAS,OACTkC,OAAQ,UACRrC,MAAOjB,EAAMkD,UACbrH,WAAY,OAAOmE,EAAME,sBAAsBF,EAAMgB,kBACrD,UAAW,CACTC,MAAOa,GAET,wCAAyC,CACvCb,MAAO6E,KAER,SAAc9F,GAAQ,KAE3B,CAAC,GAAGC,mBAA+B,CACjCsD,KAAM,QAGR,CAAC,GAAGtD,aAAyB,CAC3BxX,SAAU,WACVmY,WAAYZ,EAAMuH,YAClBJ,cAAe,UAEftB,GAAY7F,IAAS,CAEvB,CAAC,GAAGC,aAAyB,CAC3BxX,SAAU,WACV1D,MAAO,QAET,CAAC,GAAGkb,oBAAgC,CAClCsD,KAAM,OACNT,SAAU,EACV0E,UAAW,GAEb,CAAC,GAAGvH,aAAyBN,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,GAAG,SAAcnB,IAAS,CAClF,WAAY,CACVkC,QAAS,YAIf,CAAC,GAAGjC,cAA0B,CAC5B,CAAC,KAAKA,kBAA6BA,SAAqB,CACtD,CAAC,GAAGA,cAA0B,CAC5B,CAAC,kBAAkBA,wBAAmCA,cAA0B,CAC9ES,OAAQ,WAKjB,EAmCH,QAAe,SAAc,QAAQV,IACnC,MAAMyH,GAAY,SAAWzH,EAAO,CAElCK,gBAAiBL,EAAM0H,YACvB3F,iCAAkC/B,EAAMgD,WACxC4D,qBAAsB,0BACtBxE,mBAAoB,IACpBW,kBAAmB,IACnBiD,yBAA0B,UAAS,SAAKhG,EAAM2H,wBAC9Cb,4BAA6B,UAAS,SAAK9G,EAAM2H,0BAEnD,MAAO,CAACrC,GAAamC,GAAYZ,GAAYY,GAAY3D,GAAiB2D,GAAY5F,GAAiB4F,GAAYrH,GAAaqH,GAAYT,GAAaS,GAAY,GAAeA,GAAW,IA5C5JzH,IACnC,MAAMiH,EAAajH,EAAMoH,gBACzB,MAAO,CACLnF,YAAajC,EAAM4H,gBAAkB,GACrCtH,OAAQN,EAAM6H,eACdZ,aAEAS,YAAa,IAAIT,EAAajhB,KAAK8hB,MAAM9H,EAAMoD,SAAWpD,EAAMqD,aAAe,EAAIrD,EAAMc,eAAed,EAAMW,YAC9G4E,cAAe,GAAsB,IAAnBvF,EAAMgD,gBAAsBhD,EAAMW,YACpD6E,cAAe,GAAGxF,EAAMsH,eAAetH,EAAMW,aAAgC,IAAnBX,EAAMgD,eAChEsD,cAAetG,EAAMoD,SACrBwC,gBAAiB5F,EAAM+H,WACvBpC,gBAAiB3F,EAAMoD,SACvBmE,YAAavH,EAAMgI,aACnBjE,iBAAkB,OAAO/D,EAAMU,aAC/BiH,qBAAsB,GAGtBM,qBAAsB,GACtBC,wBAAyB,GACzBjC,sBAAuB,GAAGjG,EAAMiD,gBAChCwC,wBAAyB,GAAGzF,EAAMsH,gBAClC5B,wBAAyB,GAAG1F,EAAMW,cAClCqD,oBAAqB,GAAGhE,EAAMsH,eAAetH,EAAMkF,cACnDjB,mBAAoB,GAAGjE,EAAMU,iBAC7BwF,UAAWlG,EAAMkD,UACjBzC,kBAAmBT,EAAMgI,aACzBlG,eAAgB9B,EAAMmI,kBACtBrC,gBAAiB9F,EAAMoI,mBACvB7H,WAAYP,EAAMwG,UAAY,EAC/B,IC7yBH,ICFI,GAAgC,SAAUhH,EAAG1U,GAC/C,IAAI2U,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOG,OAAOC,UAAUC,eAAepZ,KAAK+Y,EAAGE,IAAM5U,EAAE8N,QAAQ8G,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCG,OAAOG,sBAA2C,KAAInU,EAAI,EAAb,IAAgB+T,EAAIC,OAAOG,sBAAsBN,GAAI7T,EAAI+T,EAAEhU,OAAQC,IAClIb,EAAE8N,QAAQ8G,EAAE/T,IAAM,GAAKgU,OAAOC,UAAUG,qBAAqBtZ,KAAK+Y,EAAGE,EAAE/T,MAAK8T,EAAEC,EAAE/T,IAAM6T,EAAEE,EAAE/T,IADuB,CAGvH,OAAO8T,CACT,EAeA,MAAM,GAAOxc,IACX,IAAIolB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAC5C,MAAM,KACF/gB,EAAI,UACJC,EAAS,cACT+gB,EACAnlB,KAAMolB,EAAU,OAChB3gB,EAAM,QACN4gB,EAAO,SACPC,EAAQ,QACR5gB,EAAO,WACP2C,EAAU,SACVnB,EAAQ,KACRd,EAAI,eACJM,EAAc,SACd8F,EAAQ,MACRoO,EAAK,SACLvO,EAAQ,MACR9J,EAAK,cACLgkB,EAAa,UACbxlB,GACEX,EACJomB,EAAa,GAAOpmB,EAAO,CAAC,OAAQ,YAAa,gBAAiB,OAAQ,SAAU,UAAW,WAAY,UAAW,aAAc,WAAY,OAAQ,iBAAkB,WAAY,QAAS,WAAY,QAAS,gBAAiB,eAErO4E,UAAWyhB,GACTD,GACE,UACJnZ,EAAS,KACTpH,EAAI,aACJygB,EAAY,kBACZjgB,GACE,aAAiB,OACfzB,EAAY0hB,EAAa,OAAQD,GACjCE,GAAU,EAAAC,GAAA,GAAa5hB,IACtB6hB,EAAYC,EAAQC,GAAa,GAAS/hB,EAAW2hB,GAC5D,IAAI/hB,EACS,kBAATO,IACFP,EAAW,CACTa,OAAQ,CAACuhB,EAAUxf,KACjB,IAAI,IACFrF,EAAG,MACHqD,GACEgC,EACJ/B,SAAgDA,EAAoB,QAAbuhB,EAAqBxhB,EAAQrD,EAAK6kB,EAAS,EAEpG3e,WAA+I,QAAlImd,EAAKnd,QAA+CA,EAAapC,aAAmC,EAASA,EAAKoC,kBAA+B,IAAPmd,EAAgBA,EAAkB,gBAAoByB,EAAA,EAAe,MAC5NvhB,SAAUA,QAAyCA,EAAUO,aAAmC,EAASA,EAAKP,UAAyB,gBAAoB,EAAc,MACzKR,SAAqB,IAAZmhB,IAGb,MAAMa,EAAgBR,IAMtB,MAAM1lB,GAAO,EAAAmmB,GAAA,GAAQf,GACfgB,EJnEO,SAAwBxM,EAAOpO,GAK5C,OAAIoO,GARN,SAAgBA,GACd,OAAOA,EAAMnS,QAAOsR,GAAQA,GAC9B,CA6BStR,EApBe,EAAA4e,GAAA,GAAQ7a,GAAU3E,KAAIyD,IAC1C,GAAiB,iBAAqBA,GAAO,CAC3C,MAAM,IACJnJ,EAAG,MACH/B,GACEkL,EACEka,EAAKplB,GAAS,CAAC,GACnB,IACE0H,GACE0d,EACJhM,EAAYkD,GAAO8I,EAAI,CAAC,QAM1B,OALa1I,OAAOwB,OAAOxB,OAAOwB,OAAO,CACvCnc,IAAKoC,OAAOpC,IACXqX,GAAY,CACbzR,MAAOD,GAGX,CACA,OAAO,IAAI,IAGf,CIsCsBwf,CAAe1M,EAAOpO,GACpCyO,EL5EO,SAA0BjW,GACvC,IAIIiW,EAJA5O,EAAW6I,UAAUrM,OAAS,QAAsB/D,IAAjBoQ,UAAU,GAAmBA,UAAU,GAAK,CACjF+D,QAAQ,EACRY,SAAS,GAuBX,OAnBEoB,GADe,IAAb5O,EACe,CACf4M,QAAQ,EACRY,SAAS,IAEW,IAAbxN,EACQ,CACf4M,QAAQ,EACRY,SAAS,GAGMiD,OAAOwB,OAAO,CAC7BrF,QAAQ,GACa,iBAAb5M,EAAwBA,EAAW,CAAC,GAE5C4O,EAAepB,UACjBoB,EAAeX,cAAgBwC,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,EAAG,IAAS,CACtEiJ,YAAY,QAAkBviB,EAAW,aAGtCiW,CACT,CKiDyB,CAAiBjW,EAAWqH,GAC7Cmb,EAAc1K,OAAOwB,OAAOxB,OAAOwB,OAAO,CAAC,EAAGrY,aAAmC,EAASA,EAAK1D,OAAQA,GACvGklB,EAAkB,CACtBvmB,MAAwF,QAAhFukB,EAAK1kB,aAA6C,EAASA,EAAUG,aAA0B,IAAPukB,EAAgBA,EAA2E,QAArEC,EAAKzf,aAAmC,EAASA,EAAKlF,iBAA8B,IAAP2kB,OAAgB,EAASA,EAAGxkB,MAC/NF,KAAmS,QAA5R8kB,EAA2I,QAArIF,EAAqF,QAA/ED,EAAK5kB,aAA6C,EAASA,EAAUC,YAAyB,IAAP2kB,EAAgBA,EAAKY,SAAkC,IAAPX,EAAgBA,EAA2E,QAArEC,EAAK5f,aAAmC,EAASA,EAAKlF,iBAA8B,IAAP8kB,OAAgB,EAASA,EAAG7kB,YAAyB,IAAP8kB,EAAgBA,EAAK7f,aAAmC,EAASA,EAAKsgB,eAEnX,OAAOM,EAAwB,gBAAoB,GAAQ/J,OAAOwB,OAAO,CACvEjR,UAAWA,EACX5G,kBAAmBA,GAClB+f,EAAY,CACb5L,MAAOwM,EACPhiB,UAAW,IAAW,CACpB,CAAC,GAAGJ,KAAahE,KAASA,EAC1B,CAAC,GAAGgE,UAAmB,CAAC,OAAQ,iBAAiBqF,SAASlF,GAC1D,CAAC,GAAGH,mBAAqC,kBAATG,EAChC,CAAC,GAAGH,cAAuBshB,GAC1BrgB,aAAmC,EAASA,EAAKb,UAAWA,EAAW+gB,EAAeW,EAAQC,EAAWJ,GAC5GjgB,eAAgB,IAAWA,EAAgBogB,EAAQC,EAAWJ,GAC9DpkB,MAAOilB,EACP5iB,SAAUA,EACVwB,KAAM0W,OAAOwB,OAAO,CAClB9d,KAA8Q,QAAvQ0lB,EAA4N,QAAtND,EAA0H,QAApHD,EAAsE,QAAhED,EAAK9f,aAAmC,EAASA,EAAKG,YAAyB,IAAP2f,OAAgB,EAASA,EAAGvlB,YAAyB,IAAPwlB,EAAgBA,EAAK/f,aAAmC,EAASA,EAAKiB,gBAA6B,IAAP+e,EAAgBA,EAAK/e,SAA6B,IAAPgf,EAAgBA,EAAkB,gBAAoBwB,EAAA,EAAkB,MAC9VC,eAAgB,GAAGT,cAClB9gB,GACHpB,UAAWA,EACXqH,SAAU4O,EACVla,UAAW0mB,KACT,EAEN,GAAKtO,QD/GW,IAAM,KCmHtB,S", "sources": ["webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/PlusOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabContext.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useIndicator.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useOffsets.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useSyncState.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useTouchMove.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useUpdate.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useVisibleRange.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/util.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/AddButton.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/OperationNode.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/TabNode.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/index.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabPanelList/TabPane.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabNavList/Wrapper.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/TabPanelList/index.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/Tabs.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js", "webpack://autogentstudio/./node_modules/rc-tabs/es/index.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/hooks/useAnimateConfig.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/hooks/useLegacyItems.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/style/motion.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/TabPane.js", "webpack://autogentstudio/./node_modules/antd/es/tabs/index.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar PlusOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z\" } }] }, \"name\": \"plus\", \"theme\": \"outlined\" };\nexport default PlusOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PlusOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlusOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PlusOutlined = function PlusOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PlusOutlinedSvg\n  }));\n};\n\n/**![plus](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MiAxNTJoNjBxOCAwIDggOHY3MDRxMCA4LTggOGgtNjBxLTggMC04LThWMTYwcTAtOCA4LTh6IiAvPjxwYXRoIGQ9Ik0xOTIgNDc0aDY3MnE4IDAgOCA4djYwcTAgOC04IDhIMTYwcS04IDAtOC04di02MHEwLTggOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PlusOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlusOutlined';\n}\nexport default RefIcon;", "import { createContext } from 'react';\nexport default /*#__PURE__*/createContext(null);", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport React, { useEffect, useRef, useState } from 'react';\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = useRef();\n  var getLength = React.useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\nexport default useIndicator;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nexport default function useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return useMemo(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || _objectSpread({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useSyncState(defaultState, onChange) {\n  var stateRef = React.useRef(defaultState);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = useState(),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = useRef();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = useRef();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef, useState } from 'react';\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nexport default function useUpdate(callback) {\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = useRef(0);\n  var callbackRef = useRef();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  useLayoutUpdateEffect(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nexport function useUpdateState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState3 = useState({}),\n    _useState4 = _slicedToArray(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}", "import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex >= endIndex ? [0, 0] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}", "/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nexport function stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nexport function genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nexport function getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}", "import * as React from 'react';\nvar AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\nexport default AddButton;", "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport { getRemovable } from \"../util\";\nimport AddButton from \"./AddButton\";\nvar OperationNode = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, _extends({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\nexport default /*#__PURE__*/React.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    focus = props.focus,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    style = props.style,\n    tabCount = props.tabCount,\n    currentPosition = props.currentPosition;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = React.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var btnRef = React.useRef(null);\n  React.useEffect(function () {\n    if (focus && btnRef.current) {\n      btnRef.current.focus();\n    }\n  }, [focus]);\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled), \"\".concat(tabPrefix, \"-focus\"), focus)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: btnRef,\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : active ? 0 : -1,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp,\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, focus && /*#__PURE__*/React.createElement(\"div\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"Tab \".concat(currentPosition, \" of \").concat(tabCount)), icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    role: \"tab\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: active ? 0 : -1,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\nexport default TabNode;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable react-hooks/exhaustive-deps */\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport TabContext from \"../TabContext\";\nimport useIndicator from \"../hooks/useIndicator\";\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport { genDataNodeKey, getRemovable, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = useRef(null);\n  var extraLeftRef = useRef(null);\n  var extraRightRef = useRef(null);\n  var tabsWrapperRef = useRef(null);\n  var tabListRef = useRef(null);\n  var operationsRef = useRef(null);\n  var innerAddButtonRef = useRef(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef(null);\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================= Focus =========================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    focusKey = _useState12[0],\n    setFocusKey = _useState12[1];\n  var _useState13 = useState(false),\n    _useState14 = _slicedToArray(_useState13, 2),\n    isMouse = _useState14[0],\n    setIsMouse = _useState14[1];\n  var enabledTabs = tabs.filter(function (tab) {\n    return !tab.disabled;\n  }).map(function (tab) {\n    return tab.key;\n  });\n  var onOffset = function onOffset(offset) {\n    var currentIndex = enabledTabs.indexOf(focusKey || activeKey);\n    var len = enabledTabs.length;\n    var nextIndex = (currentIndex + offset + len) % len;\n    var newKey = enabledTabs[nextIndex];\n    setFocusKey(newKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var code = e.code;\n    var isRTL = rtl && tabPositionTopOrBottom;\n    var firstEnabledTab = enabledTabs[0];\n    var lastEnabledTab = enabledTabs[enabledTabs.length - 1];\n    switch (code) {\n      // LEFT\n      case 'ArrowLeft':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? 1 : -1);\n          }\n          break;\n        }\n\n      // RIGHT\n      case 'ArrowRight':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? -1 : 1);\n          }\n          break;\n        }\n\n      // UP\n      case 'ArrowUp':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(-1);\n          }\n          break;\n        }\n\n      // DOWN\n      case 'ArrowDown':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(1);\n          }\n          break;\n        }\n\n      // HOME\n      case 'Home':\n        {\n          e.preventDefault();\n          setFocusKey(firstEnabledTab);\n          break;\n        }\n\n      // END\n      case 'End':\n        {\n          e.preventDefault();\n          setFocusKey(lastEnabledTab);\n          break;\n        }\n\n      // Enter & Space\n      case 'Enter':\n      case 'Space':\n        {\n          e.preventDefault();\n          onTabClick(focusKey, e);\n          break;\n        }\n      // Backspace\n      case 'Backspace':\n      case 'Delete':\n        {\n          var removeIndex = enabledTabs.indexOf(focusKey);\n          var removeTab = tabs.find(function (tab) {\n            return tab.key === focusKey;\n          });\n          var removable = getRemovable(removeTab === null || removeTab === void 0 ? void 0 : removeTab.closable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.closeIcon, editable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.disabled);\n          if (removable) {\n            e.preventDefault();\n            e.stopPropagation();\n            editable.onEdit('remove', {\n              key: focusKey,\n              event: e\n            });\n            // when remove last tab, focus previous tab\n            if (removeIndex === enabledTabs.length - 1) {\n              onOffset(-1);\n            } else {\n              onOffset(1);\n            }\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPositionTopOrBottom) {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      focus: key === focusKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      tabCount: enabledTabs.length,\n      currentPosition: i + 1,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onKeyDown: handleKeyDown,\n      onFocus: function onFocus() {\n        if (!isMouse) {\n          setFocusKey(key);\n        }\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      },\n      onBlur: function onBlur() {\n        setFocusKey(undefined);\n      },\n      onMouseDown: function onMouseDown() {\n        setIsMouse(true);\n      },\n      onMouseUp: function onMouseUp() {\n        setIsMouse(false);\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = _slicedToArray(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = useIndicator({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    \"aria-orientation\": tabPositionTopOrBottom ? 'horizontal' : 'vertical',\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\nexport default TabNavList;", "import classNames from 'classnames';\nimport * as React from 'react';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\nimport * as React from 'react';\nimport TabNavList from '.';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"../TabPanelList/TabPane\";\n// We have to create a TabNavList components.\nvar TabNavListWrapper = function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(TabContext),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TabNavListWrapper.displayName = 'TabNavListWrapper';\n}\nexport default TabNavListWrapper;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = _objectWithoutProperties(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\nexport default TabPanelList;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport TabContext from \"./TabContext\";\nimport TabNavListWrapper from \"./TabNavList/Wrapper\";\nimport TabPanelList from \"./TabPanelList\";\nimport useAnimateConfig from \"./hooks/useAnimateConfig\";\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var tabs = React.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && _typeof(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = useAnimateConfig(animated);\n\n  // ======================== Mobile ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = useMergedState(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = useState(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  useEffect(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = useMergedState(null, {\n      value: id\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  useEffect(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat(process.env.NODE_ENV === 'test' ? 'test' : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = _objectSpread(_objectSpread({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/React.createElement(TabContext.Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    id: id,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/React.createElement(TabNavListWrapper, _extends({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/React.createElement(TabPanelList, _extends({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tabs.displayName = 'Tabs';\n}\nexport default Tabs;", "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nexport default function useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = _objectSpread({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  }\n\n  // Enable tabPane animation if provide motion\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n    mergedAnimated.tabPane = false;\n  }\n  return mergedAnimated;\n}", "import Tabs from \"./Tabs\";\nexport default Tabs;", "import { getTransitionName } from '../../_util/motion';\nconst motion = {\n  motionAppear: false,\n  motionEnter: true,\n  motionLeave: true\n};\nexport default function useAnimateConfig(prefixCls) {\n  let animated = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    inkBar: true,\n    tabPane: false\n  };\n  let mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: true\n    };\n  } else {\n    mergedAnimated = Object.assign({\n      inkBar: true\n    }, typeof animated === 'object' ? animated : {});\n  }\n  if (mergedAnimated.tabPane) {\n    mergedAnimated.tabPaneMotion = Object.assign(Object.assign({}, motion), {\n      motionName: getTransitionName(prefixCls, 'switch')\n    });\n  }\n  return mergedAnimated;\n}", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tabs');\n    warning.deprecated(!children, 'Tabs.TabPane', 'items');\n  }\n  if (items) {\n    return items;\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        key,\n        props\n      } = node;\n      const _a = props || {},\n        {\n          tab\n        } = _a,\n        restProps = __rest(_a, [\"tab\"]);\n      const item = Object.assign(Object.assign({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "import { initSlideMotion } from '../../style/motion';\nconst genMotionStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow\n  } = token;\n  return [{\n    [componentCls]: {\n      [`${componentCls}-switch`]: {\n        '&-appear, &-enter': {\n          transition: 'none',\n          '&-start': {\n            opacity: 0\n          },\n          '&-active': {\n            opacity: 1,\n            transition: `opacity ${motionDurationSlow}`\n          }\n        },\n        '&-leave': {\n          position: 'absolute',\n          transition: 'none',\n          inset: 0,\n          '&-start': {\n            opacity: 1\n          },\n          '&-active': {\n            opacity: 0,\n            transition: `opacity ${motionDurationSlow}`\n          }\n        }\n      }\n    }\n  },\n  // Follow code may reuse in other components\n  [initSlideMotion(token, 'slide-up'), initSlideMotion(token, 'slide-down')]];\n};\nexport default genMotionStyle;", "import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, genFocusStyle, resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\nconst genCardStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardBg,\n    cardGutter,\n    colorBorderSecondary,\n    itemSelectedColor\n  } = token;\n  return {\n    [`${componentCls}-card`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: 0,\n          padding: tabsCardPadding,\n          background: cardBg,\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`\n        },\n        [`${componentCls}-tab-active`]: {\n          color: itemSelectedColor,\n          background: token.colorBgContainer\n        },\n        [`${componentCls}-tab-focus`]: Object.assign({}, genFocusOutline(token, -3)),\n        [`${componentCls}-ink-bar`]: {\n          visibility: 'hidden'\n        },\n        [`& ${componentCls}-tab${componentCls}-tab-focus ${componentCls}-tab-btn`]: {\n          outline: 'none'\n        }\n      },\n      // ========================== Top & Bottom ==========================\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(cardGutter)\n            }\n          }\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderBottomColor: token.colorBgContainer\n          }\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)}`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderTopColor: token.colorBgContainer\n          }\n        }\n      },\n      // ========================== Left & Right ==========================\n      [`&${componentCls}-left, &${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginTop: unit(cardGutter)\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadiusLG)} 0 0 ${unit(token.borderRadiusLG)}`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderRightColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderLeftColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genDropdownStyle = token => {\n  const {\n    componentCls,\n    itemHoverColor,\n    dropdownEdgeChildVerticalPadding\n  } = token;\n  return {\n    [`${componentCls}-dropdown`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: token.zIndexPopup,\n      display: 'block',\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-dropdown-menu`]: {\n        maxHeight: token.tabsDropdownHeight,\n        margin: 0,\n        padding: `${unit(dropdownEdgeChildVerticalPadding)} 0`,\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        textAlign: {\n          _skip_check_: true,\n          value: 'left'\n        },\n        listStyleType: 'none',\n        backgroundColor: token.colorBgContainer,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary,\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          alignItems: 'center',\n          minWidth: token.tabsDropdownWidth,\n          margin: 0,\n          padding: `${unit(token.paddingXXS)} ${unit(token.paddingSM)}`,\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${token.motionDurationSlow}`,\n          '> span': {\n            flex: 1,\n            whiteSpace: 'nowrap'\n          },\n          '&-remove': {\n            flex: 'none',\n            marginLeft: {\n              _skip_check_: true,\n              value: token.marginSM\n            },\n            color: token.colorTextDescription,\n            fontSize: token.fontSizeSM,\n            background: 'transparent',\n            border: 0,\n            cursor: 'pointer',\n            '&:hover': {\n              color: itemHoverColor\n            }\n          },\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            '&, &:hover': {\n              color: token.colorTextDisabled,\n              background: 'transparent',\n              cursor: 'not-allowed'\n            }\n          }\n        })\n      }\n    })\n  };\n};\nconst genPositionStyle = token => {\n  const {\n    componentCls,\n    margin,\n    colorBorderSecondary,\n    horizontalMargin,\n    verticalItemPadding,\n    verticalItemMargin,\n    calc\n  } = token;\n  return {\n    // ========================== Top & Bottom ==========================\n    [`${componentCls}-top, ${componentCls}-bottom`]: {\n      flexDirection: 'column',\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        margin: horizontalMargin,\n        '&::before': {\n          position: 'absolute',\n          right: {\n            _skip_check_: true,\n            value: 0\n          },\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          content: \"''\"\n        },\n        [`${componentCls}-ink-bar`]: {\n          height: token.lineWidthBold,\n          '&-animated': {\n            transition: `width ${token.motionDurationSlow}, left ${token.motionDurationSlow},\n            right ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-wrap`]: {\n          '&::before, &::after': {\n            top: 0,\n            bottom: 0,\n            width: token.controlHeight\n          },\n          '&::before': {\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowLeft\n          },\n          '&::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowRight\n          },\n          [`&${componentCls}-nav-wrap-ping-left::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-right::after`]: {\n            opacity: 1\n          }\n        }\n      }\n    },\n    [`${componentCls}-top`]: {\n      [`> ${componentCls}-nav,\n        > div > ${componentCls}-nav`]: {\n        '&::before': {\n          bottom: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          bottom: 0\n        }\n      }\n    },\n    [`${componentCls}-bottom`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        marginTop: margin,\n        marginBottom: 0,\n        '&::before': {\n          top: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          top: 0\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0\n      }\n    },\n    // ========================== Left & Right ==========================\n    [`${componentCls}-left, ${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        flexDirection: 'column',\n        minWidth: calc(token.controlHeight).mul(1.25).equal(),\n        // >>>>>>>>>>> Tab\n        [`${componentCls}-tab`]: {\n          padding: verticalItemPadding,\n          textAlign: 'center'\n        },\n        [`${componentCls}-tab + ${componentCls}-tab`]: {\n          margin: verticalItemMargin\n        },\n        // >>>>>>>>>>> Nav\n        [`${componentCls}-nav-wrap`]: {\n          flexDirection: 'column',\n          '&::before, &::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.controlHeight\n          },\n          '&::before': {\n            top: 0,\n            boxShadow: token.boxShadowTabsOverflowTop\n          },\n          '&::after': {\n            bottom: 0,\n            boxShadow: token.boxShadowTabsOverflowBottom\n          },\n          [`&${componentCls}-nav-wrap-ping-top::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-bottom::after`]: {\n            opacity: 1\n          }\n        },\n        // >>>>>>>>>>> Ink Bar\n        [`${componentCls}-ink-bar`]: {\n          width: token.lineWidthBold,\n          '&-animated': {\n            transition: `height ${token.motionDurationSlow}, top ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-list, ${componentCls}-nav-operations`]: {\n          flex: '1 0 auto',\n          // fix safari scroll problem\n          flexDirection: 'column'\n        }\n      }\n    },\n    [`${componentCls}-left`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-ink-bar`]: {\n          right: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        marginLeft: {\n          _skip_check_: true,\n          value: unit(calc(token.lineWidth).mul(-1).equal())\n        },\n        borderLeft: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingLeft: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        [`${componentCls}-ink-bar`]: {\n          left: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0,\n        marginRight: {\n          _skip_check_: true,\n          value: calc(token.lineWidth).mul(-1).equal()\n        },\n        borderRight: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingRight: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    cardPaddingSM,\n    cardPaddingLG,\n    horizontalItemPaddingSM,\n    horizontalItemPaddingLG\n  } = token;\n  return {\n    [componentCls]: {\n      '&-small': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingSM,\n            fontSize: token.titleFontSizeSM\n          }\n        }\n      },\n      '&-large': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingLG,\n            fontSize: token.titleFontSizeLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-card`]: {\n      [`&${componentCls}-small`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingSM\n          }\n        },\n        [`&${componentCls}-bottom`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadius)} ${unit(token.borderRadius)}`\n          }\n        },\n        [`&${componentCls}-top`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadius)} ${unit(token.borderRadius)} 0 0`\n          }\n        },\n        [`&${componentCls}-right`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadius)} ${unit(token.borderRadius)} 0`\n            }\n          }\n        },\n        [`&${componentCls}-left`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadius)} 0 0 ${unit(token.borderRadius)}`\n            }\n          }\n        }\n      },\n      [`&${componentCls}-large`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genTabStyle = token => {\n  const {\n    componentCls,\n    itemActiveColor,\n    itemHoverColor,\n    iconCls,\n    tabsHorizontalItemMargin,\n    horizontalItemPadding,\n    itemSelectedColor,\n    itemColor\n  } = token;\n  const tabCls = `${componentCls}-tab`;\n  return {\n    [tabCls]: {\n      position: 'relative',\n      WebkitTouchCallout: 'none',\n      WebkitTapHighlightColor: 'transparent',\n      display: 'inline-flex',\n      alignItems: 'center',\n      padding: horizontalItemPadding,\n      fontSize: token.titleFontSize,\n      background: 'transparent',\n      border: 0,\n      outline: 'none',\n      cursor: 'pointer',\n      color: itemColor,\n      '&-btn, &-remove': {\n        '&:focus:not(:focus-visible), &:active': {\n          color: itemActiveColor\n        }\n      },\n      '&-btn': {\n        outline: 'none',\n        transition: `all ${token.motionDurationSlow}`,\n        [`${tabCls}-icon:not(:last-child)`]: {\n          marginInlineEnd: token.marginSM\n        }\n      },\n      '&-remove': Object.assign({\n        flex: 'none',\n        marginRight: {\n          _skip_check_: true,\n          value: token.calc(token.marginXXS).mul(-1).equal()\n        },\n        marginLeft: {\n          _skip_check_: true,\n          value: token.marginXS\n        },\n        color: token.colorTextDescription,\n        fontSize: token.fontSizeSM,\n        background: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      }, genFocusStyle(token)),\n      '&:hover': {\n        color: itemHoverColor\n      },\n      [`&${tabCls}-active ${tabCls}-btn`]: {\n        color: itemSelectedColor,\n        textShadow: token.tabsActiveTextShadow\n      },\n      [`&${tabCls}-focus ${tabCls}-btn`]: Object.assign({}, genFocusOutline(token)),\n      [`&${tabCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed'\n      },\n      [`&${tabCls}-disabled ${tabCls}-btn, &${tabCls}-disabled ${componentCls}-remove`]: {\n        '&:focus, &:active': {\n          color: token.colorTextDisabled\n        }\n      },\n      [`& ${tabCls}-remove ${iconCls}`]: {\n        margin: 0\n      },\n      [`${iconCls}:not(:last-child)`]: {\n        marginRight: {\n          _skip_check_: true,\n          value: token.marginSM\n        }\n      }\n    },\n    [`${tabCls} + ${tabCls}`]: {\n      margin: {\n        _skip_check_: true,\n        value: tabsHorizontalItemMargin\n      }\n    }\n  };\n};\nconst genRtlStyle = token => {\n  const {\n    componentCls,\n    tabsHorizontalItemMarginRTL,\n    iconCls,\n    cardGutter,\n    calc\n  } = token;\n  const rtlCls = `${componentCls}-rtl`;\n  return {\n    [rtlCls]: {\n      direction: 'rtl',\n      [`${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: {\n            _skip_check_: true,\n            value: tabsHorizontalItemMarginRTL\n          },\n          [`${componentCls}-tab:last-of-type`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          },\n          [iconCls]: {\n            marginRight: {\n              _skip_check_: true,\n              value: 0\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(token.marginSM)\n            }\n          },\n          [`${componentCls}-tab-remove`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: unit(token.marginXS)\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(calc(token.marginXXS).mul(-1).equal())\n            },\n            [iconCls]: {\n              margin: 0\n            }\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 1\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 0\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 0\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 1\n        }\n      },\n      // ====================== Card ======================\n      [`&${componentCls}-card${componentCls}-top, &${componentCls}-card${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: cardGutter\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          }\n        }\n      }\n    },\n    [`${componentCls}-dropdown-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-menu-item`]: {\n      [`${componentCls}-dropdown-rtl`]: {\n        textAlign: {\n          _skip_check_: true,\n          value: 'right'\n        }\n      }\n    }\n  };\n};\nconst genTabsStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardHeight,\n    cardGutter,\n    itemHoverColor,\n    itemActiveColor,\n    colorBorderSecondary\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      // ========================== Navigation ==========================\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        position: 'relative',\n        display: 'flex',\n        flex: 'none',\n        alignItems: 'center',\n        [`${componentCls}-nav-wrap`]: {\n          position: 'relative',\n          display: 'flex',\n          flex: 'auto',\n          alignSelf: 'stretch',\n          overflow: 'hidden',\n          whiteSpace: 'nowrap',\n          transform: 'translate(0)',\n          // Fix chrome render bug\n          // >>>>> Ping shadow\n          '&::before, &::after': {\n            position: 'absolute',\n            zIndex: 1,\n            opacity: 0,\n            transition: `opacity ${token.motionDurationSlow}`,\n            content: \"''\",\n            pointerEvents: 'none'\n          }\n        },\n        [`${componentCls}-nav-list`]: {\n          position: 'relative',\n          display: 'flex',\n          transition: `opacity ${token.motionDurationSlow}`\n        },\n        // >>>>>>>> Operations\n        [`${componentCls}-nav-operations`]: {\n          display: 'flex',\n          alignSelf: 'stretch'\n        },\n        [`${componentCls}-nav-operations-hidden`]: {\n          position: 'absolute',\n          visibility: 'hidden',\n          pointerEvents: 'none'\n        },\n        [`${componentCls}-nav-more`]: {\n          position: 'relative',\n          padding: tabsCardPadding,\n          background: 'transparent',\n          border: 0,\n          color: token.colorText,\n          '&::after': {\n            position: 'absolute',\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            bottom: 0,\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.calc(token.controlHeightLG).div(8).equal(),\n            transform: 'translateY(100%)',\n            content: \"''\"\n          }\n        },\n        [`${componentCls}-nav-add`]: Object.assign({\n          minWidth: cardHeight,\n          marginLeft: {\n            _skip_check_: true,\n            value: cardGutter\n          },\n          padding: unit(token.paddingXS),\n          background: 'transparent',\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`,\n          outline: 'none',\n          cursor: 'pointer',\n          color: token.colorText,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`,\n          '&:hover': {\n            color: itemHoverColor\n          },\n          '&:active, &:focus:not(:focus-visible)': {\n            color: itemActiveColor\n          }\n        }, genFocusStyle(token, -3))\n      },\n      [`${componentCls}-extra-content`]: {\n        flex: 'none'\n      },\n      // ============================ InkBar ============================\n      [`${componentCls}-ink-bar`]: {\n        position: 'absolute',\n        background: token.inkBarColor,\n        pointerEvents: 'none'\n      }\n    }), genTabStyle(token)), {\n      // =========================== TabPanes ===========================\n      [`${componentCls}-content`]: {\n        position: 'relative',\n        width: '100%'\n      },\n      [`${componentCls}-content-holder`]: {\n        flex: 'auto',\n        minWidth: 0,\n        minHeight: 0\n      },\n      [`${componentCls}-tabpane`]: Object.assign(Object.assign({}, genFocusStyle(token)), {\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    }),\n    [`${componentCls}-centered`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-nav-wrap`]: {\n          [`&:not([class*='${componentCls}-nav-wrap-ping']) > ${componentCls}-nav-list`]: {\n            margin: 'auto'\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const cardHeight = token.controlHeightLG;\n  return {\n    zIndexPopup: token.zIndexPopupBase + 50,\n    cardBg: token.colorFillAlter,\n    cardHeight,\n    // Initialize with empty string, because cardPadding will be calculated with cardHeight by default.\n    cardPadding: `${(cardHeight - Math.round(token.fontSize * token.lineHeight)) / 2 - token.lineWidth}px ${token.padding}px`,\n    cardPaddingSM: `${token.paddingXXS * 1.5}px ${token.padding}px`,\n    cardPaddingLG: `${token.paddingXS}px ${token.padding}px ${token.paddingXXS * 1.5}px`,\n    titleFontSize: token.fontSize,\n    titleFontSizeLG: token.fontSizeLG,\n    titleFontSizeSM: token.fontSize,\n    inkBarColor: token.colorPrimary,\n    horizontalMargin: `0 0 ${token.margin}px 0`,\n    horizontalItemGutter: 32,\n    // Fixed Value\n    // Initialize with empty string, because horizontalItemMargin will be calculated with horizontalItemGutter by default.\n    horizontalItemMargin: ``,\n    horizontalItemMarginRTL: ``,\n    horizontalItemPadding: `${token.paddingSM}px 0`,\n    horizontalItemPaddingSM: `${token.paddingXS}px 0`,\n    horizontalItemPaddingLG: `${token.padding}px 0`,\n    verticalItemPadding: `${token.paddingXS}px ${token.paddingLG}px`,\n    verticalItemMargin: `${token.margin}px 0 0 0`,\n    itemColor: token.colorText,\n    itemSelectedColor: token.colorPrimary,\n    itemHoverColor: token.colorPrimaryHover,\n    itemActiveColor: token.colorPrimaryActive,\n    cardGutter: token.marginXXS / 2\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Tabs', token => {\n  const tabsToken = mergeToken(token, {\n    // `cardPadding` is empty by default, so we could calculate with dynamic `cardHeight`\n    tabsCardPadding: token.cardPadding,\n    dropdownEdgeChildVerticalPadding: token.paddingXXS,\n    tabsActiveTextShadow: '0 0 0.25px currentcolor',\n    tabsDropdownHeight: 200,\n    tabsDropdownWidth: 120,\n    tabsHorizontalItemMargin: `0 0 0 ${unit(token.horizontalItemGutter)}`,\n    tabsHorizontalItemMarginRTL: `0 0 0 ${unit(token.horizontalItemGutter)}`\n  });\n  return [genSizeStyle(tabsToken), genRtlStyle(tabsToken), genPositionStyle(tabsToken), genDropdownStyle(tabsToken), genCardStyle(tabsToken), genTabsStyle(tabsToken), genMotionStyle(tabsToken)];\n}, prepareComponentToken);", "const TabPane = () => null;\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'DeprecatedTabPane';\n}\nexport default TabPane;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport PlusOutlined from \"@ant-design/icons/es/icons/PlusOutlined\";\nimport classNames from 'classnames';\nimport RcTabs from 'rc-tabs';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useAnimateConfig from './hooks/useAnimateConfig';\nimport useLegacyItems from './hooks/useLegacyItems';\nimport useStyle from './style';\nimport TabPane from './TabPane';\nconst Tabs = props => {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n  const {\n      type,\n      className,\n      rootClassName,\n      size: customSize,\n      onEdit,\n      hideAdd,\n      centered,\n      addIcon,\n      removeIcon,\n      moreIcon,\n      more,\n      popupClassName,\n      children,\n      items,\n      animated,\n      style,\n      indicatorSize,\n      indicator\n    } = props,\n    otherProps = __rest(props, [\"type\", \"className\", \"rootClassName\", \"size\", \"onEdit\", \"hideAdd\", \"centered\", \"addIcon\", \"removeIcon\", \"moreIcon\", \"more\", \"popupClassName\", \"children\", \"items\", \"animated\", \"style\", \"indicatorSize\", \"indicator\"]);\n  const {\n    prefixCls: customizePrefixCls\n  } = otherProps;\n  const {\n    direction,\n    tabs,\n    getPrefixCls,\n    getPopupContainer\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tabs', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  let editable;\n  if (type === 'editable-card') {\n    editable = {\n      onEdit: (editType, _ref) => {\n        let {\n          key,\n          event\n        } = _ref;\n        onEdit === null || onEdit === void 0 ? void 0 : onEdit(editType === 'add' ? event : key, editType);\n      },\n      removeIcon: (_a = removeIcon !== null && removeIcon !== void 0 ? removeIcon : tabs === null || tabs === void 0 ? void 0 : tabs.removeIcon) !== null && _a !== void 0 ? _a : /*#__PURE__*/React.createElement(CloseOutlined, null),\n      addIcon: (addIcon !== null && addIcon !== void 0 ? addIcon : tabs === null || tabs === void 0 ? void 0 : tabs.addIcon) || /*#__PURE__*/React.createElement(PlusOutlined, null),\n      showAdd: hideAdd !== true\n    };\n  }\n  const rootPrefixCls = getPrefixCls();\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tabs');\n    process.env.NODE_ENV !== \"production\" ? warning(!('onPrevClick' in props) && !('onNextClick' in props), 'breaking', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(indicatorSize || (tabs === null || tabs === void 0 ? void 0 : tabs.indicatorSize)), 'deprecated', '`indicatorSize` has been deprecated. Please use `indicator={{ size: ... }}` instead.') : void 0;\n  }\n  const size = useSize(customSize);\n  const mergedItems = useLegacyItems(items, children);\n  const mergedAnimated = useAnimateConfig(prefixCls, animated);\n  const mergedStyle = Object.assign(Object.assign({}, tabs === null || tabs === void 0 ? void 0 : tabs.style), style);\n  const mergedIndicator = {\n    align: (_b = indicator === null || indicator === void 0 ? void 0 : indicator.align) !== null && _b !== void 0 ? _b : (_c = tabs === null || tabs === void 0 ? void 0 : tabs.indicator) === null || _c === void 0 ? void 0 : _c.align,\n    size: (_g = (_e = (_d = indicator === null || indicator === void 0 ? void 0 : indicator.size) !== null && _d !== void 0 ? _d : indicatorSize) !== null && _e !== void 0 ? _e : (_f = tabs === null || tabs === void 0 ? void 0 : tabs.indicator) === null || _f === void 0 ? void 0 : _f.size) !== null && _g !== void 0 ? _g : tabs === null || tabs === void 0 ? void 0 : tabs.indicatorSize\n  };\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RcTabs, Object.assign({\n    direction: direction,\n    getPopupContainer: getPopupContainer\n  }, otherProps, {\n    items: mergedItems,\n    className: classNames({\n      [`${prefixCls}-${size}`]: size,\n      [`${prefixCls}-card`]: ['card', 'editable-card'].includes(type),\n      [`${prefixCls}-editable-card`]: type === 'editable-card',\n      [`${prefixCls}-centered`]: centered\n    }, tabs === null || tabs === void 0 ? void 0 : tabs.className, className, rootClassName, hashId, cssVarCls, rootCls),\n    popupClassName: classNames(popupClassName, hashId, cssVarCls, rootCls),\n    style: mergedStyle,\n    editable: editable,\n    more: Object.assign({\n      icon: (_l = (_k = (_j = (_h = tabs === null || tabs === void 0 ? void 0 : tabs.more) === null || _h === void 0 ? void 0 : _h.icon) !== null && _j !== void 0 ? _j : tabs === null || tabs === void 0 ? void 0 : tabs.moreIcon) !== null && _k !== void 0 ? _k : moreIcon) !== null && _l !== void 0 ? _l : /*#__PURE__*/React.createElement(EllipsisOutlined, null),\n      transitionName: `${rootPrefixCls}-slide-up`\n    }, more),\n    prefixCls: prefixCls,\n    animated: mergedAnimated,\n    indicator: mergedIndicator\n  })));\n};\nTabs.TabPane = TabPane;\nif (process.env.NODE_ENV !== 'production') {\n  Tabs.displayName = 'Tabs';\n}\nexport default Tabs;"], "names": ["props", "ref", "AntdIcon", "A", "icon", "createContext", "options", "activeTabOffset", "horizontal", "rtl", "_options$indicator", "indicator", "size", "_indicator$align", "align", "_useState", "useState", "_useState2", "inkStyle", "setInkStyle", "inkBarRafRef", "useRef", "<PERSON><PERSON><PERSON><PERSON>", "origin", "cleanInkBarRaf", "raf", "cancel", "current", "useEffect", "newInkStyle", "width", "key", "transform", "height", "top", "style", "DEFAULT_SIZE", "left", "useSyncState", "defaultState", "onChange", "stateRef", "_React$useState", "forceUpdate", "updater", "newValue", "SPEED_OFF_MULTIPLE", "Math", "pow", "useUpdate", "callback", "count", "setCount", "effectRef", "callback<PERSON><PERSON>", "_callbackRef$current", "call", "right", "stringify", "obj", "tgt", "Map", "for<PERSON>ach", "v", "k", "JSON", "genDataNodeKey", "String", "replace", "getRemovable", "closable", "closeIcon", "editable", "disabled", "undefined", "AddButton", "prefixCls", "locale", "showAdd", "type", "className", "concat", "addAriaLabel", "onClick", "event", "onEdit", "addIcon", "content", "position", "extra", "assertExtra", "OperationNode", "id", "tabs", "mobile", "_props$more", "more", "moreProps", "tabBarGutter", "removeAriaLabel", "onTabClick", "getPopupContainer", "popupClassName", "open", "<PERSON><PERSON><PERSON>", "_useState3", "_useState4", "<PERSON><PERSON><PERSON>", "setSelectedKey", "_moreProps$icon", "moreIcon", "popupId", "dropdownPrefix", "selectedItemId", "dropdownAriaLabel", "menu", "_ref", "domEvent", "tabIndex", "role", "<PERSON><PERSON><PERSON><PERSON>", "map", "tab", "label", "removable", "e", "stopPropagation", "preventDefault", "onRemoveTab", "removeIcon", "selectOffset", "offset", "enabledTabs", "filter", "selectedIndex", "findIndex", "len", "length", "i", "ele", "document", "getElementById", "scrollIntoView", "moreStyle", "visibility", "order", "overlayClassName", "moreNode", "overlay", "visible", "onVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "onKeyDown", "which", "KeyCode", "UP", "DOWN", "ESC", "SPACE", "ENTER", "includes", "_", "next", "active", "focus", "_props$tab", "renderWrapper", "onFocus", "onBlur", "onMouseDown", "onMouseUp", "tabCount", "currentPosition", "tabPrefix", "onInternalClick", "labelNode", "btnRef", "node", "overflow", "opacity", "getSize", "refObj", "_ref$offsetWidth", "offsetWidth", "_ref$offsetHeight", "offsetHeight", "_refObj$current$getBo", "getBoundingClientRect", "abs", "getUnitValue", "tabPositionTopOrBottom", "TabNavList", "animated", "active<PERSON><PERSON>", "tabPosition", "children", "onTabScroll", "_React$useContext", "TabContext", "containerRef", "extraLeftRef", "extraRightRef", "tabsWrapperRef", "tabListRef", "operationsRef", "innerAddButtonRef", "_useSyncState", "prev", "direction", "_useSyncState2", "transformLeft", "setTransformLeft", "_useSyncState3", "_useSyncState4", "transformTop", "setTransformTop", "containerExcludeExtraSize", "setContainerExcludeExtraSize", "tabContentSize", "setTabContentSize", "_useState5", "_useState6", "addSize", "setAddSize", "_useState7", "_useState8", "operationSize", "setOperationSize", "_useUpdateState", "batchRef", "state", "flushUpdate", "push", "useUpdateState", "_useUpdateState2", "tabSizes", "setTabSizes", "tabOffsets", "holder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMemo", "_tabs$", "lastOffset", "get", "rightOffset", "_tabs", "data", "entity", "set", "join", "useOffsets", "containerExcludeExtraSizeValue", "tabContentSizeValue", "addSizeValue", "operationSizeValue", "needScroll", "floor", "visibleTabContentValue", "operationsHiddenClassName", "transformMin", "transformMax", "alignInRange", "value", "max", "min", "touchMovingRef", "_useState9", "_useState10", "lockAnimation", "setLockAnimation", "doLockAnimation", "Date", "now", "clearTouchMoving", "clearTimeout", "onOffset", "touchPosition", "setTouchPosition", "lastTimestamp", "setLastTimestamp", "lastTimeDiff", "setLastTimeDiff", "setLastOffset", "motionRef", "lastWheelDirectionRef", "touchEventsRef", "onTouchStart", "_e$touches$", "touches", "screenX", "screenY", "x", "y", "window", "clearInterval", "onTouchMove", "_e$touches$2", "offsetX", "offsetY", "onTouchEnd", "distanceX", "distanceY", "absX", "absY", "currentX", "currentY", "setInterval", "onWheel", "deltaX", "deltaY", "mixed", "onProxyTouchMove", "onProxyTouchEnd", "addEventListener", "passive", "removeEventListener", "useTouchMove", "do<PERSON>ove", "setState", "setTimeout", "_useVisibleRange", "addNodeSizeValue", "operationNodeSizeValue", "char<PERSON><PERSON><PERSON>", "transformSize", "endIndex", "startIndex", "_i", "useVisibleRange", "_useVisibleRange2", "visibleStart", "visibleEnd", "scrollToTab", "useEvent", "arguments", "tabOffset", "newTransform", "_newTransform", "_useState11", "_useState12", "focus<PERSON>ey", "setFocus<PERSON>ey", "_useState13", "_useState14", "isMouse", "setIsMouse", "currentIndex", "indexOf", "new<PERSON>ey", "handleKeyDown", "code", "isRTL", "firstEnabledTab", "lastEnabledTab", "removeIndex", "removeTab", "find", "tabNodeStyle", "marginTop", "tabNodes", "scrollLeft", "scrollTop", "updateTabSizes", "_tabListRef$current", "newSizes", "listRect", "_ref2", "_tabListRef$current2", "btnNode", "querySelector", "_getTabSize", "containerRect", "offsetTop", "offsetLeft", "_tab$getBoundingClien", "getTabSize", "_getTabSize2", "onListHolderResize", "containerSize", "extraLeftSize", "extraRightSize", "newAddSize", "newOperationSize", "tabContentFullSize", "startHiddenTabs", "slice", "endHiddenTabs", "hiddenTabs", "indicatorStyle", "pingLeft", "pingRight", "pingTop", "pingBottom", "hasDropdown", "wrapPrefix", "onResize", "transition", "inkBar", "tabMoving", "TabPane", "tabKey", "_excluded", "_excluded2", "renderTabBar", "restProps", "panes", "restTabProps", "destroyInactiveTabPane", "tabPaneAnimated", "tabPane", "tabPanePrefixCls", "item", "forceRender", "paneStyle", "paneClassName", "itemDestroyInactiveTabPane", "removeOnLeave", "leavedClassName", "tabPaneMotion", "motionStyle", "motionClassName", "uuid", "Tabs", "_props$prefixCls", "items", "defaultActiveKey", "_props$tabPosition", "tabBarStyle", "tabBarExtraContent", "mergedAnimated", "useAnimateConfig", "setMobile", "isMobile", "_useMergedState", "useMergedState", "defaultValue", "_useMergedState2", "mergedActiveKey", "setMergedActiveKey", "activeIndex", "setActiveIndex", "_tabs$newActiveIndex", "newActiveIndex", "_useMergedState3", "_useMergedState4", "mergedId", "setMergedId", "sharedProps", "tabNavBarProps", "isActiveChanged", "Provider", "motionAppear", "motionEnter", "motionLeave", "__rest", "s", "t", "p", "Object", "prototype", "hasOwnProperty", "getOwnPropertySymbols", "propertyIsEnumerable", "token", "componentCls", "motionDurationSlow", "inset", "genCardStyle", "tabsCardPadding", "cardBg", "cardGutter", "colorBorderSecondary", "itemSelectedColor", "margin", "padding", "background", "border", "lineWidth", "lineType", "motionEaseInOut", "color", "colorBgContainer", "assign", "outline", "marginLeft", "_skip_check_", "borderRadius", "borderRadiusLG", "borderBottomColor", "borderTopColor", "borderRightColor", "borderLeftColor", "genDropdownStyle", "itemHoverColor", "dropdownEdgeChildVerticalPadding", "zIndex", "zIndexPopup", "display", "maxHeight", "tabsDropdownHeight", "overflowX", "overflowY", "textAlign", "listStyleType", "backgroundColor", "backgroundClip", "boxShadow", "boxShadowSecondary", "alignItems", "min<PERSON><PERSON><PERSON>", "tabsDropdownWidth", "paddingXXS", "paddingSM", "colorText", "fontWeight", "fontSize", "lineHeight", "cursor", "flex", "whiteSpace", "marginSM", "colorTextDescription", "fontSizeSM", "controlItemBgHover", "colorTextDisabled", "genPositionStyle", "<PERSON><PERSON><PERSON><PERSON>", "verticalItemPadding", "verticalItemMargin", "calc", "flexDirection", "borderBottom", "lineWidthBold", "bottom", "controlHeight", "boxShadowTabsOverflowLeft", "boxShadowTabsOverflowRight", "marginBottom", "mul", "equal", "boxShadowTabsOverflowTop", "boxShadowTabsOverflowBottom", "borderLeft", "colorBorder", "paddingLeft", "paddingLG", "marginRight", "borderRight", "paddingRight", "genSizeStyle", "cardPaddingSM", "cardPaddingLG", "horizontalItemPaddingSM", "horizontalItemPaddingLG", "titleFontSizeSM", "titleFontSizeLG", "genTabStyle", "itemActiveColor", "iconCls", "tabsHorizontalItemMargin", "horizontalItemPadding", "itemColor", "tabCls", "WebkitTouchCallout", "WebkitTapHighlightColor", "titleFontSize", "marginInlineEnd", "marginXXS", "marginXS", "colorTextHeading", "textShadow", "tabsActiveTextShadow", "genRtlStyle", "tabsHorizontalItemMarginRTL", "rtlCls", "genTabsStyle", "cardHeight", "alignSelf", "pointerEvents", "controlHeightLG", "div", "paddingXS", "inkBarColor", "minHeight", "tabsToken", "cardPadding", "horizontalItemGutter", "zIndexPopupBase", "colorFillAlter", "round", "fontSizeLG", "colorPrimary", "horizontalItemMargin", "horizontalItemMarginRTL", "colorPrimaryHover", "colorPrimaryActive", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "rootClassName", "customSize", "<PERSON><PERSON><PERSON>", "centered", "indicatorSize", "otherProps", "customizePrefixCls", "getPrefixCls", "rootCls", "useCSSVarCls", "wrapCSSVar", "hashId", "cssVarCls", "editType", "CloseOutlined", "rootPrefixCls", "useSize", "mergedItems", "toArray", "useLegacyItems", "motionName", "mergedStyle", "mergedIndicator", "EllipsisOutlined", "transitionName"], "sourceRoot": ""}