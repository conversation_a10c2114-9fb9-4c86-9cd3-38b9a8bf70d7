/*! For license information please see component---src-pages-labs-tsx-c05b19297a0756c4ba40.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[439],{418:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4060:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},6729:function(e,t,n){n.r(t),n.d(t,{default:function(){return h}});var o=n(6540),a=n(5312),r=n(7677),l=n(418),c=n(955),s=n(9910),i=n(9644),d=n(4060),m=n(7213);const u=e=>{let{isOpen:t,labs:n,currentLab:a,onToggle:r,onSelectLab:l,isLoading:u=!1}=e;return t?o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-primary font-medium"},"Labs")),o.createElement(c.A,{title:"Close Sidebar"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(i.A,{strokeWidth:1.5,className:"h-6 w-6"})))),u&&o.createElement("div",{className:"p-4"},o.createElement(d.A,{className:"w-4 h-4 inline-block animate-spin"})),!u&&0===n.length&&o.createElement("div",{className:"p-2 mt-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},o.createElement(m.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No labs available. Please check back later."),o.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)] mt-4"},n.map((e=>o.createElement("div",{key:e.id,className:"relative"},o.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80 rounded "+((null==a?void 0:a.id)===e.id?"bg-accent":"bg-tertiary")}),o.createElement("div",{className:"group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary "+((null==a?void 0:a.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>l(e)},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm truncate"},e.title)))))))):o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"p-2 -ml-2"},o.createElement(c.A,{title:"Documentation"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(s.A,{strokeWidth:1.5,className:"h-6 w-6"})))))};var p=n(7260);var f=()=>o.createElement("div",{className:""},o.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"Using AutoGen Studio Teams in Python Code and REST API"),o.createElement(p.A,{className:"mb-6",message:"Prerequisites",description:o.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},o.createElement("li",null,"AutoGen Studio installed")),type:"info"}));const g=e=>{let{lab:t}=e;return"python-setup"===t.id?o.createElement(f,null):o.createElement("div",{className:"text-secondary"},"A Lab with the title ",o.createElement("strong",null,t.title)," is work in progress!")};var b=()=>{const{0:e,1:t}=(0,o.useState)(!1),{0:n,1:a}=(0,o.useState)([]),{0:c,1:s}=(0,o.useState)(null),{0:i,1:d}=(0,o.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("labsSidebar");return null===e||JSON.parse(e)}return!0}));return(0,o.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("labsSidebar",JSON.stringify(i))}),[i]),(0,o.useEffect)((()=>{!c&&n.length>0&&s(n[0])}),[n,c]),o.createElement("div",{className:"relative    flex h-full w-full"},o.createElement("div",{className:"absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out "+(i?"w-64":"w-12")},o.createElement(u,{isOpen:i,labs:n,currentLab:c,onToggle:()=>d(!i),onSelectLab:s,isLoading:e})),o.createElement("div",{className:"flex-1 transition-all max-w-5xl  -mr-6 duration-200 "+(i?"ml-64":"ml-12")},o.createElement("div",{className:"p-4 pt-2"},o.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},o.createElement("span",{className:"text-primary font-medium"},"Labs"),c&&o.createElement(o.Fragment,null,o.createElement(r.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-secondary"},c.title))),o.createElement("div",{className:"rounded border border-secondary border-dashed p-2 text-sm mb-4"},o.createElement(l.A,{className:"w-4 h-4 inline-block mr-2 -mt-1 text-secondary "})," ","Labs is designed to host experimental features for building and debugging multiagent applications."),c?o.createElement(g,{lab:c}):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"Select a lab from the sidebar to get started"))))};var h=e=>{let{data:t}=e;return o.createElement(a.A,{meta:t.site.siteMetadata,title:"Home",link:"/labs"},o.createElement("main",{style:{height:"100%"},className:" h-full "},o.createElement(b,null)))}},7213:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7260:function(e,t,n){n.d(t,{A:function(){return D}});var o=n(6540),a=n(8811),r=n(6029),l=n(7852),c=n(7541),s=n(7850),i=n(6942),d=n.n(i),m=n(754),u=n(2065),p=n(8719),f=n(682),g=n(2279),b=n(2187),h=n(5905),v=n(7358);const y=(e,t,n,o,a)=>({background:e,border:`${(0,b.zA)(o.lineWidth)} ${o.lineType} ${t}`,[`${a}-icon`]:{color:n}}),E=e=>{const{componentCls:t,motionDurationSlow:n,marginXS:o,marginSM:a,fontSize:r,fontSizeLG:l,lineHeight:c,borderRadiusLG:s,motionEaseInOutCirc:i,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:f}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:f,wordWrap:"break-word",borderRadius:s,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:r,lineHeight:c},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${i}, opacity ${n} ${i},\n        padding-top ${n} ${i}, padding-bottom ${n} ${i},\n        margin-bottom ${n} ${i}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:a,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:u,fontSize:l},[`${t}-description`]:{display:"block",color:m}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},x=e=>{const{componentCls:t,colorSuccess:n,colorSuccessBorder:o,colorSuccessBg:a,colorWarning:r,colorWarningBorder:l,colorWarningBg:c,colorError:s,colorErrorBorder:i,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[t]:{"&-success":y(a,o,n,e,t),"&-info":y(p,u,m,e,t),"&-warning":y(c,l,r,e,t),"&-error":Object.assign(Object.assign({},y(d,i,s,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},N=e=>{const{componentCls:t,iconCls:n,motionDurationMid:o,marginXS:a,fontSizeIcon:r,colorIcon:l,colorIconHover:c}=e;return{[t]:{"&-action":{marginInlineStart:a},[`${t}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:r,lineHeight:(0,b.zA)(r),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:l,transition:`color ${o}`,"&:hover":{color:c}}},"&-close-text":{color:l,transition:`color ${o}`,"&:hover":{color:c}}}}};var w=(0,v.OF)("Alert",(e=>[E(e),x(e),N(e)]),(e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}))),$=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]])}return n};const S={success:a.A,info:s.A,error:r.A,warning:c.A},A=e=>{const{icon:t,prefixCls:n,type:a}=e,r=S[a]||null;return t?(0,f.fx)(t,o.createElement("span",{className:`${n}-icon`},t),(()=>({className:d()(`${n}-icon`,t.props.className)}))):o.createElement(r,{className:`${n}-icon`})},k=e=>{const{isClosable:t,prefixCls:n,closeIcon:a,handleClose:r,ariaProps:c}=e,s=!0===a||void 0===a?o.createElement(l.A,null):a;return t?o.createElement("button",Object.assign({type:"button",onClick:r,className:`${n}-close-icon`,tabIndex:0},c),s):null},C=o.forwardRef(((e,t)=>{const{description:n,prefixCls:a,message:r,banner:l,className:c,rootClassName:s,style:i,onMouseEnter:f,onMouseLeave:b,onClick:h,afterClose:v,showIcon:y,closable:E,closeText:x,closeIcon:N,action:S,id:C}=e,I=$(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,M]=o.useState(!1);const j=o.useRef(null);o.useImperativeHandle(t,(()=>({nativeElement:j.current})));const{getPrefixCls:L,direction:z,closable:P,closeIcon:H,className:B,style:T}=(0,g.TP)("alert"),D=L("alert",a),[R,W,G]=w(D),q=t=>{var n;M(!0),null===(n=e.onClose)||void 0===n||n.call(e,t)},F=o.useMemo((()=>void 0!==e.type?e.type:l?"warning":"info"),[e.type,l]),X=o.useMemo((()=>!("object"!=typeof E||!E.closeIcon)||(!!x||("boolean"==typeof E?E:!1!==N&&null!=N||!!P))),[x,N,E,P]),J=!(!l||void 0!==y)||y,K=d()(D,`${D}-${F}`,{[`${D}-with-description`]:!!n,[`${D}-no-icon`]:!J,[`${D}-banner`]:!!l,[`${D}-rtl`]:"rtl"===z},B,c,s,G,W),U=(0,u.A)(I,{aria:!0,data:!0}),V=o.useMemo((()=>"object"==typeof E&&E.closeIcon?E.closeIcon:x||(void 0!==N?N:"object"==typeof P&&P.closeIcon?P.closeIcon:H)),[N,E,x,H]),Q=o.useMemo((()=>{const e=null!=E?E:P;if("object"==typeof e){const{closeIcon:t}=e;return $(e,["closeIcon"])}return{}}),[E,P]);return R(o.createElement(m.Ay,{visible:!O,motionName:`${D}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},((t,a)=>{let{className:l,style:c}=t;return o.createElement("div",Object.assign({id:C,ref:(0,p.K4)(j,a),"data-show":!O,className:d()(K,l),style:Object.assign(Object.assign(Object.assign({},T),i),c),onMouseEnter:f,onMouseLeave:b,onClick:h,role:"alert"},U),J?o.createElement(A,{description:n,icon:e.icon,prefixCls:D,type:F}):null,o.createElement("div",{className:`${D}-content`},r?o.createElement("div",{className:`${D}-message`},r):null,n?o.createElement("div",{className:`${D}-description`},n):null),S?o.createElement("div",{className:`${D}-action`},S):null,o.createElement(k,{isClosable:X,prefixCls:D,closeIcon:V,handleClose:q,ariaProps:Q}))})))}));var I=C,O=n(3029),M=n(2901),j=n(3954),L=n(2176),z=n(6822);var P=n(5501);let H=function(e){function t(){var e,n,o,a;return(0,O.A)(this,t),n=this,o=t,a=arguments,o=(0,j.A)(o),(e=(0,z.A)(n,(0,L.A)()?Reflect.construct(o,a||[],(0,j.A)(n).constructor):o.apply(n,a))).state={error:void 0,info:{componentStack:""}},e}return(0,P.A)(t,e),(0,M.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){const{message:e,description:t,id:n,children:a}=this.props,{error:r,info:l}=this.state,c=(null==l?void 0:l.componentStack)||null,s=void 0===e?(r||"").toString():e,i=void 0===t?c:t;return r?o.createElement(I,{id:n,type:"error",message:s,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},i)}):a}}])}(o.Component);var B=H;const T=I;T.ErrorBoundary=B;var D=T},7677:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}}]);
//# sourceMappingURL=component---src-pages-labs-tsx-c05b19297a0756c4ba40.js.map