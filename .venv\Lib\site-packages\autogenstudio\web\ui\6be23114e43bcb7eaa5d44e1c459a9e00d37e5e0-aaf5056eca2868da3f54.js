"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[376],{6161:function(e,t,n){n.d(t,{A:function(){return Pe}});var a=n(6540),o=n(7852),r=n(2318),i=n(8168),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},c=n(7064),d=function(e,t){return a.createElement(c.A,(0,i.A)({},e,{ref:t,icon:l}))};var s=a.forwardRef(d),u=n(6942),v=n.n(u),f=n(4467),p=n(9379),b=n(5544),m=n(2284),h=n(3986),g=n(2533),$=n(8430),k=(0,a.createContext)(null),y=n(436),A=n(8462),w=n(6956),x=n(8719),S=n(5371),_=function(e){var t=e.activeTabOffset,n=e.horizontal,o=e.rtl,r=e.indicator,i=void 0===r?{}:r,l=i.size,c=i.align,d=void 0===c?"center":c,s=(0,a.useState)(),u=(0,b.A)(s,2),v=u[0],f=u[1],p=(0,a.useRef)(),m=a.useCallback((function(e){return"function"==typeof l?l(e):"number"==typeof l?l:e}),[l]);function h(){S.A.cancel(p.current)}return(0,a.useEffect)((function(){var e={};if(t)if(n){e.width=m(t.width);var a=o?"right":"left";"start"===d&&(e[a]=t[a]),"center"===d&&(e[a]=t[a]+t.width/2,e.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(e[a]=t[a]+t.width,e.transform="translateX(-100%)")}else e.height=m(t.height),"start"===d&&(e.top=t.top),"center"===d&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===d&&(e.top=t.top+t.height,e.transform="translateY(-100%)");return h(),p.current=(0,S.A)((function(){f(e)})),h}),[t,n,o,d,m]),{style:v}},C={width:0,height:0,left:0,top:0};function E(e,t){var n=a.useRef(e),o=a.useState({}),r=(0,b.A)(o,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var z=Math.pow(.995,20);var P=n(981);function R(e){var t=(0,a.useState)(0),n=(0,b.A)(t,2),o=n[0],r=n[1],i=(0,a.useRef)(0),l=(0,a.useRef)();return l.current=e,(0,P.o)((function(){var e;null===(e=l.current)||void 0===e||e.call(l)}),[o]),function(){i.current===o&&(i.current+=1,r(i.current))}}var I={width:0,height:0,left:0,top:0,right:0};function T(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}function M(e){return String(e).replace(/"/g,"TABS_DQ")}function L(e,t,n,a){return!(!n||a||!1===e||void 0===e&&(!1===t||null===t))}var O=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.editable,r=e.locale,i=e.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null})),B=O;var D=a.forwardRef((function(e,t){var n,o=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l={};return"object"!==(0,m.A)(i)||a.isValidElement(i)?l.right=i:l=i,"right"===o&&(n=l.right),"left"===o&&(n=l.left),n?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null})),N=n(3497),j=n(8810),G=n(6928),H=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.id,r=e.tabs,l=e.locale,c=e.mobile,d=e.more,s=void 0===d?{}:d,u=e.style,p=e.className,m=e.editable,h=e.tabBarGutter,g=e.rtl,$=e.removeAriaLabel,k=e.onTabClick,y=e.getPopupContainer,A=e.popupClassName,w=(0,a.useState)(!1),x=(0,b.A)(w,2),S=x[0],_=x[1],C=(0,a.useState)(null),E=(0,b.A)(C,2),z=E[0],P=E[1],R=s.icon,I=void 0===R?"More":R,T="".concat(o,"-more-popup"),M="".concat(n,"-dropdown"),O=null!==z?"".concat(T,"-").concat(z):null,D=null==l?void 0:l.dropdownAriaLabel;var H=a.createElement(j.Ay,{onClick:function(e){var t=e.key,n=e.domEvent;k(t,n),_(!1)},prefixCls:"".concat(M,"-menu"),id:T,tabIndex:-1,role:"listbox","aria-activedescendant":O,selectedKeys:[z],"aria-label":void 0!==D?D:"expanded dropdown"},r.map((function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,l=e.label,c=L(t,r,m,n);return a.createElement(j.Dr,{key:i,id:"".concat(T,"-").concat(i),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(i),disabled:n},a.createElement("span",null,l),c&&a.createElement("button",{type:"button","aria-label":$||"remove",tabIndex:0,className:"".concat(M,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),function(e,t){e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:t,event:e})}(e,i)}},r||m.removeIcon||"×"))})));function X(e){for(var t=r.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===z}))||0,a=t.length,o=0;o<a;o+=1){var i=t[n=(n+e+a)%a];if(!i.disabled)return void P(i.key)}}(0,a.useEffect)((function(){var e=document.getElementById(O);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[z]),(0,a.useEffect)((function(){S||P(null)}),[S]);var W=(0,f.A)({},g?"marginRight":"marginLeft",h);r.length||(W.visibility="hidden",W.order=1);var K=v()((0,f.A)({},"".concat(M,"-rtl"),g)),q=c?null:a.createElement(N.A,(0,i.A)({prefixCls:M,overlay:H,visible:!!r.length&&S,onVisibleChange:_,overlayClassName:v()(K,A),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:y},s),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:W,"aria-haspopup":"listbox","aria-controls":T,id:"".concat(o,"-more"),"aria-expanded":S,onKeyDown:function(e){var t=e.which;if(S)switch(t){case G.A.UP:X(-1),e.preventDefault();break;case G.A.DOWN:X(1),e.preventDefault();break;case G.A.ESC:_(!1);break;case G.A.SPACE:case G.A.ENTER:null!==z&&k(z,e)}else[G.A.DOWN,G.A.SPACE,G.A.ENTER].includes(t)&&(_(!0),e.preventDefault())}},I));return a.createElement("div",{className:v()("".concat(n,"-nav-operations"),p),style:u,ref:t},q,a.createElement(B,{prefixCls:n,locale:l,editable:m}))})),X=a.memo(H,(function(e,t){return t.tabMoving})),W=function(e){var t=e.prefixCls,n=e.id,o=e.active,r=e.focus,i=e.tab,l=i.key,c=i.label,d=i.disabled,s=i.closeIcon,u=i.icon,p=e.closable,b=e.renderWrapper,m=e.removeAriaLabel,h=e.editable,g=e.onClick,$=e.onFocus,k=e.onBlur,y=e.onKeyDown,A=e.onMouseDown,w=e.onMouseUp,x=e.style,S=e.tabCount,_=e.currentPosition,C="".concat(t,"-tab"),E=L(p,s,h,d);function z(e){d||g(e)}var P=a.useMemo((function(){return u&&"string"==typeof c?a.createElement("span",null,c):c}),[c,u]),R=a.useRef(null);a.useEffect((function(){r&&R.current&&R.current.focus()}),[r]);var I=a.createElement("div",{key:l,"data-node-key":M(l),className:v()(C,(0,f.A)((0,f.A)((0,f.A)((0,f.A)({},"".concat(C,"-with-remove"),E),"".concat(C,"-active"),o),"".concat(C,"-disabled"),d),"".concat(C,"-focus"),r)),style:x,onClick:z},a.createElement("div",{ref:R,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(l),className:"".concat(C,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(l),"aria-disabled":d,tabIndex:d?null:o?0:-1,onClick:function(e){e.stopPropagation(),z(e)},onKeyDown:y,onMouseDown:A,onMouseUp:w,onFocus:$,onBlur:k},r&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(_," of ").concat(S)),u&&a.createElement("span",{className:"".concat(C,"-icon")},u),c&&P),E&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(C,"-remove"),onClick:function(e){var t;e.stopPropagation(),(t=e).preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:l,event:t})}},s||h.removeIcon||"×"));return b?b(I):I},K=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight,r=void 0===o?0:o;if(e.current){var i=e.current.getBoundingClientRect(),l=i.width,c=i.height;if(Math.abs(l-a)<1)return[l,c]}return[a,r]},q=function(e,t){return e[t?0:1]},F=a.forwardRef((function(e,t){var n=e.className,o=e.style,r=e.id,l=e.animated,c=e.activeKey,d=e.rtl,s=e.extra,u=e.editable,m=e.locale,h=e.tabPosition,g=e.tabBarGutter,$=e.children,S=e.onTabClick,P=e.onTabScroll,O=e.indicator,N=a.useContext(k),j=N.prefixCls,G=N.tabs,H=(0,a.useRef)(null),F=(0,a.useRef)(null),V=(0,a.useRef)(null),Y=(0,a.useRef)(null),U=(0,a.useRef)(null),Q=(0,a.useRef)(null),J=(0,a.useRef)(null),Z="top"===h||"bottom"===h,ee=E(0,(function(e,t){Z&&P&&P({direction:e>t?"left":"right"})})),te=(0,b.A)(ee,2),ne=te[0],ae=te[1],oe=E(0,(function(e,t){!Z&&P&&P({direction:e>t?"top":"bottom"})})),re=(0,b.A)(oe,2),ie=re[0],le=re[1],ce=(0,a.useState)([0,0]),de=(0,b.A)(ce,2),se=de[0],ue=de[1],ve=(0,a.useState)([0,0]),fe=(0,b.A)(ve,2),pe=fe[0],be=fe[1],me=(0,a.useState)([0,0]),he=(0,b.A)(me,2),ge=he[0],$e=he[1],ke=(0,a.useState)([0,0]),ye=(0,b.A)(ke,2),Ae=ye[0],we=ye[1],xe=function(e){var t=(0,a.useRef)([]),n=(0,a.useState)({}),o=(0,b.A)(n,2)[1],r=(0,a.useRef)("function"==typeof e?e():e),i=R((function(){var e=r.current;t.current.forEach((function(t){e=t(e)})),t.current=[],r.current=e,o({})}));return[r.current,function(e){t.current.push(e),i()}]}(new Map),Se=(0,b.A)(xe,2),_e=Se[0],Ce=Se[1],Ee=function(e,t,n){return(0,a.useMemo)((function(){for(var n,a=new Map,o=t.get(null===(n=e[0])||void 0===n?void 0:n.key)||C,r=o.left+o.width,i=0;i<e.length;i+=1){var l,c=e[i].key,d=t.get(c);d||(d=t.get(null===(l=e[i-1])||void 0===l?void 0:l.key)||C);var s=a.get(c)||(0,p.A)({},d);s.right=r-s.left-s.width,a.set(c,s)}return a}),[e.map((function(e){return e.key})).join("_"),t,n])}(G,_e,pe[0]),ze=q(se,Z),Pe=q(pe,Z),Re=q(ge,Z),Ie=q(Ae,Z),Te=Math.floor(ze)<Math.floor(Pe+Re),Me=Te?ze-Ie:ze-Re,Le="".concat(j,"-nav-operations-hidden"),Oe=0,Be=0;function De(e){return e<Oe?Oe:e>Be?Be:e}Z&&d?(Oe=0,Be=Math.max(0,Pe-Me)):(Oe=Math.min(0,Me-Pe),Be=0);var Ne=(0,a.useRef)(null),je=(0,a.useState)(),Ge=(0,b.A)(je,2),He=Ge[0],Xe=Ge[1];function We(){Xe(Date.now())}function Ke(){Ne.current&&clearTimeout(Ne.current)}!function(e,t){var n=(0,a.useState)(),o=(0,b.A)(n,2),r=o[0],i=o[1],l=(0,a.useState)(0),c=(0,b.A)(l,2),d=c[0],s=c[1],u=(0,a.useState)(0),v=(0,b.A)(u,2),f=v[0],p=v[1],m=(0,a.useState)(),h=(0,b.A)(m,2),g=h[0],$=h[1],k=(0,a.useRef)(),y=(0,a.useRef)(),A=(0,a.useRef)(null);A.current={onTouchStart:function(e){var t=e.touches[0],n=t.screenX,a=t.screenY;i({x:n,y:a}),window.clearInterval(k.current)},onTouchMove:function(e){if(r){var n=e.touches[0],a=n.screenX,o=n.screenY;i({x:a,y:o});var l=a-r.x,c=o-r.y;t(l,c);var u=Date.now();s(u),p(u-d),$({x:l,y:c})}},onTouchEnd:function(){if(r&&(i(null),$(null),g)){var e=g.x/f,n=g.y/f,a=Math.abs(e),o=Math.abs(n);if(Math.max(a,o)<.1)return;var l=e,c=n;k.current=window.setInterval((function(){Math.abs(l)<.01&&Math.abs(c)<.01?window.clearInterval(k.current):t(20*(l*=z),20*(c*=z))}),20)}},onWheel:function(e){var n=e.deltaX,a=e.deltaY,o=0,r=Math.abs(n),i=Math.abs(a);r===i?o="x"===y.current?n:a:r>i?(o=n,y.current="x"):(o=a,y.current="y"),t(-o,-o)&&e.preventDefault()}},a.useEffect((function(){function t(e){A.current.onTouchMove(e)}function n(e){A.current.onTouchEnd(e)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",n,{passive:!0}),e.current.addEventListener("touchstart",(function(e){A.current.onTouchStart(e)}),{passive:!0}),e.current.addEventListener("wheel",(function(e){A.current.onWheel(e)}),{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",n)}}),[])}(Y,(function(e,t){function n(e,t){e((function(e){return De(e+t)}))}return!!Te&&(Z?n(ae,e):n(le,t),Ke(),We(),!0)})),(0,a.useEffect)((function(){return Ke(),He&&(Ne.current=setTimeout((function(){Xe(0)}),100)),Ke}),[He]);var qe=function(e,t,n,o,r,i,l){var c,d,s,u=l.tabs,v=l.tabPosition,f=l.rtl;return["top","bottom"].includes(v)?(c="width",d=f?"right":"left",s=Math.abs(n)):(c="height",d="top",s=-n),(0,a.useMemo)((function(){if(!u.length)return[0,0];for(var n=u.length,a=n,o=0;o<n;o+=1){var r=e.get(u[o].key)||I;if(Math.floor(r[d]+r[c])>Math.floor(s+t)){a=o-1;break}}for(var i=0,l=n-1;l>=0;l-=1)if((e.get(u[l].key)||I)[d]<s){i=l+1;break}return i>=a?[0,0]:[i,a]}),[e,t,o,r,i,s,v,u.map((function(e){return e.key})).join("_"),f])}(Ee,Me,Z?ne:ie,Pe,Re,Ie,(0,p.A)((0,p.A)({},e),{},{tabs:G})),Fe=(0,b.A)(qe,2),Ve=Fe[0],Ye=Fe[1],Ue=(0,w.A)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=Ee.get(e)||{width:0,height:0,left:0,right:0,top:0};if(Z){var n=ne;d?t.right<ne?n=t.right:t.right+t.width>ne+Me&&(n=t.right+t.width-Me):t.left<-ne?n=-t.left:t.left+t.width>-ne+Me&&(n=-(t.left+t.width-Me)),le(0),ae(De(n))}else{var a=ie;t.top<-ie?a=-t.top:t.top+t.height>-ie+Me&&(a=-(t.top+t.height-Me)),ae(0),le(De(a))}})),Qe=(0,a.useState)(),Je=(0,b.A)(Qe,2),Ze=Je[0],et=Je[1],tt=(0,a.useState)(!1),nt=(0,b.A)(tt,2),at=nt[0],ot=nt[1],rt=G.filter((function(e){return!e.disabled})).map((function(e){return e.key})),it=function(e){var t=rt.indexOf(Ze||c),n=rt.length,a=rt[(t+e+n)%n];et(a)},lt=function(e){var t=e.code,n=d&&Z,a=rt[0],o=rt[rt.length-1];switch(t){case"ArrowLeft":Z&&it(n?1:-1);break;case"ArrowRight":Z&&it(n?-1:1);break;case"ArrowUp":e.preventDefault(),Z||it(-1);break;case"ArrowDown":e.preventDefault(),Z||it(1);break;case"Home":e.preventDefault(),et(a);break;case"End":e.preventDefault(),et(o);break;case"Enter":case"Space":e.preventDefault(),S(Ze,e);break;case"Backspace":case"Delete":var r=rt.indexOf(Ze),i=G.find((function(e){return e.key===Ze}));L(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,u,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),u.onEdit("remove",{key:Ze,event:e}),r===rt.length-1?it(-1):it(1))}},ct={};Z?ct[d?"marginRight":"marginLeft"]=g:ct.marginTop=g;var dt=G.map((function(e,t){var n=e.key;return a.createElement(W,{id:r,prefixCls:j,key:n,tab:e,style:0===t?void 0:ct,closable:e.closable,editable:u,active:n===c,focus:n===Ze,renderWrapper:$,removeAriaLabel:null==m?void 0:m.removeAriaLabel,tabCount:rt.length,currentPosition:t+1,onClick:function(e){S(n,e)},onKeyDown:lt,onFocus:function(){at||et(n),Ue(n),We(),Y.current&&(d||(Y.current.scrollLeft=0),Y.current.scrollTop=0)},onBlur:function(){et(void 0)},onMouseDown:function(){ot(!0)},onMouseUp:function(){ot(!1)}})})),st=function(){return Ce((function(){var e,t=new Map,n=null===(e=U.current)||void 0===e?void 0:e.getBoundingClientRect();return G.forEach((function(e){var a,o=e.key,r=null===(a=U.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(M(o),'"]'));if(r){var i=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,d=i.left,s=i.top;return Math.abs(l-n)<1?[l,c,d-t.left,s-t.top]:[n,a,r,o]}(r,n),l=(0,b.A)(i,4),c=l[0],d=l[1],s=l[2],u=l[3];t.set(o,{width:c,height:d,left:s,top:u})}})),t}))};(0,a.useEffect)((function(){st()}),[G.map((function(e){return e.key})).join("_")]);var ut=R((function(){var e=K(H),t=K(F),n=K(V);ue([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=K(J);$e(a);var o=K(Q);we(o);var r=K(U);be([r[0]-a[0],r[1]-a[1]]),st()})),vt=G.slice(0,Ve),ft=G.slice(Ye+1),pt=[].concat((0,y.A)(vt),(0,y.A)(ft)),bt=Ee.get(c),mt=_({activeTabOffset:bt,horizontal:Z,indicator:O,rtl:d}).style;(0,a.useEffect)((function(){Ue()}),[c,Oe,Be,T(bt),T(Ee),Z]),(0,a.useEffect)((function(){ut()}),[d]);var ht,gt,$t,kt,yt=!!pt.length,At="".concat(j,"-nav-wrap");return Z?d?(gt=ne>0,ht=ne!==Be):(ht=ne<0,gt=ne!==Oe):($t=ie<0,kt=ie!==Oe),a.createElement(A.A,{onResize:ut},a.createElement("div",{ref:(0,x.xK)(t,H),role:"tablist","aria-orientation":Z?"horizontal":"vertical",className:v()("".concat(j,"-nav"),n),style:o,onKeyDown:function(){We()}},a.createElement(D,{ref:F,position:"left",extra:s,prefixCls:j}),a.createElement(A.A,{onResize:ut},a.createElement("div",{className:v()(At,(0,f.A)((0,f.A)((0,f.A)((0,f.A)({},"".concat(At,"-ping-left"),ht),"".concat(At,"-ping-right"),gt),"".concat(At,"-ping-top"),$t),"".concat(At,"-ping-bottom"),kt)),ref:Y},a.createElement(A.A,{onResize:ut},a.createElement("div",{ref:U,className:"".concat(j,"-nav-list"),style:{transform:"translate(".concat(ne,"px, ").concat(ie,"px)"),transition:He?"none":void 0}},dt,a.createElement(B,{ref:J,prefixCls:j,locale:m,editable:u,style:(0,p.A)((0,p.A)({},0===dt.length?void 0:ct),{},{visibility:yt?"hidden":null})}),a.createElement("div",{className:v()("".concat(j,"-ink-bar"),(0,f.A)({},"".concat(j,"-ink-bar-animated"),l.inkBar)),style:mt}))))),a.createElement(X,(0,i.A)({},e,{removeAriaLabel:null==m?void 0:m.removeAriaLabel,ref:Q,prefixCls:j,tabs:pt,className:!yt&&Le,tabMoving:!!He})),a.createElement(D,{ref:V,position:"right",extra:s,prefixCls:j})))})),V=F,Y=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.id,l=e.active,c=e.tabKey,d=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:v()(n,l&&"".concat(n,"-active"),o),ref:t},d)}));var U=Y,Q=["renderTabBar"],J=["label","key"];var Z=function(e){var t=e.renderTabBar,n=(0,h.A)(e,Q),o=a.useContext(k).tabs;return t?t((0,p.A)((0,p.A)({},n),{},{panes:o.map((function(e){var t=e.label,n=e.key,o=(0,h.A)(e,J);return a.createElement(U,(0,i.A)({tab:t,key:n,tabKey:n},o))}))}),V):a.createElement(V,n)},ee=n(754),te=["key","forceRender","style","className","destroyInactiveTabPane"],ne=function(e){var t=e.id,n=e.activeKey,o=e.animated,r=e.tabPosition,l=e.destroyInactiveTabPane,c=a.useContext(k),d=c.prefixCls,s=c.tabs,u=o.tabPane,b="".concat(d,"-tabpane");return a.createElement("div",{className:v()("".concat(d,"-content-holder"))},a.createElement("div",{className:v()("".concat(d,"-content"),"".concat(d,"-content-").concat(r),(0,f.A)({},"".concat(d,"-content-animated"),u))},s.map((function(e){var r=e.key,c=e.forceRender,d=e.style,s=e.className,f=e.destroyInactiveTabPane,m=(0,h.A)(e,te),g=r===n;return a.createElement(ee.Ay,(0,i.A)({key:r,visible:g,forceRender:c,removeOnLeave:!(!l&&!f),leavedClassName:"".concat(b,"-hidden")},o.tabPaneMotion),(function(e,n){var o=e.style,l=e.className;return a.createElement(U,(0,i.A)({},m,{prefixCls:b,id:t,tabKey:r,animated:u,active:g,style:(0,p.A)((0,p.A)({},d),o),className:v()(s,l),ref:n}))}))}))))};n(8210);var ae=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],oe=0,re=a.forwardRef((function(e,t){var n=e.id,o=e.prefixCls,r=void 0===o?"rc-tabs":o,l=e.className,c=e.items,d=e.direction,s=e.activeKey,u=e.defaultActiveKey,y=e.editable,A=e.animated,w=e.tabPosition,x=void 0===w?"top":w,S=e.tabBarGutter,_=e.tabBarStyle,C=e.tabBarExtraContent,E=e.locale,z=e.more,P=e.destroyInactiveTabPane,R=e.renderTabBar,I=e.onChange,T=e.onTabClick,M=e.onTabScroll,L=e.getPopupContainer,O=e.popupClassName,B=e.indicator,D=(0,h.A)(e,ae),N=a.useMemo((function(){return(c||[]).filter((function(e){return e&&"object"===(0,m.A)(e)&&"key"in e}))}),[c]),j="rtl"===d,G=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,p.A)({inkBar:!0},"object"===(0,m.A)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(A),H=(0,a.useState)(!1),X=(0,b.A)(H,2),W=X[0],K=X[1];(0,a.useEffect)((function(){K((0,$.A)())}),[]);var q=(0,g.A)((function(){var e;return null===(e=N[0])||void 0===e?void 0:e.key}),{value:s,defaultValue:u}),F=(0,b.A)(q,2),V=F[0],Y=F[1],U=(0,a.useState)((function(){return N.findIndex((function(e){return e.key===V}))})),Q=(0,b.A)(U,2),J=Q[0],ee=Q[1];(0,a.useEffect)((function(){var e,t=N.findIndex((function(e){return e.key===V}));-1===t&&(t=Math.max(0,Math.min(J,N.length-1)),Y(null===(e=N[t])||void 0===e?void 0:e.key));ee(t)}),[N.map((function(e){return e.key})).join("_"),V,J]);var te=(0,g.A)(null,{value:n}),re=(0,b.A)(te,2),ie=re[0],le=re[1];(0,a.useEffect)((function(){n||(le("rc-tabs-".concat(oe)),oe+=1)}),[]);var ce={id:ie,activeKey:V,animated:G,tabPosition:x,rtl:j,mobile:W},de=(0,p.A)((0,p.A)({},ce),{},{editable:y,locale:E,more:z,tabBarGutter:S,onTabClick:function(e,t){null==T||T(e,t);var n=e!==V;Y(e),n&&(null==I||I(e))},onTabScroll:M,extra:C,style:_,panes:null,getPopupContainer:L,popupClassName:O,indicator:B});return a.createElement(k.Provider,{value:{tabs:N,prefixCls:r}},a.createElement("div",(0,i.A)({ref:t,id:n,className:v()(r,"".concat(r,"-").concat(x),(0,f.A)((0,f.A)((0,f.A)({},"".concat(r,"-mobile"),W),"".concat(r,"-editable"),y),"".concat(r,"-rtl"),j),l)},D),a.createElement(Z,(0,i.A)({},de,{renderTabBar:R})),a.createElement(ne,(0,i.A)({destroyInactiveTabPane:P},ce,{animated:G}))))}));var ie=re,le=n(2279),ce=n(934),de=n(829),se=n(3723);const ue={motionAppear:!1,motionEnter:!0,motionLeave:!0};var ve=n(2546),fe=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};var pe=n(2187),be=n(5905),me=n(7358),he=n(4277),ge=n(3561);var $e=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,ge._j)(e,"slide-up"),(0,ge._j)(e,"slide-down")]]};const ke=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${(0,pe.zA)(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-tab-focus`]:Object.assign({},(0,be.jk)(e,-3)),[`${t}-ink-bar`]:{visibility:"hidden"},[`& ${t}-tab${t}-tab-focus ${t}-tab-btn`]:{outline:"none"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,pe.zA)(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,pe.zA)(e.borderRadiusLG)} ${(0,pe.zA)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,pe.zA)(e.borderRadiusLG)} ${(0,pe.zA)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,pe.zA)(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,pe.zA)(e.borderRadiusLG)} 0 0 ${(0,pe.zA)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,pe.zA)(e.borderRadiusLG)} ${(0,pe.zA)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ye=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,be.dF)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,pe.zA)(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},be.L9),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,pe.zA)(e.paddingXXS)} ${(0,pe.zA)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Ae=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,pe.zA)(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,\n        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,pe.zA)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,pe.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,pe.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},we=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:r}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:o,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r,fontSize:e.titleFontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,pe.zA)(e.borderRadius)} ${(0,pe.zA)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,pe.zA)(e.borderRadius)} ${(0,pe.zA)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,pe.zA)(e.borderRadius)} ${(0,pe.zA)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,pe.zA)(e.borderRadius)} 0 0 ${(0,pe.zA)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a}}}}}},xe=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,be.K8)(e)),"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn`]:Object.assign({},(0,be.jk)(e)),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:r}}}},Se=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e,i=`${t}-rtl`;return{[i]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,pe.zA)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,pe.zA)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,pe.zA)(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},_e=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,be.dF)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:o},padding:(0,pe.zA)(e.paddingXS),background:"transparent",border:`${(0,pe.zA)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,pe.zA)(e.borderRadiusLG)} ${(0,pe.zA)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,be.K8)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),xe(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,be.K8)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}};var Ce=(0,me.OF)("Tabs",(e=>{const t=(0,he.oX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,pe.zA)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,pe.zA)(e.horizontalItemGutter)}`});return[we(t),Se(t),Ae(t),ye(t),ke(t),_e(t),$e(t)]}),(e=>{const t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${1.5*e.paddingXXS}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${1.5*e.paddingXXS}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));var Ee=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const ze=e=>{var t,n,i,l,c,d,u,f,p,b,m;const{type:h,className:g,rootClassName:$,size:k,onEdit:y,hideAdd:A,centered:w,addIcon:x,removeIcon:S,moreIcon:_,more:C,popupClassName:E,children:z,items:P,animated:R,style:I,indicatorSize:T,indicator:M}=e,L=Ee(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:O}=L,{direction:B,tabs:D,getPrefixCls:N,getPopupContainer:j}=a.useContext(le.QO),G=N("tabs",O),H=(0,ce.A)(G),[X,W,K]=Ce(G,H);let q;"editable-card"===h&&(q={onEdit:(e,t)=>{let{key:n,event:a}=t;null==y||y("add"===e?a:n,e)},removeIcon:null!==(t=null!=S?S:null==D?void 0:D.removeIcon)&&void 0!==t?t:a.createElement(o.A,null),addIcon:(null!=x?x:null==D?void 0:D.addIcon)||a.createElement(s,null),showAdd:!0!==A});const F=N();const V=(0,de.A)(k),Y=function(e,t){return e||function(e){return e.filter((e=>e))}((0,ve.A)(t).map((e=>{if(a.isValidElement(e)){const{key:t,props:n}=e,a=n||{},{tab:o}=a,r=fe(a,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:o})}return null})))}(P,z),U=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{}),t.tabPane&&(t.tabPaneMotion=Object.assign(Object.assign({},ue),{motionName:(0,se.b)(e,"switch")})),t}(G,R),Q=Object.assign(Object.assign({},null==D?void 0:D.style),I),J={align:null!==(n=null==M?void 0:M.align)&&void 0!==n?n:null===(i=null==D?void 0:D.indicator)||void 0===i?void 0:i.align,size:null!==(u=null!==(c=null!==(l=null==M?void 0:M.size)&&void 0!==l?l:T)&&void 0!==c?c:null===(d=null==D?void 0:D.indicator)||void 0===d?void 0:d.size)&&void 0!==u?u:null==D?void 0:D.indicatorSize};return X(a.createElement(ie,Object.assign({direction:B,getPopupContainer:j},L,{items:Y,className:v()({[`${G}-${V}`]:V,[`${G}-card`]:["card","editable-card"].includes(h),[`${G}-editable-card`]:"editable-card"===h,[`${G}-centered`]:w},null==D?void 0:D.className,g,$,W,K,H),popupClassName:v()(E,W,K,H),style:Q,editable:q,more:Object.assign({icon:null!==(m=null!==(b=null!==(p=null===(f=null==D?void 0:D.more)||void 0===f?void 0:f.icon)&&void 0!==p?p:null==D?void 0:D.moreIcon)&&void 0!==b?b:_)&&void 0!==m?m:a.createElement(r.A,null),transitionName:`${F}-slide-up`},C),prefixCls:G,animated:U,indicator:J})))};ze.TabPane=()=>null;var Pe=ze}}]);
//# sourceMappingURL=6be23114e43bcb7eaa5d44e1c459a9e00d37e5e0-aaf5056eca2868da3f54.js.map