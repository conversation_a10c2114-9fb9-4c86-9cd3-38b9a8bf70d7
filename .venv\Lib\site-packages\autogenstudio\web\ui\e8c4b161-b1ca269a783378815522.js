"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[845],{1133:function(e,n,t){t.d(n,{Gc:function(){return Bn},Ln:function(){return jn},VH:function(){return ie},VS:function(){return Gn},_0:function(){return W},ck:function(){return $n},fM:function(){return Tn},h7:function(){return Ee},of:function(){return ut},rV:function(){return Zn},tE:function(){return We}});var o=t(4848),r=t(6540),i=t(6100),s=t(2275),a=t(4371),d=t(3973),l=t(961);const c=(0,r.createContext)(null),u=c.Provider,g=s.xc.error001();function p(e,n){const t=(0,r.useContext)(c);if(null===t)throw new Error(g);return(0,a.n)(t,e,n)}function m(){const e=(0,r.useContext)(c);if(null===e)throw new Error(g);return(0,r.useMemo)((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe})),[e])}const h={display:"none"},f={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},S="react-flow__node-desc",y="react-flow__edge-desc",b=e=>e.ariaLiveMessage;function C({rfId:e}){const n=p(b);return(0,o.jsx)("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:f,children:n})}function x({rfId:e,disableKeyboardA11y:n}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{id:`${S}-${e}`,style:h,children:["Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "]}),(0,o.jsx)("div",{id:`${y}-${e}`,style:h,children:"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."}),!n&&(0,o.jsx)(C,{rfId:e})]})}const v=e=>e.userSelectionActive?"none":"all",w=(0,r.forwardRef)((({position:e="top-left",children:n,className:t,style:r,...s},a)=>{const d=p(v),l=`${e}`.split("-");return(0,o.jsx)("div",{className:(0,i.A)(["react-flow__panel",t,...l]),style:{...r,pointerEvents:d},ref:a,...s,children:n})}));function E({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:(0,o.jsx)(w,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,o.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}w.displayName="Panel";const k=e=>{const n=[],t=[];for(const[,o]of e.nodeLookup)o.selected&&n.push(o.internals.userNode);for(const[,o]of e.edgeLookup)o.selected&&t.push(o);return{selectedNodes:n,selectedEdges:t}},N=e=>e.id;function M(e,n){return(0,d.x)(e.selectedNodes.map(N),n.selectedNodes.map(N))&&(0,d.x)(e.selectedEdges.map(N),n.selectedEdges.map(N))}function P({onSelectionChange:e}){const n=m(),{selectedNodes:t,selectedEdges:o}=p(k,M);return(0,r.useEffect)((()=>{const r={nodes:t,edges:o};e?.(r),n.getState().onSelectionChangeHandlers.forEach((e=>e(r)))}),[t,o,e]),null}const D=e=>!!e.onSelectionChangeHandlers;function O({onSelectionChange:e}){const n=p(D);return e||n?(0,o.jsx)(P,{onSelectionChange:e}):null}const R=[0,0],I={x:0,y:0,zoom:1},A=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","rfId"],L=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),j={translateExtent:s.ZO,nodeOrigin:R,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function z(e){const{setNodes:n,setEdges:t,setMinZoom:o,setMaxZoom:i,setTranslateExtent:s,setNodeExtent:a,reset:l,setDefaultNodesAndEdges:c,setPaneClickDistance:u}=p(L,d.x),g=m();(0,r.useEffect)((()=>(c(e.defaultNodes,e.defaultEdges),()=>{h.current=j,l()})),[]);const h=(0,r.useRef)(j);return(0,r.useEffect)((()=>{for(const r of A){const d=e[r];d!==h.current[r]&&(void 0!==e[r]&&("nodes"===r?n(d):"edges"===r?t(d):"minZoom"===r?o(d):"maxZoom"===r?i(d):"translateExtent"===r?s(d):"nodeExtent"===r?a(d):"paneClickDistance"===r?u(d):"fitView"===r?g.setState({fitViewOnInit:d}):"fitViewOptions"===r?g.setState({fitViewOnInitOptions:d}):g.setState({[r]:d})))}h.current=e}),A.map((n=>e[n]))),null}function _(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}const B="undefined"!=typeof document?document:null;function V(e=null,n={target:B,actInsideInputWithModifier:!0}){const[t,o]=(0,r.useState)(!1),i=(0,r.useRef)(!1),a=(0,r.useRef)(new Set([])),[d,l]=(0,r.useMemo)((()=>{if(null!==e){const n=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.replace("+","\n").replace("\n\n","\n+").split("\n"))),t=n.reduce(((e,n)=>e.concat(...n)),[]);return[n,t]}return[[],[]]}),[e]);return(0,r.useEffect)((()=>{const t=n?.target||B;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&(0,s.v5)(e))return!1;const t=$(e.code,l);a.current.add(e[t]),Z(d,a.current,!1)&&(e.preventDefault(),o(!0))},r=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&(0,s.v5)(e))return!1;const t=$(e.code,l);Z(d,a.current,!0)?(o(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),o(!1)};return t?.addEventListener("keydown",e),t?.addEventListener("keyup",r),window.addEventListener("blur",c),window.addEventListener("contextmenu",c),()=>{t?.removeEventListener("keydown",e),t?.removeEventListener("keyup",r),window.removeEventListener("blur",c),window.removeEventListener("contextmenu",c)}}}),[e,o]),t}function Z(e,n,t){return e.filter((e=>t||e.length===n.size)).some((e=>e.every((e=>n.has(e)))))}function $(e,n){return n.includes(e)?"code":"key"}const T=()=>{const e=m();return(0,r.useMemo)((()=>({zoomIn:n=>{const{panZoom:t}=e.getState();return t?t.scaleBy(1.2,{duration:n?.duration}):Promise.resolve(!1)},zoomOut:n=>{const{panZoom:t}=e.getState();return t?t.scaleBy(1/1.2,{duration:n?.duration}):Promise.resolve(!1)},zoomTo:(n,t)=>{const{panZoom:o}=e.getState();return o?o.scaleTo(n,{duration:t?.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(n,t)=>{const{transform:[o,r,i],panZoom:s}=e.getState();return s?(await s.setViewport({x:n.x??o,y:n.y??r,zoom:n.zoom??i},{duration:t?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{const[n,t,o]=e.getState().transform;return{x:n,y:t,zoom:o}},fitView:n=>{const{nodeLookup:t,minZoom:o,maxZoom:r,panZoom:i,domNode:a}=e.getState();if(!i||!a)return Promise.resolve(!1);const d=(0,s.YV)(t,n),{width:l,height:c}=(0,s.Eo)(a);return(0,s.Pr)({nodes:d,width:l,height:c,minZoom:o,maxZoom:r,panZoom:i},n)},setCenter:async(n,t,o)=>{const{width:r,height:i,maxZoom:s,panZoom:a}=e.getState(),d=void 0!==o?.zoom?o.zoom:s,l=r/2-n*d,c=i/2-t*d;return a?(await a.setViewport({x:l,y:c,zoom:d},{duration:o?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},fitBounds:async(n,t)=>{const{width:o,height:r,minZoom:i,maxZoom:a,panZoom:d}=e.getState(),l=(0,s.R4)(n,o,r,i,a,t?.padding??.1);return d?(await d.setViewport(l,{duration:t?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(n,t={})=>{const{transform:o,snapGrid:r,snapToGrid:i,domNode:a}=e.getState();if(!a)return n;const{x:d,y:l}=a.getBoundingClientRect(),c={x:n.x-d,y:n.y-l},u=t.snapGrid??r,g=t.snapToGrid??i;return(0,s.Ff)(c,o,g,u)},flowToScreenPosition:n=>{const{transform:t,domNode:o}=e.getState();if(!o)return n;const{x:r,y:i}=o.getBoundingClientRect(),a=(0,s.zj)(n,t);return{x:a.x+r,y:a.y+i}}})),[])};function X(e,n){const t=[],o=new Map,r=[];for(const i of e)if("add"!==i.type)if("remove"===i.type||"replace"===i.type)o.set(i.id,[i]);else{const e=o.get(i.id);e?e.push(i):o.set(i.id,[i])}else r.push(i);for(const i of n){const e=o.get(i.id);if(!e){t.push(i);continue}if("remove"===e[0].type)continue;if("replace"===e[0].type){t.push({...e[0].item});continue}const n={...i};for(const t of e)H(t,n);t.push(n)}return r.length&&r.forEach((e=>{void 0!==e.index?t.splice(e.index,0,{...e.item}):t.push({...e.item})})),t}function H(e,n){switch(e.type){case"select":n.selected=e.selected;break;case"position":void 0!==e.position&&(n.position=e.position),void 0!==e.dragging&&(n.dragging=e.dragging);break;case"dimensions":void 0!==e.dimensions&&(n.measured??={},n.measured.width=e.dimensions.width,n.measured.height=e.dimensions.height,e.setAttributes&&(n.width=e.dimensions.width,n.height=e.dimensions.height)),"boolean"==typeof e.resizing&&(n.resizing=e.resizing)}}function W(e,n){return X(e,n)}function F(e,n){return X(e,n)}function K(e,n){return{id:e,type:"select",selected:n}}function Y(e,n=new Set,t=!1){const o=[];for(const[r,i]of e){const e=n.has(r);void 0===i.selected&&!e||i.selected===e||(t&&(i.selected=e),o.push(K(i.id,e)))}return o}function G({items:e=[],lookup:n}){const t=[],o=new Map(e.map((e=>[e.id,e])));for(const[r,i]of e.entries()){const e=n.get(i.id),o=e?.internals?.userNode??e;void 0!==o&&o!==i&&t.push({id:i.id,item:i,type:"replace"}),void 0===o&&t.push({item:i,type:"add",index:r})}for(const[r]of n){void 0===o.get(r)&&t.push({id:r,type:"remove"})}return t}function U(e){return{id:e.id,type:"remove"}}const Q=e=>(0,s.oB)(e),q=e=>(0,s.b$)(e);function J(e){return(0,r.forwardRef)(e)}const ee="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function ne(e){const[n,t]=(0,r.useState)(BigInt(0)),[o]=(0,r.useState)((()=>function(e){let n=[];return{get:()=>n,reset:()=>{n=[]},push:t=>{n.push(t),e()}}}((()=>t((e=>e+BigInt(1)))))));return ee((()=>{const n=o.get();n.length&&(e(n),o.reset())}),[n]),o}const te=(0,r.createContext)(null);function oe({children:e}){const n=m(),t=ne((0,r.useCallback)((e=>{const{nodes:t=[],setNodes:o,hasDefaultNodes:r,onNodesChange:i,nodeLookup:s}=n.getState();let a=t;for(const n of e)a="function"==typeof n?n(a):n;r?o(a):i&&i(G({items:a,lookup:s}))}),[])),i=ne((0,r.useCallback)((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i,edgeLookup:s}=n.getState();let a=t;for(const n of e)a="function"==typeof n?n(a):n;r?o(a):i&&i(G({items:a,lookup:s}))}),[])),s=(0,r.useMemo)((()=>({nodeQueue:t,edgeQueue:i})),[]);return(0,o.jsx)(te.Provider,{value:s,children:e})}const re=e=>!!e.panZoom;function ie(){const e=T(),n=m(),t=function(){const e=(0,r.useContext)(te);if(!e)throw new Error("useBatchContext must be used within a BatchProvider");return e}(),o=p(re),i=(0,r.useMemo)((()=>{const e=e=>n.getState().nodeLookup.get(e),o=e=>{t.nodeQueue.push(e)},r=e=>{t.edgeQueue.push(e)},i=e=>{const{nodeLookup:t,nodeOrigin:o}=n.getState(),r=Q(e)?e:t.get(e.id),i=r.parentId?(0,s.us)(r.position,r.measured,r.parentId,t,o):r.position,a={...r,position:i,width:r.measured?.width??r.width,height:r.measured?.height??r.height};return(0,s.kM)(a)},a=(e,n,t={replace:!1})=>{o((o=>o.map((o=>{if(o.id===e){const e="function"==typeof n?n(o):n;return t.replace&&Q(e)?e:{...o,...e}}return o}))))},d=(e,n,t={replace:!1})=>{r((o=>o.map((o=>{if(o.id===e){const e="function"==typeof n?n(o):n;return t.replace&&q(e)?e:{...o,...e}}return o}))))};return{getNodes:()=>n.getState().nodes.map((e=>({...e}))),getNode:n=>e(n)?.internals.userNode,getInternalNode:e,getEdges:()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))},getEdge:e=>n.getState().edgeLookup.get(e),setNodes:o,setEdges:r,addNodes:e=>{const n=Array.isArray(e)?e:[e];t.nodeQueue.push((e=>[...e,...n]))},addEdges:e=>{const n=Array.isArray(e)?e:[e];t.edgeQueue.push((e=>[...e,...n]))},toObject:()=>{const{nodes:e=[],edges:t=[],transform:o}=n.getState(),[r,i,s]=o;return{nodes:e.map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:s}}},deleteElements:async({nodes:e=[],edges:t=[]})=>{const{nodes:o,edges:r,onNodesDelete:i,onEdgesDelete:a,triggerNodeChanges:d,triggerEdgeChanges:l,onDelete:c,onBeforeDelete:u}=n.getState(),{nodes:g,edges:p}=await(0,s.Tq)({nodesToRemove:e,edgesToRemove:t,nodes:o,edges:r,onBeforeDelete:u}),m=p.length>0,h=g.length>0;if(m){const e=p.map(U);a?.(p),l(e)}if(h){const e=g.map(U);i?.(g),d(e)}return(h||m)&&c?.({nodes:g,edges:p}),{deletedNodes:g,deletedEdges:p}},getIntersectingNodes:(e,t=!0,o)=>{const r=(0,s.mW)(e),a=r?e:i(e),d=void 0!==o;return a?(o||n.getState().nodes).filter((o=>{const i=n.getState().nodeLookup.get(o.id);if(i&&!r&&(o.id===e.id||!i.internals.positionAbsolute))return!1;const l=(0,s.kM)(d?o:i),c=(0,s.X6)(l,a);return t&&c>0||c>=a.width*a.height})):[]},isNodeIntersecting:(e,n,t=!0)=>{const o=(0,s.mW)(e)?e:i(e);if(!o)return!1;const r=(0,s.X6)(o,n);return t&&r>0||r>=o.width*o.height},updateNode:a,updateNodeData:(e,n,t={replace:!1})=>{a(e,(e=>{const o="function"==typeof n?n(e):n;return t.replace?{...e,data:o}:{...e,data:{...e.data,...o}}}),t)},updateEdge:d,updateEdgeData:(e,n,t={replace:!1})=>{d(e,(e=>{const o="function"==typeof n?n(e):n;return t.replace?{...e,data:o}:{...e,data:{...e.data,...o}}}),t)},getNodesBounds:e=>{const{nodeLookup:t,nodeOrigin:o}=n.getState();return(0,s.Jo)(e,{nodeLookup:t,nodeOrigin:o})},getHandleConnections:({type:e,id:t,nodeId:o})=>Array.from(n.getState().connectionLookup.get(`${o}-${e}${t?`-${t}`:""}`)?.values()??[]),getNodeConnections:({type:e,handleId:t,nodeId:o})=>Array.from(n.getState().connectionLookup.get(`${o}${e?t?`-${e}-${t}`:`-${e}`:""}`)?.values()??[])}}),[]);return(0,r.useMemo)((()=>({...i,...e,viewportInitialized:o})),[o])}const se=e=>e.selected,ae={actInsideInputWithModifier:!1},de="undefined"!=typeof window?window:void 0;const le={position:"absolute",width:"100%",height:"100%",top:0,left:0},ce=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function ue({onPaneContextMenu:e,zoomOnScroll:n=!0,zoomOnPinch:t=!0,panOnScroll:i=!1,panOnScrollSpeed:a=.5,panOnScrollMode:l=s.ny.Free,zoomOnDoubleClick:c=!0,panOnDrag:u=!0,defaultViewport:g,translateExtent:h,minZoom:f,maxZoom:S,zoomActivationKeyCode:y,preventScrolling:b=!0,children:C,noWheelClassName:x,noPanClassName:v,onViewportChange:w,isControlledViewport:E,paneClickDistance:k}){const N=m(),M=(0,r.useRef)(null),{userSelectionActive:P,lib:D}=p(ce,d.x),O=V(y),R=(0,r.useRef)();!function(e){const n=m();(0,r.useEffect)((()=>{const t=()=>{if(!e.current)return!1;const t=(0,s.Eo)(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",s.xc.error004()),n.setState({width:t.width||500,height:t.height||500})};if(e.current){t(),window.addEventListener("resize",t);const n=new ResizeObserver((()=>t()));return n.observe(e.current),()=>{window.removeEventListener("resize",t),n&&e.current&&n.unobserve(e.current)}}}),[])}(M);const I=(0,r.useCallback)((e=>{w?.({x:e[0],y:e[1],zoom:e[2]}),E||N.setState({transform:e})}),[w,E]);return(0,r.useEffect)((()=>{if(M.current){R.current=(0,s.kO)({domNode:M.current,minZoom:f,maxZoom:S,translateExtent:h,viewport:g,paneClickDistance:k,onDraggingChange:e=>N.setState({paneDragging:e}),onPanZoomStart:(e,n)=>{const{onViewportChangeStart:t,onMoveStart:o}=N.getState();o?.(e,n),t?.(n)},onPanZoom:(e,n)=>{const{onViewportChange:t,onMove:o}=N.getState();o?.(e,n),t?.(n)},onPanZoomEnd:(e,n)=>{const{onViewportChangeEnd:t,onMoveEnd:o}=N.getState();o?.(e,n),t?.(n)}});const{x:e,y:n,zoom:t}=R.current.getViewport();return N.setState({panZoom:R.current,transform:[e,n,t],domNode:M.current.closest(".react-flow")}),()=>{R.current?.destroy()}}}),[]),(0,r.useEffect)((()=>{R.current?.update({onPaneContextMenu:e,zoomOnScroll:n,zoomOnPinch:t,panOnScroll:i,panOnScrollSpeed:a,panOnScrollMode:l,zoomOnDoubleClick:c,panOnDrag:u,zoomActivationKeyPressed:O,preventScrolling:b,noPanClassName:v,userSelectionActive:P,noWheelClassName:x,lib:D,onTransformChange:I})}),[e,n,t,i,a,l,c,u,O,b,v,P,x,D,I]),(0,o.jsx)("div",{className:"react-flow__renderer",ref:M,style:le,children:C})}const ge=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function pe(){const{userSelectionActive:e,userSelectionRect:n}=p(ge,d.x);return e&&n?(0,o.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}const me=(e,n)=>t=>{t.target===n.current&&e?.(t)},he=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging});function fe({isSelecting:e,selectionKeyPressed:n,selectionMode:t=s.Qc.Full,panOnDrag:a,selectionOnDrag:l,onSelectionStart:c,onSelectionEnd:u,onPaneClick:g,onPaneContextMenu:h,onPaneScroll:f,onPaneMouseEnter:S,onPaneMouseMove:y,onPaneMouseLeave:b,children:C}){const x=m(),{userSelectionActive:v,elementsSelectable:w,dragging:E}=p(he,d.x),k=w&&(e||v),N=(0,r.useRef)(null),M=(0,r.useRef)(),P=(0,r.useRef)(new Set),D=(0,r.useRef)(new Set),O=(0,r.useRef)(!1),R=(0,r.useRef)(!1),I=e=>{O.current?O.current=!1:(g?.(e),x.getState().resetSelectedElements(),x.setState({nodesSelectionActive:!1}))},A=f?e=>f(e):void 0,L=!0===a||Array.isArray(a)&&a.includes(0);return(0,o.jsxs)("div",{className:(0,i.A)(["react-flow__pane",{draggable:L,dragging:E,selection:e}]),onClick:k?void 0:me(I,N),onContextMenu:me((e=>{Array.isArray(a)&&a?.includes(2)?e.preventDefault():h?.(e)}),N),onWheel:me(A,N),onPointerEnter:k?void 0:S,onPointerDown:k?n=>{const{resetSelectedElements:t,domNode:o}=x.getState();if(M.current=o?.getBoundingClientRect(),!w||!e||0!==n.button||n.target!==N.current||!M.current)return;n.target?.setPointerCapture?.(n.pointerId),R.current=!0,O.current=!1;const{x:r,y:i}=(0,s.q1)(n.nativeEvent,M.current);t(),x.setState({userSelectionRect:{width:0,height:0,startX:r,startY:i,x:r,y:i}}),c?.(n)}:y,onPointerMove:k?e=>{const{userSelectionRect:n,transform:o,nodeLookup:r,edgeLookup:i,connectionLookup:a,triggerNodeChanges:d,triggerEdgeChanges:l,defaultEdgeOptions:c}=x.getState();if(!M.current||!n)return;O.current=!0;const{x:u,y:g}=(0,s.q1)(e.nativeEvent,M.current),{startX:p,startY:m}=n,h={startX:p,startY:m,x:u<p?u:p,y:g<m?g:m,width:Math.abs(u-p),height:Math.abs(g-m)},f=P.current,S=D.current;P.current=new Set((0,s.U$)(r,h,o,t===s.Qc.Partial,!0).map((e=>e.id))),D.current=new Set;const y=c?.selectable??!0;for(const t of P.current){const e=a.get(t);if(e)for(const{edgeId:n}of e.values()){const e=i.get(n);e&&(e.selectable??y)&&D.current.add(n)}}if(!(0,s._s)(f,P.current)){d(Y(r,P.current,!0))}if(!(0,s._s)(S,D.current)){l(Y(i,D.current))}x.setState({userSelectionRect:h,userSelectionActive:!0,nodesSelectionActive:!1})}:y,onPointerUp:k?e=>{if(0!==e.button||!R.current)return;e.target?.releasePointerCapture?.(e.pointerId);const{userSelectionRect:t}=x.getState();!v&&t&&e.target===N.current&&I?.(e),x.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:P.current.size>0}),u?.(e),(n||l)&&(O.current=!1),R.current=!1}:void 0,onPointerLeave:b,ref:N,style:le,children:[C,(0,o.jsx)(pe,{})]})}function Se({id:e,store:n,unselect:t=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeLookup:d,onError:l}=n.getState(),c=d.get(e);c?(n.setState({nodesSelectionActive:!1}),c.selected?(t||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",s.xc.error012(e))}function ye({nodeRef:e,disabled:n=!1,noDragClassName:t,handleSelector:o,nodeId:i,isSelectable:a,nodeClickDistance:d}){const l=m(),[c,u]=(0,r.useState)(!1),g=(0,r.useRef)();return(0,r.useEffect)((()=>{g.current=(0,s.I$)({getStoreItems:()=>l.getState(),onNodeMouseDown:n=>{Se({id:n,store:l,nodeRef:e})},onDragStart:()=>{u(!0)},onDragStop:()=>{u(!1)}})}),[]),(0,r.useEffect)((()=>{if(n)g.current?.destroy();else if(e.current)return g.current?.update({noDragClassName:t,handleSelector:o,domNode:e.current,isSelectable:a,nodeId:i,nodeClickDistance:d}),()=>{g.current?.destroy()}}),[t,o,n,a,e,i]),c}function be(){const e=m();return(0,r.useCallback)((n=>{const{nodeExtent:t,snapToGrid:o,snapGrid:r,nodesDraggable:i,onError:a,updateNodePositions:d,nodeLookup:l,nodeOrigin:c}=e.getState(),u=new Map,g=(e=>n=>n.selected&&(n.draggable||e&&void 0===n.draggable))(i),p=o?r[0]:5,m=o?r[1]:5,h=n.direction.x*p*n.factor,f=n.direction.y*m*n.factor;for(const[,e]of l){if(!g(e))continue;let n={x:e.internals.positionAbsolute.x+h,y:e.internals.positionAbsolute.y+f};o&&(n=(0,s.s_)(n,r));const{position:i,positionAbsolute:d}=(0,s.aE)({nodeId:e.id,nextPosition:n,nodeLookup:l,nodeExtent:t,nodeOrigin:c,onError:a});e.position=i,e.internals.positionAbsolute=d,u.set(e.id,e)}d(u)}),[])}const Ce=(0,r.createContext)(null),xe=Ce.Provider;Ce.Consumer;const ve=()=>(0,r.useContext)(Ce),we=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId});const Ee=(0,r.memo)(J((function({type:e="source",position:n=s.yX.Top,isValidConnection:t,isConnectable:r=!0,isConnectableStart:a=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:g,className:h,onMouseDown:f,onTouchStart:S,...y},b){const C=c||null,x="target"===e,v=m(),w=ve(),{connectOnClick:E,noPanClassName:k,rfId:N}=p(we,d.x),{connectingFrom:M,connectingTo:P,clickConnecting:D,isPossibleEndHandle:O,connectionInProcess:R,clickConnectionInProcess:I,valid:A}=p(((e,n,t)=>o=>{const{connectionClickStartHandle:r,connectionMode:i,connection:a}=o,{fromHandle:d,toHandle:l,isValid:c}=a,u=l?.nodeId===e&&l?.id===n&&l?.type===t;return{connectingFrom:d?.nodeId===e&&d?.id===n&&d?.type===t,connectingTo:u,clickConnecting:r?.nodeId===e&&r?.id===n&&r?.type===t,isPossibleEndHandle:i===s.WZ.Strict?d?.type!==t:e!==d?.nodeId||n!==d?.id,connectionInProcess:!!d,clickConnectionInProcess:!!r,valid:u&&c}})(w,C,e),d.x);w||v.getState().onError?.("010",s.xc.error010());const L=e=>{const{defaultEdgeOptions:n,onConnect:t,hasDefaultEdges:o}=v.getState(),r={...n,...e};if(o){const{edges:e,setEdges:n}=v.getState();n((0,s.rN)(r,e))}t?.(r),u?.(r)},j=e=>{if(!w)return;const n=(0,s.Er)(e.nativeEvent);if(a&&(n&&0===e.button||!n)){const n=v.getState();s.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:n.autoPanOnConnect,connectionMode:n.connectionMode,connectionRadius:n.connectionRadius,domNode:n.domNode,nodeLookup:n.nodeLookup,lib:n.lib,isTarget:x,handleId:C,nodeId:w,flowId:n.rfId,panBy:n.panBy,cancelConnection:n.cancelConnection,onConnectStart:n.onConnectStart,onConnectEnd:n.onConnectEnd,updateConnection:n.updateConnection,onConnect:L,isValidConnection:t||n.isValidConnection,getTransform:()=>v.getState().transform,getFromHandle:()=>v.getState().connection.fromHandle,autoPanSpeed:n.autoPanSpeed})}n?f?.(e):S?.(e)};return(0,o.jsx)("div",{"data-handleid":C,"data-nodeid":w,"data-handlepos":n,"data-id":`${N}-${w}-${C}-${e}`,className:(0,i.A)(["react-flow__handle",`react-flow__handle-${n}`,"nodrag",k,h,{source:!x,target:x,connectable:r,connectablestart:a,connectableend:l,clickconnecting:D,connectingfrom:M,connectingto:P,valid:A,connectionindicator:r&&(!R||O)&&(R||I?l:a)}]),onMouseDown:j,onTouchStart:j,onClick:E?n=>{const{onClickConnectStart:o,onClickConnectEnd:r,connectionClickStartHandle:i,connectionMode:d,isValidConnection:l,lib:c,rfId:u,nodeLookup:g,connection:p}=v.getState();if(!w||!i&&!a)return;if(!i)return o?.(n.nativeEvent,{nodeId:w,handleId:C,handleType:e}),void v.setState({connectionClickStartHandle:{nodeId:w,type:e,id:C}});const m=(0,s.oj)(n.target),h=t||l,{connection:f,isValid:S}=s.aQ.isValid(n.nativeEvent,{handle:{nodeId:w,id:C,type:e},connectionMode:d,fromNodeId:i.nodeId,fromHandleId:i.id||null,fromType:i.type,isValidConnection:h,flowId:u,doc:m,lib:c,nodeLookup:g});S&&f&&L(f);const y=structuredClone(p);delete y.inProgress,y.toPosition=y.toHandle?y.toHandle.position:null,r?.(n,y),v.setState({connectionClickStartHandle:null})}:void 0,ref:b,...y,children:g})})));const ke={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},Ne={input:function({data:e,isConnectable:n,sourcePosition:t=s.yX.Bottom}){return(0,o.jsxs)(o.Fragment,{children:[e?.label,(0,o.jsx)(Ee,{type:"source",position:t,isConnectable:n})]})},default:function({data:e,isConnectable:n,targetPosition:t=s.yX.Top,sourcePosition:r=s.yX.Bottom}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Ee,{type:"target",position:t,isConnectable:n}),e?.label,(0,o.jsx)(Ee,{type:"source",position:r,isConnectable:n})]})},output:function({data:e,isConnectable:n,targetPosition:t=s.yX.Top}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Ee,{type:"target",position:t,isConnectable:n}),e?.label]})},group:function(){return null}};const Me=e=>{const{width:n,height:t,x:o,y:r}=(0,s.aZ)(e.nodeLookup,{filter:e=>!!e.selected});return{width:(0,s.kf)(n)?n:null,height:(0,s.kf)(t)?t:null,userSelectionActive:e.userSelectionActive,transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]}) translate(${o}px,${r}px)`}};function Pe({onSelectionContextMenu:e,noPanClassName:n,disableKeyboardA11y:t}){const s=m(),{width:a,height:l,transformString:c,userSelectionActive:u}=p(Me,d.x),g=be(),h=(0,r.useRef)(null);if((0,r.useEffect)((()=>{t||h.current?.focus({preventScroll:!0})}),[t]),ye({nodeRef:h}),u||!a||!l)return null;const f=e?n=>{const t=s.getState().nodes.filter((e=>e.selected));e(n,t)}:void 0;return(0,o.jsx)("div",{className:(0,i.A)(["react-flow__nodesselection","react-flow__container",n]),style:{transform:c},children:(0,o.jsx)("div",{ref:h,className:"react-flow__nodesselection-rect",onContextMenu:f,tabIndex:t?void 0:-1,onKeyDown:t?void 0:e=>{Object.prototype.hasOwnProperty.call(ke,e.key)&&(e.preventDefault(),g({direction:ke[e.key],factor:e.shiftKey?4:1}))},style:{width:a,height:l}})})}const De="undefined"!=typeof window?window:void 0,Oe=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function Re({children:e,onPaneClick:n,onPaneMouseEnter:t,onPaneMouseMove:i,onPaneMouseLeave:s,onPaneContextMenu:a,onPaneScroll:d,paneClickDistance:l,deleteKeyCode:c,selectionKeyCode:u,selectionOnDrag:g,selectionMode:h,onSelectionStart:f,onSelectionEnd:S,multiSelectionKeyCode:y,panActivationKeyCode:b,zoomActivationKeyCode:C,elementsSelectable:x,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:E,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:P,defaultViewport:D,translateExtent:O,minZoom:R,maxZoom:I,preventScrolling:A,onSelectionContextMenu:L,noWheelClassName:j,noPanClassName:z,disableKeyboardA11y:_,onViewportChange:B,isControlledViewport:Z}){const{nodesSelectionActive:$,userSelectionActive:T}=p(Oe),X=V(u,{target:De}),H=V(b,{target:De}),W=H||P,F=H||E,K=g&&!0!==W,Y=X||T||K;return function({deleteKeyCode:e,multiSelectionKeyCode:n}){const t=m(),{deleteElements:o}=ie(),i=V(e,ae),s=V(n,{target:de});(0,r.useEffect)((()=>{if(i){const{edges:e,nodes:n}=t.getState();o({nodes:n.filter(se),edges:e.filter(se)}),t.setState({nodesSelectionActive:!1})}}),[i]),(0,r.useEffect)((()=>{t.setState({multiSelectionActive:s})}),[s])}({deleteKeyCode:c,multiSelectionKeyCode:y}),(0,o.jsx)(ue,{onPaneContextMenu:a,elementsSelectable:x,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:F,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:!X&&W,defaultViewport:D,translateExtent:O,minZoom:R,maxZoom:I,zoomActivationKeyCode:C,preventScrolling:A,noWheelClassName:j,noPanClassName:z,onViewportChange:B,isControlledViewport:Z,paneClickDistance:l,children:(0,o.jsxs)(fe,{onSelectionStart:f,onSelectionEnd:S,onPaneClick:n,onPaneMouseEnter:t,onPaneMouseMove:i,onPaneMouseLeave:s,onPaneContextMenu:a,onPaneScroll:d,panOnDrag:W,isSelecting:!!Y,selectionMode:h,selectionKeyPressed:X,selectionOnDrag:K,children:[e,$&&(0,o.jsx)(Pe,{onSelectionContextMenu:L,noPanClassName:z,disableKeyboardA11y:_})]})})}Re.displayName="FlowRenderer";const Ie=(0,r.memo)(Re);function Ae(e){return p((0,r.useCallback)((e=>n=>e?(0,s.U$)(n.nodeLookup,{x:0,y:0,width:n.width,height:n.height},n.transform,!0).map((e=>e.id)):Array.from(n.nodeLookup.keys()))(e),[e]),d.x)}const Le=e=>e.updateNodeInternals;function je({id:e,onClick:n,onMouseEnter:t,onMouseMove:a,onMouseLeave:l,onContextMenu:c,onDoubleClick:u,nodesDraggable:g,elementsSelectable:h,nodesConnectable:f,nodesFocusable:y,resizeObserver:b,noDragClassName:C,noPanClassName:x,disableKeyboardA11y:v,rfId:w,nodeTypes:E,nodeClickDistance:k,onError:N}){const{node:M,internals:P,isParent:D}=p((n=>{const t=n.nodeLookup.get(e),o=n.parentLookup.has(e);return{node:t,internals:t.internals,isParent:o}}),d.x);let O=M.type||"default",R=E?.[O]||Ne[O];void 0===R&&(N?.("003",s.xc.error003(O)),O="default",R=Ne.default);const I=!!(M.draggable||g&&void 0===M.draggable),A=!!(M.selectable||h&&void 0===M.selectable),L=!!(M.connectable||f&&void 0===M.connectable),j=!!(M.focusable||y&&void 0===M.focusable),z=m(),_=(0,s.QE)(M),B=function({node:e,nodeType:n,hasDimensions:t,resizeObserver:o}){const i=m(),s=(0,r.useRef)(null),a=(0,r.useRef)(null),d=(0,r.useRef)(e.sourcePosition),l=(0,r.useRef)(e.targetPosition),c=(0,r.useRef)(n),u=t&&!!e.internals.handleBounds;return(0,r.useEffect)((()=>{!s.current||e.hidden||u&&a.current===s.current||(a.current&&o?.unobserve(a.current),o?.observe(s.current),a.current=s.current)}),[u,e.hidden]),(0,r.useEffect)((()=>()=>{a.current&&(o?.unobserve(a.current),a.current=null)}),[]),(0,r.useEffect)((()=>{if(s.current){const t=c.current!==n,o=d.current!==e.sourcePosition,r=l.current!==e.targetPosition;(t||o||r)&&(c.current=n,d.current=e.sourcePosition,l.current=e.targetPosition,i.getState().updateNodeInternals(new Map([[e.id,{id:e.id,nodeElement:s.current,force:!0}]])))}}),[e.id,n,e.sourcePosition,e.targetPosition]),s}({node:M,nodeType:O,hasDimensions:_,resizeObserver:b}),V=ye({nodeRef:B,disabled:M.hidden||!I,noDragClassName:C,handleSelector:M.dragHandle,nodeId:e,isSelectable:A,nodeClickDistance:k}),Z=be();if(M.hidden)return null;const $=(0,s.uD)(M),T=function(e){return void 0===e.internals.handleBounds?{width:e.width??e.initialWidth??e.style?.width,height:e.height??e.initialHeight??e.style?.height}:{width:e.width??e.style?.width,height:e.height??e.style?.height}}(M),X=A||I||n||t||a||l,H=t?e=>t(e,{...P.userNode}):void 0,W=a?e=>a(e,{...P.userNode}):void 0,F=l?e=>l(e,{...P.userNode}):void 0,K=c?e=>c(e,{...P.userNode}):void 0,Y=u?e=>u(e,{...P.userNode}):void 0;return(0,o.jsx)("div",{className:(0,i.A)(["react-flow__node",`react-flow__node-${O}`,{[x]:I},M.className,{selected:M.selected,selectable:A,parent:D,draggable:I,dragging:V}]),ref:B,style:{zIndex:P.z,transform:`translate(${P.positionAbsolute.x}px,${P.positionAbsolute.y}px)`,pointerEvents:X?"all":"none",visibility:_?"visible":"hidden",...M.style,...T},"data-id":e,"data-testid":`rf__node-${e}`,onMouseEnter:H,onMouseMove:W,onMouseLeave:F,onContextMenu:K,onClick:t=>{const{selectNodesOnDrag:o,nodeDragThreshold:r}=z.getState();A&&(!o||!I||r>0)&&Se({id:e,store:z,nodeRef:B}),n&&n(t,{...P.userNode})},onDoubleClick:Y,onKeyDown:j?n=>{if(!(0,s.v5)(n.nativeEvent)&&!v)if(s.tn.includes(n.key)&&A){const t="Escape"===n.key;Se({id:e,store:z,unselect:t,nodeRef:B})}else I&&M.selected&&Object.prototype.hasOwnProperty.call(ke,n.key)&&(n.preventDefault(),z.setState({ariaLiveMessage:`Moved selected node ${n.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~P.positionAbsolute.x}, y: ${~~P.positionAbsolute.y}`}),Z({direction:ke[n.key],factor:n.shiftKey?4:1}))}:void 0,tabIndex:j?0:void 0,role:j?"button":void 0,"aria-describedby":v?void 0:`${S}-${w}`,"aria-label":M.ariaLabel,children:(0,o.jsx)(xe,{value:e,children:(0,o.jsx)(R,{id:e,data:M.data,type:O,positionAbsoluteX:P.positionAbsolute.x,positionAbsoluteY:P.positionAbsolute.y,selected:M.selected??!1,selectable:A,draggable:I,deletable:M.deletable??!0,isConnectable:L,sourcePosition:M.sourcePosition,targetPosition:M.targetPosition,dragging:V,dragHandle:M.dragHandle,zIndex:P.z,parentId:M.parentId,...$})})})}const ze=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function _e(e){const{nodesDraggable:n,nodesConnectable:t,nodesFocusable:i,elementsSelectable:s,onError:a}=p(ze,d.x),l=Ae(e.onlyRenderVisibleElements),c=function(){const e=p(Le),[n]=(0,r.useState)((()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver((n=>{const t=new Map;n.forEach((e=>{const n=e.target.getAttribute("data-id");t.set(n,{id:n,nodeElement:e.target,force:!0})})),e(t)}))));return(0,r.useEffect)((()=>()=>{n?.disconnect()}),[n]),n}();return(0,o.jsx)("div",{className:"react-flow__nodes",style:le,children:l.map((r=>(0,o.jsx)(je,{id:r,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:c,nodesDraggable:n,nodesConnectable:t,nodesFocusable:i,elementsSelectable:s,nodeClickDistance:e.nodeClickDistance,onError:a},r)))})}_e.displayName="NodeRenderer";const Be=(0,r.memo)(_e);const Ve={[s.TG.Arrow]:({color:e="none",strokeWidth:n=1})=>(0,o.jsx)("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[s.TG.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>(0,o.jsx)("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const Ze=({id:e,type:n,color:t,width:i=12.5,height:a=12.5,markerUnits:d="strokeWidth",strokeWidth:l,orient:c="auto-start-reverse"})=>{const u=function(e){const n=m();return(0,r.useMemo)((()=>Object.prototype.hasOwnProperty.call(Ve,e)?Ve[e]:(n.getState().onError?.("009",s.xc.error009(e)),null)),[e])}(n);return u?(0,o.jsx)("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${i}`,markerHeight:`${a}`,viewBox:"-10 -10 20 20",markerUnits:d,orient:c,refX:"0",refY:"0",children:(0,o.jsx)(u,{color:t,strokeWidth:l})}):null},$e=({defaultColor:e,rfId:n})=>{const t=p((e=>e.edges)),i=p((e=>e.defaultEdgeOptions)),a=(0,r.useMemo)((()=>(0,s.Hm)(t,{id:n,defaultColor:e,defaultMarkerStart:i?.markerStart,defaultMarkerEnd:i?.markerEnd})),[t,i,n,e]);return a.length?(0,o.jsx)("svg",{className:"react-flow__marker",children:(0,o.jsx)("defs",{children:a.map((e=>(0,o.jsx)(Ze,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id)))})}):null};$e.displayName="MarkerDefinitions";var Te=(0,r.memo)($e);function Xe({x:e,y:n,label:t,labelStyle:s={},labelShowBg:a=!0,labelBgStyle:d={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:g,...p}){const[m,h]=(0,r.useState)({x:1,y:0,width:0,height:0}),f=(0,i.A)(["react-flow__edge-textwrapper",g]),S=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(S.current){const e=S.current.getBBox();h({x:e.x,y:e.y,width:e.width,height:e.height})}}),[t]),void 0!==t&&t?(0,o.jsxs)("g",{transform:`translate(${e-m.width/2} ${n-m.height/2})`,className:f,visibility:m.width?"visible":"hidden",...p,children:[a&&(0,o.jsx)("rect",{width:m.width+2*l[0],x:-l[0],y:-l[1],height:m.height+2*l[1],className:"react-flow__edge-textbg",style:d,rx:c,ry:c}),(0,o.jsx)("text",{className:"react-flow__edge-text",y:m.height/2,dy:"0.3em",ref:S,style:s,children:t}),u]}):null}Xe.displayName="EdgeText";const He=(0,r.memo)(Xe);function We({path:e,labelX:n,labelY:t,label:r,labelStyle:a,labelShowBg:d,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,interactionWidth:g=20,...p}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("path",{...p,d:e,fill:"none",className:(0,i.A)(["react-flow__edge-path",p.className])}),g&&(0,o.jsx)("path",{d:e,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),r&&(0,s.kf)(n)&&(0,s.kf)(t)?(0,o.jsx)(He,{x:n,y:t,label:r,labelStyle:a,labelShowBg:d,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null]})}function Fe({pos:e,x1:n,y1:t,x2:o,y2:r}){return e===s.yX.Left||e===s.yX.Right?[.5*(n+o),t]:[n,.5*(t+r)]}function Ke({sourceX:e,sourceY:n,sourcePosition:t=s.yX.Bottom,targetX:o,targetY:r,targetPosition:i=s.yX.Top}){const[a,d]=Fe({pos:t,x1:e,y1:n,x2:o,y2:r}),[l,c]=Fe({pos:i,x1:o,y1:r,x2:e,y2:n}),[u,g,p,m]=(0,s.e_)({sourceX:e,sourceY:n,targetX:o,targetY:r,sourceControlX:a,sourceControlY:d,targetControlX:l,targetControlY:c});return[`M${e},${n} C${a},${d} ${l},${c} ${o},${r}`,u,g,p,m]}function Ye(e){return(0,r.memo)((({id:n,sourceX:t,sourceY:r,targetX:i,targetY:a,sourcePosition:d=s.yX.Bottom,targetPosition:l=s.yX.Top,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:S,markerStart:y,interactionWidth:b})=>{const[C,x,v]=Ke({sourceX:t,sourceY:r,sourcePosition:d,targetX:i,targetY:a,targetPosition:l}),w=e.isInternal?void 0:n;return(0,o.jsx)(We,{id:w,path:C,labelX:x,labelY:v,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:S,markerStart:y,interactionWidth:b})}))}const Ge=Ye({isInternal:!1}),Ue=Ye({isInternal:!0});function Qe(e){return(0,r.memo)((({id:n,sourceX:t,sourceY:r,targetX:i,targetY:a,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,sourcePosition:h=s.yX.Bottom,targetPosition:f=s.yX.Top,markerEnd:S,markerStart:y,pathOptions:b,interactionWidth:C})=>{const[x,v,w]=(0,s.oN)({sourceX:t,sourceY:r,sourcePosition:h,targetX:i,targetY:a,targetPosition:f,borderRadius:b?.borderRadius,offset:b?.offset}),E=e.isInternal?void 0:n;return(0,o.jsx)(We,{id:E,path:x,labelX:v,labelY:w,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:S,markerStart:y,interactionWidth:C})}))}Ge.displayName="SimpleBezierEdge",Ue.displayName="SimpleBezierEdgeInternal";const qe=Qe({isInternal:!1}),Je=Qe({isInternal:!0});function en(e){return(0,r.memo)((({id:n,...t})=>{const i=e.isInternal?void 0:n;return(0,o.jsx)(qe,{...t,id:i,pathOptions:(0,r.useMemo)((()=>({borderRadius:0,offset:t.pathOptions?.offset})),[t.pathOptions?.offset])})}))}qe.displayName="SmoothStepEdge",Je.displayName="SmoothStepEdgeInternal";const nn=en({isInternal:!1}),tn=en({isInternal:!0});function on(e){return(0,r.memo)((({id:n,sourceX:t,sourceY:r,targetX:i,targetY:a,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:f,interactionWidth:S})=>{const[y,b,C]=(0,s.ah)({sourceX:t,sourceY:r,targetX:i,targetY:a}),x=e.isInternal?void 0:n;return(0,o.jsx)(We,{id:x,path:y,labelX:b,labelY:C,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:f,interactionWidth:S})}))}nn.displayName="StepEdge",tn.displayName="StepEdgeInternal";const rn=on({isInternal:!1}),sn=on({isInternal:!0});function an(e){return(0,r.memo)((({id:n,sourceX:t,sourceY:r,targetX:i,targetY:a,sourcePosition:d=s.yX.Bottom,targetPosition:l=s.yX.Top,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:S,markerStart:y,pathOptions:b,interactionWidth:C})=>{const[x,v,w]=(0,s.Fp)({sourceX:t,sourceY:r,sourcePosition:d,targetX:i,targetY:a,targetPosition:l,curvature:b?.curvature}),E=e.isInternal?void 0:n;return(0,o.jsx)(We,{id:E,path:x,labelX:v,labelY:w,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:S,markerStart:y,interactionWidth:C})}))}rn.displayName="StraightEdge",sn.displayName="StraightEdgeInternal";const dn=an({isInternal:!1}),ln=an({isInternal:!0});dn.displayName="BezierEdge",ln.displayName="BezierEdgeInternal";const cn={default:ln,straight:sn,step:tn,smoothstep:Je,simplebezier:Ue},un={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},gn=(e,n,t)=>t===s.yX.Left?e-n:t===s.yX.Right?e+n:e,pn=(e,n,t)=>t===s.yX.Top?e-n:t===s.yX.Bottom?e+n:e,mn="react-flow__edgeupdater";function hn({position:e,centerX:n,centerY:t,radius:r=10,onMouseDown:s,onMouseEnter:a,onMouseOut:d,type:l}){return(0,o.jsx)("circle",{onMouseDown:s,onMouseEnter:a,onMouseOut:d,className:(0,i.A)([mn,`${mn}-${l}`]),cx:gn(n,r,e),cy:pn(t,r,e),r:r,stroke:"transparent",fill:"transparent"})}function fn({isReconnectable:e,reconnectRadius:n,edge:t,sourceX:r,sourceY:i,targetX:a,targetY:d,sourcePosition:l,targetPosition:c,onReconnect:u,onReconnectStart:g,onReconnectEnd:p,setReconnecting:h,setUpdateHover:f}){const S=m(),y=(e,n)=>{if(0!==e.button)return;const{autoPanOnConnect:o,domNode:r,isValidConnection:i,connectionMode:a,connectionRadius:d,lib:l,onConnectStart:c,onConnectEnd:m,cancelConnection:f,nodeLookup:y,rfId:b,panBy:C,updateConnection:x}=S.getState(),v="target"===n.type;h(!0),g?.(e,t,n.type);s.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:o,connectionMode:a,connectionRadius:d,domNode:r,handleId:n.id,nodeId:n.nodeId,nodeLookup:y,isTarget:v,edgeUpdaterType:n.type,lib:l,flowId:b,cancelConnection:f,panBy:C,isValidConnection:i,onConnect:e=>u?.(t,e),onConnectStart:c,onConnectEnd:m,onReconnectEnd:(e,o)=>{h(!1),p?.(e,t,n.type,o)},updateConnection:x,getTransform:()=>S.getState().transform,getFromHandle:()=>S.getState().connection.fromHandle})},b=()=>f(!0),C=()=>f(!1);return(0,o.jsxs)(o.Fragment,{children:[(!0===e||"source"===e)&&(0,o.jsx)(hn,{position:l,centerX:r,centerY:i,radius:n,onMouseDown:e=>y(e,{nodeId:t.target,id:t.targetHandle??null,type:"target"}),onMouseEnter:b,onMouseOut:C,type:"source"}),(!0===e||"target"===e)&&(0,o.jsx)(hn,{position:c,centerX:a,centerY:d,radius:n,onMouseDown:e=>y(e,{nodeId:t.source,id:t.sourceHandle??null,type:"source"}),onMouseEnter:b,onMouseOut:C,type:"target"})]})}function Sn({id:e,edgesFocusable:n,edgesReconnectable:t,elementsSelectable:a,onClick:l,onDoubleClick:c,onContextMenu:u,onMouseEnter:g,onMouseMove:h,onMouseLeave:f,reconnectRadius:S,onReconnect:b,onReconnectStart:C,onReconnectEnd:x,rfId:v,edgeTypes:w,noPanClassName:E,onError:k,disableKeyboardA11y:N}){let M=p((n=>n.edgeLookup.get(e)));const P=p((e=>e.defaultEdgeOptions));M=P?{...P,...M}:M;let D=M.type||"default",O=w?.[D]||cn[D];void 0===O&&(k?.("011",s.xc.error011(D)),D="default",O=cn.default);const R=!!(M.focusable||n&&void 0===M.focusable),I=void 0!==b&&(M.reconnectable||t&&void 0===M.reconnectable),A=!!(M.selectable||a&&void 0===M.selectable),L=(0,r.useRef)(null),[j,z]=(0,r.useState)(!1),[_,B]=(0,r.useState)(!1),V=m(),{zIndex:Z,sourceX:$,sourceY:T,targetX:X,targetY:H,sourcePosition:W,targetPosition:F}=p((0,r.useCallback)((n=>{const t=n.nodeLookup.get(M.source),o=n.nodeLookup.get(M.target);if(!t||!o)return{zIndex:M.zIndex,...un};const r=(0,s.b5)({id:e,sourceNode:t,targetNode:o,sourceHandle:M.sourceHandle||null,targetHandle:M.targetHandle||null,connectionMode:n.connectionMode,onError:k});return{zIndex:(0,s.qX)({selected:M.selected,zIndex:M.zIndex,sourceNode:t,targetNode:o,elevateOnSelect:n.elevateEdgesOnSelect}),...r||un}}),[M.source,M.target,M.sourceHandle,M.targetHandle,M.selected,M.zIndex]),d.x),K=(0,r.useMemo)((()=>M.markerStart?`url('#${(0,s.aW)(M.markerStart,v)}')`:void 0),[M.markerStart,v]),Y=(0,r.useMemo)((()=>M.markerEnd?`url('#${(0,s.aW)(M.markerEnd,v)}')`:void 0),[M.markerEnd,v]);if(M.hidden||null===$||null===T||null===X||null===H)return null;const G=c?e=>{c(e,{...M})}:void 0,U=u?e=>{u(e,{...M})}:void 0,Q=g?e=>{g(e,{...M})}:void 0,q=h?e=>{h(e,{...M})}:void 0,J=f?e=>{f(e,{...M})}:void 0;return(0,o.jsx)("svg",{style:{zIndex:Z},children:(0,o.jsxs)("g",{className:(0,i.A)(["react-flow__edge",`react-flow__edge-${D}`,M.className,E,{selected:M.selected,animated:M.animated,inactive:!A&&!l,updating:j,selectable:A}]),onClick:n=>{const{addSelectedEdges:t,unselectNodesAndEdges:o,multiSelectionActive:r}=V.getState();A&&(V.setState({nodesSelectionActive:!1}),M.selected&&r?(o({nodes:[],edges:[M]}),L.current?.blur()):t([e])),l&&l(n,M)},onDoubleClick:G,onContextMenu:U,onMouseEnter:Q,onMouseMove:q,onMouseLeave:J,onKeyDown:R?n=>{if(!N&&s.tn.includes(n.key)&&A){const{unselectNodesAndEdges:t,addSelectedEdges:o}=V.getState();"Escape"===n.key?(L.current?.blur(),t({edges:[M]})):o([e])}}:void 0,tabIndex:R?0:void 0,role:R?"button":"img","data-id":e,"data-testid":`rf__edge-${e}`,"aria-label":null===M.ariaLabel?void 0:M.ariaLabel||`Edge from ${M.source} to ${M.target}`,"aria-describedby":R?`${y}-${v}`:void 0,ref:L,children:[!_&&(0,o.jsx)(O,{id:e,source:M.source,target:M.target,type:M.type,selected:M.selected,animated:M.animated,selectable:A,deletable:M.deletable??!0,label:M.label,labelStyle:M.labelStyle,labelShowBg:M.labelShowBg,labelBgStyle:M.labelBgStyle,labelBgPadding:M.labelBgPadding,labelBgBorderRadius:M.labelBgBorderRadius,sourceX:$,sourceY:T,targetX:X,targetY:H,sourcePosition:W,targetPosition:F,data:M.data,style:M.style,sourceHandleId:M.sourceHandle,targetHandleId:M.targetHandle,markerStart:K,markerEnd:Y,pathOptions:"pathOptions"in M?M.pathOptions:void 0,interactionWidth:M.interactionWidth}),I&&(0,o.jsx)(fn,{edge:M,isReconnectable:I,reconnectRadius:S,onReconnect:b,onReconnectStart:C,onReconnectEnd:x,sourceX:$,sourceY:T,targetX:X,targetY:H,sourcePosition:W,targetPosition:F,setUpdateHover:z,setReconnecting:B})]})})}const yn=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function bn({defaultMarkerColor:e,onlyRenderVisibleElements:n,rfId:t,edgeTypes:i,noPanClassName:a,onReconnect:l,onEdgeContextMenu:c,onEdgeMouseEnter:u,onEdgeMouseMove:g,onEdgeMouseLeave:m,onEdgeClick:h,reconnectRadius:f,onEdgeDoubleClick:S,onReconnectStart:y,onReconnectEnd:b,disableKeyboardA11y:C}){const{edgesFocusable:x,edgesReconnectable:v,elementsSelectable:w,onError:E}=p(yn,d.x),k=(N=n,p((0,r.useCallback)((e=>{if(!N)return e.edges.map((e=>e.id));const n=[];if(e.width&&e.height)for(const t of e.edges){const o=e.nodeLookup.get(t.source),r=e.nodeLookup.get(t.target);o&&r&&(0,s.uj)({sourceNode:o,targetNode:r,width:e.width,height:e.height,transform:e.transform})&&n.push(t.id)}return n}),[N]),d.x));var N;return(0,o.jsxs)("div",{className:"react-flow__edges",children:[(0,o.jsx)(Te,{defaultColor:e,rfId:t}),k.map((e=>(0,o.jsx)(Sn,{id:e,edgesFocusable:x,edgesReconnectable:v,elementsSelectable:w,noPanClassName:a,onReconnect:l,onContextMenu:c,onMouseEnter:u,onMouseMove:g,onMouseLeave:m,onClick:h,reconnectRadius:f,onDoubleClick:S,onReconnectStart:y,onReconnectEnd:b,rfId:t,onError:E,edgeTypes:i,disableKeyboardA11y:C},e)))]})}bn.displayName="EdgeRenderer";const Cn=(0,r.memo)(bn),xn=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function vn({children:e}){const n=p(xn);return(0,o.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:n},children:e})}const wn=e=>e.panZoom?.syncViewport;function En(e){return e.connection.inProgress?{...e.connection,to:(0,s.Ff)(e.connection.to,e.transform)}:{...e.connection}}function kn(e){const n=function(e){if(e)return n=>{const t=En(n);return e(t)};return En}(e);return p(n,d.x)}const Nn=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function Mn({containerStyle:e,style:n,type:t,component:r}){const{nodesConnectable:a,width:l,height:c,isValid:u,inProgress:g}=p(Nn,d.x);return!!(l&&a&&g)?(0,o.jsx)("svg",{style:e,width:l,height:c,className:"react-flow__connectionline react-flow__container",children:(0,o.jsx)("g",{className:(0,i.A)(["react-flow__connection",(0,s.HF)(u)]),children:(0,o.jsx)(Pn,{style:n,type:t,CustomComponent:r,isValid:u})})}):null}const Pn=({style:e,type:n=s.Do.Bezier,CustomComponent:t,isValid:r})=>{const{inProgress:i,from:a,fromNode:d,fromHandle:l,fromPosition:c,to:u,toNode:g,toHandle:p,toPosition:m}=kn();if(!i)return;if(t)return(0,o.jsx)(t,{connectionLineType:n,connectionLineStyle:e,fromNode:d,fromHandle:l,fromX:a.x,fromY:a.y,toX:u.x,toY:u.y,fromPosition:c,toPosition:m,connectionStatus:(0,s.HF)(r),toNode:g,toHandle:p});let h="";const f={sourceX:a.x,sourceY:a.y,sourcePosition:c,targetX:u.x,targetY:u.y,targetPosition:m};switch(n){case s.Do.Bezier:[h]=(0,s.Fp)(f);break;case s.Do.SimpleBezier:[h]=Ke(f);break;case s.Do.Step:[h]=(0,s.oN)({...f,borderRadius:0});break;case s.Do.SmoothStep:[h]=(0,s.oN)(f);break;default:[h]=(0,s.ah)(f)}return(0,o.jsx)("path",{d:h,fill:"none",className:"react-flow__connection-path",style:e})};Pn.displayName="ConnectionLine";const Dn={};function On(e=Dn){(0,r.useRef)(e),m();(0,r.useEffect)((()=>{0}),[e])}function Rn({nodeTypes:e,edgeTypes:n,onInit:t,onNodeClick:i,onEdgeClick:s,onNodeDoubleClick:a,onEdgeDoubleClick:d,onNodeMouseEnter:l,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,onSelectionContextMenu:h,onSelectionStart:f,onSelectionEnd:S,connectionLineType:y,connectionLineStyle:b,connectionLineComponent:C,connectionLineContainerStyle:x,selectionKeyCode:v,selectionOnDrag:w,selectionMode:E,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,deleteKeyCode:P,onlyRenderVisibleElements:D,elementsSelectable:O,defaultViewport:R,translateExtent:I,minZoom:A,maxZoom:L,preventScrolling:j,defaultMarkerColor:z,zoomOnScroll:_,zoomOnPinch:B,panOnScroll:V,panOnScrollSpeed:Z,panOnScrollMode:$,zoomOnDoubleClick:T,panOnDrag:X,onPaneClick:H,onPaneMouseEnter:W,onPaneMouseMove:F,onPaneMouseLeave:K,onPaneScroll:Y,onPaneContextMenu:G,paneClickDistance:U,nodeClickDistance:Q,onEdgeContextMenu:q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:ne,reconnectRadius:te,onReconnect:oe,onReconnectStart:re,onReconnectEnd:se,noDragClassName:ae,noWheelClassName:de,noPanClassName:le,disableKeyboardA11y:ce,nodeExtent:ue,rfId:ge,viewport:pe,onViewportChange:me}){return On(e),On(n),m(),(0,r.useRef)(!1),(0,r.useEffect)((()=>{}),[]),function(e){const n=ie(),t=(0,r.useRef)(!1);(0,r.useEffect)((()=>{!t.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),t.current=!0)}),[e,n.viewportInitialized])}(t),function(e){const n=p(wn),t=m();(0,r.useEffect)((()=>{e&&(n?.(e),t.setState({transform:[e.x,e.y,e.zoom]}))}),[e,n])}(pe),(0,o.jsx)(Ie,{onPaneClick:H,onPaneMouseEnter:W,onPaneMouseMove:F,onPaneMouseLeave:K,onPaneContextMenu:G,onPaneScroll:Y,paneClickDistance:U,deleteKeyCode:P,selectionKeyCode:v,selectionOnDrag:w,selectionMode:E,onSelectionStart:f,onSelectionEnd:S,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,elementsSelectable:O,zoomOnScroll:_,zoomOnPinch:B,zoomOnDoubleClick:T,panOnScroll:V,panOnScrollSpeed:Z,panOnScrollMode:$,panOnDrag:X,defaultViewport:R,translateExtent:I,minZoom:A,maxZoom:L,onSelectionContextMenu:h,preventScrolling:j,noDragClassName:ae,noWheelClassName:de,noPanClassName:le,disableKeyboardA11y:ce,onViewportChange:me,isControlledViewport:!!pe,children:(0,o.jsxs)(vn,{children:[(0,o.jsx)(Cn,{edgeTypes:n,onEdgeClick:s,onEdgeDoubleClick:d,onReconnect:oe,onReconnectStart:re,onReconnectEnd:se,onlyRenderVisibleElements:D,onEdgeContextMenu:q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:ne,reconnectRadius:te,defaultMarkerColor:z,noPanClassName:le,disableKeyboardA11y:ce,rfId:ge}),(0,o.jsx)(Mn,{style:b,type:y,component:C,containerStyle:x}),(0,o.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,o.jsx)(Be,{nodeTypes:e,onNodeClick:i,onNodeDoubleClick:a,onNodeMouseEnter:l,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,nodeClickDistance:Q,onlyRenderVisibleElements:D,noPanClassName:le,noDragClassName:ae,disableKeyboardA11y:ce,nodeExtent:ue,rfId:ge}),(0,o.jsx)("div",{className:"react-flow__viewport-portal"})]})})}Rn.displayName="GraphView";const In=(0,r.memo)(Rn),An=({nodes:e,edges:n,defaultNodes:t,defaultEdges:o,width:r,height:i,fitView:a,nodeOrigin:d,nodeExtent:l}={})=>{const c=new Map,u=new Map,g=new Map,p=new Map,m=o??n??[],h=t??e??[],f=d??[0,0],S=l??s.ZO;(0,s.qn)(g,p,m),(0,s.bi)(h,c,u,{nodeOrigin:f,nodeExtent:S,elevateNodesOnSelect:!1});let y=[0,0,1];if(a&&r&&i){const e=(0,s.aZ)(c,{filter:e=>!(!e.width&&!e.initialWidth||!e.height&&!e.initialHeight)}),{x:n,y:t,zoom:o}=(0,s.R4)(e,r,i,.5,2,.1);y=[n,t,o]}return{rfId:"1",width:0,height:0,transform:y,nodes:h,nodeLookup:c,parentLookup:u,edges:m,edgeLookup:p,connectionLookup:g,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==t,hasDefaultEdges:void 0!==o,panZoom:null,minZoom:.5,maxZoom:2,translateExtent:s.ZO,nodeExtent:S,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:s.WZ.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:f,nodeDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,fitViewOnInit:!1,fitViewDone:!1,fitViewOnInitOptions:void 0,selectNodesOnDrag:!0,multiSelectionActive:!1,connection:{...s.bK},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanSpeed:15,connectionRadius:20,onError:s.KE,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1}},Ln=({nodes:e,edges:n,defaultNodes:t,defaultEdges:o,width:r,height:i,fitView:d,nodeOrigin:l,nodeExtent:c})=>(0,a.h)(((a,u)=>({...An({nodes:e,edges:n,width:r,height:i,fitView:d,nodeOrigin:l,nodeExtent:c,defaultNodes:t,defaultEdges:o}),setNodes:e=>{const{nodeLookup:n,parentLookup:t,nodeOrigin:o,elevateNodesOnSelect:r}=u();(0,s.bi)(e,n,t,{nodeOrigin:o,nodeExtent:c,elevateNodesOnSelect:r,checkEquality:!0}),a({nodes:e})},setEdges:e=>{const{connectionLookup:n,edgeLookup:t}=u();(0,s.qn)(n,t,e),a({edges:e})},setDefaultNodesAndEdges:(e,n)=>{if(e){const{setNodes:n}=u();n(e),a({hasDefaultNodes:!0})}if(n){const{setEdges:e}=u();e(n),a({hasDefaultEdges:!0})}},updateNodeInternals:(e,n={triggerFitView:!0})=>{const{triggerNodeChanges:t,nodeLookup:o,parentLookup:r,fitViewOnInit:i,fitViewDone:d,fitViewOnInitOptions:l,domNode:c,nodeOrigin:g,nodeExtent:p,debug:m,fitViewSync:h}=u(),{changes:f,updatedInternals:S}=(0,s.uL)(e,o,r,c,g,p);if(S){if((0,s.vS)(o,r,{nodeOrigin:g,nodeExtent:p}),n.triggerFitView){let e=d;!d&&i&&(e=h({...l,nodes:l?.nodes})),a({fitViewDone:e})}else a({});f?.length>0&&(m&&console.log("React Flow: trigger node changes",f),t?.(f))}},updateNodePositions:(e,n=!1)=>{const t=[],o=[],{nodeLookup:r,triggerNodeChanges:i}=u();for(const[s,a]of e){const e=r.get(s),i=!!(e?.expandParent&&e?.parentId&&a?.position),d={id:s,type:"position",position:i?{x:Math.max(0,a.position.x),y:Math.max(0,a.position.y)}:a.position,dragging:n};i&&e.parentId&&t.push({id:s,parentId:e.parentId,rect:{...a.internals.positionAbsolute,width:a.measured.width??0,height:a.measured.height??0}}),o.push(d)}if(t.length>0){const{parentLookup:e,nodeOrigin:n}=u(),i=(0,s.r8)(t,r,e,n);o.push(...i)}i(o)},triggerNodeChanges:e=>{const{onNodesChange:n,setNodes:t,nodes:o,hasDefaultNodes:r,debug:i}=u();if(e?.length){if(r){t(W(e,o))}i&&console.log("React Flow: trigger node changes",e),n?.(e)}},triggerEdgeChanges:e=>{const{onEdgesChange:n,setEdges:t,edges:o,hasDefaultEdges:r,debug:i}=u();if(e?.length){if(r){t(F(e,o))}i&&console.log("React Flow: trigger edge changes",e),n?.(e)}},addSelectedNodes:e=>{const{multiSelectionActive:n,edgeLookup:t,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=u();if(n){r(e.map((e=>K(e,!0))))}else r(Y(o,new Set([...e]),!0)),i(Y(t))},addSelectedEdges:e=>{const{multiSelectionActive:n,edgeLookup:t,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=u();if(n){i(e.map((e=>K(e,!0))))}else i(Y(t,new Set([...e]))),r(Y(o,new Set,!0))},unselectNodesAndEdges:({nodes:e,edges:n}={})=>{const{edges:t,nodes:o,nodeLookup:r,triggerNodeChanges:i,triggerEdgeChanges:s}=u(),a=n||t,d=(e||o).map((e=>{const n=r.get(e.id);return n&&(n.selected=!1),K(e.id,!1)})),l=a.map((e=>K(e.id,!1)));i(d),s(l)},setMinZoom:e=>{const{panZoom:n,maxZoom:t}=u();n?.setScaleExtent([e,t]),a({minZoom:e})},setMaxZoom:e=>{const{panZoom:n,minZoom:t}=u();n?.setScaleExtent([t,e]),a({maxZoom:e})},setTranslateExtent:e=>{u().panZoom?.setTranslateExtent(e),a({translateExtent:e})},setPaneClickDistance:e=>{u().panZoom?.setClickDistance(e)},resetSelectedElements:()=>{const{edges:e,nodes:n,triggerNodeChanges:t,triggerEdgeChanges:o}=u(),r=n.reduce(((e,n)=>n.selected?[...e,K(n.id,!1)]:e),[]),i=e.reduce(((e,n)=>n.selected?[...e,K(n.id,!1)]:e),[]);t(r),o(i)},setNodeExtent:e=>{const{nodes:n,nodeLookup:t,parentLookup:o,nodeOrigin:r,elevateNodesOnSelect:i,nodeExtent:d}=u();e[0][0]===d[0][0]&&e[0][1]===d[0][1]&&e[1][0]===d[1][0]&&e[1][1]===d[1][1]||((0,s.bi)(n,t,o,{nodeOrigin:r,nodeExtent:e,elevateNodesOnSelect:i,checkEquality:!1}),a({nodeExtent:e}))},panBy:e=>{const{transform:n,width:t,height:o,panZoom:r,translateExtent:i}=u();return(0,s.No)({delta:e,panZoom:r,transform:n,translateExtent:i,width:t,height:o})},fitView:e=>{const{panZoom:n,width:t,height:o,minZoom:r,maxZoom:i,nodeLookup:a}=u();if(!n)return Promise.resolve(!1);const d=(0,s.YV)(a,e);return(0,s.Pr)({nodes:d,width:t,height:o,panZoom:n,minZoom:r,maxZoom:i},e)},fitViewSync:e=>{const{panZoom:n,width:t,height:o,minZoom:r,maxZoom:i,nodeLookup:a}=u();if(!n)return!1;const d=(0,s.YV)(a,e);return(0,s.Pr)({nodes:d,width:t,height:o,panZoom:n,minZoom:r,maxZoom:i},e),d.size>0},cancelConnection:()=>{a({connection:{...s.bK}})},updateConnection:e=>{a({connection:e})},reset:()=>a({...An()})})),Object.is);function jn({initialNodes:e,initialEdges:n,defaultNodes:t,defaultEdges:i,initialWidth:s,initialHeight:a,fitView:d,nodeOrigin:l,nodeExtent:c,children:g}){const[p]=(0,r.useState)((()=>Ln({nodes:e,edges:n,defaultNodes:t,defaultEdges:i,width:s,height:a,fitView:d,nodeOrigin:l,nodeExtent:c})));return(0,o.jsx)(u,{value:p,children:(0,o.jsx)(oe,{children:g})})}function zn({children:e,nodes:n,edges:t,defaultNodes:i,defaultEdges:s,width:a,height:d,fitView:l,nodeOrigin:u,nodeExtent:g}){return(0,r.useContext)(c)?(0,o.jsx)(o.Fragment,{children:e}):(0,o.jsx)(jn,{initialNodes:n,initialEdges:t,defaultNodes:i,defaultEdges:s,initialWidth:a,initialHeight:d,fitView:l,nodeOrigin:u,nodeExtent:g,children:e})}const _n={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var Bn=J((function({nodes:e,edges:n,defaultNodes:t,defaultEdges:a,className:d,nodeTypes:l,edgeTypes:c,onNodeClick:u,onEdgeClick:g,onInit:p,onMove:m,onMoveStart:h,onMoveEnd:f,onConnect:S,onConnectStart:y,onConnectEnd:b,onClickConnectStart:C,onClickConnectEnd:v,onNodeMouseEnter:w,onNodeMouseMove:k,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:P,onNodeDragStart:D,onNodeDrag:A,onNodeDragStop:L,onNodesDelete:j,onEdgesDelete:B,onDelete:V,onSelectionChange:Z,onSelectionDragStart:$,onSelectionDrag:T,onSelectionDragStop:X,onSelectionContextMenu:H,onSelectionStart:W,onSelectionEnd:F,onBeforeDelete:K,connectionMode:Y,connectionLineType:G=s.Do.Bezier,connectionLineStyle:U,connectionLineComponent:Q,connectionLineContainerStyle:q,deleteKeyCode:J="Backspace",selectionKeyCode:ee="Shift",selectionOnDrag:ne=!1,selectionMode:te=s.Qc.Full,panActivationKeyCode:oe="Space",multiSelectionKeyCode:re=((0,s.Ue)()?"Meta":"Control"),zoomActivationKeyCode:ie=((0,s.Ue)()?"Meta":"Control"),snapToGrid:se,snapGrid:ae,onlyRenderVisibleElements:de=!1,selectNodesOnDrag:le,nodesDraggable:ce,nodesConnectable:ue,nodesFocusable:ge,nodeOrigin:pe=R,edgesFocusable:me,edgesReconnectable:he,elementsSelectable:fe=!0,defaultViewport:Se=I,minZoom:ye=.5,maxZoom:be=2,translateExtent:Ce=s.ZO,preventScrolling:xe=!0,nodeExtent:ve,defaultMarkerColor:we="#b1b1b7",zoomOnScroll:Ee=!0,zoomOnPinch:ke=!0,panOnScroll:Ne=!1,panOnScrollSpeed:Me=.5,panOnScrollMode:Pe=s.ny.Free,zoomOnDoubleClick:De=!0,panOnDrag:Oe=!0,onPaneClick:Re,onPaneMouseEnter:Ie,onPaneMouseMove:Ae,onPaneMouseLeave:Le,onPaneScroll:je,onPaneContextMenu:ze,paneClickDistance:_e=0,nodeClickDistance:Be=0,children:Ve,onReconnect:Ze,onReconnectStart:$e,onReconnectEnd:Te,onEdgeContextMenu:Xe,onEdgeDoubleClick:He,onEdgeMouseEnter:We,onEdgeMouseMove:Fe,onEdgeMouseLeave:Ke,reconnectRadius:Ye=10,onNodesChange:Ge,onEdgesChange:Ue,noDragClassName:Qe="nodrag",noWheelClassName:qe="nowheel",noPanClassName:Je="nopan",fitView:en,fitViewOptions:nn,connectOnClick:tn,attributionPosition:on,proOptions:rn,defaultEdgeOptions:sn,elevateNodesOnSelect:an,elevateEdgesOnSelect:dn,disableKeyboardA11y:ln=!1,autoPanOnConnect:cn,autoPanOnNodeDrag:un,autoPanSpeed:gn,connectionRadius:pn,isValidConnection:mn,onError:hn,style:fn,id:Sn,nodeDragThreshold:yn,viewport:bn,onViewportChange:Cn,width:xn,height:vn,colorMode:wn="light",debug:En,onScroll:kn,...Nn},Mn){const Pn=Sn||"1",Dn=function(e){const[n,t]=(0,r.useState)("system"===e?null:e);return(0,r.useEffect)((()=>{if("system"!==e)return void t(e);const n=_(),o=()=>t(n?.matches?"dark":"light");return o(),n?.addEventListener("change",o),()=>{n?.removeEventListener("change",o)}}),[e]),null!==n?n:_()?.matches?"dark":"light"}(wn),On=(0,r.useCallback)((e=>{e.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),kn?.(e)}),[kn]);return(0,o.jsx)("div",{"data-testid":"rf__wrapper",...Nn,onScroll:On,style:{...fn,..._n},ref:Mn,className:(0,i.A)(["react-flow",d,Dn]),id:Sn,children:(0,o.jsxs)(zn,{nodes:e,edges:n,width:xn,height:vn,fitView:en,nodeOrigin:pe,nodeExtent:ve,children:[(0,o.jsx)(In,{onInit:p,onNodeClick:u,onEdgeClick:g,onNodeMouseEnter:w,onNodeMouseMove:k,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:P,nodeTypes:l,edgeTypes:c,connectionLineType:G,connectionLineStyle:U,connectionLineComponent:Q,connectionLineContainerStyle:q,selectionKeyCode:ee,selectionOnDrag:ne,selectionMode:te,deleteKeyCode:J,multiSelectionKeyCode:re,panActivationKeyCode:oe,zoomActivationKeyCode:ie,onlyRenderVisibleElements:de,defaultViewport:Se,translateExtent:Ce,minZoom:ye,maxZoom:be,preventScrolling:xe,zoomOnScroll:Ee,zoomOnPinch:ke,zoomOnDoubleClick:De,panOnScroll:Ne,panOnScrollSpeed:Me,panOnScrollMode:Pe,panOnDrag:Oe,onPaneClick:Re,onPaneMouseEnter:Ie,onPaneMouseMove:Ae,onPaneMouseLeave:Le,onPaneScroll:je,onPaneContextMenu:ze,paneClickDistance:_e,nodeClickDistance:Be,onSelectionContextMenu:H,onSelectionStart:W,onSelectionEnd:F,onReconnect:Ze,onReconnectStart:$e,onReconnectEnd:Te,onEdgeContextMenu:Xe,onEdgeDoubleClick:He,onEdgeMouseEnter:We,onEdgeMouseMove:Fe,onEdgeMouseLeave:Ke,reconnectRadius:Ye,defaultMarkerColor:we,noDragClassName:Qe,noWheelClassName:qe,noPanClassName:Je,rfId:Pn,disableKeyboardA11y:ln,nodeExtent:ve,viewport:bn,onViewportChange:Cn}),(0,o.jsx)(z,{nodes:e,edges:n,defaultNodes:t,defaultEdges:a,onConnect:S,onConnectStart:y,onConnectEnd:b,onClickConnectStart:C,onClickConnectEnd:v,nodesDraggable:ce,nodesConnectable:ue,nodesFocusable:ge,edgesFocusable:me,edgesReconnectable:he,elementsSelectable:fe,elevateNodesOnSelect:an,elevateEdgesOnSelect:dn,minZoom:ye,maxZoom:be,nodeExtent:ve,onNodesChange:Ge,onEdgesChange:Ue,snapToGrid:se,snapGrid:ae,connectionMode:Y,translateExtent:Ce,connectOnClick:tn,defaultEdgeOptions:sn,fitView:en,fitViewOptions:nn,onNodesDelete:j,onEdgesDelete:B,onDelete:V,onNodeDragStart:D,onNodeDrag:A,onNodeDragStop:L,onSelectionDrag:T,onSelectionDragStart:$,onSelectionDragStop:X,onMove:m,onMoveStart:h,onMoveEnd:f,noPanClassName:Je,nodeOrigin:pe,rfId:Pn,autoPanOnConnect:cn,autoPanOnNodeDrag:un,autoPanSpeed:gn,onError:hn,connectionRadius:pn,isValidConnection:mn,selectNodesOnDrag:le,nodeDragThreshold:yn,onBeforeDelete:K,paneClickDistance:_e,debug:En}),(0,o.jsx)(O,{onSelectionChange:Z}),Ve,(0,o.jsx)(E,{proOptions:rn,position:on}),(0,o.jsx)(x,{rfId:Pn,disableKeyboardA11y:ln})]})})}));const Vn=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");function Zn({children:e}){const n=p(Vn);return n?(0,l.createPortal)(e,n):null}function $n(e){const[n,t]=(0,r.useState)(e),o=(0,r.useCallback)((e=>t((n=>W(e,n)))),[]);return[n,t,o]}function Tn(e){const[n,t]=(0,r.useState)(e),o=(0,r.useCallback)((e=>t((n=>F(e,n)))),[]);return[n,t,o]}s.xc.error014();function Xn({dimensions:e,lineWidth:n,variant:t,className:r}){return(0,o.jsx)("path",{strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`,className:(0,i.A)(["react-flow__background-pattern",t,r])})}function Hn({radius:e,className:n}){return(0,o.jsx)("circle",{cx:e,cy:e,r:e,className:(0,i.A)(["react-flow__background-pattern","dots",n])})}var Wn;!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(Wn||(Wn={}));const Fn={[Wn.Dots]:1,[Wn.Lines]:1,[Wn.Cross]:6},Kn=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function Yn({id:e,variant:n=Wn.Dots,gap:t=20,size:s,lineWidth:a=1,offset:l=0,color:c,bgColor:u,style:g,className:m,patternClassName:h}){const f=(0,r.useRef)(null),{transform:S,patternId:y}=p(Kn,d.x),b=s||Fn[n],C=n===Wn.Dots,x=n===Wn.Cross,v=Array.isArray(t)?t:[t,t],w=[v[0]*S[2]||1,v[1]*S[2]||1],E=b*S[2],k=Array.isArray(l)?l:[l,l],N=x?[E,E]:w,M=[k[0]*S[2]||1+N[0]/2,k[1]*S[2]||1+N[1]/2],P=`${y}${e||""}`;return(0,o.jsxs)("svg",{className:(0,i.A)(["react-flow__background",m]),style:{...g,...le,"--xy-background-color-props":u,"--xy-background-pattern-color-props":c},ref:f,"data-testid":"rf__background",children:[(0,o.jsx)("pattern",{id:P,x:S[0]%w[0],y:S[1]%w[1],width:w[0],height:w[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${M[0]},-${M[1]})`,children:C?(0,o.jsx)(Hn,{radius:E/2,className:h}):(0,o.jsx)(Xn,{dimensions:N,lineWidth:a,variant:n,className:h})}),(0,o.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${P})`})]})}Yn.displayName="Background";const Gn=(0,r.memo)(Yn);function Un(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,o.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function Qn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,o.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function qn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,o.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function Jn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,o.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function et(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,o.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function nt({children:e,className:n,...t}){return(0,o.jsx)("button",{type:"button",className:(0,i.A)(["react-flow__controls-button",n]),...t,children:e})}const tt=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom});function ot({style:e,showZoom:n=!0,showFitView:t=!0,showInteractive:r=!0,fitViewOptions:s,onZoomIn:a,onZoomOut:l,onFitView:c,onInteractiveChange:u,className:g,children:h,position:f="bottom-left",orientation:S="vertical","aria-label":y="React Flow controls"}){const b=m(),{isInteractive:C,minZoomReached:x,maxZoomReached:v}=p(tt,d.x),{zoomIn:E,zoomOut:k,fitView:N}=ie(),M="horizontal"===S?"horizontal":"vertical";return(0,o.jsxs)(w,{className:(0,i.A)(["react-flow__controls",M,g]),position:f,style:e,"data-testid":"rf__controls","aria-label":y,children:[n&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(nt,{onClick:()=>{E(),a?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:v,children:(0,o.jsx)(Un,{})}),(0,o.jsx)(nt,{onClick:()=>{k(),l?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:x,children:(0,o.jsx)(Qn,{})})]}),t&&(0,o.jsx)(nt,{className:"react-flow__controls-fitview",onClick:()=>{N(s),c?.()},title:"fit view","aria-label":"fit view",children:(0,o.jsx)(qn,{})}),r&&(0,o.jsx)(nt,{className:"react-flow__controls-interactive",onClick:()=>{b.setState({nodesDraggable:!C,nodesConnectable:!C,elementsSelectable:!C}),u?.(!C)},title:"toggle interactivity","aria-label":"toggle interactivity",children:C?(0,o.jsx)(et,{}):(0,o.jsx)(Jn,{})}),h]})}ot.displayName="Controls";(0,r.memo)(ot);const rt=(0,r.memo)((function({id:e,x:n,y:t,width:r,height:s,style:a,color:d,strokeColor:l,strokeWidth:c,className:u,borderRadius:g,shapeRendering:p,selected:m,onClick:h}){const{background:f,backgroundColor:S}=a||{},y=d||f||S;return(0,o.jsx)("rect",{className:(0,i.A)(["react-flow__minimap-node",{selected:m},u]),x:n,y:t,rx:g,ry:g,width:r,height:s,style:{fill:y,stroke:l,strokeWidth:c},shapeRendering:p,onClick:h?n=>h(n,e):void 0})})),it=e=>e.nodes.map((e=>e.id)),st=e=>e instanceof Function?e:()=>e;const at=(0,r.memo)((function({id:e,nodeColorFunc:n,nodeStrokeColorFunc:t,nodeClassNameFunc:r,nodeBorderRadius:i,nodeStrokeWidth:a,shapeRendering:l,NodeComponent:c,onClick:u}){const{node:g,x:m,y:h,width:f,height:S}=p((n=>{const t=n.nodeLookup.get(e),{x:o,y:r}=t.internals.positionAbsolute,{width:i,height:a}=(0,s.uD)(t);return{node:t,x:o,y:r,width:i,height:a}}),d.x);return g&&!g.hidden&&(0,s.QE)(g)?(0,o.jsx)(c,{x:m,y:h,width:f,height:S,style:g.style,selected:!!g.selected,className:r(g),color:n(g),borderRadius:i,strokeColor:t(g),strokeWidth:a,shapeRendering:l,onClick:u,id:g.id}):null}));var dt=(0,r.memo)((function({nodeStrokeColor:e,nodeColor:n,nodeClassName:t="",nodeBorderRadius:r=5,nodeStrokeWidth:i,nodeComponent:s=rt,onClick:a}){const l=p(it,d.x),c=st(n),u=st(e),g=st(t),m="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return(0,o.jsx)(o.Fragment,{children:l.map((e=>(0,o.jsx)(at,{id:e,nodeColorFunc:c,nodeStrokeColorFunc:u,nodeClassNameFunc:g,nodeBorderRadius:r,nodeStrokeWidth:i,NodeComponent:s,onClick:a,shapeRendering:m},e)))})}));const lt=e=>{const n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:e.nodeLookup.size>0?(0,s.Mi)((0,s.aZ)(e.nodeLookup),n):n,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height}};function ct({style:e,className:n,nodeStrokeColor:t,nodeColor:a,nodeClassName:l="",nodeBorderRadius:c=5,nodeStrokeWidth:u,nodeComponent:g,bgColor:h,maskColor:f,maskStrokeColor:S,maskStrokeWidth:y,position:b="bottom-right",onClick:C,onNodeClick:x,pannable:v=!1,zoomable:E=!1,ariaLabel:k="React Flow mini map",inversePan:N,zoomStep:M=10,offsetScale:P=5}){const D=m(),O=(0,r.useRef)(null),{boundingRect:R,viewBB:I,rfId:A,panZoom:L,translateExtent:j,flowWidth:z,flowHeight:_}=p(lt,d.x),B=e?.width??200,V=e?.height??150,Z=R.width/B,$=R.height/V,T=Math.max(Z,$),X=T*B,H=T*V,W=P*T,F=R.x-(X-R.width)/2-W,K=R.y-(H-R.height)/2-W,Y=X+2*W,G=H+2*W,U=`react-flow__minimap-desc-${A}`,Q=(0,r.useRef)(0),q=(0,r.useRef)();Q.current=T,(0,r.useEffect)((()=>{if(O.current&&L)return q.current=(0,s.di)({domNode:O.current,panZoom:L,getTransform:()=>D.getState().transform,getViewScale:()=>Q.current}),()=>{q.current?.destroy()}}),[L]),(0,r.useEffect)((()=>{q.current?.update({translateExtent:j,width:z,height:_,inversePan:N,pannable:v,zoomStep:M,zoomable:E})}),[v,E,N,M,j,z,_]);const J=C?e=>{const[n,t]=q.current?.pointer(e)||[0,0];C(e,{x:n,y:t})}:void 0,ee=x?(0,r.useCallback)(((e,n)=>{const t=D.getState().nodeLookup.get(n);x(e,t)}),[]):void 0;return(0,o.jsx)(w,{position:b,style:{...e,"--xy-minimap-background-color-props":"string"==typeof h?h:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof f?f:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof S?S:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof y?y*T:void 0,"--xy-minimap-node-background-color-props":"string"==typeof a?a:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof t?t:void 0,"--xy-minimap-node-stroke-width-props":"string"==typeof u?u:void 0},className:(0,i.A)(["react-flow__minimap",n]),"data-testid":"rf__minimap",children:(0,o.jsxs)("svg",{width:B,height:V,viewBox:`${F} ${K} ${Y} ${G}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":U,ref:O,onClick:J,children:[k&&(0,o.jsx)("title",{id:U,children:k}),(0,o.jsx)(dt,{onClick:ee,nodeColor:a,nodeStrokeColor:t,nodeBorderRadius:c,nodeClassName:l,nodeStrokeWidth:u,nodeComponent:g}),(0,o.jsx)("path",{className:"react-flow__minimap-mask",d:`M${F-W},${K-W}h${Y+2*W}v${G+2*W}h${-Y-2*W}z\n        M${I.x},${I.y}h${I.width}v${I.height}h${-I.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}ct.displayName="MiniMap";const ut=(0,r.memo)(ct);(0,r.memo)((function({nodeId:e,position:n,variant:t=s.xN.Handle,className:a,style:d={},children:l,color:c,minWidth:u=10,minHeight:g=10,maxWidth:p=Number.MAX_VALUE,maxHeight:h=Number.MAX_VALUE,keepAspectRatio:f=!1,shouldResize:S,onResizeStart:y,onResize:b,onResizeEnd:C}){const x=ve(),v="string"==typeof e?e:x,w=m(),E=(0,r.useRef)(null),k=t===s.xN.Line?"right":"bottom-right",N=n??k,M=(0,r.useRef)(null);(0,r.useEffect)((()=>{if(E.current&&v)return M.current||(M.current=(0,s.ET)({domNode:E.current,nodeId:v,getStoreItems:()=>{const{nodeLookup:e,transform:n,snapGrid:t,snapToGrid:o,nodeOrigin:r,domNode:i}=w.getState();return{nodeLookup:e,transform:n,snapGrid:t,snapToGrid:o,nodeOrigin:r,paneDomNode:i}},onChange:(e,n)=>{const{triggerNodeChanges:t,nodeLookup:o,parentLookup:r,nodeOrigin:i}=w.getState(),a=[],d={x:e.x,y:e.y},l=o.get(v);if(l&&l.expandParent&&l.parentId){const n=l.origin??i,t=e.width??l.measured.width??0,c=e.height??l.measured.height??0,u={id:l.id,parentId:l.parentId,rect:{width:t,height:c,...(0,s.us)({x:e.x??l.position.x,y:e.y??l.position.y},{width:t,height:c},l.parentId,o,n)}},g=(0,s.r8)([u],o,r,i);a.push(...g),d.x=e.x?Math.max(n[0]*t,e.x):void 0,d.y=e.y?Math.max(n[1]*c,e.y):void 0}if(void 0!==d.x&&void 0!==d.y){const e={id:v,type:"position",position:{...d}};a.push(e)}if(void 0!==e.width&&void 0!==e.height){const n={id:v,type:"dimensions",resizing:!0,setAttributes:!0,dimensions:{width:e.width,height:e.height}};a.push(n)}for(const s of n){const e={...s,type:"position"};a.push(e)}t(a)},onEnd:()=>{const e={id:v,type:"dimensions",resizing:!1};w.getState().triggerNodeChanges([e])}})),M.current.update({controlPosition:N,boundaries:{minWidth:u,minHeight:g,maxWidth:p,maxHeight:h},keepAspectRatio:f,onResizeStart:y,onResize:b,onResizeEnd:C,shouldResize:S}),()=>{M.current?.destroy()}}),[N,u,g,p,h,f,y,b,C,S]);const P=N.split("-"),D=t===s.xN.Line?"borderColor":"backgroundColor",O=c?{...d,[D]:c}:d;return(0,o.jsx)("div",{className:(0,i.A)(["react-flow__resize-control","nodrag",...P,t,a]),ref:E,style:O,children:l})}))}}]);
//# sourceMappingURL=e8c4b161-b1ca269a783378815522.js.map