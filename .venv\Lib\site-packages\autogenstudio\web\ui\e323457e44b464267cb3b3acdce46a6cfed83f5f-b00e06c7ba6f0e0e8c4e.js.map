{"version": 3, "file": "e323457e44b464267cb3b3acdce46a6cfed83f5f-b00e06c7ba6f0e0e8c4e.js", "mappings": "8KAEA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,uZAA2Z,KAAQ,OAAQ,MAAS,Y,UCM1kB,EAAe,SAAsBA,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,qHCb5C,EADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iLAAqL,KAAQ,QAAS,MAAS,YCMtW,EAAgB,SAAuBJ,EAAOC,GAChD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,qECH5C,MAaaC,EAAiBC,IAC5B,MACMC,EAAS,CAAC,EAShB,MAViB,CAAC,EAAG,EAAG,EAAG,EAAG,GAErBC,SAAQC,IACfF,EAAO,YACFE,oBACKA,mBACAA,yBACLA,WArBa,EAACC,EAAUC,EAAYC,EAAON,KAClD,MAAM,kBACJO,EAAiB,iBACjBC,GACER,EACJ,MAAO,CACLS,aAAcF,EACdD,QACAI,WAAYF,EACZJ,WACAC,aACD,EAWMM,CAAcX,EAAM,kBAAkBG,KAAiBH,EAAM,oBAAoBG,KAAiBH,EAAMY,iBAAkBZ,EAAM,IAEhIC,CAAM,EAEFY,EAAgBb,IAC3B,MAAM,aACJc,GACEd,EACJ,MAAO,CACL,QAASe,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAchB,IAAS,CAC9DiB,WAAY,OACZ,CAAC,iBAAiBH,cAA0B,CAC1CR,MAAON,EAAMkB,kBACbC,OAAQ,cACR,oBAAqB,CACnBb,MAAON,EAAMkB,mBAEf,WAAY,CACVE,cAAe,WAItB,EAEUC,EAAiBrB,IAAS,CACrCsB,KAAM,CACJC,OAAQ,UACRC,cAAe,QACfC,aAAc,cACdrB,SAAU,MACVsB,WAAY1B,EAAM2B,eAClBC,WAAY,2BACZC,OAAQ,qCACRC,aAAc,GAEhBC,IAAK,CACHR,OAAQ,UACRC,cAAe,QACfC,aAAc,eACdrB,SAAU,MACVsB,WAAY1B,EAAM2B,eAClBC,WAAY,4BACZC,OAAQ,qCACRG,kBAAmB,EACnBF,aAAc,GAEhBG,KAAM,CACJC,QAAS,EAETC,gBAAiB,KAAK,IAExB,SAAU,CACRC,eAAgB,YAChBC,sBAAuB,QAEzB,SAAU,CACRD,eAAgB,gBAElBE,OAAQ,CACN5B,WAAY,KAGd,SAAU,CACR6B,aAAc,EACdC,YAAa,QACbN,QAAS,EACTO,GAAI,CACFF,aAAc,SACdC,YAAa,EACbhB,cAAe,QACfC,aAAc,IAGlBiB,GAAI,CACFC,cAAe,SACfD,GAAI,CACFC,cAAe,SAGnBC,GAAI,CACFD,cAAe,WAGjB,kBAAmB,CACjBpB,OAAQ,SAEVsB,IAAK,CACHX,QAAS,cACTY,WAAY,WACZC,SAAU,aACVnB,WAAY,2BACZC,OAAQ,qCACRC,aAAc,EACdJ,WAAY1B,EAAM2B,eAElBL,KAAM,CACJ0B,QAAS,SACTzB,OAAQ,EACRW,QAAS,EACT9B,SAAU,UACVsB,WAAY,UACZE,WAAY,cACZC,OAAQ,IAGZoB,WAAY,CACVzB,cAAe,UACfC,aAAc,EACdyB,kBAAmB,qCACnBC,QAAS,OAGAC,EAAoBpD,IAC/B,MAAM,aACJc,EAAY,UACZuC,GACErD,EACEsD,EAAaD,EACnB,MAAO,CACL,iBAAkB,CAChBE,SAAU,WACV,OAAQ,CACNC,iBAAkBxD,EAAMyD,KAAKzD,EAAMqD,WAAWK,KAAK,GAAGC,QACtDC,UAAW5D,EAAMyD,KAAKH,GAAYI,KAAK,GAAGC,QAC1ClD,aAAc,eAAc,QAAK6C,OAEnC,CAAC,GAAGxC,0BAAsC,CACxCyC,SAAU,WACVM,eAAgB7D,EAAMyD,KAAKzD,EAAM8D,UAAUC,IAAI,GAAGJ,QAClDK,cAAehE,EAAM8D,SACrBxD,MAAON,EAAMiE,qBAEbvD,WAAY,SACZN,SAAUJ,EAAMI,SAChB8D,UAAW,SACX9C,cAAe,QAEjB+C,SAAU,CACR5C,OAAQ,cAER6C,cAAe,OACfC,OAAQ,QAGb,EAEUC,EAAoBtE,IAAS,CACxC,CAAC,GAAGA,EAAMc,6BAA8B,CACtC,sCAGW,CACTR,MAAON,EAAMuE,eAGjB,CAAC,GAAGvE,EAAMc,+BAAgC,CACxC0D,kBAAmB,KC3LjBC,EAAqBzE,IACzB,MAAM,aACJc,EAAY,eACZ4D,GACE1E,EACJ,MAAO,CACL,CAACc,GAAeC,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAC5IV,MAAON,EAAM2E,UACbC,UAAW,aACXvE,WAAYL,EAAMK,WAClB,CAAC,IAAIS,eAA2B,CAC9BR,MAAON,EAAMiE,sBAEf,CAAC,IAAInD,aAAyB,CAC5BR,MAAON,EAAMuE,cAEf,CAAC,IAAIzD,aAAyB,CAC5BR,MAAON,EAAM6E,cAEf,CAAC,IAAI/D,YAAwB,CAC3BR,MAAON,EAAM8E,WACb,sBAAuB,CACrBxE,MAAON,EAAM+E,kBAEf,WAAY,CACVzE,MAAON,EAAMgF,kBAGjB,CAAC,IAAIlE,cAA0B,CAC7BR,MAAON,EAAMkB,kBACbC,OAAQ,cACRF,WAAY,QAEd,qCAGI,CACFR,aAAc,QAEfV,EAAeC,IAAS,CACzB,CAAC,iBACOc,mBACAA,mBACAA,mBACAA,mBACAA,aACJ,CACF8C,UAAWc,GAEb,qGASM,CACJ,uFAMI,CACFd,UAAWc,MAGbrD,EAAerB,IAASa,EAAcb,IAAS,CAEjD,CAAC,aACGc,sBACAA,wBACAA,oBACAA,kBACAC,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAchB,IAAS,CACzDwE,kBAAmBxE,EAAMiF,cAEzB7B,EAAkBpD,IAASsE,EAAkBtE,ID+Gd,CACrC,yCAGI,CACFgD,QAAS,eACTkC,SAAU,QAEZ,yBAA0B,CACxBpC,WAAY,SACZqC,SAAU,SACVC,aAAc,WAEd,YAAa,CACXC,cAAe,UAEjB,SAAU,CACR5D,aAAc,EACdyD,SAAU,qBACVlC,QAAS,eACTmC,SAAU,SACVC,aAAc,WACdC,cAAe,SAEfC,UAAW,gBAGf,2BAA4B,CAC1BtC,QAAS,cACTmC,SAAU,SACVI,gBAAiB,EACjBC,gBAAiB,cC9I+D,CAC9E,QAAS,CACPC,UAAW,SAGhB,EAOH,OAAe,QAAc,cAAczF,GAAS,CAACyE,EAAmBzE,MALnC,KAAM,CACzC0E,eAAgB,QAChBnE,kBAAmB,YCwBrB,MA1GiBb,IACf,MAAM,UACJgG,EACA,aAAcC,EAAS,UACvBC,EAAS,MACTC,EAAK,UACLJ,EAAS,UACTK,EAAS,SACTC,GAAW,EAAI,MACfC,EAAK,OACLC,EAAM,SACNC,EAAQ,MACRC,EAAK,UACLC,EAAS,UACTC,EAAyB,gBAAoB,EAAe,OAC1D3G,EACEC,EAAM,SAAa,MACnB2G,EAAgB,UAAa,GAC7BC,EAAc,SAAa,OAC1BC,EAASC,GAAc,WAAeT,GAC7C,aAAgB,KACdS,EAAWT,EAAM,GAChB,CAACA,IACJ,aAAgB,KACd,IAAIU,EACJ,GAA2B,QAAtBA,EAAK/G,EAAI6G,eAA4B,IAAPE,OAAgB,EAASA,EAAGC,kBAAmB,CAChF,MAAM,SACJC,GACEjH,EAAI6G,QAAQG,kBAChBC,EAASC,QACT,MAAM,OACJC,GACEF,EAASZ,MACbY,EAASG,kBAAkBD,EAAQA,EACrC,IACC,IACH,MAoBME,EAAgB,KACpBf,EAAOO,EAAQS,OAAO,GAwBjBC,EAAYC,EAAQC,GAAa,EAAS1B,GAC3C2B,EAAoB,IAAW3B,EAAW,GAAGA,iBAA0B,CAC3E,CAAC,GAAGA,SAAgC,QAAdD,EACtB,CAAC,GAAGC,KAAaU,OAAgBA,GAChCR,EAAWuB,EAAQC,GACtB,OAAOF,EAAwB,gBAAoB,MAAO,CACxDtB,UAAWyB,EACXxB,MAAOA,GACO,gBAAoByB,EAAA,EAAU,CAC5C3H,IAAKA,EACLmG,UAAWA,EACXE,MAAOQ,EACPe,SAzDeC,IACf,IAAI,OACFC,GACED,EACJf,EAAWgB,EAAOzB,MAAM0B,QAAQ,UAAW,IAAI,EAsD/CC,UA9CgBC,IAChB,IAAI,QACFC,GACED,EAEAtB,EAAcE,UAClBD,EAAYC,QAAUqB,EAAO,EAyC7BC,QApCcC,IACd,IAAI,QACFF,EAAO,QACPG,EAAO,OACPC,EAAM,QACNC,EAAO,SACPC,GACEJ,EAEAxB,EAAYC,UAAYqB,GAAWvB,EAAcE,SAAWwB,GAAWC,GAAUC,GAAWC,IAG5FN,IAAYO,EAAA,EAAQC,OACtBrB,IACAb,SAA8CA,KACrC0B,IAAYO,EAAA,EAAQE,KAC7BpC,IACF,EAoBAqC,mBAtDyB,KACzBjC,EAAcE,SAAU,CAAI,EAsD5BgC,iBApDuB,KACvBlC,EAAcE,SAAU,CAAK,EAoD7BiC,OApBa,KACbzB,GAAe,EAoBf,aAAcrB,EACd+C,KAAM,EACN3C,SAAUA,IACM,OAAdM,GAAqB,QAAaA,EAAW,CAC/CT,UAAW,GAAGF,2BACX,MAAM,E,6BCjHb,IAAIiD,EAAsC,SAAUC,EAASC,EAAYC,EAAGC,GAM1E,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GACjD,SAASC,EAAUnD,GACjB,IACEoD,EAAKL,EAAUM,KAAKrD,GACtB,CAAE,MAAOsD,GACPJ,EAAOI,EACT,CACF,CACA,SAASC,EAASvD,GAChB,IACEoD,EAAKL,EAAiB,MAAE/C,GAC1B,CAAE,MAAOsD,GACPJ,EAAOI,EACT,CACF,CACA,SAASF,EAAKI,GApBhB,IAAexD,EAqBXwD,EAAOC,KAAOR,EAAQO,EAAOxD,QArBlBA,EAqBiCwD,EAAOxD,MApB9CA,aAAiB8C,EAAI9C,EAAQ,IAAI8C,GAAE,SAAUG,GAClDA,EAAQjD,EACV,KAkB4D0D,KAAKP,EAAWI,EAC5E,CACAH,GAAML,EAAYA,EAAUY,MAAMf,EAASC,GAAc,KAAKQ,OAChE,GACF,EAmDA,MA9CqB7B,IACnB,IAAI,WACFoC,EAAU,SACVC,GACErC,EACJ,MAAOsC,EAAQC,GAAa,YAAe,IACpCC,EAAaC,GAAkB,YAAe,GAC/CC,EAAY,SAAa,MACzBC,EAAc,KACdD,EAAU1D,SACZ4D,aAAaF,EAAU1D,QACzB,EAEI6D,EAAc,CAAC,EACjBT,EAAWU,SACbD,EAAYC,OAASV,EAAWU,QAElC,aAAgB,IAAMH,GAAa,IAEnC,MAAMI,GAAU,EAAAC,EAAA,IAASlB,GAAKX,OAAU,OAAQ,OAAQ,GAAQ,YAC9D,IAAIjC,EACJ4C,SAAsCA,EAAEmB,iBACxCnB,SAAsCA,EAAEoB,kBACxCT,GAAe,GACf,IACE,MAAMU,EAAkC,mBAApBf,EAAWe,WAA4Bf,EAAWe,OAASf,EAAWe,KAC1F,IAAKA,GCzDI,SAAgBC,GAE7B,OADgBC,UAAU/D,OAAS,QAAsBgE,IAAjBD,UAAU,IAAmBA,UAAU,IAC9D,MAACD,EAAuD,GAClEG,MAAMC,QAAQJ,GAAaA,EAAY,CAACA,EACjD,CDqDmBK,CAAOpB,GAAU,GAAMqB,KAAK,KAAO,GAAIb,GACpDJ,GAAe,GACfF,GAAU,GAEVI,IACAD,EAAU1D,QAAU2E,YAAW,KAC7BpB,GAAU,EAAM,GACf,KAC0B,QAA5BrD,EAAKkD,EAAWwB,cAA2B,IAAP1E,GAAyBA,EAAG2E,KAAKzB,EAAYN,EACpF,CAAE,MAAOgC,GAEP,MADArB,GAAe,GACTqB,CACR,CACF,MACA,MAAO,CACLxB,SACAE,cACAO,UACD,EE1EY,SAASgB,EAAgBC,EAAYC,GAClD,OAAO,WAAc,KACnB,MAAMC,IAAYF,EAClB,MAAO,CAACE,EAAS3K,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGyK,GAAiBC,GAAiC,iBAAfF,EAA0BA,EAAa,MAAM,GAChI,CAACA,GACN,CCEA,MAPoBxF,IAClB,MAAMrG,GAAM,IAAAgM,aAAOb,GAInB,OAHA,IAAAc,YAAU,KACRjM,EAAI6G,QAAUR,CAAK,IAEdrG,EAAI6G,OAAO,ECepB,MApBwB,CAACqF,EAASC,EAAgBjC,KAAa,IAAAkC,UAAQ,KACrD,IAAZF,EACK,CACLG,MAAOF,QAAuDA,EAAiBjC,IAGlE,IAAAoC,gBAAeJ,GACvB,CACLG,MAAOH,GAGY,iBAAZA,EACF9K,OAAOC,OAAO,CACnBgL,MAAOF,QAAuDA,EAAiBjC,GAC9EgC,GAEE,CACLG,MAAOH,IAER,CAACA,EAASC,EAAgBjC,IClBzBqC,EAAgC,SAAUC,EAAG7C,GAC/C,IAAI8C,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOpL,OAAOuL,UAAUC,eAAelB,KAAKc,EAAGE,IAAM/C,EAAEkD,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCpL,OAAO0L,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAItL,OAAO0L,sBAAsBN,GAAIO,EAAIL,EAAEvF,OAAQ4F,IAClIpD,EAAEkD,QAAQH,EAAEK,IAAM,GAAK3L,OAAOuL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAOA,MAAMQ,EAA0B,cAAiB,CAAClN,EAAOC,KACvD,MACI+F,UAAWmH,EACXzG,UAAW0G,EAAY,UAAS,UAChClH,EAAS,cACTmH,EAAa,cACbC,EAAa,SACbnD,EACApE,UAAWwH,EAAmB,MAC9BpH,GACEnG,EACJwN,EAAYhB,EAAOxM,EAAO,CAAC,YAAa,YAAa,YAAa,gBAAiB,gBAAiB,WAAY,YAAa,WACzH,aACJyN,EACA1H,UAAW2H,EACXxH,UAAWyH,EACXxH,MAAOyH,IACL,QAAmB,cACjB7H,EAAYwH,QAAiEA,EAAsBG,EACnGG,EAAYP,GAAgB,QAAWrN,EAAKqN,GAAiBrN,EAC7D+F,EAAYyH,EAAa,aAAcN,GAM7C,MAAO3F,EAAYC,EAAQC,GAAa,EAAS1B,GAC3C8H,EAAqB,IAAW9H,EAAW2H,EAAkB,CACjE,CAAC,GAAG3H,SAAgC,QAAdD,GACrBG,EAAWmH,EAAe5F,EAAQC,GAC/BqG,EAAc1M,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGsM,GAAezH,GACnE,OAAOqB,EAGP,gBAAoB4F,EAAW/L,OAAOC,OAAO,CAC3C4E,UAAW4H,EACX3H,MAAO4H,EACP9N,IAAK4N,GACJL,GAAYrD,GAAU,IAK3B,Q,UCzDA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8XAAkY,KAAQ,OAAQ,MAAS,YCMjjB,EAAe,SAAsBnK,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,WCfrC,SAAS,GAAO4N,GACrB,OAAY,IAARA,EACK,EAAC,GAAO,GAEV3C,MAAMC,QAAQ0C,GAAOA,EAAM,CAACA,EACrC,CACO,SAASC,GAAQC,EAAKC,EAAaC,GACxC,OAAY,IAARF,QAAwB9C,IAAR8C,EACXC,EAEFD,GAAOE,GAAWD,CAC3B,CA2BO,MAAME,GAAcL,GAAO,CAAC,SAAU,UAAUM,gBAAgBN,GCKvE,OAlCgBlG,IACd,IAAI,UACF9B,EAAS,OACToE,EAAM,OACNmE,EAAM,SACNC,EAAQ,SACRC,EAAQ,KACRrO,EAAI,SACJsO,EAAQ,OACRhD,EACAiD,QAASC,GACP9G,EACJ,MAAM+G,EAAe,GAAOJ,GACtBK,EAAY,GAAO1O,IAEvBgK,OAAQ2E,EACRC,KAAMC,GACJV,QAAuCA,EAAS,CAAC,EAC/CW,EAAY9E,EAAS2E,EAAaE,EAClCE,EAAYlB,GAAQY,EAAazE,EAAS,EAAI,GAAI8E,GAClDjJ,EAAiC,iBAAdkJ,EAAyBA,EAAYD,EAC9D,OAAoB,gBAAoB,IAAS,CAC/C5C,MAAO6C,GACO,gBAAoB,SAAU,CAC5CC,KAAM,SACNlJ,UAAW,IAAW,GAAGF,SAAkB,CACzC,CAAC,GAAGA,kBAA2BoE,EAC/B,CAAC,GAAGpE,oBAA6BwI,IAEnC3D,QAASa,EACT,aAAczF,EACdyI,SAAUA,GACTtE,EAAS6D,GAAQa,EAAU,GAAiB,gBAAoBO,EAAA,EAAe,OAAO,GAAQpB,GAAQa,EAAU,GAAIF,EAA0B,gBAAoBU,GAAA,EAAiB,MAAqB,gBAAoB,EAAc,OAAO,IAAO,E,UClC7P,MAAMC,GAA2B,cAAiB,CAACzH,EAAM7H,KACvD,IAAI,MACFkG,EAAK,SACLgE,GACErC,EACJ,MAAM0H,EAAU,SAAa,MAQ7B,OAPA,sBAA0BvP,GAAK,KAAM,CACnCwP,SAAU,KACR,MAAMC,EAAOF,EAAQ1I,QACrB,OAAO4I,EAAKC,aAAeD,EAAKE,YAAY,EAE9CC,UAAW,IAAML,EAAQ1I,QAAQ8I,iBAEf,gBAAoB,OAAQ,CAC9C,eAAe,EACf3P,IAAKuP,EACLrJ,MAAO9E,OAAOC,OAAO,CACnBuC,SAAU,QACVP,QAAS,QACTwM,KAAM,EACNC,IAAK,EACLrO,cAAe,OACfe,gBAAiB,yBAChB0D,IACFgE,EAAS,IAGd,SAAS6F,GAAWC,EAAUC,GAC5B,IAAIC,EAAU,EACd,MAAMC,EAAkB,GACxB,IAAK,IAAIpD,EAAI,EAAGA,EAAIiD,EAAS7I,OAAQ4F,GAAK,EAAG,CAE3C,GAAImD,IAAYD,EACd,OAAOE,EAET,MAAMC,EAAOJ,EAASjD,GAGhBsD,EAAUH,GAFD9B,GAAYgC,GACFE,OAAOF,GAAMjJ,OAAS,GAI/C,GAAIkJ,EAAUJ,EAAK,CACjB,MAAMM,EAAUN,EAAMC,EAEtB,OADAC,EAAgBK,KAAKF,OAAOF,GAAMK,MAAM,EAAGF,IACpCJ,CACT,CACAA,EAAgBK,KAAKJ,GACrBF,EAAUG,CACZ,CACA,OAAOL,CACT,CAEA,MAAMU,GAAsB,EAItBC,GAAkC,EAClCC,GAAgB,CACpBvN,QAAS,cACTmC,SAAU,SACVK,gBAAiB,YAEJ,SAASgL,GAAgB9Q,GACtC,MAAM,cACJ+Q,EAAa,MACbC,EAAK,KACL/F,EAAI,SACJd,EAAQ,KACRnB,EAAI,SACJiI,EAAQ,SACRC,EAAQ,WACRC,GACEnR,EACEiQ,EAAW,WAAc,KAAM,EAAAmB,EAAA,GAAQnG,IAAO,CAACA,IAC/CoG,EAAU,WAAc,IAhDZpB,IAAYA,EAASqB,QAAO,CAACC,EAAUlB,IAASkB,GAAYlD,GAAYgC,GAAQE,OAAOF,GAAMjJ,OAAS,IAAI,GAgDxFoK,CAAYvB,IAAW,CAAChF,IAGtDwG,EAAc,WAAc,IAAMtH,EAAS8F,GAAU,IAAQ,CAAChF,KAE7DyG,EAAkBC,GAAuB,WAAe,MACzDC,EAAY,SAAa,MAEzBC,EAAuB,SAAa,MACpCC,EAAkB,SAAa,MAE/BC,EAAsB,SAAa,MACnCC,EAAuB,SAAa,OACnCC,EAAaC,GAAkB,YAAe,IAC9CC,EAAcC,GAAmB,WAAezB,KAChD0B,EAAgBC,GAAqB,WAAe,IACpDC,EAAkBC,GAAuB,WAAe,OAE/D,EAAAC,EAAA,IAAgB,KAEZL,EADErB,GAAiBC,GAASK,EAxCH,EA2CTV,GAClB,GACC,CAACK,EAAO/F,EAAMjC,EAAM+H,EAAed,KAEtC,EAAAwC,EAAA,IAAgB,KACd,IAAIzL,EAAI0L,EAAIC,EAAIC,EAChB,GAjD2B,IAiDvBT,EAAyC,CAC3CC,EAjDuB,GAmDvB,MAAMS,EAAiBhB,EAAqB/K,SAAWgM,iBAAiBjB,EAAqB/K,SAAS1D,WACtGoP,EAAoBK,EACtB,MAAO,GArDkB,IAqDdV,EAAuC,CAChD,MAAMY,KAAmD,QAAlC/L,EAAK8K,EAAgBhL,eAA4B,IAAPE,OAAgB,EAASA,EAAGyI,YAC7F2C,EAAgBW,EAtDe,EAsD6BnC,IAC5De,EAAoBoB,EAAa,CAAC,EAAG1B,GAAW,MAChDa,EAAea,GAEf,MAAMC,GAA6D,QAAlCN,EAAKZ,EAAgBhL,eAA4B,IAAP4L,OAAgB,EAASA,EAAG7C,cAAgB,EAEjHoD,EAAkC,IAATjK,EAAa,GAA4C,QAAtC2J,EAAKZ,EAAoBjL,eAA4B,IAAP6L,OAAgB,EAASA,EAAG9C,cAAgB,EACtIqD,GAAmE,QAAvCN,EAAKZ,EAAqBlL,eAA4B,IAAP8L,OAAgB,EAASA,EAAG/C,cAAgB,EACvHsD,EAAgBC,KAAKC,IAAIL,EAE/BC,EAAyBC,GACzBZ,EAAkBa,EAAgB,GAClChC,EAAW4B,EACb,IACC,CAACZ,IAEJ,MAAMmB,EAAc5B,EAAmB0B,KAAKG,MAAM7B,EAAiB,GAAKA,EAAiB,IAAM,GAAK,GACpG,EAAAe,EAAA,IAAgB,KACd,IAAIzL,EACJ,MAAOwM,EAAUC,GAAY/B,GAAoB,CAAC,EAAG,GACrD,GAAI8B,IAAaC,EAAU,CACzB,MACMV,IAD0C,QAA5B/L,EAAK4K,EAAU9K,eAA4B,IAAPE,OAAgB,EAASA,EAAG6I,cAAgB,GACrEwC,EAC/B,IAAIqB,EAAiBJ,EACjBG,EAAWD,GAAa,IAC1BE,EAAiBX,EAAaS,EAAWC,GAE3C9B,EAAoBoB,EAAa,CAACS,EAAUE,GAAkB,CAACA,EAAgBD,GACjF,IACC,CAAC/B,EAAkB4B,IAEtB,MAAMK,EAAe,WAAc,KAEjC,IAAK5C,EACH,OAAO5G,EAAS8F,GAAU,GAE5B,GA1FiC,IA0F7BkC,IAAkDT,GAAoBA,EAAiB,KAAOA,EAAiB,GAAI,CACrH,MAAMkC,EAAUzJ,EAAS8F,GAAU,GAGnC,MAAI,CAACW,GAAiCD,IAAqBrC,SAAS6D,GAC3DyB,EAEW,gBAAoB,OAAQ,CAC9CzN,MAAO9E,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGuP,IAAgB,CACrDhL,gBAAiBmD,KAElB4K,EACL,CACA,OAAOzJ,EAAS8G,EAAWhB,EAAWD,GAAWC,EAAUyB,EAAiB,IAAKO,EAAY,GAC5F,CAAChB,EAAUkB,EAAcT,EAAkBzB,GAAU4D,QAAO,QAAmB3C,KAE5E4C,EAAe,CACnB9C,QACAnP,OAAQ,EACRW,QAAS,EACTY,WAAiC,WAArBmP,EAAgC,SAAW,WAEzD,OAAoB,gBAAoB,WAAgB,KAAMoB,EAjHnC,IAiHiDxB,GAAuD,gBAAoB,WAAgB,KAAmB,gBAAoB5C,GAAa,CACzNpJ,MAAO9E,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwS,GAAejD,IAAgB,CAClFhL,gBAAiBmD,IAEnB/I,IAAK6R,GACJL,GAA2B,gBAAoBlC,GAAa,CAC7DpJ,MAAO9E,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwS,GAAejD,IAAgB,CAClFhL,gBAAiBmD,EAAO,IAE1B/I,IAAK8R,GACJN,GAA2B,gBAAoBlC,GAAa,CAC7DpJ,MAAO9E,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwS,GAAejD,IAAgB,CAClFhL,gBAAiB,IAEnB5F,IAAK+R,GACJ7H,EAAS,IAAI,KA/HmB,IA+HTgI,GAAiDT,GAAoBA,EAAiB,KAAOA,EAAiB,IAAoB,gBAAoBnC,GAAa,CAC3LpJ,MAAO9E,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwS,GAAe,CACpD/D,IAAK,MAEP9P,IAAK2R,GACJzH,EAAS6F,GAAWC,EAAUqD,IAAc,IAtIlB,IAsI2BnB,GAAyD,gBAAoB,OAAQ,CAC3IhM,MAAO,CACL/C,WAAY,WAEdnD,IAAK4R,IAET,CCnLA,OAjBwB/J,IACtB,IAAI,eACFiM,EAAc,WACdC,EAAU,SACV7J,EAAQ,aACR8J,GACEnM,EACJ,OAAMmM,aAAmD,EAASA,EAAa3H,QAAWyH,EAGtE,gBAAoB,IAAS1S,OAAOC,OAAO,CAC7D4S,OAAMF,QAAa5I,GAClB6I,GAAe9J,GAJTA,CAIkB,ECdzB,GAAgC,SAAUsC,EAAG7C,GAC/C,IAAI8C,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOpL,OAAOuL,UAAUC,eAAelB,KAAKc,EAAGE,IAAM/C,EAAEkD,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCpL,OAAO0L,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAItL,OAAO0L,sBAAsBN,GAAIO,EAAIL,EAAEvF,OAAQ4F,IAClIpD,EAAEkD,QAAQH,EAAEK,IAAM,GAAK3L,OAAOuL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAkDA,MACMyH,GAAoB,cAAiB,CAACnU,EAAOC,KACjD,IAAI+G,EACJ,MACIhB,UAAWmH,EAAkB,UAC7BjH,EAAS,MACTC,EAAK,KACLiJ,EAAI,SACJgF,EAAQ,SACRjK,EAAQ,SACRkK,EAAQ,SACRC,EAAQ,SACRC,EAAQ,UACR7N,EAAS,MACT4F,GACEtM,EACJwN,EAAY,GAAOxN,EAAO,CAAC,YAAa,YAAa,QAAS,OAAQ,WAAY,WAAY,WAAY,WAAY,WAAY,YAAa,WAC3I,aACJyN,EAAY,UACZ1H,GACE,aAAiB,OACdyO,IAAc,EAAAC,EAAA,GAAU,QACzBC,EAAgB,SAAa,MAC7BC,EAAc,SAAa,MAE3B3O,EAAYyH,EAAa,aAAcN,GACvCyH,GAAY,EAAAC,EAAA,GAAKrH,EAAW,CAAC,OAAQ,OAAQ,SAAU,YAAa,SAAU,WAAY,YAEzFsH,EAAYC,GAAclJ,EAAgByI,IAC1CU,EAASC,IAAc,EAAAC,EAAA,IAAe,EAAO,CAClD5O,MAAOyO,EAAWC,WAEd,YACJG,EAAc,CAAC,SACbJ,EACEK,EAAcC,IAClB,IAAIrO,EACAqO,IAC4B,QAA7BrO,EAAK+N,EAAWO,eAA4B,IAAPtO,GAAyBA,EAAG2E,KAAKoJ,IAEzEE,EAAWI,EAAK,EAGZE,EAAc,EAAYP,IAChC,EAAAvC,EAAA,IAAgB,KACd,IAAIzL,GACCgO,GAAWO,IACiB,QAA9BvO,EAAK2N,EAAY7N,eAA4B,IAAPE,GAAyBA,EAAGG,QACrE,GACC,CAAC6N,IACJ,MAAMQ,EAAc5L,IAClBA,SAAsCA,EAAEmB,iBACxCqK,GAAY,EAAK,EAEbK,EAAenP,IACnB,IAAIU,EAC2B,QAA9BA,EAAK+N,EAAWlN,gBAA6B,IAAPb,GAAyBA,EAAG2E,KAAKoJ,EAAYzO,GACpF8O,GAAY,EAAM,EAEdM,EAAe,KACnB,IAAI1O,EAC2B,QAA9BA,EAAK+N,EAAWvO,gBAA6B,IAAPQ,GAAyBA,EAAG2E,KAAKoJ,GACxEK,GAAY,EAAM,GAGbO,EAAYzL,GAAc2B,EAAgB0I,IAC3C,OACJnK,EAAM,YACNE,GACAO,QAAS+K,IACP,EAAa,CACf1L,aACAC,cAGK0L,GAAoBC,IAAyB,YAAe,IAC5DC,GAAuBC,IAA4B,YAAe,IAClEC,GAAcC,IAAmB,YAAe,IAChDC,GAAkBC,IAAuB,YAAe,IACxDC,GAAiBC,IAAsB,YAAe,IACtDvC,GAAgBwC,IAAkB1K,EAAgBwI,EAAU,CACjEmC,YAAY,EACZC,OAAQC,GAAcA,EAAalC,aAA+C,EAASA,EAAWmC,SAAWnC,aAA+C,EAASA,EAAWoC,UAE/K3F,GAAU4F,KAAe,EAAA3B,EAAA,GAAeqB,GAAeO,kBAAmB,EAAO,CACtFxQ,MAAOiQ,GAAetF,WAElB8F,GAAuBhD,MAAoB9C,IAA0C,gBAA9BsF,GAAeC,aAEtE,KACJxN,GAAO,GACLuN,GACES,GAAsB,WAAc,IAE1CD,UAE0B3L,IAA1BmL,GAAeU,QAAwBV,GAAepF,YAEtDoF,GAAeC,YAAc1B,GAAca,IAAa,CAACoB,GAAsBR,GAAgBzB,EAAYa,KAC3G,EAAAlD,EAAA,IAAgB,KACVsB,KAAmBiD,KACrBlB,IAAsB,OAAe,oBACrCE,IAAyB,OAAe,iBAC1C,GACC,CAACgB,GAAqBjD,KACzB,MAAOmD,GAAaC,IAAkB,WAAeJ,IAC/CK,GAAoB,WAAc,KAClCJ,KAGS,IAAThO,GACK+M,GAEFF,KACN,CAACmB,GAAqBjB,GAAuBF,MAGhD,EAAApD,EAAA,IAAgB,KACd0E,GAAeC,IAAqBL,GAAqB,GACxD,CAACK,GAAmBL,KACvB,MAAMM,GAAmBN,KAAyBG,GAAcf,GAAmBF,IAC7EqB,GAAkBP,IAAiC,IAAT/N,IAAckO,GACxDK,GAAeR,IAAwB/N,GAAO,GAAKkO,IAOlDM,GAAeC,IAAoB,WAAe,GAQnDC,GAAeC,IACnB,IAAI3Q,EACJkP,GAAgByB,GAEZ1B,KAAiB0B,IACkB,QAApC3Q,EAAKuP,GAAepF,kBAA+B,IAAPnK,GAAyBA,EAAG2E,KAAK4K,GAAgBoB,GAChG,EAGF,aAAgB,KACd,MAAMC,EAAUlD,EAAc5N,QAC9B,GAAIiN,IAAkBmD,IAAeU,EAAS,CAC5C,MAAMC,EJ9LL,SAAuBC,GAE5B,MAAMC,EAAWC,SAASC,cAAc,MACxCH,EAAII,YAAYH,GAKhB,MAAMI,EAAOL,EAAIM,wBACXC,EAAYN,EAASK,wBAI3B,OAFAN,EAAIQ,YAAYP,GAIdI,EAAKrI,KAAOuI,EAAUvI,MAAQuI,EAAUE,MAAQJ,EAAKI,OAErDJ,EAAKpI,IAAMsI,EAAUtI,KAAOsI,EAAUG,OAASL,EAAKK,MAExD,CI2K8BC,CAAcb,GAClCzB,KAAqB0B,GACvBzB,GAAoByB,EAExB,IACC,CAAC9D,GAAgBmD,GAAa/M,EAAUoN,GAAclB,GAAiBmB,KAG1E,aAAgB,KACd,MAAMI,EAAUlD,EAAc5N,QAC9B,GAAoC,oBAAzB4R,uBAAyCd,IAAYV,KAAgBH,GAC9E,OAGF,MAAM4B,EAAW,IAAID,sBAAqB,KACxCpC,KAAqBsB,EAAQgB,aAAa,IAG5C,OADAD,EAASE,QAAQjB,GACV,KACLe,EAASG,YAAY,CACtB,GACA,CAAC5B,GAAaH,KAEjB,MAAM9C,GAAe,EAAgBsC,GAAepK,QAAS4I,EAAW9J,KAAMd,GACxE4O,GAAe,WAAc,KACjC,GAAKhF,KAAkBmD,GAGvB,MAAO,CAACnC,EAAW9J,KAAMd,EAAUmC,EAAO2H,GAAa3H,OAAO0M,KAAK3K,GAAY,GAC9E,CAAC0F,GAAgBmD,GAAa5K,EAAO2H,GAAa3H,MAAO+K,KAG5D,GAAIrC,EACF,OAAoB,gBAAoB,EAAU,CAChD1O,MAAkC,QAA1BU,EAAK+N,EAAW9J,YAAyB,IAAPjE,EAAgBA,EAAyB,iBAAbmD,EAAwBA,EAAW,GACzG5D,OAAQkP,EACRjP,SAAUkP,EACVjP,MAAOsO,EAAWtO,MAClBT,UAAWA,EACXE,UAAWA,EACXC,MAAOA,EACPJ,UAAWA,EACXW,UAAWA,EACXN,UAAW2O,EAAW3O,UACtBC,SAAU0O,EAAW1O,SACrBM,UAAWoO,EAAWpO,YAK1B,MAAMsS,GAAe,KACnB,MAAM,WACJzC,EAAU,OACVC,GACEF,GACJ,OAAOC,EAA2B,gBAAoB,SAAU,CAC9DpH,KAAM,SACN8J,IAAK,SACLhT,UAAW,GAAGF,KAAaiL,GAAW,WAAa,WACnDpG,QAASjB,GApFS,EAACA,EAAGuP,KACxB,IAAInS,EACJ6P,GAAYsC,EAAKlI,UACkB,QAAlCjK,EAAKuP,GAAe6C,gBAA6B,IAAPpS,GAAyBA,EAAG2E,KAAK4K,GAAgB3M,EAAGuP,EAAK,EAiFpFE,CAAczP,EAAG,CAC7BqH,UAAWA,KAEb,aAAcA,GAAWuD,EAAWmC,SAAWnC,aAA+C,EAASA,EAAWoC,QAC/F,mBAAXH,EAAwBA,EAAOxF,IAAYwF,GAAW,IAAI,EAGhE6C,GAAa,KACjB,IAAKxE,EACH,OAEF,MAAM,KACJ1U,EAAI,QACJ+L,EAAO,SACPuC,GACEqG,EACEwE,GAAY,EAAAnI,EAAA,GAAQjF,GAAS,KAAOqI,aAA+C,EAASA,EAAWa,MACvGpP,EAAiC,iBAAdsT,EAAyBA,EAAY,GAC9D,OAAOpE,EAAY7G,SAAS,QAAwB,gBAAoB,IAAS,CAC/E4K,IAAK,OACL5M,OAAmB,IAAZH,EAAoB,GAAKoN,GAClB,gBAAoB,SAAU,CAC5CnK,KAAM,SACNnP,IAAK0U,EACLzO,UAAW,GAAGF,SACd6E,QAAS2K,EACT,aAAcvP,EACdyI,SAAUA,GACTtO,GAAqB,gBAAoB,EAAc,CACxDoZ,KAAM,aACA,IAAI,EAkBRC,GAAmBxH,GAAe,CAACA,GAAegH,KAAgBK,KAdjE3D,EAGe,gBAAoB,GAAStU,OAAOC,OAAO,CAC7D4X,IAAK,QACJhP,EAAY,CACblE,UAAWA,EACXoE,OAAQA,EACRmE,OAAQiG,EACR9I,OAAQkK,GACRjH,QAASrE,GACTkE,SAAUrE,WAVH,MAkBX,OAAoB,gBAAoB,IAAgB,CACtDuP,SApIexR,IACf,IAAI,YACFyR,GACEzR,EACJuP,GAAiBkC,EAAY,EAiI7BvF,UAAW2C,KACV6C,GAA2B,gBAAoB,GAAiB,CACjE3F,aAAcA,GACdF,eAAgBgD,GAChB/C,WAAYqD,IACE,gBAAoB,EAAYhW,OAAOC,OAAO,CAC5D4E,UAAW,IAAW,CACpB,CAAC,GAAGF,KAAaoJ,KAASA,EAC1B,CAAC,GAAGpJ,cAAuBoO,EAC3B,CAAC,GAAGpO,cAAuB+N,GAC3B,CAAC,GAAG/N,0BAAmCsR,GACvC,CAAC,GAAGtR,4BAAqCuR,IACxCrR,GACHF,UAAWmH,EACXhH,MAAO9E,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG6E,GAAQ,CAC7CN,gBAAiB0R,GAAevO,QAAOoC,IAEzC1E,UAAWA,EACXzG,KAAK,QAAW2Z,EAAWlF,EAAezU,GAC1C8F,UAAWA,EACX8E,QAASsK,EAAY7G,SAAS,QAAUkH,OAAcpK,EACtD,aAAc2N,cAAmD,EAASA,GAAac,WACvFvN,MAAOA,GACNsI,GAAyB,gBAAoB,GAAU,CACxD7D,cAAegG,KAAyBG,GACxCjM,KAAMd,EACNnB,KAAMA,GACNgI,MAAOwG,GACPrG,WAAYuG,GACZzG,SAAUA,GACVC,SAAU,CAAC9G,EAAQ6G,GAAU3G,GAAawK,EAAYa,EAAYnB,KACjE,CAACnE,EAAM4B,IAhUZ,SAA4BnK,EAAM8L,GAChC,IAAI,KACFrR,EAAI,KACJX,EAAI,UACJkY,EACAC,OAAQC,EAAG,OACXpX,EAAM,SACNqX,EAAQ,OACRC,GACEpS,EACAqS,EAAiBvG,EACrB,SAASwG,EAAKC,EAAKC,GACZA,IAGLH,EAA8B,gBAAoBE,EAAK,CAAC,EAAGF,GAC7D,CAQA,OAPAC,EAAK,SAAUxX,GACfwX,EAAK,IAAKN,GACVM,EAAK,MAAOJ,GACZI,EAAK,OAAQxY,GACbwY,EAAK,OAAQ7X,GACb6X,EAAK,MAAOH,GACZG,EAAK,IAAKF,GACHC,CACT,CAuS4BI,CAAmBva,EAAoB,gBAAoB,WAAgB,KAAMqQ,EAAKjJ,OAAS,GAAK6K,IAAgBhB,IAAY8H,GAA6B,gBAAoB,OAAQ,CACjNG,IAAK,eACL,eAAe,GACd7I,GAASA,EAxCW4B,IAAe,CAACA,IAAgBhB,IAA0B,gBAAoB,OAAQ,CAC3G,eAAe,EACfiI,IAAK,YAnQY,OAoQA3C,GAAeU,OAAQwC,GAAiBxH,IAqCzCuI,CAAevI,UAAmB,IAEtD,UCpWI,GAAgC,SAAUxF,EAAG7C,GAC/C,IAAI8C,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOpL,OAAOuL,UAAUC,eAAelB,KAAKc,EAAGE,IAAM/C,EAAEkD,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCpL,OAAO0L,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAItL,OAAO0L,sBAAsBN,GAAIO,EAAIL,EAAEvF,OAAQ4F,IAClIpD,EAAEkD,QAAQH,EAAEK,IAAM,GAAK3L,OAAOuL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAyBA,OArB0B,cAAiB,CAAC1F,EAAI/G,KAC9C,IAAI,SACAoU,EAAQ,IACRoG,GACEzT,EACJwG,EAAY,GAAOxG,EAAI,CAAC,WAAY,QAKtC,MAAM0T,EAAcrZ,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGkM,GAAY,CAC9DiN,SAAarP,IAARqP,GAA0C,WAArBjN,EAAUzF,OAAsB,sBAAwB0S,IAIpF,cADOC,EAAYC,SACC,gBAAoB,GAAMtZ,OAAOC,OAAO,CAAC,EAAGoZ,EAAa,CAC3Eza,IAAKA,EACLoU,WAAYA,EACZ3N,UAAW,MACV,ICvBL,OAL+B,cAAiB,CAAC1G,EAAOC,IAAsB,gBAAoB,GAAMoB,OAAOC,OAAO,CACpHrB,IAAKA,GACJD,EAAO,CACR0G,UAAW,WCLT,GAAgC,SAAU+F,EAAG7C,GAC/C,IAAI8C,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOpL,OAAOuL,UAAUC,eAAelB,KAAKc,EAAGE,IAAM/C,EAAEkD,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCpL,OAAO0L,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAItL,OAAO0L,sBAAsBN,GAAIO,EAAIL,EAAEvF,OAAQ4F,IAClIpD,EAAEkD,QAAQH,EAAEK,IAAM,GAAK3L,OAAOuL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAKA,MAAMkO,GAAO,CAAC5T,EAAI/G,KAChB,IAAI,SACAoU,GACErN,EACJwG,EAAY,GAAOxG,EAAI,CAAC,aAC1B,MAAM6T,EAAiB,WAAc,IAC/BxG,GAAgC,iBAAbA,GACd,EAAAQ,EAAA,GAAKR,EAAU,CAAC,aAAc,SAEhCA,GACN,CAACA,IAKJ,OAAoB,gBAAoB,GAAMhT,OAAOC,OAAO,CAC1DrB,IAAKA,GACJuN,EAAW,CACZ6G,SAAUwG,EACVnU,UAAW,SACV,EAEL,OAA4B,aAAiBkU,IClCzC,GAAgC,SAAUnO,EAAG7C,GAC/C,IAAI8C,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOpL,OAAOuL,UAAUC,eAAelB,KAAKc,EAAGE,IAAM/C,EAAEkD,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCpL,OAAO0L,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAItL,OAAO0L,sBAAsBN,GAAIO,EAAIL,EAAEvF,OAAQ4F,IAClIpD,EAAEkD,QAAQH,EAAEK,IAAM,GAAK3L,OAAOuL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAIA,MAAMoO,GAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,GAiBpC,OAhB2B,cAAiB,CAAC9a,EAAOC,KAClD,MAAM,MACF8a,EAAQ,GACN/a,EACJwN,EAAY,GAAOxN,EAAO,CAAC,UAK7B,MAAM0G,EAAYoU,GAAexM,SAASyM,GAAS,IAAIA,IAAU,KACjE,OAAoB,gBAAoB,GAAM1Z,OAAOC,OAAO,CAC1DrB,IAAKA,GACJuN,EAAW,CACZ9G,UAAWA,IACV,ICrBL,MAAM,GAAa,EACnB,GAAWkU,KAAO,GAClB,GAAWI,KAAO,GAClB,GAAWC,MAAQ,GACnB,GAAWC,UAAY,GACvB,S,mBCXAC,EAAOC,QAAU,WACf,IAAIC,EAAYrD,SAASsD,eACzB,IAAKD,EAAUE,WACb,OAAO,WAAa,EAKtB,IAHA,IAAIC,EAASxD,SAASyD,cAElBC,EAAS,GACJ1O,EAAI,EAAGA,EAAIqO,EAAUE,WAAYvO,IACxC0O,EAAOjL,KAAK4K,EAAUM,WAAW3O,IAGnC,OAAQwO,EAAOI,QAAQC,eACrB,IAAK,QACL,IAAK,WACHL,EAAOM,OACP,MAEF,QACEN,EAAS,KAKb,OADAH,EAAUU,kBACH,WACc,UAAnBV,EAAUjM,MACViM,EAAUU,kBAELV,EAAUE,YACbG,EAAOlb,SAAQ,SAASwb,GACtBX,EAAUY,SAASD,EACrB,IAGFR,GACAA,EAAOrU,OACT,CACF,C,oCCpCA,IAAI+U,EAAkB,EAAQ,MAE1BC,EAA4B,CAC9B,aAAc,OACd,YAAa,MACb,QAAW,QA2GbhB,EAAOC,QAjGP,SAAcnQ,EAAMmR,GAClB,IAAIC,EACFC,EACAC,EACAP,EACAX,EACA9Y,EACAia,GAAU,EACPJ,IACHA,EAAU,CAAC,GAEbC,EAAQD,EAAQC,QAAS,EACzB,IAkDE,GAjDAE,EAAmBL,IAEnBF,EAAQhE,SAASyE,cACjBpB,EAAYrD,SAASsD,gBAErB/Y,EAAOyV,SAASC,cAAc,SACzByE,YAAczR,EAEnB1I,EAAKoa,WAAa,OAElBpa,EAAK4D,MAAMyW,IAAM,QAEjBra,EAAK4D,MAAMtC,SAAW,QACtBtB,EAAK4D,MAAM4J,IAAM,EACjBxN,EAAK4D,MAAM0W,KAAO,mBAElBta,EAAK4D,MAAM/C,WAAa,MAExBb,EAAK4D,MAAM2W,iBAAmB,OAC9Bva,EAAK4D,MAAM4W,cAAgB,OAC3Bxa,EAAK4D,MAAM6W,aAAe,OAC1Bza,EAAK4D,MAAM5E,WAAa,OACxBgB,EAAK0a,iBAAiB,QAAQ,SAASrT,GAErC,GADAA,EAAEoB,kBACEoR,EAAQxR,OAEV,GADAhB,EAAEmB,sBAC6B,IAApBnB,EAAEsT,cAA+B,CAC1Cb,GAASc,QAAQC,KAAK,iCACtBf,GAASc,QAAQC,KAAK,4BACtBC,OAAOH,cAAcI,YACrB,IAAI1S,EAASuR,EAA0BC,EAAQxR,SAAWuR,EAAmC,QAC7FkB,OAAOH,cAAcK,QAAQ3S,EAAQK,EACvC,MACErB,EAAEsT,cAAcI,YAChB1T,EAAEsT,cAAcK,QAAQnB,EAAQxR,OAAQK,GAGxCmR,EAAQ1Q,SACV9B,EAAEmB,iBACFqR,EAAQ1Q,OAAO9B,EAAEsT,eAErB,IAEAlF,SAASwF,KAAKtF,YAAY3V,GAE1ByZ,EAAMyB,mBAAmBlb,GACzB8Y,EAAUY,SAASD,IAEFhE,SAAS0F,YAAY,QAEpC,MAAM,IAAIC,MAAM,iCAElBnB,GAAU,CACZ,CAAE,MAAOoB,GACPvB,GAASc,QAAQvR,MAAM,qCAAsCgS,GAC7DvB,GAASc,QAAQC,KAAK,4BACtB,IACEC,OAAOH,cAAcK,QAAQnB,EAAQxR,QAAU,OAAQK,GACvDmR,EAAQ1Q,QAAU0Q,EAAQ1Q,OAAO2R,OAAOH,eACxCV,GAAU,CACZ,CAAE,MAAOoB,GACPvB,GAASc,QAAQvR,MAAM,uCAAwCgS,GAC/DvB,GAASc,QAAQvR,MAAM,0BACvB0Q,EAjFN,SAAgBA,GACd,IAAIuB,GAAW,YAAYC,KAAKC,UAAUC,WAAa,IAAM,QAAU,KACvE,OAAO1B,EAAQtU,QAAQ,gBAAiB6V,EAC1C,CA8EgBjT,CAAO,YAAawR,EAAUA,EAAQE,QAnFjC,oCAoFfe,OAAOY,OAAO3B,EAASrR,EACzB,CACF,CAAE,QACIoQ,IACkC,mBAAzBA,EAAU6C,YACnB7C,EAAU6C,YAAYlC,GAEtBX,EAAUU,mBAIVxZ,GACFyV,SAASwF,KAAKlF,YAAY/V,GAE5Bga,GACF,CAEA,OAAOC,CACT,C,iFC/GI2B,EAAqB,SAA4BC,GACnD,IAAI,UAAef,OAAOrF,SAASqG,gBAAiB,CAClD,IAAIC,EAAgBjT,MAAMC,QAAQ8S,GAAaA,EAAY,CAACA,GACxDC,EAAkBhB,OAAOrF,SAASqG,gBACtC,OAAOC,EAAcC,MAAK,SAAUC,GAClC,OAAOA,KAAQH,EAAgBlY,KACjC,GACF,CACA,OAAO,CACT,EACIsY,EAAsB,SAA6BL,EAAW9X,GAChE,IAAK6X,EAAmBC,GACtB,OAAO,EAET,IAAItG,EAAME,SAASC,cAAc,OAC7ByG,EAAS5G,EAAI3R,MAAMiY,GAEvB,OADAtG,EAAI3R,MAAMiY,GAAa9X,EAChBwR,EAAI3R,MAAMiY,KAAeM,CAClC,EACO,SAASC,EAAeP,EAAWQ,GACxC,OAAKvT,MAAMC,QAAQ8S,SAA6BhT,IAAfwT,EAG1BT,EAAmBC,GAFjBK,EAAoBL,EAAWQ,EAG1C,C", "sources": ["webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/EditOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/EditOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/EnterOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/EnterOutlined.js", "webpack://autogentstudio/./node_modules/antd/es/typography/style/mixins.js", "webpack://autogentstudio/./node_modules/antd/es/typography/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Editable.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useCopyClick.js", "webpack://autogentstudio/./node_modules/antd/es/_util/toList.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useMergedConfig.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/usePrevious.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useTooltipProps.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Typography.js", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/CopyOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/CopyOutlined.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/util.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/CopyBtn.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/Ellipsis.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/EllipsisTooltip.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/index.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Link.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Paragraph.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Text.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Title.js", "webpack://autogentstudio/./node_modules/antd/es/typography/index.js", "webpack://autogentstudio/./node_modules/toggle-selection/index.js", "webpack://autogentstudio/./node_modules/copy-to-clipboard/index.js", "webpack://autogentstudio/./node_modules/rc-util/es/Dom/styleChecker.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar EditOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z\" } }] }, \"name\": \"edit\", \"theme\": \"outlined\" };\nexport default EditOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EditOutlinedSvg from \"@ant-design/icons-svg/es/asn/EditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EditOutlined = function EditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EditOutlinedSvg\n  }));\n};\n\n/**![edit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI1Ny43IDc1MmMyIDAgNC0uMiA2LS41TDQzMS45IDcyMmMyLS40IDMuOS0xLjMgNS4zLTIuOGw0MjMuOS00MjMuOWE5Ljk2IDkuOTYgMCAwMDAtMTQuMUw2OTQuOSAxMTQuOWMtMS45LTEuOS00LjQtMi45LTcuMS0yLjlzLTUuMiAxLTcuMSAyLjlMMjU2LjggNTM4LjhjLTEuNSAxLjUtMi40IDMuMy0yLjggNS4zbC0yOS41IDE2OC4yYTMzLjUgMzMuNSAwIDAwOS40IDI5LjhjNi42IDYuNCAxNC45IDkuOSAyMy44IDkuOXptNjcuNC0xNzQuNEw2ODcuOCAyMTVsNzMuMyA3My4zLTM2Mi43IDM2Mi42LTg4LjkgMTUuNyAxNS42LTg5ek04ODAgODM2SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MzZjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTM2YzAtMTcuNy0xNC4zLTMyLTMyLTMyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EditOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar EnterOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"enter\", \"theme\": \"outlined\" };\nexport default EnterOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EnterOutlinedSvg from \"@ant-design/icons-svg/es/asn/EnterOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EnterOutlined = function EnterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EnterOutlinedSvg\n  }));\n};\n\n/**![enter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAxNzBoLTYwYy00LjQgMC04IDMuNi04IDh2NTE4SDMxMHYtNzNjMC02LjctNy44LTEwLjUtMTMtNi4zbC0xNDEuOSAxMTJhOCA4IDAgMDAwIDEyLjZsMTQxLjkgMTEyYzUuMyA0LjIgMTMgLjQgMTMtNi4zdi03NWg0OThjMzUuMyAwIDY0LTI4LjcgNjQtNjRWMTc4YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EnterOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EnterOutlined';\n}\nexport default RefIcon;", "/*\n.typography-title(@fontSize; @fontWeight; @lineHeight; @headingColor; @headingMarginBottom;) {\n margin-bottom: @headingMarginBottom;\n color: @headingColor;\n font-weight: @fontWeight;\n fontSize: @fontSize;\n line-height: @lineHeight;\n}\n*/\nimport { gold } from '@ant-design/colors';\nimport { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst getTitleStyle = (fontSize, lineHeight, color, token) => {\n  const {\n    titleMarginBottom,\n    fontWeightStrong\n  } = token;\n  return {\n    marginBottom: titleMarginBottom,\n    color,\n    fontWeight: fontWeightStrong,\n    fontSize,\n    lineHeight\n  };\n};\nexport const getTitleStyles = token => {\n  const headings = [1, 2, 3, 4, 5];\n  const styles = {};\n  headings.forEach(headingLevel => {\n    styles[`\n      h${headingLevel}&,\n      div&-h${headingLevel},\n      div&-h${headingLevel} > textarea,\n      h${headingLevel}\n    `] = getTitleStyle(token[`fontSizeHeading${headingLevel}`], token[`lineHeightHeading${headingLevel}`], token.colorTextHeading, token);\n  });\n  return styles;\n};\nexport const getLinkStyles = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    'a&, a': Object.assign(Object.assign({}, operationUnit(token)), {\n      userSelect: 'text',\n      [`&[disabled], &${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        '&:active, &:hover': {\n          color: token.colorTextDisabled\n        },\n        '&:active': {\n          pointerEvents: 'none'\n        }\n      }\n    })\n  };\n};\nexport const getResetStyles = token => ({\n  code: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.2em 0.1em',\n    fontSize: '85%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3\n  },\n  kbd: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.15em 0.1em',\n    fontSize: '90%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.06)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderBottomWidth: 2,\n    borderRadius: 3\n  },\n  mark: {\n    padding: 0,\n    // FIXME hardcode in v4\n    backgroundColor: gold[2]\n  },\n  'u, ins': {\n    textDecoration: 'underline',\n    textDecorationSkipInk: 'auto'\n  },\n  's, del': {\n    textDecoration: 'line-through'\n  },\n  strong: {\n    fontWeight: 600\n  },\n  // list\n  'ul, ol': {\n    marginInline: 0,\n    marginBlock: '0 1em',\n    padding: 0,\n    li: {\n      marginInline: '20px 0',\n      marginBlock: 0,\n      paddingInline: '4px 0',\n      paddingBlock: 0\n    }\n  },\n  ul: {\n    listStyleType: 'circle',\n    ul: {\n      listStyleType: 'disc'\n    }\n  },\n  ol: {\n    listStyleType: 'decimal'\n  },\n  // pre & block\n  'pre, blockquote': {\n    margin: '1em 0'\n  },\n  pre: {\n    padding: '0.4em 0.6em',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3,\n    fontFamily: token.fontFamilyCode,\n    // Compatible for marked\n    code: {\n      display: 'inline',\n      margin: 0,\n      padding: 0,\n      fontSize: 'inherit',\n      fontFamily: 'inherit',\n      background: 'transparent',\n      border: 0\n    }\n  },\n  blockquote: {\n    paddingInline: '0.6em 0',\n    paddingBlock: 0,\n    borderInlineStart: '4px solid rgba(100, 100, 100, 0.2)',\n    opacity: 0.85\n  }\n});\nexport const getEditableStyles = token => {\n  const {\n    componentCls,\n    paddingSM\n  } = token;\n  const inputShift = paddingSM;\n  return {\n    '&-edit-content': {\n      position: 'relative',\n      'div&': {\n        insetInlineStart: token.calc(token.paddingSM).mul(-1).equal(),\n        marginTop: token.calc(inputShift).mul(-1).equal(),\n        marginBottom: `calc(1em - ${unit(inputShift)})`\n      },\n      [`${componentCls}-edit-content-confirm`]: {\n        position: 'absolute',\n        insetInlineEnd: token.calc(token.marginXS).add(2).equal(),\n        insetBlockEnd: token.marginXS,\n        color: token.colorTextDescription,\n        // default style\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        fontStyle: 'normal',\n        pointerEvents: 'none'\n      },\n      textarea: {\n        margin: '0!important',\n        // Fix Editable Textarea flash in Firefox\n        MozTransition: 'none',\n        height: '1em'\n      }\n    }\n  };\n};\nexport const getCopyableStyles = token => ({\n  [`${token.componentCls}-copy-success`]: {\n    [`\n    &,\n    &:hover,\n    &:focus`]: {\n      color: token.colorSuccess\n    }\n  },\n  [`${token.componentCls}-copy-icon-only`]: {\n    marginInlineStart: 0\n  }\n});\nexport const getEllipsisStyles = () => ({\n  [`\n  a&-ellipsis,\n  span&-ellipsis\n  `]: {\n    display: 'inline-block',\n    maxWidth: '100%'\n  },\n  '&-ellipsis-single-line': {\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    // https://blog.csdn.net/iefreer/article/details/50421025\n    'a&, span&': {\n      verticalAlign: 'bottom'\n    },\n    '> code': {\n      paddingBlock: 0,\n      maxWidth: 'calc(100% - 1.2em)',\n      display: 'inline-block',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      verticalAlign: 'bottom',\n      // https://github.com/ant-design/ant-design/issues/45953\n      boxSizing: 'content-box'\n    }\n  },\n  '&-ellipsis-multiple-line': {\n    display: '-webkit-box',\n    overflow: 'hidden',\n    WebkitLineClamp: 3,\n    WebkitBoxOrient: 'vertical'\n  }\n});", "import { operationUnit } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nimport { getCopyableStyles, getEditableStyles, getEllipsisStyles, getLinkStyles, getResetStyles, getTitleStyles } from './mixins';\nconst genTypographyStyle = token => {\n  const {\n    componentCls,\n    titleMarginTop\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n      color: token.colorText,\n      wordBreak: 'break-word',\n      lineHeight: token.lineHeight,\n      [`&${componentCls}-secondary`]: {\n        color: token.colorTextDescription\n      },\n      [`&${componentCls}-success`]: {\n        color: token.colorSuccess\n      },\n      [`&${componentCls}-warning`]: {\n        color: token.colorWarning\n      },\n      [`&${componentCls}-danger`]: {\n        color: token.colorError,\n        'a&:active, a&:focus': {\n          color: token.colorErrorActive\n        },\n        'a&:hover': {\n          color: token.colorErrorHover\n        }\n      },\n      [`&${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        userSelect: 'none'\n      },\n      [`\n        div&,\n        p\n      `]: {\n        marginBottom: '1em'\n      }\n    }, getTitleStyles(token)), {\n      [`\n      & + h1${componentCls},\n      & + h2${componentCls},\n      & + h3${componentCls},\n      & + h4${componentCls},\n      & + h5${componentCls}\n      `]: {\n        marginTop: titleMarginTop\n      },\n      [`\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5`]: {\n        [`\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        `]: {\n          marginTop: titleMarginTop\n        }\n      }\n    }), getResetStyles(token)), getLinkStyles(token)), {\n      // Operation\n      [`\n        ${componentCls}-expand,\n        ${componentCls}-collapse,\n        ${componentCls}-edit,\n        ${componentCls}-copy\n      `]: Object.assign(Object.assign({}, operationUnit(token)), {\n        marginInlineStart: token.marginXXS\n      })\n    }), getEditableStyles(token)), getCopyableStyles(token)), getEllipsisStyles()), {\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = () => ({\n  titleMarginTop: '1.2em',\n  titleMarginBottom: '0.5em'\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Typography', token => [genTypographyStyle(token)], prepareComponentToken);", "\"use client\";\n\nimport * as React from 'react';\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef(null);\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    var _a;\n    if ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = _ref => {\n    let {\n      target\n    } = _ref;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = _ref2 => {\n    let {\n      keyCode\n    } = _ref2;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = _ref3 => {\n    let {\n      keyCode,\n      ctrlKey,\n      altKey,\n      metaKey,\n      shiftKey\n    } = _ref3;\n    // Check if it's a real key\n    if (lastKeyCode.current !== keyCode || inComposition.current || ctrlKey || altKey || metaKey || shiftKey) {\n      return;\n    }\n    if (keyCode === KeyCode.ENTER) {\n      confirmChange();\n      onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n    } else if (keyCode === KeyCode.ESC) {\n      onCancel();\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${component}`]: !!component\n  }, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport * as React from 'react';\nimport copy from 'copy-to-clipboard';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../../_util/toList';\nconst useCopyClick = _ref => {\n  let {\n    copyConfig,\n    children\n  } = _ref;\n  const [copied, setCopied] = React.useState(false);\n  const [copyLoading, setCopyLoading] = React.useState(false);\n  const copyIdRef = React.useRef(null);\n  const cleanCopyId = () => {\n    if (copyIdRef.current) {\n      clearTimeout(copyIdRef.current);\n    }\n  };\n  const copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  React.useEffect(() => cleanCopyId, []);\n  // Keep copy action up to date\n  const onClick = useEvent(e => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    setCopyLoading(true);\n    try {\n      const text = typeof copyConfig.text === 'function' ? yield copyConfig.text() : copyConfig.text;\n      copy(text || toList(children, true).join('') || '', copyOptions);\n      setCopyLoading(false);\n      setCopied(true);\n      // Trigger tips update\n      cleanCopyId();\n      copyIdRef.current = setTimeout(() => {\n        setCopied(false);\n      }, 3000);\n      (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n    } catch (error) {\n      setCopyLoading(false);\n      throw error;\n    }\n  }));\n  return {\n    copied,\n    copyLoading,\n    onClick\n  };\n};\nexport default useCopyClick;", "export default function toList(candidate) {\n  let skipEmpty = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (skipEmpty && (candidate === undefined || candidate === null)) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}", "import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "import { useEffect, useRef } from 'react';\nconst usePrevious = value => {\n  const ref = useRef(undefined);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePrevious;", "import { isValidElement, useMemo } from 'react';\nconst useTooltipProps = (tooltip, editConfigText, children) => useMemo(() => {\n  if (tooltip === true) {\n    return {\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    };\n  }\n  if (/*#__PURE__*/isValidElement(tooltip)) {\n    return {\n      title: tooltip\n    };\n  }\n  if (typeof tooltip === 'object') {\n    return Object.assign({\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    }, tooltip);\n  }\n  return {\n    title: tooltip\n  };\n}, [tooltip, editConfigText, children]);\nexport default useTooltipProps;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst Typography = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      component: Component = 'article',\n      className,\n      rootClassName,\n      setContentRef,\n      children,\n      direction: typographyDirection,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"component\", \"className\", \"rootClassName\", \"setContentRef\", \"children\", \"direction\", \"style\"]);\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('typography');\n  const direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  const mergedRef = setContentRef ? composeRef(ref, setContentRef) : ref;\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography');\n    warning.deprecated(!setContentRef, 'setContentRef', 'ref');\n  }\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const componentClassName = classNames(prefixCls, contextClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(\n  /*#__PURE__*/\n  // @ts-expect-error: Expression produces a union type that is too complex to represent.\n  React.createElement(Component, Object.assign({\n    className: componentClassName,\n    style: mergedStyle,\n    ref: mergedRef\n  }, restProps), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\nexport default Typography;", "// This icon file is generated automatically.\nvar CopyOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z\" } }] }, \"name\": \"copy\", \"theme\": \"outlined\" };\nexport default CopyOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CopyOutlinedSvg from \"@ant-design/icons-svg/es/asn/CopyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CopyOutlined = function CopyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CopyOutlinedSvg\n  }));\n};\n\n/**![copy](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgyOTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNDk2djY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04Vjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek03MDQgMTkySDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NTMwLjdjMCA4LjUgMy40IDE2LjYgOS40IDIyLjZsMTczLjMgMTczLjNjMi4yIDIuMiA0LjcgNCA3LjQgNS41djEuOWg0LjJjMy41IDEuMyA3LjIgMiAxMSAySDcwNGMxNy43IDAgMzItMTQuMyAzMi0zMlYyMjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTM1MCA4NTYuMkwyNjMuOSA3NzBIMzUwdjg2LjJ6TTY2NCA4ODhINDE0Vjc0NmMwLTIyLjEtMTcuOS00MC00MC00MEgyMzJWMjY0aDQzMnY2MjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CopyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CopyOutlined';\n}\nexport default RefIcon;", "export function toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\n/**\n * Check for element is native ellipsis\n * ref:\n * - https://github.com/ant-design/ant-design/issues/50143\n * - https://github.com/ant-design/ant-design/issues/50414\n */\nexport function isEleEllipsis(ele) {\n  // Create a new div to get the size\n  const childDiv = document.createElement('em');\n  ele.appendChild(childDiv);\n  // For test case\n  if (process.env.NODE_ENV !== 'production') {\n    childDiv.className = 'ant-typography-css-ellipsis-content-measure';\n  }\n  const rect = ele.getBoundingClientRect();\n  const childRect = childDiv.getBoundingClientRect();\n  // Reset\n  ele.removeChild(childDiv);\n  // Range checker\n  return (\n    // Horizontal out of range\n    rect.left > childRect.left || childRect.right > rect.right ||\n    // Vertical out of range\n    rect.top > childRect.top || childRect.bottom > rect.bottom\n  );\n}\nexport const isValidText = val => ['string', 'number'].includes(typeof val);", "\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport Tooltip from '../../tooltip';\nimport { getNode, toList } from './util';\nconst CopyBtn = _ref => {\n  let {\n    prefixCls,\n    copied,\n    locale,\n    iconOnly,\n    tooltips,\n    icon,\n    tabIndex,\n    onCopy,\n    loading: btnLoading\n  } = _ref;\n  const tooltipNodes = toList(tooltips);\n  const iconNodes = toList(icon);\n  const {\n    copied: copiedText,\n    copy: copyText\n  } = locale !== null && locale !== void 0 ? locale : {};\n  const systemStr = copied ? copiedText : copyText;\n  const copyTitle = getNode(tooltipNodes[copied ? 1 : 0], systemStr);\n  const ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: copyTitle\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: classNames(`${prefixCls}-copy`, {\n      [`${prefixCls}-copy-success`]: copied,\n      [`${prefixCls}-copy-icon-only`]: iconOnly\n    }),\n    onClick: onCopy,\n    \"aria-label\": ariaLabel,\n    tabIndex: tabIndex\n  }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], btnLoading ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n};\nexport default CopyBtn;", "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isValidText } from './util';\nconst MeasureText = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    style,\n    children\n  } = _ref;\n  const spanRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    isExceed: () => {\n      const span = spanRef.current;\n      return span.scrollHeight > span.clientHeight;\n    },\n    getHeight: () => spanRef.current.clientHeight\n  }));\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: spanRef,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      pointerEvents: 'none',\n      backgroundColor: 'rgba(255, 0, 0, 0.65)'\n    }, style)\n  }, children);\n});\nconst getNodesLen = nodeList => nodeList.reduce((totalLen, node) => totalLen + (isValidText(node) ? String(node).length : 1), 0);\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = isValidText(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\n// Measure for the `text` is exceed the `rows` or not\nconst STATUS_MEASURE_NONE = 0;\nconst STATUS_MEASURE_PREPARE = 1;\nconst STATUS_MEASURE_START = 2;\nconst STATUS_MEASURE_NEED_ELLIPSIS = 3;\nconst STATUS_MEASURE_NO_NEED_ELLIPSIS = 4;\nconst lineClipStyle = {\n  display: '-webkit-box',\n  overflow: 'hidden',\n  WebkitBoxOrient: 'vertical'\n};\nexport default function EllipsisMeasure(props) {\n  const {\n    enableMeasure,\n    width,\n    text,\n    children,\n    rows,\n    expanded,\n    miscDeps,\n    onEllipsis\n  } = props;\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const nodeLen = React.useMemo(() => getNodesLen(nodeList), [text]);\n  // ========================= Full Content =========================\n  // Used for measure only, which means it's always render as no need ellipsis\n  const fullContent = React.useMemo(() => children(nodeList, false), [text]);\n  // ========================= Cut Content ==========================\n  const [ellipsisCutIndex, setEllipsisCutIndex] = React.useState(null);\n  const cutMidRef = React.useRef(null);\n  // ========================= NeedEllipsis =========================\n  const measureWhiteSpaceRef = React.useRef(null);\n  const needEllipsisRef = React.useRef(null);\n  // Measure for `rows-1` height, to avoid operation exceed the line height\n  const descRowsEllipsisRef = React.useRef(null);\n  const symbolRowEllipsisRef = React.useRef(null);\n  const [canEllipsis, setCanEllipsis] = React.useState(false);\n  const [needEllipsis, setNeedEllipsis] = React.useState(STATUS_MEASURE_NONE);\n  const [ellipsisHeight, setEllipsisHeight] = React.useState(0);\n  const [parentWhiteSpace, setParentWhiteSpace] = React.useState(null);\n  // Trigger start measure\n  useLayoutEffect(() => {\n    if (enableMeasure && width && nodeLen) {\n      setNeedEllipsis(STATUS_MEASURE_PREPARE);\n    } else {\n      setNeedEllipsis(STATUS_MEASURE_NONE);\n    }\n  }, [width, text, rows, enableMeasure, nodeList]);\n  // Measure process\n  useLayoutEffect(() => {\n    var _a, _b, _c, _d;\n    if (needEllipsis === STATUS_MEASURE_PREPARE) {\n      setNeedEllipsis(STATUS_MEASURE_START);\n      // Parent ref `white-space`\n      const nextWhiteSpace = measureWhiteSpaceRef.current && getComputedStyle(measureWhiteSpaceRef.current).whiteSpace;\n      setParentWhiteSpace(nextWhiteSpace);\n    } else if (needEllipsis === STATUS_MEASURE_START) {\n      const isOverflow = !!((_a = needEllipsisRef.current) === null || _a === void 0 ? void 0 : _a.isExceed());\n      setNeedEllipsis(isOverflow ? STATUS_MEASURE_NEED_ELLIPSIS : STATUS_MEASURE_NO_NEED_ELLIPSIS);\n      setEllipsisCutIndex(isOverflow ? [0, nodeLen] : null);\n      setCanEllipsis(isOverflow);\n      // Get the basic height of ellipsis rows\n      const baseRowsEllipsisHeight = ((_b = needEllipsisRef.current) === null || _b === void 0 ? void 0 : _b.getHeight()) || 0;\n      // Get the height of `rows - 1` + symbol height\n      const descRowsEllipsisHeight = rows === 1 ? 0 : ((_c = descRowsEllipsisRef.current) === null || _c === void 0 ? void 0 : _c.getHeight()) || 0;\n      const symbolRowEllipsisHeight = ((_d = symbolRowEllipsisRef.current) === null || _d === void 0 ? void 0 : _d.getHeight()) || 0;\n      const maxRowsHeight = Math.max(baseRowsEllipsisHeight,\n      // height of rows with ellipsis\n      descRowsEllipsisHeight + symbolRowEllipsisHeight);\n      setEllipsisHeight(maxRowsHeight + 1);\n      onEllipsis(isOverflow);\n    }\n  }, [needEllipsis]);\n  // ========================= Cut Measure ==========================\n  const cutMidIndex = ellipsisCutIndex ? Math.ceil((ellipsisCutIndex[0] + ellipsisCutIndex[1]) / 2) : 0;\n  useLayoutEffect(() => {\n    var _a;\n    const [minIndex, maxIndex] = ellipsisCutIndex || [0, 0];\n    if (minIndex !== maxIndex) {\n      const midHeight = ((_a = cutMidRef.current) === null || _a === void 0 ? void 0 : _a.getHeight()) || 0;\n      const isOverflow = midHeight > ellipsisHeight;\n      let targetMidIndex = cutMidIndex;\n      if (maxIndex - minIndex === 1) {\n        targetMidIndex = isOverflow ? minIndex : maxIndex;\n      }\n      setEllipsisCutIndex(isOverflow ? [minIndex, targetMidIndex] : [targetMidIndex, maxIndex]);\n    }\n  }, [ellipsisCutIndex, cutMidIndex]);\n  // ========================= Text Content =========================\n  const finalContent = React.useMemo(() => {\n    // Skip everything if `enableMeasure` is disabled\n    if (!enableMeasure) {\n      return children(nodeList, false);\n    }\n    if (needEllipsis !== STATUS_MEASURE_NEED_ELLIPSIS || !ellipsisCutIndex || ellipsisCutIndex[0] !== ellipsisCutIndex[1]) {\n      const content = children(nodeList, false);\n      // Limit the max line count to avoid scrollbar blink unless no need ellipsis\n      // https://github.com/ant-design/ant-design/issues/42958\n      if ([STATUS_MEASURE_NO_NEED_ELLIPSIS, STATUS_MEASURE_NONE].includes(needEllipsis)) {\n        return content;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: Object.assign(Object.assign({}, lineClipStyle), {\n          WebkitLineClamp: rows\n        })\n      }, content);\n    }\n    return children(expanded ? nodeList : sliceNodes(nodeList, ellipsisCutIndex[0]), canEllipsis);\n  }, [expanded, needEllipsis, ellipsisCutIndex, nodeList].concat(_toConsumableArray(miscDeps)));\n  // ============================ Render ============================\n  const measureStyle = {\n    width,\n    margin: 0,\n    padding: 0,\n    whiteSpace: parentWhiteSpace === 'nowrap' ? 'normal' : 'inherit'\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, finalContent, needEllipsis === STATUS_MEASURE_START && (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows\n    }),\n    ref: needEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows - 1\n    }),\n    ref: descRowsEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: 1\n    }),\n    ref: symbolRowEllipsisRef\n  }, children([], true)))), needEllipsis === STATUS_MEASURE_NEED_ELLIPSIS && ellipsisCutIndex && ellipsisCutIndex[0] !== ellipsisCutIndex[1] && (/*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign({}, measureStyle), {\n      top: 400\n    }),\n    ref: cutMidRef\n  }, children(sliceNodes(nodeList, cutMidIndex), true))), needEllipsis === STATUS_MEASURE_PREPARE && (/*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      whiteSpace: 'inherit'\n    },\n    ref: measureWhiteSpaceRef\n  })));\n}", "\"use client\";\n\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = _ref => {\n  let {\n    enableEllipsis,\n    isEllipsis,\n    children,\n    tooltipProps\n  } = _ref;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enableEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport { ConfigContext } from '../../config-provider';\nimport useLocale from '../../locale/useLocale';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport useCopyClick from '../hooks/useCopyClick';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport usePrevious from '../hooks/usePrevious';\nimport useTooltipProps from '../hooks/useTooltipProps';\nimport Typography from '../Typography';\nimport CopyBtn from './CopyBtn';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nimport { isEleEllipsis, isValidText } from './util';\nfunction wrapperDecorations(_ref, content) {\n  let {\n    mark,\n    code,\n    underline,\n    delete: del,\n    strong,\n    keyboard,\n    italic\n  } = _ref;\n  let currentContent = content;\n  function wrap(tag, needed) {\n    if (!needed) {\n      return;\n    }\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap('strong', strong);\n  wrap('u', underline);\n  wrap('del', del);\n  wrap('code', code);\n  wrap('mark', mark);\n  wrap('kbd', keyboard);\n  wrap('i', italic);\n  return currentContent;\n}\nconst ELLIPSIS_STR = '...';\nconst Base = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      type,\n      disabled,\n      children,\n      ellipsis,\n      editable,\n      copyable,\n      component,\n      title\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [textLocale] = useLocale('Text');\n  const typographyRef = React.useRef(null);\n  const editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  const textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']);\n  // ========================== Editable ==========================\n  const [enableEdit, editConfig] = useMergedConfig(editable);\n  const [editing, setEditing] = useMergedState(false, {\n    value: editConfig.editing\n  });\n  const {\n    triggerType = ['icon']\n  } = editConfig;\n  const triggerEdit = edit => {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  const prevEditing = usePrevious(editing);\n  useLayoutEffect(() => {\n    var _a;\n    if (!editing && prevEditing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  const onEditClick = e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  const onEditChange = value => {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  const onEditCancel = () => {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  const [enableCopy, copyConfig] = useMergedConfig(copyable);\n  const {\n    copied,\n    copyLoading,\n    onClick: onCopyClick\n  } = useCopyClick({\n    copyConfig,\n    children\n  });\n  // ========================== Ellipsis ==========================\n  const [isLineClampSupport, setIsLineClampSupport] = React.useState(false);\n  const [isTextOverflowSupport, setIsTextOverflowSupport] = React.useState(false);\n  const [isJsEllipsis, setIsJsEllipsis] = React.useState(false);\n  const [isNativeEllipsis, setIsNativeEllipsis] = React.useState(false);\n  const [isNativeVisible, setIsNativeVisible] = React.useState(true);\n  const [enableEllipsis, ellipsisConfig] = useMergedConfig(ellipsis, {\n    expandable: false,\n    symbol: isExpanded => isExpanded ? textLocale === null || textLocale === void 0 ? void 0 : textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n  });\n  const [expanded, setExpanded] = useMergedState(ellipsisConfig.defaultExpanded || false, {\n    value: ellipsisConfig.expanded\n  });\n  const mergedEnableEllipsis = enableEllipsis && (!expanded || ellipsisConfig.expandable === 'collapsible');\n  // Shared prop to reduce bundle size\n  const {\n    rows = 1\n  } = ellipsisConfig;\n  const needMeasureEllipsis = React.useMemo(() =>\n  // Disable ellipsis\n  mergedEnableEllipsis && (\n  // Provide suffix\n  ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n  // Can't use css ellipsis since we need to provide the place for button\n  ellipsisConfig.expandable || enableEdit || enableCopy), [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useLayoutEffect(() => {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  const [cssEllipsis, setCssEllipsis] = React.useState(mergedEnableEllipsis);\n  const canUseCssEllipsis = React.useMemo(() => {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  // We use effect to change from css ellipsis to js ellipsis.\n  // To make SSR still can see the ellipsis.\n  useLayoutEffect(() => {\n    setCssEllipsis(canUseCssEllipsis && mergedEnableEllipsis);\n  }, [canUseCssEllipsis, mergedEnableEllipsis]);\n  const isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  const cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  const cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  const onExpandClick = (e, info) => {\n    var _a;\n    setExpanded(info.expanded);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e, info);\n  };\n  const [ellipsisWidth, setEllipsisWidth] = React.useState(0);\n  const onResize = _ref2 => {\n    let {\n      offsetWidth\n    } = _ref2;\n    setEllipsisWidth(offsetWidth);\n  };\n  // >>>>> JS Ellipsis\n  const onJsEllipsis = jsEllipsis => {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      const currentEllipsis = isEleEllipsis(textEle);\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible, ellipsisWidth]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    const observer = new IntersectionObserver(() => {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return () => {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  const tooltipProps = useTooltipProps(ellipsisConfig.tooltip, editConfig.text, children);\n  const topAriaLabel = React.useMemo(() => {\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    return [editConfig.text, children, title, tooltipProps.title].find(isValidText);\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_a = editConfig.text) !== null && _a !== void 0 ? _a : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  const renderExpand = () => {\n    const {\n      expandable,\n      symbol\n    } = ellipsisConfig;\n    return expandable ? (/*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      key: \"expand\",\n      className: `${prefixCls}-${expanded ? 'collapse' : 'expand'}`,\n      onClick: e => onExpandClick(e, {\n        expanded: !expanded\n      }),\n      \"aria-label\": expanded ? textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n    }, typeof symbol === 'function' ? symbol(expanded) : symbol)) : null;\n  };\n  // Edit\n  const renderEdit = () => {\n    if (!enableEdit) {\n      return;\n    }\n    const {\n      icon,\n      tooltip,\n      tabIndex\n    } = editConfig;\n    const editTitle = toArray(tooltip)[0] || (textLocale === null || textLocale === void 0 ? void 0 : textLocale.edit);\n    const ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? (/*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      ref: editIconRef,\n      className: `${prefixCls}-edit`,\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel,\n      tabIndex: tabIndex\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    })))) : null;\n  };\n  // Copy\n  const renderCopy = () => {\n    if (!enableCopy) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(CopyBtn, Object.assign({\n      key: \"copy\"\n    }, copyConfig, {\n      prefixCls: prefixCls,\n      copied: copied,\n      locale: textLocale,\n      onCopy: onCopyClick,\n      loading: copyLoading,\n      iconOnly: children === null || children === undefined\n    }));\n  };\n  const renderOperations = canEllipsis => [canEllipsis && renderExpand(), renderEdit(), renderCopy()];\n  const renderEllipsis = canEllipsis => [canEllipsis && !expanded && (/*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    key: \"ellipsis\"\n  }, ELLIPSIS_STR)), ellipsisConfig.suffix, renderOperations(canEllipsis)];\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis\n  }, resizeRef => (/*#__PURE__*/React.createElement(EllipsisTooltip, {\n    tooltipProps: tooltipProps,\n    enableEllipsis: mergedEnableEllipsis,\n    isEllipsis: isMergedEllipsis\n  }, /*#__PURE__*/React.createElement(Typography, Object.assign({\n    className: classNames({\n      [`${prefixCls}-${type}`]: type,\n      [`${prefixCls}-disabled`]: disabled,\n      [`${prefixCls}-ellipsis`]: enableEllipsis,\n      [`${prefixCls}-ellipsis-single-line`]: cssTextOverflow,\n      [`${prefixCls}-ellipsis-multiple-line`]: cssLineClamp\n    }, className),\n    prefixCls: customizePrefixCls,\n    style: Object.assign(Object.assign({}, style), {\n      WebkitLineClamp: cssLineClamp ? rows : undefined\n    }),\n    component: component,\n    ref: composeRef(resizeRef, typographyRef, ref),\n    direction: direction,\n    onClick: triggerType.includes('text') ? onEditClick : undefined,\n    \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n    title: title\n  }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n    enableMeasure: mergedEnableEllipsis && !cssEllipsis,\n    text: children,\n    rows: rows,\n    width: ellipsisWidth,\n    onEllipsis: onJsEllipsis,\n    expanded: expanded,\n    miscDeps: [copied, expanded, copyLoading, enableEdit, enableCopy, textLocale]\n  }, (node, canEllipsis) => wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, node.length > 0 && canEllipsis && !expanded && topAriaLabel ? (/*#__PURE__*/React.createElement(\"span\", {\n    key: \"show-content\",\n    \"aria-hidden\": true\n  }, node)) : node, renderEllipsis(canEllipsis))))))));\n});\nexport default Base;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'usage', '`ellipsis` only supports boolean value.') : void 0;\n  }\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;", "\"use client\";\n\nimport * as React from 'react';\nimport Base from './Base';\nconst Paragraph = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(Base, Object.assign({\n  ref: ref\n}, props, {\n  component: \"div\"\n}))));\nexport default Paragraph;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Text');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'usage', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Title');\n    process.env.NODE_ENV !== \"production\" ? warning(TITLE_ELE_LIST.includes(level), 'usage', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n  }\n  const component = TITLE_ELE_LIST.includes(level) ? `h${level}` : `h1`;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;", "\"use client\";\n\nimport Link from './<PERSON>';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nconst Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n", "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n", "import canUseDom from \"./canUseDom\";\nvar isStyleNameSupport = function isStyleNameSupport(styleName) {\n  if (canUseDom() && window.document.documentElement) {\n    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    var documentElement = window.document.documentElement;\n    return styleNameList.some(function (name) {\n      return name in documentElement.style;\n    });\n  }\n  return false;\n};\nvar isStyleValueSupport = function isStyleValueSupport(styleName, value) {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n  var ele = document.createElement('div');\n  var origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n  return isStyleNameSupport(styleName);\n}"], "names": ["props", "ref", "AntdIcon", "A", "icon", "getTitleStyles", "token", "styles", "for<PERSON>ach", "headingLevel", "fontSize", "lineHeight", "color", "titleMarginBottom", "fontWeightStrong", "marginBottom", "fontWeight", "getTitleStyle", "colorTextHeading", "getLinkStyles", "componentCls", "Object", "assign", "userSelect", "colorTextDisabled", "cursor", "pointerEvents", "getResetStyles", "code", "margin", "paddingInline", "paddingBlock", "fontFamily", "fontFamilyCode", "background", "border", "borderRadius", "kbd", "borderBottomWidth", "mark", "padding", "backgroundColor", "textDecoration", "textDecorationSkipInk", "strong", "marginInline", "marginBlock", "li", "ul", "listStyleType", "ol", "pre", "whiteSpace", "wordWrap", "display", "blockquote", "borderInlineStart", "opacity", "getEditableStyles", "paddingSM", "inputShift", "position", "insetInlineStart", "calc", "mul", "equal", "marginTop", "insetInlineEnd", "marginXS", "add", "insetBlockEnd", "colorTextDescription", "fontStyle", "textarea", "MozTransition", "height", "getCopyableStyles", "colorSuccess", "marginInlineStart", "genTypographyStyle", "titleMarginTop", "colorText", "wordBreak", "colorWarning", "colorError", "colorErrorActive", "colorErrorHover", "marginXXS", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "verticalAlign", "boxSizing", "WebkitLineClamp", "WebkitBoxOrient", "direction", "prefixCls", "aria<PERSON><PERSON><PERSON>", "className", "style", "max<PERSON><PERSON><PERSON>", "autoSize", "value", "onSave", "onCancel", "onEnd", "component", "enterIcon", "inComposition", "lastKeyCode", "current", "setCurrent", "_a", "resizableTextArea", "textArea", "focus", "length", "setSelectionRange", "confirmChange", "trim", "wrapCSSVar", "hashId", "cssVarCls", "textAreaClassName", "TextArea", "onChange", "_ref", "target", "replace", "onKeyDown", "_ref2", "keyCode", "onKeyUp", "_ref3", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "KeyCode", "ENTER", "ESC", "onCompositionStart", "onCompositionEnd", "onBlur", "rows", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "copyConfig", "children", "copied", "setCopied", "copyLoading", "setCopyLoading", "copyIdRef", "cleanCopyId", "clearTimeout", "copyOptions", "format", "onClick", "useEvent", "preventDefault", "stopPropagation", "text", "candidate", "arguments", "undefined", "Array", "isArray", "toList", "join", "setTimeout", "onCopy", "call", "error", "useMergedConfig", "propConfig", "templateConfig", "support", "useRef", "useEffect", "tooltip", "editConfigText", "useMemo", "title", "isValidElement", "__rest", "s", "t", "p", "prototype", "hasOwnProperty", "indexOf", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "Typography", "customizePrefixCls", "Component", "rootClassName", "setContentRef", "typographyDirection", "restProps", "getPrefixCls", "contextDirection", "contextClassName", "contextStyle", "mergedRef", "componentClassName", "mergedStyle", "val", "getNode", "dom", "defaultNode", "needDom", "isValidText", "includes", "locale", "iconOnly", "tooltips", "tabIndex", "loading", "btnLoading", "tooltipNodes", "iconNodes", "copiedText", "copy", "copyText", "systemStr", "copyTitle", "type", "CheckOutlined", "LoadingOutlined", "MeasureText", "spanRef", "isExceed", "span", "scrollHeight", "clientHeight", "getHeight", "left", "top", "sliceNodes", "nodeList", "len", "currLen", "currentNodeList", "node", "nextLen", "String", "restLen", "push", "slice", "STATUS_MEASURE_NONE", "STATUS_MEASURE_NO_NEED_ELLIPSIS", "lineClipStyle", "EllipsisMeasure", "enableMeasure", "width", "expanded", "miscDeps", "onEllipsis", "toArray", "nodeLen", "reduce", "totalLen", "getNodesLen", "fullContent", "ellipsisCutIndex", "setEllipsisCutIndex", "cutMidRef", "measureWhiteSpaceRef", "needEllipsisRef", "descRowsEllipsisRef", "symbolRowEllipsisRef", "canEllipsis", "setCanEllipsis", "needEllipsis", "setNeedEllipsis", "ellipsisHeight", "setEllipsisHeight", "parentWhiteSpace", "setParentWhiteSpace", "useLayoutEffect", "_b", "_c", "_d", "nextWhiteSpace", "getComputedStyle", "isOverflow", "baseRowsEllipsisHeight", "descRowsEllipsisHeight", "symbolRowEllipsisHeight", "maxRowsHeight", "Math", "max", "cutMidIndex", "ceil", "minIndex", "maxIndex", "targetMidIndex", "finalContent", "content", "concat", "measureStyle", "enableEllipsis", "isEllipsis", "tooltipProps", "open", "Base", "disabled", "ellipsis", "editable", "copyable", "textLocale", "useLocale", "typographyRef", "editIconRef", "textProps", "omit", "enableEdit", "editConfig", "editing", "setEditing", "useMergedState", "triggerType", "triggerEdit", "edit", "onStart", "prevEditing", "onEditClick", "onEditChange", "onEditCancel", "enableCopy", "onCopyClick", "isLineClampSupport", "setIsLineClampSupport", "isTextOverflowSupport", "setIsTextOverflowSupport", "isJsEllipsis", "setIsJsEllipsis", "isNativeEllipsis", "setIsNativeEllipsis", "isNativeVisible", "setIsNativeVisible", "ellipsisConfig", "expandable", "symbol", "isExpanded", "collapse", "expand", "setExpanded", "defaultExpanded", "mergedEnableEllipsis", "needMeasureEllipsis", "suffix", "cssEllipsis", "setCssEllipsis", "canUseCssEllipsis", "isMergedEllipsis", "cssTextOverflow", "cssLineClamp", "ellip<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onJsEllipsis", "js<PERSON><PERSON><PERSON>", "textEle", "currentEllipsis", "ele", "childDiv", "document", "createElement", "append<PERSON><PERSON><PERSON>", "rect", "getBoundingClientRect", "childRect", "<PERSON><PERSON><PERSON><PERSON>", "right", "bottom", "isEleEllipsis", "IntersectionObserver", "observer", "offsetParent", "observe", "disconnect", "topAriaLabel", "find", "renderExpand", "key", "info", "onExpand", "onExpandClick", "renderEdit", "editTitle", "role", "renderOperations", "onResize", "offsetWidth", "resizeRef", "toString", "underline", "delete", "del", "keyboard", "italic", "currentC<PERSON>nt", "wrap", "tag", "needed", "wrapperDecorations", "renderEllipsis", "rel", "mergedProps", "navigate", "Text", "mergedEllipsis", "TITLE_ELE_LIST", "level", "Link", "Title", "Paragraph", "module", "exports", "selection", "getSelection", "rangeCount", "active", "activeElement", "ranges", "getRangeAt", "tagName", "toUpperCase", "blur", "removeAllRanges", "range", "addRange", "deselectCurrent", "clipboardToIE11Formatting", "options", "debug", "message", "reselectPrevious", "success", "createRange", "textContent", "ariaHidden", "all", "clip", "webkitUserSelect", "MozUserSelect", "msUserSelect", "addEventListener", "clipboardData", "console", "warn", "window", "clearData", "setData", "body", "selectNodeContents", "execCommand", "Error", "err", "copyKey", "test", "navigator", "userAgent", "prompt", "<PERSON><PERSON><PERSON><PERSON>", "isStyleNameSupport", "styleName", "documentElement", "styleNameList", "some", "name", "isStyleValueSupport", "origin", "isStyleSupport", "styleValue"], "sourceRoot": ""}