{"version": 3, "file": "component---src-pages-callback-tsx-efc07a0ddb6be8b7dcf6.js", "mappings": "2LAMA,MAAM,MAAEA,GAAUC,EAAAA,EA0FlB,UAxFqBC,IAA6B,IAA5B,KAAEC,EAAI,SAAEC,GAAeF,EAC3C,MAAM,mBAAEG,IAAuBC,EAAAA,EAAAA,MACzB,EAACC,EAAK,EAAEC,IAAYC,EAAAA,EAAAA,UAAwB,OAC5C,EAACC,EAAY,EAAEC,IAAmBF,EAAAA,EAAAA,WAAS,GAqCjD,OAnCAG,EAAAA,EAAAA,YAAU,KACYC,WAClB,IAEE,MAAMC,EAAS,IAAIC,gBAAgBX,EAASY,QACtCC,EAAOH,EAAOI,IAAI,QAClBC,EAAQL,EAAOI,IAAI,SACnBE,EAAYN,EAAOI,IAAI,SAE7B,GAAIE,EAGF,OAFAZ,EAAS,yBAAyBY,UAClCT,GAAgB,GAIlB,IAAKM,EAGH,OAFAT,EAAS,+CACTG,GAAgB,SAMZN,EAAmBY,EAAME,QAASE,GACxCV,GAAgB,EAClB,CAAE,MAAOW,GACPC,QAAQhB,MAAM,8BAA+Be,GAC7Cd,EAAS,qCACTG,GAAgB,EAClB,GAGFa,EAAa,GACZ,CAACpB,EAASY,OAAQX,IAGnBoB,EAAAA,cAACC,EAAAA,EAAM,CACLC,KAAMxB,EAAKyB,KAAKC,aAChBC,MAAM,iBACNC,KAAK,YACLC,YAAY,GAEZP,EAAAA,cAAA,OAAKQ,UAAU,sDACZvB,EACCe,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACS,EAAAA,EAAI,CAACC,KAAK,UACXV,EAAAA,cAACzB,EAAK,CAACoC,MAAO,EAAGH,UAAU,QAAO,iCAIlC1B,EACFkB,EAAAA,cAACY,EAAAA,EAAK,CACJC,QAAQ,uBACRC,YAAahC,EACbiC,KAAK,QACLC,UAAQ,EACRR,UAAU,aAGZR,EAAAA,cAACY,EAAAA,EAAK,CACJC,QAAQ,4BACRC,YAAY,2EACZC,KAAK,UACLC,UAAQ,EACRR,UAAU,cAIT,C,wNC9Eb,MAAMS,EAAoB,CAACC,EAASC,EAAaC,EAAWC,EAAOC,KAAa,CAC9EC,WAAYL,EACZM,OAAQ,IAAG,QAAKH,EAAMI,cAAcJ,EAAMK,YAAYP,IACtD,CAAC,GAAGG,UAAkB,CACpBK,MAAOP,KAGEQ,EAAeP,IAC1B,MAAM,aACJQ,EACAC,mBAAoBC,EAAQ,SAC5BC,EAAQ,SACRC,EAAQ,SACRC,EAAQ,WACRC,EAAU,WACVC,EACAC,eAAgBC,EAAY,oBAC5BC,EAAmB,wBACnBC,EAAuB,UACvBC,EAAS,iBACTC,EAAgB,uBAChBC,EAAsB,eACtBC,GACEvB,EACJ,MAAO,CACL,CAACQ,GAAegB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAezB,IAAS,CACtE0B,SAAU,WACVC,QAAS,OACTC,WAAY,SACZC,QAASN,EACTO,SAAU,aACVb,eACA,CAAC,IAAIT,SAAqB,CACxBuB,UAAW,OAEb,CAAC,GAAGvB,aAAyB,CAC3BwB,KAAM,EACNC,SAAU,GAEZ,CAAC,GAAGzB,UAAsB,CACxB0B,gBAAiBvB,EACjBI,WAAY,GAEd,gBAAiB,CACfY,QAAS,OACTd,WACAE,cAEF,YAAa,CACXT,MAAOe,GAET,CAAC,IAAIb,kBAA8B,CACjC2B,SAAU,SACVC,QAAS,EACTC,WAAY,cAAc3B,KAAYQ,cAAgCR,KAAYQ,2BACpER,KAAYQ,qBAAuCR,KAAYQ,6BAC7DR,KAAYQ,KAE9B,CAAC,IAAIV,yBAAqC,CACxC8B,UAAW,EACXC,aAAc,eACdC,WAAY,EACZC,cAAe,EACfL,QAAS,KAGb,CAAC,GAAG5B,sBAAkC,CACpCoB,WAAY,aACZC,QAASP,EACT,CAAC,GAAGd,UAAsB,CACxB0B,gBAAiBtB,EACjBC,SAAUM,EACVJ,WAAY,GAEd,CAAC,GAAGP,aAAyB,CAC3BmB,QAAS,QACTY,aAAc5B,EACdL,MAAOe,EACPR,SAAUC,GAEZ,CAAC,GAAGN,iBAA6B,CAC/BmB,QAAS,QACTrB,MAAOc,IAGX,CAAC,GAAGZ,YAAwB,CAC1B+B,aAAc,EACdpC,OAAQ,eACRc,aAAc,GAEjB,EAEUyB,EAAe1C,IAC1B,MAAM,aACJQ,EAAY,aACZmC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,aACdC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,WACdC,EAAU,iBACVC,EAAgB,aAChBC,EAAY,UACZC,EAAS,gBACTC,EAAe,YACfC,GACEtD,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,YAAaZ,EAAkBiD,EAAgBD,EAAoBD,EAAc3C,EAAOQ,GACxF,SAAUZ,EAAkB0D,EAAaD,EAAiBD,EAAWpD,EAAOQ,GAC5E,YAAaZ,EAAkBoD,EAAgBD,EAAoBD,EAAc9C,EAAOQ,GACxF,UAAWgB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG7B,EAAkBuD,EAAcD,EAAkBD,EAAYjD,EAAOQ,IAAgB,CAC9H,CAAC,GAAGA,uBAAmC,CACrC+C,OAAQ,EACR1B,QAAS,MAIhB,EAEU2B,EAAiBxD,IAC5B,MAAM,aACJQ,EAAY,QACZiD,EAAO,kBACPC,EAAiB,SACjB/C,EAAQ,aACRgD,EAAY,UACZC,EAAS,eACTC,GACE7D,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,WAAY,CACVsD,kBAAmBnD,GAErB,CAAC,GAAGH,gBAA4B,CAC9BsD,kBAAmBnD,EACnBkB,QAAS,EACTM,SAAU,SACVtB,SAAU8C,EACV5C,YAAY,QAAK4C,GACjBI,gBAAiB,cACjB5D,OAAQ,OACR6D,QAAS,OACTC,OAAQ,UACR,CAAC,GAAGR,WAAkB,CACpBnD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,KAIb,eAAgB,CACdvD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,KAId,EAUH,OAAe,QAAc,SAAS7D,GAAS,CAACO,EAAaP,GAAQ0C,EAAa1C,GAAQwD,EAAexD,MARpEA,IAE5B,CACLmB,wBAAyBnB,EAAMkE,iBAC/B3C,eAAgB,GAAGvB,EAAMmE,kCACzB7C,uBAAwB,GAAGtB,EAAMoE,eAAepE,EAAMqE,mCC3KtDC,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAO/C,OAAOmD,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,mBAAjC/C,OAAOuD,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIlD,OAAOuD,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKxD,OAAOmD,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAeA,MAAMU,EAAgB,CACpBC,QAASC,EAAA,EACTC,KAAMC,EAAA,EACN9H,MAAO+H,EAAA,EACPC,QAASC,EAAA,GAELC,EAAWC,IACf,MAAM,KACJC,EAAI,UACJC,EAAS,KACTpG,GACEkG,EACEG,EAAWZ,EAAczF,IAAS,KACxC,OAAImG,GACK,QAAeA,EAAmB,gBAAoB,OAAQ,CACnE1G,UAAW,GAAG2G,UACbD,IAAO,KAAM,CACd1G,UAAW,IAAW,GAAG2G,SAAkBD,EAAKD,MAAMzG,eAGtC,gBAAoB4G,EAAU,CAChD5G,UAAW,GAAG2G,UACd,EAEEE,EAAgBJ,IACpB,MAAM,WACJK,EAAU,UACVH,EAAS,UACTI,EAAS,YACTC,EAAW,UACXC,GACER,EACES,GAAgC,IAAdH,QAAoC3H,IAAd2H,EAAuC,gBAAoBI,EAAA,EAAe,MAAQJ,EAChI,OAAOD,EAA2B,gBAAoB,SAAUzE,OAAOC,OAAO,CAC5E/B,KAAM,SACN6G,QAASJ,EACThH,UAAW,GAAG2G,eACdU,SAAU,GACTJ,GAAYC,GAAoB,IAAI,EAEnC9G,EAAqB,cAAiB,CAACqG,EAAOa,KAClD,MAAM,YACFhH,EACAqG,UAAWY,EAAkB,QAC7BlH,EAAO,OACPmH,EAAM,UACNxH,EAAS,cACTyH,EAAa,MACbC,EAAK,aACLC,EAAY,aACZC,EAAY,QACZR,EAAO,WACPS,EAAU,SACVrH,EAAQ,SACRsH,EAAQ,UACRC,EAAS,UACThB,EAAS,OACTiB,EAAM,GACNC,GACExB,EACJyB,EAAa/C,EAAOsB,EAAO,CAAC,cAAe,YAAa,UAAW,SAAU,YAAa,gBAAiB,QAAS,eAAgB,eAAgB,UAAW,aAAc,WAAY,WAAY,YAAa,YAAa,SAAU,QACpO0B,EAAQC,GAAa,YAAe,GAK3C,MAAMC,EAAc,SAAa,MACjC,sBAA0Bf,GAAK,KAAM,CACnCgB,cAAeD,EAAYE,YAE7B,MAAM,aACJC,EAAY,UACZ5F,EACAkF,SAAUW,EACV1B,UAAW2B,EACX1I,UAAW2I,EACXjB,MAAOkB,IACL,QAAmB,SACjBjC,EAAY6B,EAAa,QAASjB,IACjCsB,EAAYC,EAAQC,GAAa,EAASpC,GAC3CK,EAAc3B,IAClB,IAAI2D,EACJZ,GAAU,GACe,QAAxBY,EAAKvC,EAAMwC,eAA4B,IAAPD,GAAyBA,EAAGtD,KAAKe,EAAOpB,EAAE,EAEvE9E,EAAO,WAAc,SACNnB,IAAfqH,EAAMlG,KACDkG,EAAMlG,KAGRiH,EAAS,UAAY,QAC3B,CAACf,EAAMlG,KAAMiH,IAEVV,EAAa,WAAc,MACP,iBAAbgB,IAAyBA,EAASf,eACzCgB,IAGoB,kBAAbD,EACFA,GAGS,IAAdf,SAAuBA,KAGlB0B,KACR,CAACV,EAAWhB,EAAWe,EAAUW,IAE9BS,KAAa1B,QAAuBpI,IAAboB,IAAgCA,EACvDM,EAAW,IAAW6F,EAAW,GAAGA,KAAapG,IAAQ,CAC7D,CAAC,GAAGoG,wBAAiCrG,EACrC,CAAC,GAAGqG,cAAuBuC,EAC3B,CAAC,GAAGvC,cAAuBa,EAC3B,CAAC,GAAGb,SAAgC,QAAd/D,GACrB+F,EAAkB3I,EAAWyH,EAAesB,EAAWD,GACpDK,GAAY,EAAAC,EAAA,GAAUlB,EAAY,CACtCmB,MAAM,EACNnL,MAAM,IAEFgJ,EAAkB,WAAc,IACZ,iBAAbY,GAAyBA,EAASf,UACpCe,EAASf,UAEdgB,SAGc3I,IAAd2H,EACKA,EAEsB,iBAApB0B,GAAgCA,EAAgB1B,UAClD0B,EAAgB1B,UAElB2B,IACN,CAAC3B,EAAWe,EAAUC,EAAWW,IAC9BY,EAAkB,WAAc,KACpC,MAAMC,EAASzB,QAA2CA,EAAWW,EACrE,GAAsB,iBAAXc,EAAqB,CAC9B,MACIxC,UAAWyC,GACTD,EAEN,OADcpE,EAAOoE,EAAQ,CAAC,aAEhC,CACA,MAAO,CAAC,CAAC,GACR,CAACzB,EAAUW,IACd,OAAOI,EAAwB,gBAAoB,KAAW,CAC5DY,SAAUtB,EACVuB,WAAY,GAAG/C,WACfgD,cAAc,EACdC,aAAa,EACbC,aAAcC,IAAQ,CACpB3G,UAAW2G,EAAKC,eAElBC,WAAYnC,IACX,CAAC5J,EAAMgM,KACR,IACEjK,UAAWkK,EACXxC,MAAOyC,GACLlM,EACJ,OAAoB,gBAAoB,MAAOoE,OAAOC,OAAO,CAC3D2F,GAAIA,EACJX,KAAK,QAAWe,EAAa4B,GAC7B,aAAc9B,EACdnI,UAAW,IAAWc,EAAUoJ,GAChCxC,MAAOrF,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGsG,GAAelB,GAAQyC,GAC5ExC,aAAcA,EACdC,aAAcA,EACdR,QAASA,EACTgD,KAAM,SACLjB,GAAYD,EAA2B,gBAAoB1C,EAAU,CACtElG,YAAaA,EACboG,KAAMD,EAAMC,KACZC,UAAWA,EACXpG,KAAMA,IACF,KAAmB,gBAAoB,MAAO,CAClDP,UAAW,GAAG2G,aACbtG,EAAuB,gBAAoB,MAAO,CACnDL,UAAW,GAAG2G,aACbtG,GAAW,KAAMC,EAA2B,gBAAoB,MAAO,CACxEN,UAAW,GAAG2G,iBACbrG,GAAe,MAAO0H,EAAsB,gBAAoB,MAAO,CACxEhI,UAAW,GAAG2G,YACbqB,GAAU,KAAmB,gBAAoBnB,EAAe,CACjEC,WAAYA,EACZH,UAAWA,EACXI,UAAWG,EACXF,YAAaA,EACbC,UAAWqC,IACV,IACF,IAKL,Q,gEClNA,IAAIe,EAA6B,SAAUC,GACzC,SAASD,IACP,IAAIE,ECPYjF,EAAGkF,EAAGnF,EDgBtB,OARA,OAAgBoF,KAAMJ,GCRN/E,EDSGmF,KCTAD,EDSMH,ECTHhF,EDSkBqF,UCRnCF,GAAI,EAAAG,EAAA,GAAeH,IDQxBD,GCR4B,EAAAK,EAAA,GAA0BtF,GAAG,EAAAuF,EAAA,KAA6BC,QAAQC,UAAUP,EAAGnF,GAAK,IAAI,EAAAsF,EAAA,GAAerF,GAAG0F,aAAeR,EAAES,MAAM3F,EAAGD,KDS1JnG,MAAQ,CACZZ,WAAOc,EACP+G,KAAM,CACJ+E,eAAgB,KAGbX,CACT,CAEA,OADA,OAAUF,EAAeC,IAClB,OAAaD,EAAe,CAAC,CAClCc,IAAK,oBACLC,MAAO,SAA2B9M,EAAO6H,GACvCsE,KAAKY,SAAS,CACZ/M,QACA6H,QAEJ,GACC,CACDgF,IAAK,SACLC,MAAO,WACL,MAAM,QACJ/K,EAAO,YACPC,EAAW,GACX2H,EAAE,SACFqD,GACEb,KAAKhE,OACH,MACJnI,EAAK,KACL6H,GACEsE,KAAKvL,MACHgM,GAAkB/E,aAAmC,EAASA,EAAK+E,iBAAmB,KACtFK,OAAkC,IAAZlL,GAA2B/B,GAAS,IAAIkN,WAAanL,EAC3EoL,OAA0C,IAAhBnL,EAA8B4K,EAAiB5K,EAC/E,OAAIhC,EACkB,gBAAoB,EAAO,CAC7C2J,GAAIA,EACJ1H,KAAM,QACNF,QAASkL,EACTjL,YAA0B,gBAAoB,MAAO,CACnDoH,MAAO,CACLhG,SAAU,QACVgK,UAAW,SAEZD,KAGAH,CACT,IAEJ,CAtDiC,CAsD/B,aACF,QE3DA,MAAM,EAAQ,EACd,EAAMjB,cAAgB,EACtB,O", "sources": ["webpack://autogentstudio/./src/pages/callback.tsx", "webpack://autogentstudio/./node_modules/antd/es/alert/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/Alert.js", "webpack://autogentstudio/./node_modules/antd/es/alert/ErrorBoundary.js", "webpack://autogentstudio/./node_modules/@babel/runtime/helpers/esm/callSuper.js", "webpack://autogentstudio/./node_modules/antd/es/alert/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useAuth } from \"../auth/context\";\nimport { Spin, Typography, Alert } from \"antd\";\nimport Layout from \"../components/layout\";\nimport { graphql } from \"gatsby\";\n\nconst { Title } = Typography;\n\nconst CallbackPage = ({ data, location }: any) => {\n  const { handleAuthCallback } = useAuth();\n  const [error, setError] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(true);\n\n  useEffect(() => {\n    const processAuth = async () => {\n      try {\n        // Get the authorization code and state from URL search params\n        const params = new URLSearchParams(location.search);\n        const code = params.get(\"code\");\n        const state = params.get(\"state\");\n        const authError = params.get(\"error\");\n\n        if (authError) {\n          setError(`Authentication error: ${authError}`);\n          setIsProcessing(false);\n          return;\n        }\n\n        if (!code) {\n          setError(\"No authorization code found in the URL\");\n          setIsProcessing(false);\n          return;\n        }\n\n        // Handle the authorization code - for popup window\n        // The actual token handling is done by the backend HTML response\n        await handleAuthCallback(code, state || undefined);\n        setIsProcessing(false);\n      } catch (err) {\n        console.error(\"Error during auth callback:\", err);\n        setError(\"Failed to complete authentication\");\n        setIsProcessing(false);\n      }\n    };\n\n    processAuth();\n  }, [location.search, handleAuthCallback]);\n\n  return (\n    <Layout\n      meta={data.site.siteMetadata}\n      title=\"Authenticating\"\n      link=\"/callback\"\n      showHeader={false}\n    >\n      <div className=\"flex flex-col items-center justify-center h-screen\">\n        {isProcessing ? (\n          <>\n            <Spin size=\"large\" />\n            <Title level={4} className=\"mt-4\">\n              Completing Authentication...\n            </Title>\n          </>\n        ) : error ? (\n          <Alert\n            message=\"Authentication Error\"\n            description={error}\n            type=\"error\"\n            showIcon\n            className=\"max-w-md\"\n          />\n        ) : (\n          <Alert\n            message=\"Authentication Successful\"\n            description=\"You have been successfully authenticated. You can close this window now.\"\n            type=\"success\"\n            showIcon\n            className=\"max-w-md\"\n          />\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport const query = graphql`\n  query CallbackPageQuery {\n    site {\n      siteMetadata {\n        description\n        title\n      }\n    }\n  }\n`;\n\nexport default CallbackPage;\n", "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genAlertTypeStyle = (bgColor, borderColor, iconColor, token, alertCls) => ({\n  background: bgColor,\n  border: `${unit(token.lineWidth)} ${token.lineType} ${borderColor}`,\n  [`${alertCls}-icon`]: {\n    color: iconColor\n  }\n});\nexport const genBaseStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow: duration,\n    marginXS,\n    marginSM,\n    fontSize,\n    fontSizeLG,\n    lineHeight,\n    borderRadiusLG: borderRadius,\n    motionEaseInOutCirc,\n    withDescriptionIconSize,\n    colorText,\n    colorTextHeading,\n    withDescriptionPadding,\n    defaultPadding\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      padding: defaultPadding,\n      wordWrap: 'break-word',\n      borderRadius,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-content`]: {\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginXS,\n        lineHeight: 0\n      },\n      '&-description': {\n        display: 'none',\n        fontSize,\n        lineHeight\n      },\n      '&-message': {\n        color: colorTextHeading\n      },\n      [`&${componentCls}-motion-leave`]: {\n        overflow: 'hidden',\n        opacity: 1,\n        transition: `max-height ${duration} ${motionEaseInOutCirc}, opacity ${duration} ${motionEaseInOutCirc},\n        padding-top ${duration} ${motionEaseInOutCirc}, padding-bottom ${duration} ${motionEaseInOutCirc},\n        margin-bottom ${duration} ${motionEaseInOutCirc}`\n      },\n      [`&${componentCls}-motion-leave-active`]: {\n        maxHeight: 0,\n        marginBottom: '0 !important',\n        paddingTop: 0,\n        paddingBottom: 0,\n        opacity: 0\n      }\n    }),\n    [`${componentCls}-with-description`]: {\n      alignItems: 'flex-start',\n      padding: withDescriptionPadding,\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginSM,\n        fontSize: withDescriptionIconSize,\n        lineHeight: 0\n      },\n      [`${componentCls}-message`]: {\n        display: 'block',\n        marginBottom: marginXS,\n        color: colorTextHeading,\n        fontSize: fontSizeLG\n      },\n      [`${componentCls}-description`]: {\n        display: 'block',\n        color: colorText\n      }\n    },\n    [`${componentCls}-banner`]: {\n      marginBottom: 0,\n      border: '0 !important',\n      borderRadius: 0\n    }\n  };\n};\nexport const genTypeStyle = token => {\n  const {\n    componentCls,\n    colorSuccess,\n    colorSuccessBorder,\n    colorSuccessBg,\n    colorWarning,\n    colorWarningBorder,\n    colorWarningBg,\n    colorError,\n    colorErrorBorder,\n    colorErrorBg,\n    colorInfo,\n    colorInfoBorder,\n    colorInfoBg\n  } = token;\n  return {\n    [componentCls]: {\n      '&-success': genAlertTypeStyle(colorSuccessBg, colorSuccessBorder, colorSuccess, token, componentCls),\n      '&-info': genAlertTypeStyle(colorInfoBg, colorInfoBorder, colorInfo, token, componentCls),\n      '&-warning': genAlertTypeStyle(colorWarningBg, colorWarningBorder, colorWarning, token, componentCls),\n      '&-error': Object.assign(Object.assign({}, genAlertTypeStyle(colorErrorBg, colorErrorBorder, colorError, token, componentCls)), {\n        [`${componentCls}-description > pre`]: {\n          margin: 0,\n          padding: 0\n        }\n      })\n    }\n  };\n};\nexport const genActionStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    motionDurationMid,\n    marginXS,\n    fontSizeIcon,\n    colorIcon,\n    colorIconHover\n  } = token;\n  return {\n    [componentCls]: {\n      '&-action': {\n        marginInlineStart: marginXS\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: marginXS,\n        padding: 0,\n        overflow: 'hidden',\n        fontSize: fontSizeIcon,\n        lineHeight: unit(fontSizeIcon),\n        backgroundColor: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        [`${iconCls}-close`]: {\n          color: colorIcon,\n          transition: `color ${motionDurationMid}`,\n          '&:hover': {\n            color: colorIconHover\n          }\n        }\n      },\n      '&-close-text': {\n        color: colorIcon,\n        transition: `color ${motionDurationMid}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const paddingHorizontal = 12; // Fixed value here.\n  return {\n    withDescriptionIconSize: token.fontSizeHeading3,\n    defaultPadding: `${token.paddingContentVerticalSM}px ${paddingHorizontal}px`,\n    withDescriptionPadding: `${token.paddingMD}px ${token.paddingContentHorizontalLG}px`\n  };\n};\nexport default genStyleHooks('Alert', token => [genBaseStyle(token), genTypeStyle(token), genActionStyle(token)], prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport InfoCircleFilled from \"@ant-design/icons/es/icons/InfoCircleFilled\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst iconMapFilled = {\n  success: CheckCircleFilled,\n  info: InfoCircleFilled,\n  error: CloseCircleFilled,\n  warning: ExclamationCircleFilled\n};\nconst IconNode = props => {\n  const {\n    icon,\n    prefixCls,\n    type\n  } = props;\n  const iconType = iconMapFilled[type] || null;\n  if (icon) {\n    return replaceElement(icon, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-icon`\n    }, icon), () => ({\n      className: classNames(`${prefixCls}-icon`, icon.props.className)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(iconType, {\n    className: `${prefixCls}-icon`\n  });\n};\nconst CloseIconNode = props => {\n  const {\n    isClosable,\n    prefixCls,\n    closeIcon,\n    handleClose,\n    ariaProps\n  } = props;\n  const mergedCloseIcon = closeIcon === true || closeIcon === undefined ? /*#__PURE__*/React.createElement(CloseOutlined, null) : closeIcon;\n  return isClosable ? (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\",\n    onClick: handleClose,\n    className: `${prefixCls}-close-icon`,\n    tabIndex: 0\n  }, ariaProps), mergedCloseIcon)) : null;\n};\nconst Alert = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      description,\n      prefixCls: customizePrefixCls,\n      message,\n      banner,\n      className,\n      rootClassName,\n      style,\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      afterClose,\n      showIcon,\n      closable,\n      closeText,\n      closeIcon,\n      action,\n      id\n    } = props,\n    otherProps = __rest(props, [\"description\", \"prefixCls\", \"message\", \"banner\", \"className\", \"rootClassName\", \"style\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"afterClose\", \"showIcon\", \"closable\", \"closeText\", \"closeIcon\", \"action\", \"id\"]);\n  const [closed, setClosed] = React.useState(false);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Alert');\n    warning.deprecated(!closeText, 'closeText', 'closable.closeIcon');\n  }\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const {\n    getPrefixCls,\n    direction,\n    closable: contextClosable,\n    closeIcon: contextCloseIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('alert');\n  const prefixCls = getPrefixCls('alert', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const handleClose = e => {\n    var _a;\n    setClosed(true);\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props, e);\n  };\n  const type = React.useMemo(() => {\n    if (props.type !== undefined) {\n      return props.type;\n    }\n    // banner mode defaults to 'warning'\n    return banner ? 'warning' : 'info';\n  }, [props.type, banner]);\n  // closeable when closeText or closeIcon is assigned\n  const isClosable = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) return true;\n    if (closeText) {\n      return true;\n    }\n    if (typeof closable === 'boolean') {\n      return closable;\n    }\n    // should be true when closeIcon is 0 or ''\n    if (closeIcon !== false && closeIcon !== null && closeIcon !== undefined) {\n      return true;\n    }\n    return !!contextClosable;\n  }, [closeText, closeIcon, closable, contextClosable]);\n  // banner mode defaults to Icon\n  const isShowIcon = banner && showIcon === undefined ? true : showIcon;\n  const alertCls = classNames(prefixCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-description`]: !!description,\n    [`${prefixCls}-no-icon`]: !isShowIcon,\n    [`${prefixCls}-banner`]: !!banner,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, cssVarCls, hashId);\n  const restProps = pickAttrs(otherProps, {\n    aria: true,\n    data: true\n  });\n  const mergedCloseIcon = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) {\n      return closable.closeIcon;\n    }\n    if (closeText) {\n      return closeText;\n    }\n    if (closeIcon !== undefined) {\n      return closeIcon;\n    }\n    if (typeof contextClosable === 'object' && contextClosable.closeIcon) {\n      return contextClosable.closeIcon;\n    }\n    return contextCloseIcon;\n  }, [closeIcon, closable, closeText, contextCloseIcon]);\n  const mergedAriaProps = React.useMemo(() => {\n    const merged = closable !== null && closable !== void 0 ? closable : contextClosable;\n    if (typeof merged === 'object') {\n      const {\n          closeIcon: _\n        } = merged,\n        ariaProps = __rest(merged, [\"closeIcon\"]);\n      return ariaProps;\n    }\n    return {};\n  }, [closable, contextClosable]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !closed,\n    motionName: `${prefixCls}-motion`,\n    motionAppear: false,\n    motionEnter: false,\n    onLeaveStart: node => ({\n      maxHeight: node.offsetHeight\n    }),\n    onLeaveEnd: afterClose\n  }, (_ref, setRef) => {\n    let {\n      className: motionClassName,\n      style: motionStyle\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n      id: id,\n      ref: composeRef(internalRef, setRef),\n      \"data-show\": !closed,\n      className: classNames(alertCls, motionClassName),\n      style: Object.assign(Object.assign(Object.assign({}, contextStyle), style), motionStyle),\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      role: \"alert\"\n    }, restProps), isShowIcon ? (/*#__PURE__*/React.createElement(IconNode, {\n      description: description,\n      icon: props.icon,\n      prefixCls: prefixCls,\n      type: type\n    })) : null, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-content`\n    }, message ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-message`\n    }, message) : null, description ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-description`\n    }, description) : null), action ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-action`\n    }, action) : null, /*#__PURE__*/React.createElement(CloseIconNode, {\n      isClosable: isClosable,\n      prefixCls: prefixCls,\n      closeIcon: mergedCloseIcon,\n      handleClose: handleClose,\n      ariaProps: mergedAriaProps\n    }));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Alert.displayName = 'Alert';\n}\nexport default Alert;", "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };", "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;"], "names": ["Title", "Typography", "_ref", "data", "location", "handleAuthCallback", "useAuth", "error", "setError", "useState", "isProcessing", "setIsProcessing", "useEffect", "async", "params", "URLSearchParams", "search", "code", "get", "state", "authError", "undefined", "err", "console", "processAuth", "React", "Layout", "meta", "site", "siteMetadata", "title", "link", "showHeader", "className", "Spin", "size", "level", "<PERSON><PERSON>", "message", "description", "type", "showIcon", "genAlertTypeStyle", "bgColor", "borderColor", "iconColor", "token", "alertCls", "background", "border", "lineWidth", "lineType", "color", "genBaseStyle", "componentCls", "motionDurationSlow", "duration", "marginXS", "marginSM", "fontSize", "fontSizeLG", "lineHeight", "borderRadiusLG", "borderRadius", "motionEaseInOutCirc", "withDescriptionIconSize", "colorText", "colorTextHeading", "withDescriptionPadding", "defaultPadding", "Object", "assign", "position", "display", "alignItems", "padding", "wordWrap", "direction", "flex", "min<PERSON><PERSON><PERSON>", "marginInlineEnd", "overflow", "opacity", "transition", "maxHeight", "marginBottom", "paddingTop", "paddingBottom", "genTypeStyle", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "margin", "genActionStyle", "iconCls", "motionDurationMid", "fontSizeIcon", "colorIcon", "colorIconHover", "marginInlineStart", "backgroundColor", "outline", "cursor", "fontSizeHeading3", "paddingContentVerticalSM", "paddingMD", "paddingContentHorizontalLG", "__rest", "s", "e", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "iconMapFilled", "success", "CheckCircleFilled", "info", "InfoCircleFilled", "CloseCircleFilled", "warning", "ExclamationCircleFilled", "IconNode", "props", "icon", "prefixCls", "iconType", "CloseIconNode", "isClosable", "closeIcon", "handleClose", "ariaProps", "mergedCloseIcon", "CloseOutlined", "onClick", "tabIndex", "ref", "customizePrefixCls", "banner", "rootClassName", "style", "onMouseEnter", "onMouseLeave", "afterClose", "closable", "closeText", "action", "id", "otherProps", "closed", "setClosed", "internalRef", "nativeElement", "current", "getPrefixCls", "contextClosable", "contextCloseIcon", "contextClassName", "contextStyle", "wrapCSSVar", "hashId", "cssVarCls", "_a", "onClose", "isShowIcon", "restProps", "pickAttrs", "aria", "mergedAriaProps", "merged", "_", "visible", "motionName", "motionAppear", "motionEnter", "onLeaveStart", "node", "offsetHeight", "onLeaveEnd", "setRef", "motionClassName", "motionStyle", "role", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "o", "this", "arguments", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "componentStack", "key", "value", "setState", "children", "errorMessage", "toString", "errorDescription", "overflowX"], "sourceRoot": ""}