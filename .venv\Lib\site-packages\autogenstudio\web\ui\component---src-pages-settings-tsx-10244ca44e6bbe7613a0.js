/*! For license information please see component---src-pages-settings-tsx-10244ca44e6bbe7613a0.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[512],{418:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1325:function(e,t,n){n.d(t,{C:function(){return i}});var o=n(1511),r=n(7134),a=n(1806);const l={show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1},i=(0,o.v)()((0,r.Zr)(((e,t)=>({serverSettings:null,isLoading:!1,error:null,uiSettings:l,initializeSettings:async n=>{if(!t().isLoading)try{e({isLoading:!0,error:null});const t=await a.Y.getSettings(n),o=(e=>e&&e.config&&e.config.ui?e.config.ui:l)(t);e({serverSettings:t,uiSettings:o,isLoading:!1})}catch(o){console.error("Failed to load settings:",o),e({error:"Failed to load settings",isLoading:!1,uiSettings:l})}},updateUISettings:n=>{const{uiSettings:o}=t(),r={...o,...n};e({uiSettings:r})},resetUISettings:async()=>(e({uiSettings:l}),Promise.resolve())})),{name:"ags-app-settings-0",partialize:e=>({uiSettings:e.uiSettings})}))},1806:function(e,t,n){n.d(t,{Y:function(){return a}});var o=n(7387);let r=function(e){function t(){return e.apply(this,arguments)||this}(0,o.A)(t,e);var n=t.prototype;return n.getSettings=async function(e){const t=await fetch(`${this.getBaseUrl()}/settings/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch settings");return n.data},n.updateSettings=async function(e,t){const n={...e,user_id:e.user_id||t};console.log("settingsData",n);const o=await fetch(`${this.getBaseUrl()}/settings/`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(n)}),r=await o.json();if(!r.status)throw new Error(r.message||"Failed to update settings");return r.data},t}(n(3838).y);const a=new r},7690:function(e,t,n){n.r(t),n.d(t,{default:function(){return gi}});var o=n(6540),r=n(5312),a=n(6161),l=n(1788);const i=(0,l.A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);var c=n(7073);const s=(0,l.A)("Variable",[["path",{d:"M8 21s-4-3-4-9 4-9 4-9",key:"uto9ud"}],["path",{d:"M16 3s4 3 4 9-4 9-4 9",key:"4w2vsq"}],["line",{x1:"15",x2:"9",y1:"9",y2:"15",key:"f7djnv"}],["line",{x1:"9",x2:"15",y1:"9",y2:"15",key:"1shsy8"}]]);var d=n(1325),u=n(955),p=n(9910),f=n(9644);const m=e=>{let{isOpen:t,sections:n,currentSection:r,onToggle:a,onSelectSection:l}=e;return t?o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-primary font-medium"},"Settings"),o.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},n.length)),o.createElement(u.A,{title:"Close Sidebar"},o.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(f.A,{strokeWidth:1.5,className:"h-6 w-6"})))),o.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)]"},n.map((e=>o.createElement("div",{key:e.id,className:"relative"},o.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded \n                "+(r.id===e.id?"bg-accent":"bg-tertiary")}),o.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary \n                "+(r.id===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>l(e)},o.createElement("div",{className:"flex items-center gap-2"},o.createElement(e.icon,{className:"w-4 h-4"}),o.createElement("span",{className:"text-sm"},e.title)))))))):o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"p-2 -ml-2"},o.createElement(u.A,{title:`Settings (${n.length})`},o.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(p.A,{strokeWidth:1.5,className:"h-6 w-6"})))))};var g=n(2744),h=n(9036),v=n(2941),b=n(9492),y=n(8852),x=n(418),C=n(1806);const S=e=>{let{checked:t,onChange:n,label:r,description:a,disabled:l=!1}=e;return o.createElement("div",{className:"flex justify-between items-start p-4 hover:bg-secondary/5 rounded transition-colors"},o.createElement("div",{className:"flex flex-col gap-1"},o.createElement("label",{className:"font-medium"},r),a&&o.createElement("span",{className:"text-sm text-secondary"},a)),o.createElement("div",{className:"relative"},o.createElement("input",{type:"checkbox",checked:t,onChange:e=>n(e.target.checked),disabled:l,className:"sr-only",id:`toggle-${r.replace(/\s+/g,"-").toLowerCase()}`}),o.createElement("label",{htmlFor:`toggle-${r.replace(/\s+/g,"-").toLowerCase()}`,className:`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50 cursor-pointer ${l?"opacity-50 cursor-not-allowed":""} ${t?"bg-accent":"bg-gray-300"}`},o.createElement("span",{className:"inline-block w-4 h-4 transform bg-white rounded-full transition-transform "+(t?"translate-x-6":"translate-x-1")}))))};var w=e=>{var t,n;let{userId:r}=e;const{serverSettings:a,uiSettings:l,initializeSettings:i}=(0,d.C)(),{0:c,1:s}=(0,o.useState)({show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1}),{0:p,1:f}=(0,o.useState)(!1),{0:m,1:g}=(0,o.useState)(!1),[w,E]=h.Ay.useMessage();(0,o.useEffect)((()=>{s(l)}),[l]);const A=(e,t)=>{s((n=>({...n,[e]:t}))),f(!0)};return o.createElement("div",{className:" "},E,o.createElement("div",{className:"flex justify-between items-center mb-4"},o.createElement("h3",{className:"text-lg font-medium"},"UI Settings"),o.createElement("div",{className:"space-x-2 inline-flex"},o.createElement(u.A,{title:"Reset to defaults"},o.createElement(v.Ay,{icon:o.createElement(b.A,{className:"w-4 h-4"}),onClick:async()=>{try{if(g(!0),!a)return w.error("Settings not loaded"),void g(!1);const e={show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1};s(e);const t={...a,config:{...a.config,ui:e},created_at:void 0,updated_at:void 0};console.log("Updated settings:",t),await C.Y.updateSettings(t,r),await i(r),f(!1),w.success("UI settings reset successfully")}catch(e){console.error("Failed to reset UI settings:",e),w.error("Failed to reset UI settings")}finally{g(!1)}},disabled:m},"Reset")),o.createElement(u.A,{title:p?"Save your changes":"No unsaved changes"},o.createElement(v.Ay,{type:"primary",icon:o.createElement("div",{className:"relative"},o.createElement(y.A,{className:"w-4 h-4"}),p&&o.createElement("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"})),onClick:async()=>{try{if(g(!0),!a)return w.error("Settings not loaded"),void g(!1);const e={...a,config:{...a.config,ui:c},created_at:void 0,updated_at:void 0};await C.Y.updateSettings(e,r),await i(r),f(!1),w.success("UI settings saved successfully")}catch(e){console.error("Failed to save UI settings:",e),w.error("Failed to save UI settings")}finally{g(!1)}},disabled:!p||m,loading:m},"Save")))),o.createElement("div",{className:"space-y-0 rounded border border-secondary"},o.createElement(S,{checked:c.show_llm_call_events,onChange:e=>A("show_llm_call_events",e),label:"Show LLM Events",description:"Display detailed LLM call logs in the message thread",disabled:m}),o.createElement(S,{checked:null!==(t=c.expanded_messages_by_default)&&void 0!==t&&t,onChange:e=>A("expanded_messages_by_default",e),label:"Expand Messages by Default",description:"Automatically expand message threads when they load",disabled:m}),o.createElement(S,{checked:null!==(n=c.show_agent_flow_by_default)&&void 0!==n&&n,onChange:e=>A("show_agent_flow_by_default",e),label:"Show Agent Flow by Default",description:"Display the agent flow diagram automatically",disabled:m})),o.createElement("div",{className:"mt-4 hidden text-xs text-secondary"},o.createElement(x.A,{strokeWidth:1.5,className:"inline-block mr-1 h-4 w-4"})," ","These settings are automatically saved and synced across browser sessions"))},E=n(6143);const A=(0,l.A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);var k=n(7199);const $=e=>{var t,n,r;let{modelComponent:a,onModelUpdate:l}=e;const{0:i,1:c}=(0,o.useState)(!1),s=()=>{c(!1)};return o.createElement(o.Fragment,null,o.createElement("div",{className:" "},o.createElement("div",{className:"flex justify-between items-center mb-4"},o.createElement("h3",{className:"text-lg font-medium"},"Default Model Configuration"),o.createElement(u.A,{title:"Edit Default Model Settings"},o.createElement(v.Ay,{type:"primary",icon:o.createElement(A,{className:"w-4 h-4 mr-1"}),onClick:()=>{c(!0)},className:"flex items-center"},"Edit Model"))),o.createElement("div",{className:"mb-6"},"Configure a default model that will be used for system level tasks."),o.createElement("div",{className:"bg-secondary p-4 rounded"},o.createElement("div",{className:"grid grid-cols-2 gap-4"},o.createElement("div",null,o.createElement("p",{className:"text-sm font-medium text-primary"},"Model"),o.createElement("p",{className:"text-sm"},a.label||"Not set"),o.createElement("p",{className:"text-base"},(null===(t=a.config)||void 0===t?void 0:t.model)||"Not set")),o.createElement("div",null,o.createElement("p",{className:"text-sm font-medium text-primary"},"Model Provider"),o.createElement("p",{className:"  break-all text-sm"},a.provider||"Not set")),(null===(n=a.config)||void 0===n?void 0:n.temperature)&&o.createElement("div",null,o.createElement("p",{className:"text-sm font-medium text-primary"},"Temperature"),o.createElement("p",{className:"text-base"},null===(r=a.config)||void 0===r?void 0:r.temperature))))),o.createElement(E.A,{title:"Edit Default Model Client",placement:"right",size:"large",onClose:s,open:i,className:"component-editor-drawer"},o.createElement(k.L,{component:a,onChange:async e=>{await l(e),c(!1)},onClose:s,navigationDepth:!0})))};var N=n(436),O=n(2605),I=n(9957),z=n(5319),K=n(6942),P=n.n(K),R=n(9853),M=n(2279),T=n(829),B=n(7072),j=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var D=e=>{var{prefixCls:t,className:n,hoverable:r=!0}=e,a=j(e,["prefixCls","className","hoverable"]);const{getPrefixCls:l}=o.useContext(M.QO),i=l("card",t),c=P()(`${i}-grid`,n,{[`${i}-grid-hoverable`]:r});return o.createElement("div",Object.assign({},a,{className:c}))},L=n(2187),H=n(5905),_=n(7358),F=n(4277);const W=e=>{const{antCls:t,componentCls:n,headerHeight:o,headerPadding:r,tabsMarginBottom:a}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:o,marginBottom:-1,padding:`0 ${(0,L.zA)(r)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,L.zA)(e.borderRadiusLG)} ${(0,L.zA)(e.borderRadiusLG)} 0 0`},(0,H.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},H.L9),{[`\n          > ${n}-typography,\n          > ${n}-typography-edit-content\n        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:a,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},q=e=>{const{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:o,lineWidth:r}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`\n      ${(0,L.zA)(r)} 0 0 0 ${n},\n      0 ${(0,L.zA)(r)} 0 0 ${n},\n      ${(0,L.zA)(r)} ${(0,L.zA)(r)} 0 0 ${n},\n      ${(0,L.zA)(r)} 0 0 0 ${n} inset,\n      0 ${(0,L.zA)(r)} 0 0 ${n} inset;\n    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:o}}},V=e=>{const{componentCls:t,iconCls:n,actionsLiMargin:o,cardActionsIconSize:r,colorBorderSecondary:a,actionsBg:l}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:l,borderTop:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${a}`,display:"flex",borderRadius:`0 0 ${(0,L.zA)(e.borderRadiusLG)} ${(0,L.zA)(e.borderRadiusLG)}`},(0,H.t6)()),{"& > li":{margin:o,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:(0,L.zA)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:r,lineHeight:(0,L.zA)(e.calc(r).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${a}`}}})},X=e=>Object.assign(Object.assign({margin:`${(0,L.zA)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,H.t6)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},H.L9),"&-description":{color:e.colorTextDescription}}),U=e=>{const{componentCls:t,colorFillAlter:n,headerPadding:o,bodyPadding:r}=e;return{[`${t}-head`]:{padding:`0 ${(0,L.zA)(o)}`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,L.zA)(e.padding)} ${(0,L.zA)(r)}`}}},G=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},Y=e=>{const{componentCls:t,cardShadow:n,cardHeadPadding:o,colorBorderSecondary:r,boxShadowTertiary:a,bodyPadding:l,extraColor:i}=e;return{[t]:Object.assign(Object.assign({},(0,H.dF)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:a},[`${t}-head`]:W(e),[`${t}-extra`]:{marginInlineStart:"auto",color:i,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:l,borderRadius:`0 0 ${(0,L.zA)(e.borderRadiusLG)} ${(0,L.zA)(e.borderRadiusLG)}`},(0,H.t6)()),[`${t}-grid`]:q(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${(0,L.zA)(e.borderRadiusLG)} ${(0,L.zA)(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:V(e),[`${t}-meta`]:X(e)}),[`${t}-bordered`]:{border:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${r}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${t}-contain-grid`]:{borderRadius:`${(0,L.zA)(e.borderRadiusLG)} ${(0,L.zA)(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:o}}},[`${t}-type-inner`]:U(e),[`${t}-loading`]:G(e),[`${t}-rtl`]:{direction:"rtl"}}},Q=e=>{const{componentCls:t,bodyPaddingSM:n,headerPaddingSM:o,headerHeightSM:r,headerFontSizeSM:a}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:r,padding:`0 ${(0,L.zA)(o)}`,fontSize:a,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}};var J=(0,_.OF)("Card",(e=>{const t=(0,F.oX)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[Y(t),Q(t)]}),(e=>{var t,n;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(n=e.headerPadding)&&void 0!==n?n:e.paddingLG}})),Z=n(124),ee=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const te=e=>{const{actionClasses:t,actions:n=[],actionStyle:r}=e;return o.createElement("ul",{className:t,style:r},n.map(((e,t)=>{const r=`action-${t}`;return o.createElement("li",{style:{width:100/n.length+"%"},key:r},o.createElement("span",null,e))})))},ne=o.forwardRef(((e,t)=>{const{prefixCls:n,className:r,rootClassName:l,style:i,extra:c,headStyle:s={},bodyStyle:d={},title:u,loading:p,bordered:f,variant:m,size:g,type:h,cover:v,actions:b,tabList:y,children:x,activeTabKey:C,defaultActiveTabKey:S,tabBarExtraContent:w,hoverable:E,tabProps:A={},classNames:k,styles:$}=e,N=ee(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:O,direction:I,card:z}=o.useContext(M.QO),[K]=(0,Z.A)("card",m,f);const j=e=>{var t;return P()(null===(t=null==z?void 0:z.classNames)||void 0===t?void 0:t[e],null==k?void 0:k[e])},L=e=>{var t;return Object.assign(Object.assign({},null===(t=null==z?void 0:z.styles)||void 0===t?void 0:t[e]),null==$?void 0:$[e])},H=o.useMemo((()=>{let e=!1;return o.Children.forEach(x,(t=>{(null==t?void 0:t.type)===D&&(e=!0)})),e}),[x]),_=O("card",n),[F,W,q]=J(_),V=o.createElement(B.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},x),X=void 0!==C,U=Object.assign(Object.assign({},A),{[X?"activeKey":"defaultActiveKey"]:X?C:S,tabBarExtraContent:w});let G;const Y=(0,T.A)(g),Q=Y&&"default"!==Y?Y:"large",ne=y?o.createElement(a.A,Object.assign({size:Q},U,{className:`${_}-head-tabs`,onChange:t=>{var n;null===(n=e.onTabChange)||void 0===n||n.call(e,t)},items:y.map((e=>{var{tab:t}=e,n=ee(e,["tab"]);return Object.assign({label:t},n)}))})):null;if(u||c||ne){const e=P()(`${_}-head`,j("header")),t=P()(`${_}-head-title`,j("title")),n=P()(`${_}-extra`,j("extra")),r=Object.assign(Object.assign({},s),L("header"));G=o.createElement("div",{className:e,style:r},o.createElement("div",{className:`${_}-head-wrapper`},u&&o.createElement("div",{className:t,style:L("title")},u),c&&o.createElement("div",{className:n,style:L("extra")},c)),ne)}const oe=P()(`${_}-cover`,j("cover")),re=v?o.createElement("div",{className:oe,style:L("cover")},v):null,ae=P()(`${_}-body`,j("body")),le=Object.assign(Object.assign({},d),L("body")),ie=o.createElement("div",{className:ae,style:le},p?V:x),ce=P()(`${_}-actions`,j("actions")),se=(null==b?void 0:b.length)?o.createElement(te,{actionClasses:ce,actionStyle:L("actions"),actions:b}):null,de=(0,R.A)(N,["onTabChange"]),ue=P()(_,null==z?void 0:z.className,{[`${_}-loading`]:p,[`${_}-bordered`]:"borderless"!==K,[`${_}-hoverable`]:E,[`${_}-contain-grid`]:H,[`${_}-contain-tabs`]:null==y?void 0:y.length,[`${_}-${Y}`]:Y,[`${_}-type-${h}`]:!!h,[`${_}-rtl`]:"rtl"===I},r,l,W,q),pe=Object.assign(Object.assign({},null==z?void 0:z.style),i);return F(o.createElement("div",Object.assign({ref:t},de,{className:ue,style:pe}),G,re,ie,se))}));var oe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var re=e=>{const{prefixCls:t,className:n,avatar:r,title:a,description:l}=e,i=oe(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:c}=o.useContext(M.QO),s=c("card",t),d=P()(`${s}-meta`,n),u=r?o.createElement("div",{className:`${s}-meta-avatar`},r):null,p=a?o.createElement("div",{className:`${s}-meta-title`},a):null,f=l?o.createElement("div",{className:`${s}-meta-description`},l):null,m=p||f?o.createElement("div",{className:`${s}-meta-detail`},p,f):null;return o.createElement("div",Object.assign({},i,{className:d}),u,m)};const ae=ne;ae.Grid=D,ae.Meta=re;var le=ae,ie={},ce="rc-table-internal-hook",se=n(5544),de=n(6956),ue=n(981),pe=n(3210),fe=n(961);function me(e){var t=o.createContext(void 0);return{Context:t,Provider:function(e){var n=e.value,r=e.children,a=o.useRef(n);a.current=n;var l=o.useState((function(){return{getValue:function(){return a.current},listeners:new Set}})),i=(0,se.A)(l,1)[0];return(0,ue.A)((function(){(0,fe.unstable_batchedUpdates)((function(){i.listeners.forEach((function(e){e(n)}))}))}),[n]),o.createElement(t.Provider,{value:i},r)},defaultValue:e}}function ge(e,t){var n=(0,de.A)("function"==typeof t?t:function(e){if(void 0===t)return e;if(!Array.isArray(t))return e[t];var n={};return t.forEach((function(t){n[t]=e[t]})),n}),r=o.useContext(null==e?void 0:e.Context),a=r||{},l=a.listeners,i=a.getValue,c=o.useRef();c.current=n(r?i():null==e?void 0:e.defaultValue);var s=o.useState({}),d=(0,se.A)(s,2)[1];return(0,ue.A)((function(){if(r)return l.add(e),function(){l.delete(e)};function e(e){var t=n(e);(0,pe.A)(c.current,t,!0)||d({})}}),[r]),c.current}var he=n(8168),ve=n(8719);function be(){var e=o.createContext(null);function t(){return o.useContext(e)}return{makeImmutable:function(n,r){var a=(0,ve.f3)(n),l=function(l,i){var c=a?{ref:i}:{},s=o.useRef(0),d=o.useRef(l);return null!==t()?o.createElement(n,(0,he.A)({},l,c)):(r&&!r(d.current,l)||(s.current+=1),d.current=l,o.createElement(e.Provider,{value:s.current},o.createElement(n,(0,he.A)({},l,c))))};return a?o.forwardRef(l):l},responseImmutable:function(e,n){var r=(0,ve.f3)(e),a=function(n,a){var l=r?{ref:a}:{};return t(),o.createElement(e,(0,he.A)({},n,l))};return r?o.memo(o.forwardRef(a),n):o.memo(a,n)},useImmutableMark:t}}var ye=be(),xe=(ye.makeImmutable,ye.responseImmutable,ye.useImmutableMark,be()),Ce=xe.makeImmutable,Se=xe.responseImmutable,we=xe.useImmutableMark,Ee=me();var Ae=n(2284),ke=n(9379),$e=n(4467),Ne=n(8104),Oe=n(6300),Ie=n(8210),ze=o.createContext({renderWithProps:!1});function Ke(e){var t=[],n={};return e.forEach((function(e){for(var o,r=e||{},a=r.key,l=r.dataIndex,i=a||(o=l,null==o?[]:Array.isArray(o)?o:[o]).join("-")||"RC_TABLE_KEY";n[i];)i="".concat(i,"_next");n[i]=!0,t.push(i)})),t}function Pe(e){return null!=e}function Re(e,t,n,r,a,l){var i=o.useContext(ze),c=we();return(0,Ne.A)((function(){if(Pe(r))return[r];var l,c=null==t||""===t?[]:Array.isArray(t)?t:[t],s=(0,Oe.A)(e,c),d=s,u=void 0;if(a){var p=a(s,e,n);!(l=p)||"object"!==(0,Ae.A)(l)||Array.isArray(l)||o.isValidElement(l)?d=p:(d=p.children,u=p.props,i.renderWithProps=!0)}return[d,u]}),[c,e,r,t,a,n],(function(e,t){if(l){var n=(0,se.A)(e,2)[1],o=(0,se.A)(t,2)[1];return l(o,n)}return!!i.renderWithProps||!(0,pe.A)(e,t,!0)}))}var Me=n(1470);function Te(e){var t,n,r,a,l,i,c,s;var d=e.component,u=e.children,p=e.ellipsis,f=e.scope,m=e.prefixCls,g=e.className,h=e.align,v=e.record,b=e.render,y=e.dataIndex,x=e.renderIndex,C=e.shouldCellUpdate,S=e.index,w=e.rowType,E=e.colSpan,A=e.rowSpan,k=e.fixLeft,$=e.fixRight,N=e.firstFixLeft,O=e.lastFixLeft,I=e.firstFixRight,z=e.lastFixRight,K=e.appendNode,R=e.additionalProps,M=void 0===R?{}:R,T=e.isSticky,B="".concat(m,"-cell"),j=ge(Ee,["supportSticky","allColumnsFixedLeft","rowHoverable"]),D=j.supportSticky,L=j.allColumnsFixedLeft,H=j.rowHoverable,_=Re(v,y,x,u,b,C),F=(0,se.A)(_,2),W=F[0],q=F[1],V={},X="number"==typeof k&&D,U="number"==typeof $&&D;X&&(V.position="sticky",V.left=k),U&&(V.position="sticky",V.right=$);var G=null!==(t=null!==(n=null!==(r=null==q?void 0:q.colSpan)&&void 0!==r?r:M.colSpan)&&void 0!==n?n:E)&&void 0!==t?t:1,Y=null!==(a=null!==(l=null!==(i=null==q?void 0:q.rowSpan)&&void 0!==i?i:M.rowSpan)&&void 0!==l?l:A)&&void 0!==a?a:1,Q=function(e,t){return ge(Ee,(function(n){var o,r,a,l;return[(o=e,r=t||1,a=n.hoverStartRow,l=n.hoverEndRow,o<=l&&o+r-1>=a),n.onHover]}))}(S,Y),J=(0,se.A)(Q,2),Z=J[0],ee=J[1],te=(0,Me._q)((function(e){var t;v&&ee(S,S+Y-1),null==M||null===(t=M.onMouseEnter)||void 0===t||t.call(M,e)})),ne=(0,Me._q)((function(e){var t;v&&ee(-1,-1),null==M||null===(t=M.onMouseLeave)||void 0===t||t.call(M,e)}));if(0===G||0===Y)return null;var oe=null!==(c=M.title)&&void 0!==c?c:function(e){var t,n=e.ellipsis,r=e.rowType,a=e.children,l=!0===n?{showTitle:!0}:n;return l&&(l.showTitle||"header"===r)&&("string"==typeof a||"number"==typeof a?t=a.toString():o.isValidElement(a)&&"string"==typeof a.props.children&&(t=a.props.children)),t}({rowType:w,ellipsis:p,children:W}),re=P()(B,g,(s={},(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)(s,"".concat(B,"-fix-left"),X&&D),"".concat(B,"-fix-left-first"),N&&D),"".concat(B,"-fix-left-last"),O&&D),"".concat(B,"-fix-left-all"),O&&L&&D),"".concat(B,"-fix-right"),U&&D),"".concat(B,"-fix-right-first"),I&&D),"".concat(B,"-fix-right-last"),z&&D),"".concat(B,"-ellipsis"),p),"".concat(B,"-with-append"),K),"".concat(B,"-fix-sticky"),(X||U)&&T&&D),(0,$e.A)(s,"".concat(B,"-row-hover"),!q&&Z)),M.className,null==q?void 0:q.className),ae={};h&&(ae.textAlign=h);var le=(0,ke.A)((0,ke.A)((0,ke.A)((0,ke.A)({},null==q?void 0:q.style),V),ae),M.style),ie=W;return"object"!==(0,Ae.A)(ie)||Array.isArray(ie)||o.isValidElement(ie)||(ie=null),p&&(O||I)&&(ie=o.createElement("span",{className:"".concat(B,"-content")},ie)),o.createElement(d,(0,he.A)({},q,M,{className:re,style:le,title:oe,scope:f,onMouseEnter:H?te:void 0,onMouseLeave:H?ne:void 0,colSpan:1!==G?G:null,rowSpan:1!==Y?Y:null}),K,ie)}var Be=o.memo(Te);function je(e,t,n,o,r){var a,l,i=n[e]||{},c=n[t]||{};"left"===i.fixed?a=o.left["rtl"===r?t:e]:"right"===c.fixed&&(l=o.right["rtl"===r?e:t]);var s=!1,d=!1,u=!1,p=!1,f=n[t+1],m=n[e-1],g=f&&!f.fixed||m&&!m.fixed||n.every((function(e){return"left"===e.fixed}));if("rtl"===r){if(void 0!==a)p=!(m&&"left"===m.fixed)&&g;else if(void 0!==l){u=!(f&&"right"===f.fixed)&&g}}else if(void 0!==a){s=!(f&&"left"===f.fixed)&&g}else if(void 0!==l){d=!(m&&"right"===m.fixed)&&g}return{fixLeft:a,fixRight:l,lastFixLeft:s,firstFixRight:d,lastFixRight:u,firstFixLeft:p,isSticky:o.isSticky}}var De=o.createContext({});var Le=n(3986),He=["children"];function _e(e){return e.children}_e.Row=function(e){var t=e.children,n=(0,Le.A)(e,He);return o.createElement("tr",n,t)},_e.Cell=function(e){var t=e.className,n=e.index,r=e.children,a=e.colSpan,l=void 0===a?1:a,i=e.rowSpan,c=e.align,s=ge(Ee,["prefixCls","direction"]),d=s.prefixCls,u=s.direction,p=o.useContext(De),f=p.scrollColumnIndex,m=p.stickyOffsets,g=n+l-1+1===f?l+1:l,h=je(n,n+g-1,p.flattenColumns,m,u);return o.createElement(Be,(0,he.A)({className:t,index:n,component:"td",prefixCls:d,record:null,dataIndex:null,align:c,colSpan:g,rowSpan:i,render:function(){return r}},h))};var Fe=_e;var We=Se((function(e){var t=e.children,n=e.stickyOffsets,r=e.flattenColumns,a=ge(Ee,"prefixCls"),l=r.length-1,i=r[l],c=o.useMemo((function(){return{stickyOffsets:n,flattenColumns:r,scrollColumnIndex:null!=i&&i.scrollbar?l:null}}),[i,r,l,n]);return o.createElement(De.Provider,{value:c},o.createElement("tfoot",{className:"".concat(a,"-summary")},t))})),qe=Fe,Ve=n(8462),Xe=n(2467),Ue=n(9777),Ge=n(2987),Ye=n(2065);function Qe(e,t,n,o,r,a,l){e.push({record:t,indent:n,index:l});var i=a(t),c=null==r?void 0:r.has(i);if(t&&Array.isArray(t[o])&&c)for(var s=0;s<t[o].length;s+=1)Qe(e,t[o][s],n+1,o,r,a,s)}function Je(e,t,n,r){return o.useMemo((function(){if(null!=n&&n.size){for(var o=[],a=0;a<(null==e?void 0:e.length);a+=1){Qe(o,e[a],0,t,n,r,a)}return o}return null==e?void 0:e.map((function(e,t){return{record:e,indent:0,index:t}}))}),[e,t,n,r])}function Ze(e,t,n,o){var r,a=ge(Ee,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),l=a.flattenColumns,i=a.expandableType,c=a.expandedKeys,s=a.childrenColumnName,d=a.onTriggerExpand,u=a.rowExpandable,p=a.onRow,f=a.expandRowByClick,m=a.rowClassName,g="nest"===i,h="row"===i&&(!u||u(e)),v=h||g,b=c&&c.has(t),y=s&&e&&e[s],x=(0,Me._q)(d),C=null==p?void 0:p(e,n),S=null==C?void 0:C.onClick;"string"==typeof m?r=m:"function"==typeof m&&(r=m(e,n,o));var w=Ke(l);return(0,ke.A)((0,ke.A)({},a),{},{columnsKey:w,nestExpandable:g,expanded:b,hasNestChildren:y,record:e,onTriggerExpand:x,rowSupportExpand:h,expandable:v,rowProps:(0,ke.A)((0,ke.A)({},C),{},{className:P()(r,null==C?void 0:C.className),onClick:function(t){f&&v&&d(e,t);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null==S||S.apply(void 0,[t].concat(o))}})})}var et=function(e){var t=e.prefixCls,n=e.children,r=e.component,a=e.cellComponent,l=e.className,i=e.expanded,c=e.colSpan,s=e.isEmpty,d=ge(Ee,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),u=d.scrollbarSize,p=d.fixHeader,f=d.fixColumn,m=d.componentWidth,g=d.horizonScroll,h=n;return(s?g&&m:f)&&(h=o.createElement("div",{style:{width:m-(p&&!s?u:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},h)),o.createElement(r,{className:l,style:{display:i?null:"none"}},o.createElement(Be,{component:a,prefixCls:t,colSpan:c},h))};function tt(e){var t=e.prefixCls,n=e.record,r=e.onExpand,a=e.expanded,l=e.expandable,i="".concat(t,"-row-expand-icon");if(!l)return o.createElement("span",{className:P()(i,"".concat(t,"-row-spaced"))});return o.createElement("span",{className:P()(i,(0,$e.A)((0,$e.A)({},"".concat(t,"-row-expanded"),a),"".concat(t,"-row-collapsed"),!a)),onClick:function(e){r(n,e),e.stopPropagation()}})}function nt(e,t,n,o){return"string"==typeof e?e:"function"==typeof e?e(t,n,o):""}function ot(e,t,n,r,a){var l,i,c=e.record,s=e.prefixCls,d=e.columnsKey,u=e.fixedInfoList,p=e.expandIconColumnIndex,f=e.nestExpandable,m=e.indentSize,g=e.expandIcon,h=e.expanded,v=e.hasNestChildren,b=e.onTriggerExpand,y=d[n],x=u[n];return n===(p||0)&&f&&(l=o.createElement(o.Fragment,null,o.createElement("span",{style:{paddingLeft:"".concat(m*r,"px")},className:"".concat(s,"-row-indent indent-level-").concat(r)}),g({prefixCls:s,expanded:h,expandable:v,record:c,onExpand:b}))),t.onCell&&(i=t.onCell(c,a)),{key:y,fixedInfo:x,appendCellNode:l,additionalCellProps:i||{}}}var rt=Se((function(e){var t=e.className,n=e.style,r=e.record,a=e.index,l=e.renderIndex,i=e.rowKey,c=e.indent,s=void 0===c?0:c,d=e.rowComponent,u=e.cellComponent,p=e.scopeCellComponent,f=Ze(r,i,a,s),m=f.prefixCls,g=f.flattenColumns,h=f.expandedRowClassName,v=f.expandedRowRender,b=f.rowProps,y=f.expanded,x=f.rowSupportExpand,C=o.useRef(!1);C.current||(C.current=y);var S,w=nt(h,r,a,s),E=o.createElement(d,(0,he.A)({},b,{"data-row-key":i,className:P()(t,"".concat(m,"-row"),"".concat(m,"-row-level-").concat(s),null==b?void 0:b.className,(0,$e.A)({},w,s>=1)),style:(0,ke.A)((0,ke.A)({},n),null==b?void 0:b.style)}),g.map((function(e,t){var n=e.render,i=e.dataIndex,c=e.className,d=ot(f,e,t,s,a),g=d.key,h=d.fixedInfo,v=d.appendCellNode,b=d.additionalCellProps;return o.createElement(Be,(0,he.A)({className:c,ellipsis:e.ellipsis,align:e.align,scope:e.rowScope,component:e.rowScope?p:u,prefixCls:m,key:g,record:r,index:a,renderIndex:l,dataIndex:i,render:n,shouldCellUpdate:e.shouldCellUpdate},h,{appendNode:v,additionalProps:b}))})));if(x&&(C.current||y)){var A=v(r,a,s+1,y);S=o.createElement(et,{expanded:y,className:P()("".concat(m,"-expanded-row"),"".concat(m,"-expanded-row-level-").concat(s+1),w),prefixCls:m,component:d,cellComponent:u,colSpan:g.length,isEmpty:!1},A)}return o.createElement(o.Fragment,null,E,S)}));function at(e){var t=e.columnKey,n=e.onColumnResize,r=o.useRef();return o.useEffect((function(){r.current&&n(t,r.current.offsetWidth)}),[]),o.createElement(Ve.A,{data:t},o.createElement("td",{ref:r,style:{padding:0,border:0,height:0}},o.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function lt(e){var t=e.prefixCls,n=e.columnsKey,r=e.onColumnResize;return o.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},o.createElement(Ve.A.Collection,{onBatchResize:function(e){e.forEach((function(e){var t=e.data,n=e.size;r(t,n.offsetWidth)}))}},n.map((function(e){return o.createElement(at,{key:e,columnKey:e,onColumnResize:r})}))))}var it=Se((function(e){var t,n=e.data,r=e.measureColumnWidth,a=ge(Ee,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),l=a.prefixCls,i=a.getComponent,c=a.onColumnResize,s=a.flattenColumns,d=a.getRowKey,u=a.expandedKeys,p=a.childrenColumnName,f=a.emptyNode,m=Je(n,p,u,d),g=o.useRef({renderWithProps:!1}),h=i(["body","wrapper"],"tbody"),v=i(["body","row"],"tr"),b=i(["body","cell"],"td"),y=i(["body","cell"],"th");t=n.length?m.map((function(e,t){var n=e.record,r=e.indent,a=e.index,l=d(n,t);return o.createElement(rt,{key:l,rowKey:l,record:n,index:t,renderIndex:a,rowComponent:v,cellComponent:b,scopeCellComponent:y,indent:r})})):o.createElement(et,{expanded:!0,className:"".concat(l,"-placeholder"),prefixCls:l,component:v,cellComponent:b,colSpan:s.length,isEmpty:!0},f);var x=Ke(s);return o.createElement(ze.Provider,{value:g.current},o.createElement(h,{className:"".concat(l,"-tbody")},r&&o.createElement(lt,{prefixCls:l,columnsKey:x,onColumnResize:c}),t))})),ct=["expandable"],st="RC_TABLE_INTERNAL_COL_DEFINE";var dt=["columnType"];var ut=function(e){for(var t=e.colWidths,n=e.columns,r=e.columCount,a=ge(Ee,["tableLayout"]).tableLayout,l=[],i=!1,c=(r||n.length)-1;c>=0;c-=1){var s=t[c],d=n&&n[c],u=void 0,p=void 0;if(d&&(u=d[st],"auto"===a&&(p=d.minWidth)),s||p||u||i){var f=u||{},m=(f.columnType,(0,Le.A)(f,dt));l.unshift(o.createElement("col",(0,he.A)({key:c,style:{width:s,minWidth:p}},m))),i=!0}}return o.createElement("colgroup",null,l)},pt=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];var ft=o.forwardRef((function(e,t){var n=e.className,r=e.noData,a=e.columns,l=e.flattenColumns,i=e.colWidths,c=e.columCount,s=e.stickyOffsets,d=e.direction,u=e.fixHeader,p=e.stickyTopOffset,f=e.stickyBottomOffset,m=e.stickyClassName,g=e.onScroll,h=e.maxContentScroll,v=e.children,b=(0,Le.A)(e,pt),y=ge(Ee,["prefixCls","scrollbarSize","isSticky","getComponent"]),x=y.prefixCls,C=y.scrollbarSize,S=y.isSticky,w=(0,y.getComponent)(["header","table"],"table"),E=S&&!u?0:C,A=o.useRef(null),k=o.useCallback((function(e){(0,ve.Xf)(t,e),(0,ve.Xf)(A,e)}),[]);o.useEffect((function(){var e;function t(e){var t=e,n=t.currentTarget,o=t.deltaX;o&&(g({currentTarget:n,scrollLeft:n.scrollLeft+o}),e.preventDefault())}return null===(e=A.current)||void 0===e||e.addEventListener("wheel",t,{passive:!1}),function(){var e;null===(e=A.current)||void 0===e||e.removeEventListener("wheel",t)}}),[]);var $=o.useMemo((function(){return l.every((function(e){return e.width}))}),[l]),O=l[l.length-1],I={fixed:O?O.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(x,"-cell-scrollbar")}}},z=(0,o.useMemo)((function(){return E?[].concat((0,N.A)(a),[I]):a}),[E,a]),K=(0,o.useMemo)((function(){return E?[].concat((0,N.A)(l),[I]):l}),[E,l]),R=(0,o.useMemo)((function(){var e=s.right,t=s.left;return(0,ke.A)((0,ke.A)({},s),{},{left:"rtl"===d?[].concat((0,N.A)(t.map((function(e){return e+E}))),[0]):t,right:"rtl"===d?e:[].concat((0,N.A)(e.map((function(e){return e+E}))),[0]),isSticky:S})}),[E,s,S]),M=function(e,t){return(0,o.useMemo)((function(){for(var n=[],o=0;o<t;o+=1){var r=e[o];if(void 0===r)return null;n[o]=r}return n}),[e.join("_"),t])}(i,c);return o.createElement("div",{style:(0,ke.A)({overflow:"hidden"},S?{top:p,bottom:f}:{}),ref:k,className:P()(n,(0,$e.A)({},m,!!m))},o.createElement(w,{style:{tableLayout:"fixed",visibility:r||M?null:"hidden"}},(!r||!h||$)&&o.createElement(ut,{colWidths:M?[].concat((0,N.A)(M),[E]):[],columCount:c+1,columns:K}),v((0,ke.A)((0,ke.A)({},b),{},{stickyOffsets:R,columns:z,flattenColumns:K}))))}));var mt=o.memo(ft);var gt=function(e){var t,n=e.cells,r=e.stickyOffsets,a=e.flattenColumns,l=e.rowComponent,i=e.cellComponent,c=e.onHeaderRow,s=e.index,d=ge(Ee,["prefixCls","direction"]),u=d.prefixCls,p=d.direction;c&&(t=c(n.map((function(e){return e.column})),s));var f=Ke(n.map((function(e){return e.column})));return o.createElement(l,t,n.map((function(e,t){var n,l=e.column,c=je(e.colStart,e.colEnd,a,r,p);return l&&l.onHeaderCell&&(n=e.column.onHeaderCell(l)),o.createElement(Be,(0,he.A)({},e,{scope:l.title?e.colSpan>1?"colgroup":"col":null,ellipsis:l.ellipsis,align:l.align,component:i,prefixCls:u,key:f[t]},c,{additionalProps:n,rowType:"header"}))})))};var ht=Se((function(e){var t=e.stickyOffsets,n=e.columns,r=e.flattenColumns,a=e.onHeaderRow,l=ge(Ee,["prefixCls","getComponent"]),i=l.prefixCls,c=l.getComponent,s=o.useMemo((function(){return function(e){var t=[];!function e(n,o){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;t[r]=t[r]||[];var a=o;return n.filter(Boolean).map((function(n){var o={key:n.key,className:n.className||"",children:n.title,column:n,colStart:a},l=1,i=n.children;return i&&i.length>0&&(l=e(i,a,r+1).reduce((function(e,t){return e+t}),0),o.hasSubColumns=!0),"colSpan"in n&&(l=n.colSpan),"rowSpan"in n&&(o.rowSpan=n.rowSpan),o.colSpan=l,o.colEnd=o.colStart+l-1,t[r].push(o),a+=l,l}))}(e,0);for(var n=t.length,o=function(e){t[e].forEach((function(t){"rowSpan"in t||t.hasSubColumns||(t.rowSpan=n-e)}))},r=0;r<n;r+=1)o(r);return t}(n)}),[n]),d=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),p=c(["header","cell"],"th");return o.createElement(d,{className:"".concat(i,"-thead")},s.map((function(e,n){return o.createElement(gt,{key:n,flattenColumns:r,cells:e,stickyOffsets:t,rowComponent:u,cellComponent:p,onHeaderRow:a,index:n})})))})),vt=n(2546);function bt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"number"==typeof t?t:t.endsWith("%")?e*parseFloat(t)/100:null}var yt=["children"],xt=["fixed"];function Ct(e){return(0,vt.A)(e).filter((function(e){return o.isValidElement(e)})).map((function(e){var t=e.key,n=e.props,o=n.children,r=(0,Le.A)(n,yt),a=(0,ke.A)({key:t},r);return o&&(a.children=Ct(o)),a}))}function St(e){return e.filter((function(e){return e&&"object"===(0,Ae.A)(e)&&!e.hidden})).map((function(e){var t=e.children;return t&&t.length>0?(0,ke.A)((0,ke.A)({},e),{},{children:St(t)}):e}))}function wt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key";return e.filter((function(e){return e&&"object"===(0,Ae.A)(e)})).reduce((function(e,n,o){var r=n.fixed,a=!0===r?"left":r,l="".concat(t,"-").concat(o),i=n.children;return i&&i.length>0?[].concat((0,N.A)(e),(0,N.A)(wt(i,l).map((function(e){return(0,ke.A)({fixed:a},e)})))):[].concat((0,N.A)(e),[(0,ke.A)((0,ke.A)({key:l},n),{},{fixed:a})])}),[])}var Et=function(e,t){var n=e.prefixCls,r=e.columns,a=e.children,l=e.expandable,i=e.expandedKeys,c=e.columnTitle,s=e.getRowKey,d=e.onTriggerExpand,u=e.expandIcon,p=e.rowExpandable,f=e.expandIconColumnIndex,m=e.direction,g=e.expandRowByClick,h=e.columnWidth,v=e.fixed,b=e.scrollWidth,y=e.clientWidth,x=o.useMemo((function(){return St((r||Ct(a)||[]).slice())}),[r,a]),C=o.useMemo((function(){if(l){var e=x.slice();if(!e.includes(ie)){var t=f||0;t>=0&&(t||"left"===v||!v)&&e.splice(t,0,ie),"right"===v&&e.splice(x.length,0,ie)}0;var r=e.indexOf(ie);e=e.filter((function(e,t){return e!==ie||t===r}));var a,m=x[r];a=v||(m?m.fixed:null);var b=(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)({},st,{className:"".concat(n,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",c),"fixed",a),"className","".concat(n,"-row-expand-icon-cell")),"width",h),"render",(function(e,t,r){var a=s(t,r),l=i.has(a),c=!p||p(t),f=u({prefixCls:n,expanded:l,expandable:c,record:t,onExpand:d});return g?o.createElement("span",{onClick:function(e){return e.stopPropagation()}},f):f}));return e.map((function(e){return e===ie?b:e}))}return x.filter((function(e){return e!==ie}))}),[l,x,s,i,u,m]),S=o.useMemo((function(){var e=C;return t&&(e=t(e)),e.length||(e=[{render:function(){return null}}]),e}),[t,C,m]),w=o.useMemo((function(){return"rtl"===m?function(e){return e.map((function(e){var t=e.fixed,n=(0,Le.A)(e,xt),o=t;return"left"===t?o="right":"right"===t&&(o="left"),(0,ke.A)({fixed:o},n)}))}(wt(S)):wt(S)}),[S,m,b]),E=o.useMemo((function(){for(var e=-1,t=w.length-1;t>=0;t-=1){var n=w[t].fixed;if("left"===n||!0===n){e=t;break}}if(e>=0)for(var o=0;o<=e;o+=1){var r=w[o].fixed;if("left"!==r&&!0!==r)return!0}var a=w.findIndex((function(e){return"right"===e.fixed}));if(a>=0)for(var l=a;l<w.length;l+=1){if("right"!==w[l].fixed)return!0}return!1}),[w]),A=function(e,t,n){return o.useMemo((function(){if(t&&t>0){var o=0,r=0;e.forEach((function(e){var n=bt(t,e.width);n?o+=n:r+=1}));var a=Math.max(t,n),l=Math.max(a-o,r),i=r,c=l/r,s=0,d=e.map((function(e){var n=(0,ke.A)({},e),o=bt(t,n.width);if(o)n.width=o;else{var r=Math.floor(c);n.width=1===i?l:r,l-=r,i-=1}return s+=n.width,n}));if(s<a){var u=a/s;l=a,d.forEach((function(e,t){var n=Math.floor(e.width*u);e.width=t===d.length-1?l:n,l-=n}))}return[d,Math.max(s,a)]}return[e,t]}),[e,t,n])}(w,b,y),k=(0,se.A)(A,2),$=k[0],N=k[1];return[S,$,N,E]};function At(e,t,n){var r=function(e){var t,n=e.expandable,o=(0,Le.A)(e,ct);return!1===(t="expandable"in e?(0,ke.A)((0,ke.A)({},o),n):o).showExpandColumn&&(t.expandIconColumnIndex=-1),t}(e),a=r.expandIcon,l=r.expandedRowKeys,i=r.defaultExpandedRowKeys,c=r.defaultExpandAllRows,s=r.expandedRowRender,d=r.onExpand,u=r.onExpandedRowsChange,p=a||tt,f=r.childrenColumnName||"children",m=o.useMemo((function(){return s?"row":!!(e.expandable&&e.internalHooks===ce&&e.expandable.__PARENT_RENDER_ICON__||t.some((function(e){return e&&"object"===(0,Ae.A)(e)&&e[f]})))&&"nest"}),[!!s,t]),g=o.useState((function(){return i||(c?function(e,t,n){var o=[];return function e(r){(r||[]).forEach((function(r,a){o.push(t(r,a)),e(r[n])}))}(e),o}(t,n,f):[])})),h=(0,se.A)(g,2),v=h[0],b=h[1],y=o.useMemo((function(){return new Set(l||v||[])}),[l,v]),x=o.useCallback((function(e){var o,r=n(e,t.indexOf(e)),a=y.has(r);a?(y.delete(r),o=(0,N.A)(y)):o=[].concat((0,N.A)(y),[r]),b(o),d&&d(!a,e),u&&u(o)}),[n,y,t,d,u]);return[r,m,y,p,f,x]}function kt(e){var t=(0,o.useRef)(e),n=(0,o.useState)({}),r=(0,se.A)(n,2)[1],a=(0,o.useRef)(null),l=(0,o.useRef)([]);return(0,o.useEffect)((function(){return function(){a.current=null}}),[]),[t.current,function(e){l.current.push(e);var n=Promise.resolve();a.current=n,n.then((function(){if(a.current===n){var e=l.current,o=t.current;l.current=[],e.forEach((function(e){t.current=e(t.current)})),a.current=null,o!==t.current&&r({})}}))}]}var $t=(0,n(998).A)()?window:null;var Nt=function(e,t,n){var r=(0,o.useMemo)((function(){var o=t.length,r=function(n,o,r){for(var a=[],l=0,i=n;i!==o;i+=r)a.push(l),t[i].fixed&&(l+=e[i]||0);return a},a=r(0,o,1),l=r(o-1,-1,-1).reverse();return"rtl"===n?{left:l,right:a}:{left:a,right:l}}),[e,t,n]);return r};var Ot=function(e){var t=e.className,n=e.children;return o.createElement("div",{className:t},n)};function It(e,t,n,o){var r=fe.unstable_batchedUpdates?function(e){fe.unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,r,o),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,r,o)}}}var zt=n(5371),Kt=n(6588);function Pt(e){var t=(0,Kt.rb)(e).getBoundingClientRect(),n=document.documentElement;return{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var Rt=function(e,t){var n,r,a=e.scrollBodyRef,l=e.onScroll,i=e.offsetScroll,c=e.container,s=e.direction,d=ge(Ee,"prefixCls"),u=(null===(n=a.current)||void 0===n?void 0:n.scrollWidth)||0,p=(null===(r=a.current)||void 0===r?void 0:r.clientWidth)||0,f=u&&p*(p/u),m=o.useRef(),g=kt({scrollLeft:0,isHiddenScrollBar:!0}),h=(0,se.A)(g,2),v=h[0],b=h[1],y=o.useRef({delta:0,x:0}),x=o.useState(!1),C=(0,se.A)(x,2),S=C[0],w=C[1],E=o.useRef(null);o.useEffect((function(){return function(){zt.A.cancel(E.current)}}),[]);var A=function(){w(!1)},k=function(e){var t,n=(e||(null===(t=window)||void 0===t?void 0:t.event)).buttons;if(S&&0!==n){var o=y.current.x+e.pageX-y.current.x-y.current.delta,r="rtl"===s;o=Math.max(r?f-p:0,Math.min(r?0:p-f,o)),(!r||Math.abs(o)+Math.abs(f)<p)&&(l({scrollLeft:o/p*(u+2)}),y.current.x=e.pageX)}else S&&w(!1)},$=function(){zt.A.cancel(E.current),E.current=(0,zt.A)((function(){if(a.current){var e=Pt(a.current).top,t=e+a.current.offsetHeight,n=c===window?document.documentElement.scrollTop+window.innerHeight:Pt(c).top+c.clientHeight;t-(0,Ge.A)()<=n||e>=n-i?b((function(e){return(0,ke.A)((0,ke.A)({},e),{},{isHiddenScrollBar:!0})})):b((function(e){return(0,ke.A)((0,ke.A)({},e),{},{isHiddenScrollBar:!1})}))}}))},N=function(e){b((function(t){return(0,ke.A)((0,ke.A)({},t),{},{scrollLeft:e/u*p||0})}))};return o.useImperativeHandle(t,(function(){return{setScrollLeft:N,checkScrollBarVisible:$}})),o.useEffect((function(){var e=It(document.body,"mouseup",A,!1),t=It(document.body,"mousemove",k,!1);return $(),function(){e.remove(),t.remove()}}),[f,S]),o.useEffect((function(){if(a.current){for(var e=[],t=(0,Kt.rb)(a.current);t;)e.push(t),t=t.parentElement;return e.forEach((function(e){return e.addEventListener("scroll",$,!1)})),window.addEventListener("resize",$,!1),window.addEventListener("scroll",$,!1),c.addEventListener("scroll",$,!1),function(){e.forEach((function(e){return e.removeEventListener("scroll",$)})),window.removeEventListener("resize",$),window.removeEventListener("scroll",$),c.removeEventListener("scroll",$)}}}),[c]),o.useEffect((function(){v.isHiddenScrollBar||b((function(e){var t=a.current;return t?(0,ke.A)((0,ke.A)({},e),{},{scrollLeft:t.scrollLeft/t.scrollWidth*t.clientWidth}):e}))}),[v.isHiddenScrollBar]),u<=p||!f||v.isHiddenScrollBar?null:o.createElement("div",{style:{height:(0,Ge.A)(),width:p,bottom:i},className:"".concat(d,"-sticky-scroll")},o.createElement("div",{onMouseDown:function(e){e.persist(),y.current.delta=e.pageX-v.scrollLeft,y.current.x=0,w(!0),e.preventDefault()},ref:m,className:P()("".concat(d,"-sticky-scroll-bar"),(0,$e.A)({},"".concat(d,"-sticky-scroll-bar-active"),S)),style:{width:"".concat(f,"px"),transform:"translate3d(".concat(v.scrollLeft,"px, 0, 0)")}}))},Mt=o.forwardRef(Rt);var Tt=function(e){return null};var Bt=function(e){return null},jt="rc-table",Dt=[],Lt={};function Ht(){return"No Data"}function _t(e,t){var n=(0,ke.A)({rowKey:"key",prefixCls:jt,emptyText:Ht},e),r=n.prefixCls,a=n.className,l=n.rowClassName,i=n.style,c=n.data,s=n.rowKey,d=n.scroll,u=n.tableLayout,p=n.direction,f=n.title,m=n.footer,g=n.summary,h=n.caption,v=n.id,b=n.showHeader,y=n.components,x=n.emptyText,C=n.onRow,S=n.onHeaderRow,w=n.onScroll,E=n.internalHooks,A=n.transformColumns,k=n.internalRefs,$=n.tailor,N=n.getContainerWidth,O=n.sticky,I=n.rowHoverable,z=void 0===I||I,K=c||Dt,R=!!K.length,M=E===ce;var T=o.useCallback((function(e,t){return(0,Oe.A)(y,e)||t}),[y]),B=o.useMemo((function(){return"function"==typeof s?s:function(e){return e&&e[s]}}),[s]),j=T(["body"]),D=function(){var e=o.useState(-1),t=(0,se.A)(e,2),n=t[0],r=t[1],a=o.useState(-1),l=(0,se.A)(a,2),i=l[0],c=l[1];return[n,i,o.useCallback((function(e,t){r(e),c(t)}),[])]}(),L=(0,se.A)(D,3),H=L[0],_=L[1],F=L[2],W=At(n,K,B),q=(0,se.A)(W,6),V=q[0],X=q[1],U=q[2],G=q[3],Y=q[4],Q=q[5],J=null==d?void 0:d.x,Z=o.useState(0),ee=(0,se.A)(Z,2),te=ee[0],ne=ee[1],oe=Et((0,ke.A)((0,ke.A)((0,ke.A)({},n),V),{},{expandable:!!V.expandedRowRender,columnTitle:V.columnTitle,expandedKeys:U,getRowKey:B,onTriggerExpand:Q,expandIcon:G,expandIconColumnIndex:V.expandIconColumnIndex,direction:p,scrollWidth:M&&$&&"number"==typeof J?J:null,clientWidth:te}),M?A:null),re=(0,se.A)(oe,4),ae=re[0],le=re[1],ie=re[2],ue=re[3],fe=null!=ie?ie:J,me=o.useMemo((function(){return{columns:ae,flattenColumns:le}}),[ae,le]),ge=o.useRef(),ve=o.useRef(),be=o.useRef(),ye=o.useRef();o.useImperativeHandle(t,(function(){return{nativeElement:ge.current,scrollTo:function(e){var t,n;if(be.current instanceof HTMLElement){var o=e.index,r=e.top,a=e.key;if("number"!=typeof(n=r)||Number.isNaN(n)){var l,i=null!=a?a:B(K[o]);null===(l=be.current.querySelector('[data-row-key="'.concat(i,'"]')))||void 0===l||l.scrollIntoView()}else{var c;null===(c=be.current)||void 0===c||c.scrollTo({top:r})}}else null!==(t=be.current)&&void 0!==t&&t.scrollTo&&be.current.scrollTo(e)}}}));var xe,Ce,Se,we=o.useRef(),Ie=o.useState(!1),ze=(0,se.A)(Ie,2),Re=ze[0],Me=ze[1],Te=o.useState(!1),Be=(0,se.A)(Te,2),De=Be[0],Le=Be[1],He=kt(new Map),_e=(0,se.A)(He,2),qe=_e[0],Qe=_e[1],Je=Ke(le).map((function(e){return qe.get(e)})),Ze=o.useMemo((function(){return Je}),[Je.join("_")]),et=Nt(Ze,le,p),tt=d&&Pe(d.y),nt=d&&Pe(fe)||Boolean(V.fixed),ot=nt&&le.some((function(e){return e.fixed})),rt=o.useRef(),at=function(e,t){var n="object"===(0,Ae.A)(e)?e:{},r=n.offsetHeader,a=void 0===r?0:r,l=n.offsetSummary,i=void 0===l?0:l,c=n.offsetScroll,s=void 0===c?0:c,d=n.getContainer,u=(void 0===d?function(){return $t}:d)()||$t,p=!!e;return o.useMemo((function(){return{isSticky:p,stickyClassName:p?"".concat(t,"-sticky-holder"):"",offsetHeader:a,offsetSummary:i,offsetScroll:s,container:u}}),[p,s,a,i,t,u])}(O,r),lt=at.isSticky,ct=at.offsetHeader,st=at.offsetSummary,dt=at.offsetScroll,pt=at.stickyClassName,ft=at.container,gt=o.useMemo((function(){return null==g?void 0:g(K)}),[g,K]),vt=(tt||lt)&&o.isValidElement(gt)&&gt.type===Fe&&gt.props.fixed;tt&&(Ce={overflowY:R?"scroll":"auto",maxHeight:d.y}),nt&&(xe={overflowX:"auto"},tt||(Ce={overflowY:"hidden"}),Se={width:!0===fe?"auto":fe,minWidth:"100%"});var bt=o.useCallback((function(e,t){(0,Xe.A)(ge.current)&&Qe((function(n){if(n.get(e)!==t){var o=new Map(n);return o.set(e,t),o}return n}))}),[]),yt=function(e){var t=(0,o.useRef)(e||null),n=(0,o.useRef)();function r(){window.clearTimeout(n.current)}return(0,o.useEffect)((function(){return r}),[]),[function(e){t.current=e,r(),n.current=window.setTimeout((function(){t.current=null,n.current=void 0}),100)},function(){return t.current}]}(null),xt=(0,se.A)(yt,2),Ct=xt[0],St=xt[1];function wt(e,t){t&&("function"==typeof t?t(e):t.scrollLeft!==e&&(t.scrollLeft=e,t.scrollLeft!==e&&setTimeout((function(){t.scrollLeft=e}),0)))}var It=(0,de.A)((function(e){var t,n=e.currentTarget,o=e.scrollLeft,r="rtl"===p,a="number"==typeof o?o:n.scrollLeft,l=n||Lt;St()&&St()!==l||(Ct(l),wt(a,ve.current),wt(a,be.current),wt(a,we.current),wt(a,null===(t=rt.current)||void 0===t?void 0:t.setScrollLeft));var i=n||ve.current;if(i){var c=M&&$&&"number"==typeof fe?fe:i.scrollWidth,s=i.clientWidth;if(c===s)return Me(!1),void Le(!1);r?(Me(-a<c-s),Le(-a>0)):(Me(a>0),Le(a<c-s))}})),zt=(0,de.A)((function(e){It(e),null==w||w(e)})),Pt=function(){var e;nt&&be.current?It({currentTarget:(0,Kt.rb)(be.current),scrollLeft:null===(e=be.current)||void 0===e?void 0:e.scrollLeft}):(Me(!1),Le(!1))},Rt=o.useRef(!1);o.useEffect((function(){Rt.current&&Pt()}),[nt,c,ae.length]),o.useEffect((function(){Rt.current=!0}),[]);var Tt=o.useState(0),Bt=(0,se.A)(Tt,2),_t=Bt[0],Ft=Bt[1],Wt=o.useState(!0),qt=(0,se.A)(Wt,2),Vt=qt[0],Xt=qt[1];o.useEffect((function(){$&&M||(be.current instanceof Element?Ft((0,Ge.V)(be.current).width):Ft((0,Ge.V)(ye.current).width)),Xt((0,Ue.F)("position","sticky"))}),[]),o.useEffect((function(){M&&k&&(k.body.current=be.current)}));var Ut,Gt=o.useCallback((function(e){return o.createElement(o.Fragment,null,o.createElement(ht,e),"top"===vt&&o.createElement(We,e,gt))}),[vt,gt]),Yt=o.useCallback((function(e){return o.createElement(We,e,gt)}),[gt]),Qt=T(["table"],"table"),Jt=o.useMemo((function(){return u||(ot?"max-content"===fe?"auto":"fixed":tt||lt||le.some((function(e){return e.ellipsis}))?"fixed":"auto")}),[tt,ot,le,u,lt]),Zt={colWidths:Ze,columCount:le.length,stickyOffsets:et,onHeaderRow:S,fixHeader:tt,scroll:d},en=o.useMemo((function(){return R?null:"function"==typeof x?x():x}),[R,x]),tn=o.createElement(it,{data:K,measureColumnWidth:tt||nt||lt}),nn=o.createElement(ut,{colWidths:le.map((function(e){return e.width})),columns:le}),on=null!=h?o.createElement("caption",{className:"".concat(r,"-caption")},h):void 0,rn=(0,Ye.A)(n,{data:!0}),an=(0,Ye.A)(n,{aria:!0});if(tt||lt){var ln;"function"==typeof j?(ln=j(K,{scrollbarSize:_t,ref:be,onScroll:It}),Zt.colWidths=le.map((function(e,t){var n=e.width,o=t===le.length-1?n-_t:n;return"number"!=typeof o||Number.isNaN(o)?0:o}))):ln=o.createElement("div",{style:(0,ke.A)((0,ke.A)({},xe),Ce),onScroll:zt,ref:be,className:P()("".concat(r,"-body"))},o.createElement(Qt,(0,he.A)({style:(0,ke.A)((0,ke.A)({},Se),{},{tableLayout:Jt})},an),on,nn,tn,!vt&&gt&&o.createElement(We,{stickyOffsets:et,flattenColumns:le},gt)));var cn=(0,ke.A)((0,ke.A)((0,ke.A)({noData:!K.length,maxContentScroll:nt&&"max-content"===fe},Zt),me),{},{direction:p,stickyClassName:pt,onScroll:It});Ut=o.createElement(o.Fragment,null,!1!==b&&o.createElement(mt,(0,he.A)({},cn,{stickyTopOffset:ct,className:"".concat(r,"-header"),ref:ve}),Gt),ln,vt&&"top"!==vt&&o.createElement(mt,(0,he.A)({},cn,{stickyBottomOffset:st,className:"".concat(r,"-summary"),ref:we}),Yt),lt&&be.current&&be.current instanceof Element&&o.createElement(Mt,{ref:rt,offsetScroll:dt,scrollBodyRef:be,onScroll:It,container:ft,direction:p}))}else Ut=o.createElement("div",{style:(0,ke.A)((0,ke.A)({},xe),Ce),className:P()("".concat(r,"-content")),onScroll:It,ref:be},o.createElement(Qt,(0,he.A)({style:(0,ke.A)((0,ke.A)({},Se),{},{tableLayout:Jt})},an),on,nn,!1!==b&&o.createElement(ht,(0,he.A)({},Zt,me)),tn,gt&&o.createElement(We,{stickyOffsets:et,flattenColumns:le},gt)));var sn=o.createElement("div",(0,he.A)({className:P()(r,a,(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)({},"".concat(r,"-rtl"),"rtl"===p),"".concat(r,"-ping-left"),Re),"".concat(r,"-ping-right"),De),"".concat(r,"-layout-fixed"),"fixed"===u),"".concat(r,"-fixed-header"),tt),"".concat(r,"-fixed-column"),ot),"".concat(r,"-fixed-column-gapped"),ot&&ue),"".concat(r,"-scroll-horizontal"),nt),"".concat(r,"-has-fix-left"),le[0]&&le[0].fixed),"".concat(r,"-has-fix-right"),le[le.length-1]&&"right"===le[le.length-1].fixed)),style:i,id:v,ref:ge},rn),f&&o.createElement(Ot,{className:"".concat(r,"-title")},f(K)),o.createElement("div",{ref:ye,className:"".concat(r,"-container")},Ut),m&&o.createElement(Ot,{className:"".concat(r,"-footer")},m(K)));nt&&(sn=o.createElement(Ve.A,{onResize:function(e){var t,n=e.width;null===(t=rt.current)||void 0===t||t.checkScrollBarVisible();var o=ge.current?ge.current.offsetWidth:n;M&&N&&ge.current&&(o=N(ge.current,o)||o),o!==te&&(Pt(),ne(o))}},sn));var dn=function(e,t,n){var o=e.map((function(o,r){return je(r,r,e,t,n)}));return(0,Ne.A)((function(){return o}),[o],(function(e,t){return!(0,pe.A)(e,t)}))}(le,et,p),un=o.useMemo((function(){return{scrollX:fe,prefixCls:r,getComponent:T,scrollbarSize:_t,direction:p,fixedInfoList:dn,isSticky:lt,supportSticky:Vt,componentWidth:te,fixHeader:tt,fixColumn:ot,horizonScroll:nt,tableLayout:Jt,rowClassName:l,expandedRowClassName:V.expandedRowClassName,expandIcon:G,expandableType:X,expandRowByClick:V.expandRowByClick,expandedRowRender:V.expandedRowRender,onTriggerExpand:Q,expandIconColumnIndex:V.expandIconColumnIndex,indentSize:V.indentSize,allColumnsFixedLeft:le.every((function(e){return"left"===e.fixed})),emptyNode:en,columns:ae,flattenColumns:le,onColumnResize:bt,hoverStartRow:H,hoverEndRow:_,onHover:F,rowExpandable:V.rowExpandable,onRow:C,getRowKey:B,expandedKeys:U,childrenColumnName:Y,rowHoverable:z}}),[fe,r,T,_t,p,dn,lt,Vt,te,tt,ot,nt,Jt,l,V.expandedRowClassName,G,X,V.expandRowByClick,V.expandedRowRender,Q,V.expandIconColumnIndex,V.indentSize,en,ae,le,bt,H,_,F,V.rowExpandable,C,B,U,Y,z]);return o.createElement(Ee.Provider,{value:un},sn)}var Ft=o.forwardRef(_t);function Wt(e){return Ce(Ft,e)}var qt=Wt();qt.EXPAND_COLUMN=ie,qt.INTERNAL_HOOKS=ce,qt.Column=Tt,qt.ColumnGroup=Bt,qt.Summary=qe;var Vt=qt,Xt=n(551),Ut=me(null),Gt=me(null);var Yt=function(e){var t=e.rowInfo,n=e.column,r=e.colIndex,a=e.indent,l=e.index,i=e.component,c=e.renderIndex,s=e.record,d=e.style,u=e.className,p=e.inverse,f=e.getHeight,m=n.render,g=n.dataIndex,h=n.className,v=n.width,b=ge(Gt,["columnsOffset"]).columnsOffset,y=ot(t,n,r,a,l),x=y.key,C=y.fixedInfo,S=y.appendCellNode,w=y.additionalCellProps,E=w.style,A=w.colSpan,k=void 0===A?1:A,$=w.rowSpan,N=void 0===$?1:$,O=function(e,t,n){return n[e+(t||1)]-(n[e]||0)}(r-1,k,b),I=k>1?v-O:0,z=(0,ke.A)((0,ke.A)((0,ke.A)({},E),d),{},{flex:"0 0 ".concat(O,"px"),width:"".concat(O,"px"),marginRight:I,pointerEvents:"auto"}),K=o.useMemo((function(){return p?N<=1:0===k||0===N||N>1}),[N,k,p]);K?z.visibility="hidden":p&&(z.height=null==f?void 0:f(N));var R=K?function(){return null}:m,M={};return 0!==N&&0!==k||(M.rowSpan=1,M.colSpan=1),o.createElement(Be,(0,he.A)({className:P()(h,u),ellipsis:n.ellipsis,align:n.align,scope:n.rowScope,component:i,prefixCls:t.prefixCls,key:x,record:s,index:l,renderIndex:c,dataIndex:g,render:R,shouldCellUpdate:n.shouldCellUpdate},C,{appendNode:S,additionalProps:(0,ke.A)((0,ke.A)({},w),{},{style:z},M)}))},Qt=["data","index","className","rowKey","style","extra","getHeight"],Jt=o.forwardRef((function(e,t){var n,r=e.data,a=e.index,l=e.className,i=e.rowKey,c=e.style,s=e.extra,d=e.getHeight,u=(0,Le.A)(e,Qt),p=r.record,f=r.indent,m=r.index,g=ge(Ee,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),h=g.scrollX,v=g.flattenColumns,b=g.prefixCls,y=g.fixColumn,x=g.componentWidth,C=ge(Ut,["getComponent"]).getComponent,S=Ze(p,i,a,f),w=C(["body","row"],"div"),E=C(["body","cell"],"div"),A=S.rowSupportExpand,k=S.expanded,$=S.rowProps,N=S.expandedRowRender,O=S.expandedRowClassName;if(A&&k){var I=N(p,a,f+1,k),z=nt(O,p,a,f),K={};y&&(K={style:(0,$e.A)({},"--virtual-width","".concat(x,"px"))});var R="".concat(b,"-expanded-row-cell");n=o.createElement(w,{className:P()("".concat(b,"-expanded-row"),"".concat(b,"-expanded-row-level-").concat(f+1),z)},o.createElement(Be,{component:E,prefixCls:b,className:P()(R,(0,$e.A)({},"".concat(R,"-fixed"),y)),additionalProps:K},I))}var M=(0,ke.A)((0,ke.A)({},c),{},{width:h});s&&(M.position="absolute",M.pointerEvents="none");var T=o.createElement(w,(0,he.A)({},$,u,{"data-row-key":i,ref:A?null:t,className:P()(l,"".concat(b,"-row"),null==$?void 0:$.className,(0,$e.A)({},"".concat(b,"-row-extra"),s)),style:(0,ke.A)((0,ke.A)({},M),null==$?void 0:$.style)}),v.map((function(e,t){return o.createElement(Yt,{key:t,component:E,rowInfo:S,column:e,colIndex:t,indent:f,index:a,renderIndex:m,record:p,inverse:s,getHeight:d})})));return A?o.createElement("div",{ref:t},T,n):T}));var Zt=Se(Jt),en=o.forwardRef((function(e,t){var n=e.data,r=e.onScroll,a=ge(Ee,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),l=a.flattenColumns,i=a.onColumnResize,c=a.getRowKey,s=a.expandedKeys,d=a.prefixCls,u=a.childrenColumnName,p=a.scrollX,f=a.direction,m=ge(Ut),g=m.sticky,h=m.scrollY,v=m.listItemHeight,b=m.getComponent,y=m.onScroll,x=o.useRef(),C=Je(n,u,s,c),S=o.useMemo((function(){var e=0;return l.map((function(t){var n=t.width;return[t.key,n,e+=n]}))}),[l]),w=o.useMemo((function(){return S.map((function(e){return e[2]}))}),[S]);o.useEffect((function(){S.forEach((function(e){var t=(0,se.A)(e,2),n=t[0],o=t[1];i(n,o)}))}),[S]),o.useImperativeHandle(t,(function(){var e,t={scrollTo:function(e){var t;null===(t=x.current)||void 0===t||t.scrollTo(e)},nativeElement:null===(e=x.current)||void 0===e?void 0:e.nativeElement};return Object.defineProperty(t,"scrollLeft",{get:function(){var e;return(null===(e=x.current)||void 0===e?void 0:e.getScrollInfo().x)||0},set:function(e){var t;null===(t=x.current)||void 0===t||t.scrollTo({left:e})}}),t}));var E=function(e,t){var n,o=null===(n=C[t])||void 0===n?void 0:n.record,r=e.onCell;if(r){var a,l=r(o,t);return null!==(a=null==l?void 0:l.rowSpan)&&void 0!==a?a:1}return 1},A=o.useMemo((function(){return{columnsOffset:w}}),[w]),k="".concat(d,"-tbody"),$=b(["body","wrapper"]),N={};return g&&(N.position="sticky",N.bottom=0,"object"===(0,Ae.A)(g)&&g.offsetScroll&&(N.bottom=g.offsetScroll)),o.createElement(Gt.Provider,{value:A},o.createElement(Xt.A,{fullHeight:!1,ref:x,prefixCls:"".concat(k,"-virtual"),styles:{horizontalScrollBar:N},className:k,height:h,itemHeight:v||24,data:C,itemKey:function(e){return c(e.record)},component:$,scrollWidth:p,direction:f,onVirtualScroll:function(e){var t,n=e.x;r({currentTarget:null===(t=x.current)||void 0===t?void 0:t.nativeElement,scrollLeft:n})},onScroll:y,extraRender:function(e){var t=e.start,n=e.end,r=e.getSize,a=e.offsetY;if(n<0)return null;for(var i=l.filter((function(e){return 0===E(e,t)})),s=t,d=function(e){if(!(i=i.filter((function(t){return 0===E(t,e)}))).length)return s=e,1},u=t;u>=0&&!d(u);u-=1);for(var p=l.filter((function(e){return 1!==E(e,n)})),f=n,m=function(e){if(!(p=p.filter((function(t){return 1!==E(t,e)}))).length)return f=Math.max(e-1,n),1},g=n;g<C.length&&!m(g);g+=1);for(var h=[],v=function(e){if(!C[e])return 1;l.some((function(t){return E(t,e)>1}))&&h.push(e)},b=s;b<=f;b+=1)v(b);return h.map((function(e){var t=C[e],n=c(t.record,e),l=r(n);return o.createElement(Zt,{key:e,data:t,rowKey:n,index:e,style:{top:-a+l.top},extra:!0,getHeight:function(t){var o=e+t-1,a=c(C[o].record,o),l=r(n,a);return l.bottom-l.top}})}))}},(function(e,t,n){var r=c(e.record,t);return o.createElement(Zt,{data:e,rowKey:r,index:t,style:n.style})})))}));var tn=Se(en),nn=function(e,t){var n=t.ref,r=t.onScroll;return o.createElement(tn,{ref:n,data:e,onScroll:r})};function on(e,t){var n=e.data,r=e.columns,a=e.scroll,l=e.sticky,i=e.prefixCls,c=void 0===i?jt:i,s=e.className,d=e.listItemHeight,u=e.components,p=e.onScroll,f=a||{},m=f.x,g=f.y;"number"!=typeof m&&(m=1),"number"!=typeof g&&(g=500);var h=(0,Me._q)((function(e,t){return(0,Oe.A)(u,e)||t})),v=(0,Me._q)(p),b=o.useMemo((function(){return{sticky:l,scrollY:g,listItemHeight:d,getComponent:h,onScroll:v}}),[l,g,d,h,v]);return o.createElement(Ut.Provider,{value:b},o.createElement(Vt,(0,he.A)({},e,{className:P()(s,"".concat(c,"-virtual")),scroll:(0,ke.A)((0,ke.A)({},a),{},{x:m}),components:(0,ke.A)((0,ke.A)({},u),{},{body:null!=n&&n.length?nn:void 0}),columns:r,internalHooks:ce,tailor:!0,ref:t})))}var rn=o.forwardRef(on);function an(e){return Ce(rn,e)}an();var ln=e=>null;var cn=e=>null,sn=n(4103),dn=o.createContext(null),un=o.createContext({}),pn=function(e){for(var t=e.prefixCls,n=e.level,r=e.isStart,a=e.isEnd,l="".concat(t,"-indent-unit"),i=[],c=0;c<n;c+=1)i.push(o.createElement("span",{key:c,className:P()(l,(0,$e.A)((0,$e.A)({},"".concat(l,"-start"),r[c]),"".concat(l,"-end"),a[c]))}));return o.createElement("span",{"aria-hidden":"true",className:"".concat(t,"-indent")},i)},fn=o.memo(pn);function mn(e,t){return e[t]}var gn=["children"];function hn(e,t){return"".concat(e,"-").concat(t)}function vn(e,t){return null!=e?e:t}function bn(e){var t=e||{},n=t.title||"title";return{title:n,_title:t._title||[n],key:t.key||"key",children:t.children||"children"}}function yn(e){return function e(t){return(0,vt.A)(t).map((function(t){if(!function(e){return e&&e.type&&e.type.isTreeNode}(t))return(0,Ie.Ay)(!t,"Tree/TreeNode can only accept TreeNode as children."),null;var n=t.key,o=t.props,r=o.children,a=(0,Le.A)(o,gn),l=(0,ke.A)({key:n},a),i=e(r);return i.length&&(l.children=i),l})).filter((function(e){return e}))}(e)}function xn(e,t,n){var o=bn(n),r=o._title,a=o.key,l=o.children,i=new Set(!0===t?[]:t),c=[];return function e(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return n.map((function(s,d){for(var u,p=hn(o?o.pos:"0",d),f=vn(s[a],p),m=0;m<r.length;m+=1){var g=r[m];if(void 0!==s[g]){u=s[g];break}}var h=Object.assign((0,R.A)(s,[].concat((0,N.A)(r),[a,l])),{title:u,key:f,parent:o,pos:p,children:null,data:s,isStart:[].concat((0,N.A)(o?o.isStart:[]),[0===d]),isEnd:[].concat((0,N.A)(o?o.isEnd:[]),[d===n.length-1])});return c.push(h),!0===t||i.has(f)?h.children=e(s[l]||[],h):h.children=[],h}))}(e),c}function Cn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.initWrapper,o=t.processEntity,r=t.onProcessFinished,a=t.externalGetKey,l=t.childrenPropName,i=t.fieldNames,c=a||(arguments.length>2?arguments[2]:void 0),s={},d={},u={posEntities:s,keyEntities:d};return n&&(u=n(u)||u),function(e,t,n){var o,r=("object"===(0,Ae.A)(n)?n:{externalGetKey:n})||{},a=r.childrenPropName,l=r.externalGetKey,i=bn(r.fieldNames),c=i.key,s=i.children,d=a||s;l?"string"==typeof l?o=function(e){return e[l]}:"function"==typeof l&&(o=function(e){return l(e)}):o=function(e,t){return vn(e[c],t)},function n(r,a,l,i){var c=r?r[d]:e,s=r?hn(l.pos,a):"0",u=r?[].concat((0,N.A)(i),[r]):[];if(r){var p=o(r,s),f={node:r,index:a,pos:s,key:p,parentPos:l.node?l.pos:null,level:l.level+1,nodes:u};t(f)}c&&c.forEach((function(e,t){n(e,t,{node:r,pos:s,level:l?l.level+1:-1},u)}))}(null)}(e,(function(e){var t=e.node,n=e.index,r=e.pos,a=e.key,l=e.parentPos,i=e.level,c={node:t,nodes:e.nodes,index:n,key:a,pos:r,level:i},p=vn(a,r);s[r]=c,d[p]=c,c.parent=s[l],c.parent&&(c.parent.children=c.parent.children||[],c.parent.children.push(c)),o&&o(c,u)}),{externalGetKey:c,childrenPropName:l,fieldNames:i}),r&&r(u),u}function Sn(e,t){var n=t.expandedKeys,o=t.selectedKeys,r=t.loadedKeys,a=t.loadingKeys,l=t.checkedKeys,i=t.halfCheckedKeys,c=t.dragOverNodeKey,s=t.dropPosition,d=mn(t.keyEntities,e);return{eventKey:e,expanded:-1!==n.indexOf(e),selected:-1!==o.indexOf(e),loaded:-1!==r.indexOf(e),loading:-1!==a.indexOf(e),checked:-1!==l.indexOf(e),halfChecked:-1!==i.indexOf(e),pos:String(d?d.pos:""),dragOver:c===e&&0===s,dragOverGapTop:c===e&&-1===s,dragOverGapBottom:c===e&&1===s}}function wn(e){var t=e.data,n=e.expanded,o=e.selected,r=e.checked,a=e.loaded,l=e.loading,i=e.halfChecked,c=e.dragOver,s=e.dragOverGapTop,d=e.dragOverGapBottom,u=e.pos,p=e.active,f=e.eventKey,m=(0,ke.A)((0,ke.A)({},t),{},{expanded:n,selected:o,checked:r,loaded:a,loading:l,halfChecked:i,dragOver:c,dragOverGapTop:s,dragOverGapBottom:d,pos:u,active:p,key:f});return"props"in m||Object.defineProperty(m,"props",{get:function(){return(0,Ie.Ay)(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),m}var En=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],An="open",kn="close",$n=function(e){var t,n,r,a=e.eventKey,l=e.className,i=e.style,c=e.dragOver,s=e.dragOverGapTop,d=e.dragOverGapBottom,u=e.isLeaf,p=e.isStart,f=e.isEnd,m=e.expanded,g=e.selected,h=e.checked,v=e.halfChecked,b=e.loading,y=e.domRef,x=e.active,C=e.data,S=e.onMouseMove,w=e.selectable,E=(0,Le.A)(e,En),A=o.useContext(dn),k=o.useContext(un),$=o.useRef(null),N=o.useState(!1),O=(0,se.A)(N,2),I=O[0],z=O[1],K=!!(A.disabled||e.disabled||null!==(t=k.nodeDisabled)&&void 0!==t&&t.call(k,C)),R=o.useMemo((function(){return!(!A.checkable||!1===e.checkable)&&A.checkable}),[A.checkable,e.checkable]),M=function(t){K||R&&!e.disableCheckbox&&A.onNodeCheck(t,wn(e),!h)},T=o.useMemo((function(){return"boolean"==typeof w?w:A.selectable}),[w,A.selectable]),B=function(t){A.onNodeClick(t,wn(e)),T?function(t){K||A.onNodeSelect(t,wn(e))}(t):M(t)},j=function(t){A.onNodeDoubleClick(t,wn(e))},D=function(t){A.onNodeMouseEnter(t,wn(e))},L=function(t){A.onNodeMouseLeave(t,wn(e))},H=function(t){A.onNodeContextMenu(t,wn(e))},_=o.useMemo((function(){return!(!A.draggable||A.draggable.nodeDraggable&&!A.draggable.nodeDraggable(C))}),[A.draggable,C]),F=function(t){b||A.onNodeExpand(t,wn(e))},W=o.useMemo((function(){var e=(mn(A.keyEntities,a)||{}).children;return Boolean((e||[]).length)}),[A.keyEntities,a]),q=o.useMemo((function(){return!1!==u&&(u||!A.loadData&&!W||A.loadData&&e.loaded&&!W)}),[u,A.loadData,W,e.loaded]);o.useEffect((function(){b||"function"!=typeof A.loadData||!m||q||e.loaded||A.onNodeLoad(wn(e))}),[b,A.loadData,A.onNodeLoad,m,q,e]);var V=o.useMemo((function(){var e;return null!==(e=A.draggable)&&void 0!==e&&e.icon?o.createElement("span",{className:"".concat(A.prefixCls,"-draggable-icon")},A.draggable.icon):null}),[A.draggable]),X=function(t){var n=e.switcherIcon||A.switcherIcon;return"function"==typeof n?n((0,ke.A)((0,ke.A)({},e),{},{isLeaf:t})):n},U=o.useMemo((function(){if(!R)return null;var t="boolean"!=typeof R?R:null;return o.createElement("span",{className:P()("".concat(A.prefixCls,"-checkbox"),(0,$e.A)((0,$e.A)((0,$e.A)({},"".concat(A.prefixCls,"-checkbox-checked"),h),"".concat(A.prefixCls,"-checkbox-indeterminate"),!h&&v),"".concat(A.prefixCls,"-checkbox-disabled"),K||e.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":v?"mixed":h,"aria-disabled":K||e.disableCheckbox,"aria-label":"Select ".concat("string"==typeof e.title?e.title:"tree node")},t)}),[R,h,v,K,e.disableCheckbox,e.title]),G=o.useMemo((function(){return q?null:m?An:kn}),[q,m]),Y=o.useMemo((function(){return o.createElement("span",{className:P()("".concat(A.prefixCls,"-iconEle"),"".concat(A.prefixCls,"-icon__").concat(G||"docu"),(0,$e.A)({},"".concat(A.prefixCls,"-icon_loading"),b))})}),[A.prefixCls,G,b]),Q=o.useMemo((function(){var t=Boolean(A.draggable);return!e.disabled&&t&&A.dragOverNodeKey===a?A.dropIndicatorRender({dropPosition:A.dropPosition,dropLevelOffset:A.dropLevelOffset,indent:A.indent,prefixCls:A.prefixCls,direction:A.direction}):null}),[A.dropPosition,A.dropLevelOffset,A.indent,A.prefixCls,A.direction,A.draggable,A.dragOverNodeKey,A.dropIndicatorRender]),J=o.useMemo((function(){var t,n,r=e.title,a=void 0===r?"---":r,l="".concat(A.prefixCls,"-node-content-wrapper");if(A.showIcon){var i=e.icon||A.icon;t=i?o.createElement("span",{className:P()("".concat(A.prefixCls,"-iconEle"),"".concat(A.prefixCls,"-icon__customize"))},"function"==typeof i?i(e):i):Y}else A.loadData&&b&&(t=Y);return n="function"==typeof a?a(C):A.titleRender?A.titleRender(C):a,o.createElement("span",{ref:$,title:"string"==typeof a?a:"",className:P()(l,"".concat(l,"-").concat(G||"normal"),(0,$e.A)({},"".concat(A.prefixCls,"-node-selected"),!K&&(g||I))),onMouseEnter:D,onMouseLeave:L,onContextMenu:H,onClick:B,onDoubleClick:j},t,o.createElement("span",{className:"".concat(A.prefixCls,"-title")},n),Q)}),[A.prefixCls,A.showIcon,e,A.icon,Y,A.titleRender,C,G,D,L,H,B,j]),Z=(0,Ye.A)(E,{aria:!0,data:!0}),ee=(mn(A.keyEntities,a)||{}).level,te=f[f.length-1],ne=!K&&_,oe=A.draggingNodeKey===a,re=void 0!==w?{"aria-selected":!!w}:void 0;return o.createElement("div",(0,he.A)({ref:y,role:"treeitem","aria-expanded":u?void 0:m,className:P()(l,"".concat(A.prefixCls,"-treenode"),(r={},(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)(r,"".concat(A.prefixCls,"-treenode-disabled"),K),"".concat(A.prefixCls,"-treenode-switcher-").concat(m?"open":"close"),!u),"".concat(A.prefixCls,"-treenode-checkbox-checked"),h),"".concat(A.prefixCls,"-treenode-checkbox-indeterminate"),v),"".concat(A.prefixCls,"-treenode-selected"),g),"".concat(A.prefixCls,"-treenode-loading"),b),"".concat(A.prefixCls,"-treenode-active"),x),"".concat(A.prefixCls,"-treenode-leaf-last"),te),"".concat(A.prefixCls,"-treenode-draggable"),_),"dragging",oe),(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)(r,"drop-target",A.dropTargetKey===a),"drop-container",A.dropContainerKey===a),"drag-over",!K&&c),"drag-over-gap-top",!K&&s),"drag-over-gap-bottom",!K&&d),"filter-node",null===(n=A.filterTreeNode)||void 0===n?void 0:n.call(A,wn(e))),"".concat(A.prefixCls,"-treenode-leaf"),q))),style:i,draggable:ne,onDragStart:ne?function(t){t.stopPropagation(),z(!0),A.onNodeDragStart(t,e);try{t.dataTransfer.setData("text/plain","")}catch(n){}}:void 0,onDragEnter:_?function(t){t.preventDefault(),t.stopPropagation(),A.onNodeDragEnter(t,e)}:void 0,onDragOver:_?function(t){t.preventDefault(),t.stopPropagation(),A.onNodeDragOver(t,e)}:void 0,onDragLeave:_?function(t){t.stopPropagation(),A.onNodeDragLeave(t,e)}:void 0,onDrop:_?function(t){t.preventDefault(),t.stopPropagation(),z(!1),A.onNodeDrop(t,e)}:void 0,onDragEnd:_?function(t){t.stopPropagation(),z(!1),A.onNodeDragEnd(t,e)}:void 0,onMouseMove:S},re,Z),o.createElement(fn,{prefixCls:A.prefixCls,level:ee,isStart:p,isEnd:f}),V,function(){if(q){var e=X(!0);return!1!==e?o.createElement("span",{className:P()("".concat(A.prefixCls,"-switcher"),"".concat(A.prefixCls,"-switcher-noop"))},e):null}var t=X(!1);return!1!==t?o.createElement("span",{onClick:F,className:P()("".concat(A.prefixCls,"-switcher"),"".concat(A.prefixCls,"-switcher_").concat(m?An:kn))},t):null}(),U,J)};$n.isTreeNode=1;var Nn=$n;function On(e,t){if(!e)return[];var n=e.slice(),o=n.indexOf(t);return o>=0&&n.splice(o,1),n}function In(e,t){var n=(e||[]).slice();return-1===n.indexOf(t)&&n.push(t),n}function zn(e){return e.split("-")}function Kn(e,t){var n=[];return function e(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((function(t){var o=t.key,r=t.children;n.push(o),e(r)}))}(mn(t,e).children),n}function Pn(e){if(e.parent){var t=zn(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function Rn(e,t,n,o,r,a,l,i,c,s){var d,u=e.clientX,p=e.clientY,f=e.target.getBoundingClientRect(),m=f.top,g=f.height,h=(("rtl"===s?-1:1)*(((null==r?void 0:r.x)||0)-u)-12)/o,v=c.filter((function(e){var t;return null===(t=i[e])||void 0===t||null===(t=t.children)||void 0===t?void 0:t.length})),b=mn(i,n.eventKey);if(p<m+g/2){var y=l.findIndex((function(e){return e.key===b.key})),x=l[y<=0?0:y-1].key;b=mn(i,x)}var C=b.key,S=b,w=b.key,E=0,A=0;if(!v.includes(C))for(var k=0;k<h&&Pn(b);k+=1)b=b.parent,A+=1;var $,N=t.data,O=b.node,I=!0;return $=zn(b.pos),0===Number($[$.length-1])&&0===b.level&&p<m+g/2&&a({dragNode:N,dropNode:O,dropPosition:-1})&&b.key===n.eventKey?E=-1:(S.children||[]).length&&v.includes(w)?a({dragNode:N,dropNode:O,dropPosition:0})?E=0:I=!1:0===A?h>-1.5?a({dragNode:N,dropNode:O,dropPosition:1})?E=1:I=!1:a({dragNode:N,dropNode:O,dropPosition:0})?E=0:a({dragNode:N,dropNode:O,dropPosition:1})?E=1:I=!1:a({dragNode:N,dropNode:O,dropPosition:1})?E=1:I=!1,{dropPosition:E,dropLevelOffset:A,dropTargetKey:b.key,dropTargetPos:b.pos,dragOverNodeKey:w,dropContainerKey:0===E?null:(null===(d=b.parent)||void 0===d?void 0:d.key)||null,dropAllowed:I}}function Mn(e,t){if(e)return t.multiple?e.slice():e.length?[e[0]]:e}function Tn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else{if("object"!==(0,Ae.A)(e))return(0,Ie.Ay)(!1,"`checkedKeys` is not an array or an object"),null;t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0}}return t}function Bn(e,t){var n=new Set;function o(e){if(!n.has(e)){var r=mn(t,e);if(r){n.add(e);var a=r.parent;r.node.disabled||a&&o(a.key)}}}return(e||[]).forEach((function(e){o(e)})),(0,N.A)(n)}function jn(e,t){var n=new Set;return e.forEach((function(e){t.has(e)||n.add(e)})),n}function Dn(e){var t=e||{},n=t.disabled,o=t.disableCheckbox,r=t.checkable;return!(!n&&!o)||!1===r}function Ln(e,t,n,o){var r,a=[];r=o||Dn;var l,i=new Set(e.filter((function(e){var t=!!mn(n,e);return t||a.push(e),t}))),c=new Map,s=0;return Object.keys(n).forEach((function(e){var t=n[e],o=t.level,r=c.get(o);r||(r=new Set,c.set(o,r)),r.add(t),s=Math.max(s,o)})),(0,Ie.Ay)(!a.length,"Tree missing follow keys: ".concat(a.slice(0,100).map((function(e){return"'".concat(e,"'")})).join(", "))),l=!0===t?function(e,t,n,o){for(var r=new Set(e),a=new Set,l=0;l<=n;l+=1)(t.get(l)||new Set).forEach((function(e){var t=e.key,n=e.node,a=e.children,l=void 0===a?[]:a;r.has(t)&&!o(n)&&l.filter((function(e){return!o(e.node)})).forEach((function(e){r.add(e.key)}))}));for(var i=new Set,c=n;c>=0;c-=1)(t.get(c)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!o(n)&&e.parent&&!i.has(e.parent.key))if(o(e.parent.node))i.add(t.key);else{var l=!0,c=!1;(t.children||[]).filter((function(e){return!o(e.node)})).forEach((function(e){var t=e.key,n=r.has(t);l&&!n&&(l=!1),c||!n&&!a.has(t)||(c=!0)})),l&&r.add(t.key),c&&a.add(t.key),i.add(t.key)}}));return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(jn(a,r))}}(i,c,s,r):function(e,t,n,o,r){for(var a=new Set(e),l=new Set(t),i=0;i<=o;i+=1)(n.get(i)||new Set).forEach((function(e){var t=e.key,n=e.node,o=e.children,i=void 0===o?[]:o;a.has(t)||l.has(t)||r(n)||i.filter((function(e){return!r(e.node)})).forEach((function(e){a.delete(e.key)}))}));l=new Set;for(var c=new Set,s=o;s>=0;s-=1)(n.get(s)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!r(n)&&e.parent&&!c.has(e.parent.key))if(r(e.parent.node))c.add(t.key);else{var o=!0,i=!1;(t.children||[]).filter((function(e){return!r(e.node)})).forEach((function(e){var t=e.key,n=a.has(t);o&&!n&&(o=!1),i||!n&&!l.has(t)||(i=!0)})),o||a.delete(t.key),i&&l.add(t.key),c.add(t.key)}}));return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(jn(l,a))}}(i,t.halfCheckedKeys,c,s,r),l}var Hn=n(2533);var _n=n(8877),Fn=n(1196),Wn=n(8603),qn=n(934);const Vn=o.createContext(null),Xn=Vn.Provider;var Un=Vn;const Gn=o.createContext(null),Yn=Gn.Provider;var Qn=n(8873),Jn=n(57),Zn=n(4424),eo=n(6827),to=n(8119),no=n(4241);const oo=e=>{const{componentCls:t,antCls:n}=e,o=`${t}-group`;return{[o]:Object.assign(Object.assign({},(0,H.dF)(e)),{display:"inline-block",fontSize:0,[`&${o}-rtl`]:{direction:"rtl"},[`&${o}-block`]:{display:"flex"},[`${n}-badge ${n}-badge-count`]:{zIndex:1},[`> ${n}-badge:not(:first-child) > ${n}-button-wrapper`]:{borderInlineStart:"none"}})}},ro=e=>{const{componentCls:t,wrapperMarginInlineEnd:n,colorPrimary:o,radioSize:r,motionDurationSlow:a,motionDurationMid:l,motionEaseInOutCirc:i,colorBgContainer:c,colorBorder:s,lineWidth:d,colorBgContainerDisabled:u,colorTextDisabled:p,paddingXS:f,dotColorDisabled:m,lineType:g,radioColor:h,radioBgColor:v,calc:b}=e,y=`${t}-inner`,x=b(r).sub(b(4).mul(2)),C=b(1).mul(r).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},(0,H.dF)(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:n,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${(0,L.zA)(d)} ${g} ${o}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},(0,H.dF)(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,\n        &:hover ${y}`]:{borderColor:o},[`${t}-input:focus-visible + ${y}`]:Object.assign({},(0,H.jk)(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:C,height:C,marginBlockStart:b(1).mul(r).div(-2).equal({unit:!0}),marginInlineStart:b(1).mul(r).div(-2).equal({unit:!0}),backgroundColor:h,borderBlockStart:0,borderInlineStart:0,borderRadius:C,transform:"scale(0)",opacity:0,transition:`all ${a} ${i}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:C,height:C,backgroundColor:c,borderColor:s,borderStyle:"solid",borderWidth:d,borderRadius:"50%",transition:`all ${l}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[y]:{borderColor:o,backgroundColor:v,"&::after":{transform:`scale(${e.calc(e.dotSize).div(r).equal()})`,opacity:1,transition:`all ${a} ${i}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[y]:{backgroundColor:u,borderColor:s,cursor:"not-allowed","&::after":{backgroundColor:m}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:p,cursor:"not-allowed"},[`&${t}-checked`]:{[y]:{"&::after":{transform:`scale(${b(x).div(r).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:f,paddingInlineEnd:f}})}},ao=e=>{const{buttonColor:t,controlHeight:n,componentCls:o,lineWidth:r,lineType:a,colorBorder:l,motionDurationSlow:i,motionDurationMid:c,buttonPaddingInline:s,fontSize:d,buttonBg:u,fontSizeLG:p,controlHeightLG:f,controlHeightSM:m,paddingXS:g,borderRadius:h,borderRadiusSM:v,borderRadiusLG:b,buttonCheckedBg:y,buttonSolidCheckedColor:x,colorTextDisabled:C,colorBgContainerDisabled:S,buttonCheckedBgDisabled:w,buttonCheckedColorDisabled:E,colorPrimary:A,colorPrimaryHover:k,colorPrimaryActive:$,buttonSolidCheckedBg:N,buttonSolidCheckedHoverBg:O,buttonSolidCheckedActiveBg:I,calc:z}=e;return{[`${o}-button-wrapper`]:{position:"relative",display:"inline-block",height:n,margin:0,paddingInline:s,paddingBlock:0,color:t,fontSize:d,lineHeight:(0,L.zA)(z(n).sub(z(r).mul(2)).equal()),background:u,border:`${(0,L.zA)(r)} ${a} ${l}`,borderBlockStartWidth:z(r).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:r,cursor:"pointer",transition:[`color ${c}`,`background ${c}`,`box-shadow ${c}`].join(","),a:{color:t},[`> ${o}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:z(r).mul(-1).equal(),insetInlineStart:z(r).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:r,paddingInline:0,backgroundColor:l,transition:`background-color ${i}`,content:'""'}},"&:first-child":{borderInlineStart:`${(0,L.zA)(r)} ${a} ${l}`,borderStartStartRadius:h,borderEndStartRadius:h},"&:last-child":{borderStartEndRadius:h,borderEndEndRadius:h},"&:first-child:last-child":{borderRadius:h},[`${o}-group-large &`]:{height:f,fontSize:p,lineHeight:(0,L.zA)(z(f).sub(z(r).mul(2)).equal()),"&:first-child":{borderStartStartRadius:b,borderEndStartRadius:b},"&:last-child":{borderStartEndRadius:b,borderEndEndRadius:b}},[`${o}-group-small &`]:{height:m,paddingInline:z(g).sub(r).equal(),paddingBlock:0,lineHeight:(0,L.zA)(z(m).sub(z(r).mul(2)).equal()),"&:first-child":{borderStartStartRadius:v,borderEndStartRadius:v},"&:last-child":{borderStartEndRadius:v,borderEndEndRadius:v}},"&:hover":{position:"relative",color:A},"&:has(:focus-visible)":Object.assign({},(0,H.jk)(e)),[`${o}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${o}-button-wrapper-disabled)`]:{zIndex:1,color:A,background:y,borderColor:A,"&::before":{backgroundColor:A},"&:first-child":{borderColor:A},"&:hover":{color:k,borderColor:k,"&::before":{backgroundColor:k}},"&:active":{color:$,borderColor:$,"&::before":{backgroundColor:$}}},[`${o}-group-solid &-checked:not(${o}-button-wrapper-disabled)`]:{color:x,background:N,borderColor:N,"&:hover":{color:x,background:O,borderColor:O},"&:active":{color:x,background:I,borderColor:I}},"&-disabled":{color:C,backgroundColor:S,borderColor:l,cursor:"not-allowed","&:first-child, &:hover":{color:C,backgroundColor:S,borderColor:l}},[`&-disabled${o}-button-wrapper-checked`]:{color:E,backgroundColor:w,borderColor:l,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}};var lo=(0,_.OF)("Radio",(e=>{const{controlOutline:t,controlOutlineWidth:n}=e,o=`0 0 0 ${(0,L.zA)(n)} ${t}`,r=o,a=(0,F.oX)(e,{radioFocusShadow:o,radioButtonFocusShadow:r});return[oo(a),ro(a),ao(a)]}),(e=>{const{wireframe:t,padding:n,marginXS:o,lineWidth:r,fontSizeLG:a,colorText:l,colorBgContainer:i,colorTextDisabled:c,controlItemBgActiveDisabled:s,colorTextLightSolid:d,colorPrimary:u,colorPrimaryHover:p,colorPrimaryActive:f,colorWhite:m}=e;return{radioSize:a,dotSize:t?a-8:a-2*(4+r),dotColorDisabled:c,buttonSolidCheckedColor:d,buttonSolidCheckedBg:u,buttonSolidCheckedHoverBg:p,buttonSolidCheckedActiveBg:f,buttonBg:i,buttonCheckedBg:i,buttonColor:l,buttonCheckedBgDisabled:s,buttonCheckedColorDisabled:c,buttonPaddingInline:n-r,wrapperMarginInlineEnd:o,radioColor:t?u:m,radioBgColor:t?i:u}}),{unitless:{radioSize:!0,dotSize:!0}}),io=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const co=(e,t)=>{var n,r;const a=o.useContext(Un),l=o.useContext(Gn),{getPrefixCls:i,direction:c,radio:s}=o.useContext(M.QO),d=o.useRef(null),u=(0,ve.K4)(t,d),{isFormItemInput:p}=o.useContext(no.$W);const f=t=>{var n,o;null===(n=e.onChange)||void 0===n||n.call(e,t),null===(o=null==a?void 0:a.onChange)||void 0===o||o.call(a,t)},{prefixCls:m,className:g,rootClassName:h,children:v,style:b,title:y}=e,x=io(e,["prefixCls","className","rootClassName","children","style","title"]),C=i("radio",m),S="button"===((null==a?void 0:a.optionType)||l),w=S?`${C}-button`:C,E=(0,qn.A)(C),[A,k,$]=lo(C,E),N=Object.assign({},x),O=o.useContext(to.A);a&&(N.name=a.name,N.onChange=f,N.checked=e.value===a.value,N.disabled=null!==(n=N.disabled)&&void 0!==n?n:a.disabled),N.disabled=null!==(r=N.disabled)&&void 0!==r?r:O;const I=P()(`${w}-wrapper`,{[`${w}-wrapper-checked`]:N.checked,[`${w}-wrapper-disabled`]:N.disabled,[`${w}-wrapper-rtl`]:"rtl"===c,[`${w}-wrapper-in-form-item`]:p,[`${w}-wrapper-block`]:!!(null==a?void 0:a.block)},null==s?void 0:s.className,g,h,k,$,E),[z,K]=(0,eo.A)(N.onClick);return A(o.createElement(Jn.A,{component:"Radio",disabled:N.disabled},o.createElement("label",{className:I,style:Object.assign(Object.assign({},null==s?void 0:s.style),b),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:y,onClick:z},o.createElement(Qn.A,Object.assign({},N,{className:P()(N.className,{[Zn.D]:!S}),type:"radio",prefixCls:w,ref:u,onClick:K})),void 0!==v?o.createElement("span",{className:`${w}-label`},v):null)))};var so=o.forwardRef(co),uo=n(6855);const po=o.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:r}=o.useContext(M.QO),a=(0,uo.A)(),{prefixCls:l,className:i,rootClassName:c,options:s,buttonStyle:d="outline",disabled:u,children:p,size:f,style:m,id:g,optionType:h,name:v=a,defaultValue:b,value:y,block:x=!1,onChange:C,onMouseEnter:S,onMouseLeave:w,onFocus:E,onBlur:A}=e,[k,$]=(0,Hn.A)(b,{value:y}),N=o.useCallback((t=>{const n=k,o=t.target.value;"value"in e||$(o),o!==n&&(null==C||C(t))}),[k,$,C]),O=n("radio",l),I=`${O}-group`,z=(0,qn.A)(O),[K,R,B]=lo(O,z);let j=p;s&&s.length>0&&(j=s.map((e=>"string"==typeof e||"number"==typeof e?o.createElement(so,{key:e.toString(),prefixCls:O,disabled:u,value:e,checked:k===e},e):o.createElement(so,{key:`radio-group-value-options-${e.value}`,prefixCls:O,disabled:e.disabled||u,value:e.value,checked:k===e.value,title:e.title,style:e.style,id:e.id,required:e.required},e.label))));const D=(0,T.A)(f),L=P()(I,`${I}-${d}`,{[`${I}-${D}`]:D,[`${I}-rtl`]:"rtl"===r,[`${I}-block`]:x},i,c,R,B,z),H=o.useMemo((()=>({onChange:N,value:k,disabled:u,name:v,optionType:h,block:x})),[N,k,u,v,h,x]);return K(o.createElement("div",Object.assign({},(0,Ye.A)(e,{aria:!0,data:!0}),{className:L,style:m,onMouseEnter:S,onMouseLeave:w,onFocus:E,onBlur:A,id:g,ref:t}),o.createElement(Xn,{value:H},j)))}));var fo=o.memo(po),mo=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const go=(e,t)=>{const{getPrefixCls:n}=o.useContext(M.QO),{prefixCls:r}=e,a=mo(e,["prefixCls"]),l=n("radio",r);return o.createElement(Yn,{value:"button"},o.createElement(so,Object.assign({prefixCls:l},a,{type:"radio",ref:t})))};var ho=o.forwardRef(go);const vo=so;vo.Button=ho,vo.Group=fo,vo.__ANT_RADIO=!0;var bo=vo;const yo={},xo="SELECT_ALL",Co="SELECT_INVERT",So="SELECT_NONE",wo=[],Eo=(e,t)=>{let n=[];return(t||[]).forEach((t=>{n.push(t),t&&"object"==typeof t&&e in t&&(n=[].concat((0,N.A)(n),(0,N.A)(Eo(e,t[e]))))})),n};var Ao=(e,t)=>{const{preserveSelectedRowKeys:n,selectedRowKeys:r,defaultSelectedRowKeys:a,getCheckboxProps:l,onChange:i,onSelect:c,onSelectAll:s,onSelectInvert:d,onSelectNone:u,onSelectMultiple:p,columnWidth:f,type:m,selections:g,fixed:h,renderCell:v,hideSelectAll:b,checkStrictly:y=!0}=t||{},{prefixCls:x,data:C,pageData:S,getRecordByKey:w,getRowKey:E,expandType:A,childrenColumnName:k,locale:$,getPopupContainer:O}=e,I=(0,_n.rJ)("Table"),[z,K]=function(e){const[t,n]=(0,o.useState)(null);return[(0,o.useCallback)(((o,r,a)=>{const l=null!=t?t:o,i=Math.min(l||0,o),c=Math.max(l||0,o),s=r.slice(i,c+1).map((t=>e(t))),d=s.some((e=>!a.has(e))),u=[];return s.forEach((e=>{d?(a.has(e)||u.push(e),a.add(e)):(a.delete(e),u.push(e))})),n(d?c:null),u}),[t]),e=>{n(e)}]}((e=>e)),[R,M]=(0,Hn.A)(r||a||wo,{value:r}),T=o.useRef(new Map),B=(0,o.useCallback)((e=>{if(n){const t=new Map;e.forEach((e=>{let n=w(e);!n&&T.current.has(e)&&(n=T.current.get(e)),t.set(e,n)})),T.current=t}}),[w,n]);o.useEffect((()=>{B(R)}),[R]);const j=(0,o.useMemo)((()=>Eo(k,S)),[k,S]),{keyEntities:D}=(0,o.useMemo)((()=>{if(y)return{keyEntities:null};let e=C;if(n){const t=new Set(j.map(((e,t)=>E(e,t)))),n=Array.from(T.current).reduce(((e,n)=>{let[o,r]=n;return t.has(o)?e:e.concat(r)}),[]);e=[].concat((0,N.A)(e),(0,N.A)(n))}return Cn(e,{externalGetKey:E,childrenPropName:k})}),[C,E,y,k,n,j]),L=(0,o.useMemo)((()=>{const e=new Map;return j.forEach(((t,n)=>{const o=E(t,n),r=(l?l(t):null)||{};e.set(o,r)})),e}),[j,E,l]),H=(0,o.useCallback)((e=>{const t=E(e);let n;return n=L.has(t)?L.get(E(e)):l?l(e):void 0,!!(null==n?void 0:n.disabled)}),[L,E]),[_,F]=(0,o.useMemo)((()=>{if(y)return[R||[],[]];const{checkedKeys:e,halfCheckedKeys:t}=Ln(R,!0,D,H);return[e||[],t]}),[R,y,D,H]),W=(0,o.useMemo)((()=>{const e="radio"===m?_.slice(0,1):_;return new Set(e)}),[_,m]),q=(0,o.useMemo)((()=>"radio"===m?new Set:new Set(F)),[F,m]);o.useEffect((()=>{t||M(wo)}),[!!t]);const V=(0,o.useCallback)(((e,t)=>{let o,r;B(e),n?(o=e,r=e.map((e=>T.current.get(e)))):(o=[],r=[],e.forEach((e=>{const t=w(e);void 0!==t&&(o.push(e),r.push(t))}))),M(o),null==i||i(o,r,{type:t})}),[M,w,i,n]),X=(0,o.useCallback)(((e,t,n,o)=>{if(c){const r=n.map((e=>w(e)));c(w(e),t,r,o)}V(n,"single")}),[c,w,V]),U=(0,o.useMemo)((()=>{if(!g||b)return null;return(!0===g?[xo,Co,So]:g).map((e=>e===xo?{key:"all",text:$.selectionAll,onSelect(){V(C.map(((e,t)=>E(e,t))).filter((e=>{const t=L.get(e);return!(null==t?void 0:t.disabled)||W.has(e)})),"all")}}:e===Co?{key:"invert",text:$.selectInvert,onSelect(){const e=new Set(W);S.forEach(((t,n)=>{const o=E(t,n),r=L.get(o);(null==r?void 0:r.disabled)||(e.has(o)?e.delete(o):e.add(o))}));const t=Array.from(e);d&&(I.deprecated(!1,"onSelectInvert","onChange"),d(t)),V(t,"invert")}}:e===So?{key:"none",text:$.selectNone,onSelect(){null==u||u(),V(Array.from(W).filter((e=>{const t=L.get(e);return null==t?void 0:t.disabled})),"none")}}:e)).map((e=>Object.assign(Object.assign({},e),{onSelect:function(){for(var t,n,o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];null===(n=e.onSelect)||void 0===n||(t=n).call.apply(t,[e].concat(r)),K(null)}})))}),[g,W,S,E,d,V]),G=(0,o.useCallback)((e=>{var n;if(!t)return e.filter((e=>e!==yo));let r=(0,N.A)(e);const a=new Set(W),l=j.map(E).filter((e=>!L.get(e).disabled)),i=l.every((e=>a.has(e))),c=l.some((e=>a.has(e))),d=()=>{const e=[];i?l.forEach((t=>{a.delete(t),e.push(t)})):l.forEach((t=>{a.has(t)||(a.add(t),e.push(t))}));const t=Array.from(a);null==s||s(!i,t.map((e=>w(e))),e.map((e=>w(e)))),V(t,"all"),K(null)};let u,C,S;if("radio"!==m){let e;if(U){const t={getPopupContainer:O,items:U.map(((e,t)=>{const{key:n,text:o,onSelect:r}=e;return{key:null!=n?n:t,onClick:()=>{null==r||r(l)},label:o}}))};e=o.createElement("div",{className:`${x}-selection-extra`},o.createElement(Wn.A,{menu:t,getPopupContainer:O},o.createElement("span",null,o.createElement(sn.A,null))))}const t=j.map(((e,t)=>{const n=E(e,t),o=L.get(n)||{};return Object.assign({checked:a.has(n)},o)})).filter((e=>{let{disabled:t}=e;return t})),n=!!t.length&&t.length===j.length,r=n&&t.every((e=>{let{checked:t}=e;return t})),s=n&&t.some((e=>{let{checked:t}=e;return t}));C=o.createElement(Fn.A,{checked:n?r:!!j.length&&i,indeterminate:n?!r&&s:!i&&c,onChange:d,disabled:0===j.length||n,"aria-label":e?"Custom selection":"Select all",skipGroup:!0}),u=!b&&o.createElement("div",{className:`${x}-selection`},C,e)}S="radio"===m?(e,t,n)=>{const r=E(t,n),l=a.has(r),i=L.get(r);return{node:o.createElement(bo,Object.assign({},i,{checked:l,onClick:e=>{var t;e.stopPropagation(),null===(t=null==i?void 0:i.onClick)||void 0===t||t.call(i,e)},onChange:e=>{var t;a.has(r)||X(r,!0,[r],e.nativeEvent),null===(t=null==i?void 0:i.onChange)||void 0===t||t.call(i,e)}})),checked:l}}:(e,t,n)=>{var r;const i=E(t,n),c=a.has(i),s=q.has(i),d=L.get(i);let u;return u="nest"===A?s:null!==(r=null==d?void 0:d.indeterminate)&&void 0!==r?r:s,{node:o.createElement(Fn.A,Object.assign({},d,{indeterminate:u,checked:c,skipGroup:!0,onClick:e=>{var t;e.stopPropagation(),null===(t=null==d?void 0:d.onClick)||void 0===t||t.call(d,e)},onChange:e=>{var t;const{nativeEvent:n}=e,{shiftKey:o}=n,r=l.findIndex((e=>e===i)),s=_.some((e=>l.includes(e)));if(o&&y&&s){const e=z(r,l,a),t=Array.from(a);null==p||p(!c,t.map((e=>w(e))),e.map((e=>w(e)))),V(t,"multiple")}else{const e=_;if(y){const t=c?On(e,i):In(e,i);X(i,!c,t,n)}else{const t=Ln([].concat((0,N.A)(e),[i]),!0,D,H),{checkedKeys:o,halfCheckedKeys:r}=t;let a=o;if(c){const e=new Set(o);e.delete(i),a=Ln(Array.from(e),{checked:!1,halfCheckedKeys:r},D,H).checkedKeys}X(i,!c,a,n)}}K(c?null:r),null===(t=null==d?void 0:d.onChange)||void 0===t||t.call(d,e)}})),checked:c}};if(!r.includes(yo))if(0===r.findIndex((e=>{var t;return"EXPAND_COLUMN"===(null===(t=e[st])||void 0===t?void 0:t.columnType)}))){const[e,...t]=r;r=[e,yo].concat((0,N.A)(t))}else r=[yo].concat((0,N.A)(r));const k=r.indexOf(yo);r=r.filter(((e,t)=>e!==yo||t===k));const $=r[k-1],I=r[k+1];let R=h;void 0===R&&(void 0!==(null==I?void 0:I.fixed)?R=I.fixed:void 0!==(null==$?void 0:$.fixed)&&(R=$.fixed)),R&&$&&"EXPAND_COLUMN"===(null===(n=$[st])||void 0===n?void 0:n.columnType)&&void 0===$.fixed&&($.fixed=R);const M=P()(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:g&&"checkbox"===m}),T={fixed:R,width:f,className:`${x}-selection-column`,title:(null==t?void 0:t.columnTitle)?"function"==typeof t.columnTitle?t.columnTitle(C):t.columnTitle:u,render:(e,t,n)=>{const{node:o,checked:r}=S(e,t,n);return v?v(r,t,n,o):o},onCell:t.onCell,[st]:{className:M}};return r.map((e=>e===yo?T:e))}),[E,j,t,_,W,q,f,U,A,L,p,X,H]);return[G,W]};function ko(e,t){return(0,o.useImperativeHandle)(e,(()=>{const e=t(),{nativeElement:n}=e;return"undefined"!=typeof Proxy?new Proxy(n,{get(t,n){return e[n]?e[n]:Reflect.get(t,n)}}):(r=e,(o=n)._antProxy=o._antProxy||{},Object.keys(r).forEach((e=>{if(!(e in o._antProxy)){const t=o[e];o._antProxy[e]=t,o[e]=r[e]}})),o);var o,r}))}function $o(e){return null!=e&&e===e.window}var No=e=>{var t,n;if("undefined"==typeof window)return 0;let o=0;return $o(e)?o=e.pageYOffset:e instanceof Document?o=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(o=e.scrollTop),e&&!$o(e)&&"number"!=typeof o&&(o=null===(n=(null!==(t=e.ownerDocument)&&void 0!==t?t:e).documentElement)||void 0===n?void 0:n.scrollTop),o};var Oo=n(5128),Io=n(8551),zo=n(8055),Ko={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},Po=n(7064),Ro=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:Ko}))};var Mo=o.forwardRef(Ro),To={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},Bo=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:To}))};var jo=o.forwardRef(Bo),Do=n(329),Lo=n(8e3),Ho=n(6928),_o={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},Fo=[10,20,50,100];var Wo=function(e){var t=e.pageSizeOptions,n=void 0===t?Fo:t,r=e.locale,a=e.changeSize,l=e.pageSize,i=e.goButton,c=e.quickGo,s=e.rootPrefixCls,d=e.disabled,u=e.buildOptionText,p=e.showSizeChanger,f=e.sizeChangerRender,m=o.useState(""),g=(0,se.A)(m,2),h=g[0],v=g[1],b=function(){return!h||Number.isNaN(h)?void 0:Number(h)},y="function"==typeof u?u:function(e){return"".concat(e," ").concat(r.items_per_page)},x=function(e){""!==h&&(e.keyCode!==Ho.A.ENTER&&"click"!==e.type||(v(""),null==c||c(b())))},C="".concat(s,"-options");if(!p&&!c)return null;var S=null,w=null,E=null;return p&&f&&(S=f({disabled:d,size:l,onSizeChange:function(e){null==a||a(Number(e))},"aria-label":r.page_size,className:"".concat(C,"-size-changer"),options:(n.some((function(e){return e.toString()===l.toString()}))?n:n.concat([l]).sort((function(e,t){return(Number.isNaN(Number(e))?0:Number(e))-(Number.isNaN(Number(t))?0:Number(t))}))).map((function(e){return{label:y(e),value:e}}))})),c&&(i&&(E="boolean"==typeof i?o.createElement("button",{type:"button",onClick:x,onKeyUp:x,disabled:d,className:"".concat(C,"-quick-jumper-button")},r.jump_to_confirm):o.createElement("span",{onClick:x,onKeyUp:x},i)),w=o.createElement("div",{className:"".concat(C,"-quick-jumper")},r.jump_to,o.createElement("input",{disabled:d,type:"text",value:h,onChange:function(e){v(e.target.value)},onKeyUp:x,onBlur:function(e){i||""===h||(v(""),e.relatedTarget&&(e.relatedTarget.className.indexOf("".concat(s,"-item-link"))>=0||e.relatedTarget.className.indexOf("".concat(s,"-item"))>=0)||null==c||c(b()))},"aria-label":r.page}),r.page,E)),o.createElement("li",{className:C},S,w)};var qo=function(e){var t=e.rootPrefixCls,n=e.page,r=e.active,a=e.className,l=e.showTitle,i=e.onClick,c=e.onKeyPress,s=e.itemRender,d="".concat(t,"-item"),u=P()(d,"".concat(d,"-").concat(n),(0,$e.A)((0,$e.A)({},"".concat(d,"-active"),r),"".concat(d,"-disabled"),!n),a),p=s(n,"page",o.createElement("a",{rel:"nofollow"},n));return p?o.createElement("li",{title:l?String(n):null,className:u,onClick:function(){i(n)},onKeyDown:function(e){c(e,i,n)},tabIndex:0},p):null},Vo=function(e,t,n){return n};function Xo(){}function Uo(e){var t=Number(e);return"number"==typeof t&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function Go(e,t,n){var o=void 0===e?t:e;return Math.floor((n-1)/o)+1}var Yo=function(e){var t=e.prefixCls,n=void 0===t?"rc-pagination":t,r=e.selectPrefixCls,a=void 0===r?"rc-select":r,l=e.className,i=e.current,c=e.defaultCurrent,s=void 0===c?1:c,d=e.total,u=void 0===d?0:d,p=e.pageSize,f=e.defaultPageSize,m=void 0===f?10:f,g=e.onChange,h=void 0===g?Xo:g,v=e.hideOnSinglePage,b=e.align,y=e.showPrevNextJumpers,x=void 0===y||y,C=e.showQuickJumper,S=e.showLessItems,w=e.showTitle,E=void 0===w||w,A=e.onShowSizeChange,k=void 0===A?Xo:A,$=e.locale,N=void 0===$?_o:$,O=e.style,I=e.totalBoundaryShowSizeChanger,z=void 0===I?50:I,K=e.disabled,R=e.simple,M=e.showTotal,T=e.showSizeChanger,B=void 0===T?u>z:T,j=e.sizeChangerRender,D=e.pageSizeOptions,L=e.itemRender,H=void 0===L?Vo:L,_=e.jumpPrevIcon,F=e.jumpNextIcon,W=e.prevIcon,q=e.nextIcon,V=o.useRef(null),X=(0,Hn.A)(10,{value:p,defaultValue:m}),U=(0,se.A)(X,2),G=U[0],Y=U[1],Q=(0,Hn.A)(1,{value:i,defaultValue:s,postState:function(e){return Math.max(1,Math.min(e,Go(void 0,G,u)))}}),J=(0,se.A)(Q,2),Z=J[0],ee=J[1],te=o.useState(Z),ne=(0,se.A)(te,2),oe=ne[0],re=ne[1];(0,o.useEffect)((function(){re(Z)}),[Z]);var ae=Math.max(1,Z-(S?3:5)),le=Math.min(Go(void 0,G,u),Z+(S?3:5));function ie(t,r){var a=t||o.createElement("button",{type:"button","aria-label":r,className:"".concat(n,"-item-link")});return"function"==typeof t&&(a=o.createElement(t,(0,ke.A)({},e))),a}function ce(e){var t=e.target.value,n=Go(void 0,G,u);return""===t?t:Number.isNaN(Number(t))?oe:t>=n?n:Number(t)}var de=u>G&&C;function ue(e){var t=ce(e);switch(t!==oe&&re(t),e.keyCode){case Ho.A.ENTER:pe(t);break;case Ho.A.UP:pe(t-1);break;case Ho.A.DOWN:pe(t+1)}}function pe(e){if(function(e){return Uo(e)&&e!==Z&&Uo(u)&&u>0}(e)&&!K){var t=Go(void 0,G,u),n=e;return e>t?n=t:e<1&&(n=1),n!==oe&&re(n),ee(n),null==h||h(n,G),n}return Z}var fe=Z>1,me=Z<Go(void 0,G,u);function ge(){fe&&pe(Z-1)}function ve(){me&&pe(Z+1)}function be(){pe(ae)}function ye(){pe(le)}function xe(e,t){if("Enter"===e.key||e.charCode===Ho.A.ENTER||e.keyCode===Ho.A.ENTER){for(var n=arguments.length,o=new Array(n>2?n-2:0),r=2;r<n;r++)o[r-2]=arguments[r];t.apply(void 0,o)}}function Ce(e){"click"!==e.type&&e.keyCode!==Ho.A.ENTER||pe(oe)}var Se=null,we=(0,Ye.A)(e,{aria:!0,data:!0}),Ee=M&&o.createElement("li",{className:"".concat(n,"-total-text")},M(u,[0===u?0:(Z-1)*G+1,Z*G>u?u:Z*G])),Ne=null,Oe=Go(void 0,G,u);if(v&&u<=G)return null;var Ie=[],ze={rootPrefixCls:n,onClick:pe,onKeyPress:xe,showTitle:E,itemRender:H,page:-1},Ke=Z-1>0?Z-1:0,Pe=Z+1<Oe?Z+1:Oe,Re=C&&C.goButton,Me="object"===(0,Ae.A)(R)?R.readOnly:!R,Te=Re,Be=null;R&&(Re&&(Te="boolean"==typeof Re?o.createElement("button",{type:"button",onClick:Ce,onKeyUp:Ce},N.jump_to_confirm):o.createElement("span",{onClick:Ce,onKeyUp:Ce},Re),Te=o.createElement("li",{title:E?"".concat(N.jump_to).concat(Z,"/").concat(Oe):null,className:"".concat(n,"-simple-pager")},Te)),Be=o.createElement("li",{title:E?"".concat(Z,"/").concat(Oe):null,className:"".concat(n,"-simple-pager")},Me?oe:o.createElement("input",{type:"text","aria-label":N.jump_to,value:oe,disabled:K,onKeyDown:function(e){e.keyCode!==Ho.A.UP&&e.keyCode!==Ho.A.DOWN||e.preventDefault()},onKeyUp:ue,onChange:ue,onBlur:function(e){pe(ce(e))},size:3}),o.createElement("span",{className:"".concat(n,"-slash")},"/"),Oe));var je=S?1:2;if(Oe<=3+2*je){Oe||Ie.push(o.createElement(qo,(0,he.A)({},ze,{key:"noPager",page:1,className:"".concat(n,"-item-disabled")})));for(var De=1;De<=Oe;De+=1)Ie.push(o.createElement(qo,(0,he.A)({},ze,{key:De,page:De,active:Z===De})))}else{var Le=S?N.prev_3:N.prev_5,He=S?N.next_3:N.next_5,_e=H(ae,"jump-prev",ie(_,"prev page")),Fe=H(le,"jump-next",ie(F,"next page"));x&&(Se=_e?o.createElement("li",{title:E?Le:null,key:"prev",onClick:be,tabIndex:0,onKeyDown:function(e){xe(e,be)},className:P()("".concat(n,"-jump-prev"),(0,$e.A)({},"".concat(n,"-jump-prev-custom-icon"),!!_))},_e):null,Ne=Fe?o.createElement("li",{title:E?He:null,key:"next",onClick:ye,tabIndex:0,onKeyDown:function(e){xe(e,ye)},className:P()("".concat(n,"-jump-next"),(0,$e.A)({},"".concat(n,"-jump-next-custom-icon"),!!F))},Fe):null);var We=Math.max(1,Z-je),qe=Math.min(Z+je,Oe);Z-1<=je&&(qe=1+2*je),Oe-Z<=je&&(We=Oe-2*je);for(var Ve=We;Ve<=qe;Ve+=1)Ie.push(o.createElement(qo,(0,he.A)({},ze,{key:Ve,page:Ve,active:Z===Ve})));if(Z-1>=2*je&&3!==Z&&(Ie[0]=o.cloneElement(Ie[0],{className:P()("".concat(n,"-item-after-jump-prev"),Ie[0].props.className)}),Ie.unshift(Se)),Oe-Z>=2*je&&Z!==Oe-2){var Xe=Ie[Ie.length-1];Ie[Ie.length-1]=o.cloneElement(Xe,{className:P()("".concat(n,"-item-before-jump-next"),Xe.props.className)}),Ie.push(Ne)}1!==We&&Ie.unshift(o.createElement(qo,(0,he.A)({},ze,{key:1,page:1}))),qe!==Oe&&Ie.push(o.createElement(qo,(0,he.A)({},ze,{key:Oe,page:Oe})))}var Ue=function(e){var t=H(e,"prev",ie(W,"prev page"));return o.isValidElement(t)?o.cloneElement(t,{disabled:!fe}):t}(Ke);if(Ue){var Ge=!fe||!Oe;Ue=o.createElement("li",{title:E?N.prev_page:null,onClick:ge,tabIndex:Ge?null:0,onKeyDown:function(e){xe(e,ge)},className:P()("".concat(n,"-prev"),(0,$e.A)({},"".concat(n,"-disabled"),Ge)),"aria-disabled":Ge},Ue)}var Qe,Je,Ze=function(e){var t=H(e,"next",ie(q,"next page"));return o.isValidElement(t)?o.cloneElement(t,{disabled:!me}):t}(Pe);Ze&&(R?(Qe=!me,Je=fe?0:null):Je=(Qe=!me||!Oe)?null:0,Ze=o.createElement("li",{title:E?N.next_page:null,onClick:ve,tabIndex:Je,onKeyDown:function(e){xe(e,ve)},className:P()("".concat(n,"-next"),(0,$e.A)({},"".concat(n,"-disabled"),Qe)),"aria-disabled":Qe},Ze));var et=P()(n,l,(0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)((0,$e.A)({},"".concat(n,"-start"),"start"===b),"".concat(n,"-center"),"center"===b),"".concat(n,"-end"),"end"===b),"".concat(n,"-simple"),R),"".concat(n,"-disabled"),K));return o.createElement("ul",(0,he.A)({className:et,style:O,ref:V},we),Ee,Ue,R?Be:Ie,Ze,o.createElement(Wo,{locale:N,rootPrefixCls:n,disabled:K,selectPrefixCls:a,changeSize:function(e){var t=Go(e,G,u),n=Z>t&&0!==t?t:Z;Y(e),re(n),null==k||k(Z,e),ee(n),null==h||h(n,e)},pageSize:G,pageSizeOptions:D,quickGo:de?pe:null,goButton:Te,showSizeChanger:B,sizeChangerRender:j}))},Qo=n(6069),Jo=n(9155),Zo=n(1320),er=n(1594),tr=n(6716),nr=n(9222);const or=e=>{const{componentCls:t}=e;return{[`${t}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-item`]:{cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${t}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${t}-simple-pager`]:{color:e.colorTextDisabled},[`${t}-jump-prev, ${t}-jump-next`]:{[`${t}-item-link-icon`]:{opacity:0},[`${t}-item-ellipsis`]:{opacity:1}}},[`&${t}-simple`]:{[`${t}-prev, ${t}-next`]:{[`&${t}-disabled ${t}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},rr=e=>{const{componentCls:t}=e;return{[`&${t}-mini ${t}-total-text, &${t}-mini ${t}-simple-pager`]:{height:e.itemSizeSM,lineHeight:(0,L.zA)(e.itemSizeSM)},[`&${t}-mini ${t}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:(0,L.zA)(e.calc(e.itemSizeSM).sub(2).equal())},[`&${t}-mini:not(${t}-disabled) ${t}-item:not(${t}-item-active)`]:{backgroundColor:"transparent",borderColor:"transparent","&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},[`&${t}-mini ${t}-prev, &${t}-mini ${t}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:(0,L.zA)(e.itemSizeSM)},[`&${t}-mini:not(${t}-disabled)`]:{[`${t}-prev, ${t}-next`]:{[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover ${t}-item-link`]:{backgroundColor:"transparent"}}},[`\n    &${t}-mini ${t}-prev ${t}-item-link,\n    &${t}-mini ${t}-next ${t}-item-link\n    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:(0,L.zA)(e.itemSizeSM)}},[`&${t}-mini ${t}-jump-prev, &${t}-mini ${t}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:(0,L.zA)(e.itemSizeSM)},[`&${t}-mini ${t}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:(0,L.zA)(e.itemSizeSM),input:Object.assign(Object.assign({},(0,er.BZ)(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},ar=e=>{const{componentCls:t}=e;return{[`\n    &${t}-simple ${t}-prev,\n    &${t}-simple ${t}-next\n    `]:{height:e.itemSizeSM,lineHeight:(0,L.zA)(e.itemSizeSM),verticalAlign:"top",[`${t}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:(0,L.zA)(e.itemSizeSM)}}},[`&${t}-simple ${t}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${(0,L.zA)(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${(0,L.zA)(e.inputOutlineOffset)} 0 ${(0,L.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},lr=e=>{const{componentCls:t}=e;return{[`${t}-jump-prev, ${t}-jump-next`]:{outline:0,[`${t}-item-container`]:{position:"relative",[`${t}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${t}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}}},[`\n    ${t}-prev,\n    ${t}-jump-prev,\n    ${t}-jump-next\n    `]:{marginInlineEnd:e.marginXS},[`\n    ${t}-prev,\n    ${t}-next,\n    ${t}-jump-prev,\n    ${t}-jump-next\n    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:(0,L.zA)(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${t}-prev, ${t}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${t}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${(0,L.zA)(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover`]:{[`${t}-item-link`]:{backgroundColor:"transparent"}}},[`${t}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${t}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:(0,L.zA)(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},(0,er.wj)(e)),(0,nr.nI)(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},(0,nr.eT)(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},ir=e=>{const{componentCls:t}=e;return{[`${t}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:(0,L.zA)(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${(0,L.zA)(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${(0,L.zA)(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${t}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},cr=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,H.dF)(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${t}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:(0,L.zA)(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),ir(e)),lr(e)),ar(e)),rr(e)),or(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${t}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${t}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},sr=e=>{const{componentCls:t}=e;return{[`${t}:not(${t}-disabled)`]:{[`${t}-item`]:Object.assign({},(0,H.K8)(e)),[`${t}-jump-prev, ${t}-jump-next`]:{"&:focus-visible":Object.assign({[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}},(0,H.jk)(e))},[`${t}-prev, ${t}-next`]:{[`&:focus-visible ${t}-item-link`]:Object.assign({},(0,H.jk)(e))}}}},dr=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},(0,tr.b)(e)),ur=e=>(0,F.oX)(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},(0,tr.C)(e));var pr=(0,_.OF)("Pagination",(e=>{const t=ur(e);return[cr(t),sr(t)]}),dr);const fr=e=>{const{componentCls:t}=e;return{[`${t}${t}-bordered${t}-disabled:not(${t}-mini)`]:{"&, &:hover":{[`${t}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${t}-item-link`]:{borderColor:e.colorBorder}},[`${t}-item, ${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${t}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${t}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${t}-prev, ${t}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${t}${t}-bordered:not(${t}-mini)`]:{[`${t}-prev, ${t}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${t}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${t}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${t}-disabled`]:{[`${t}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${t}-item`]:{backgroundColor:e.itemBg,border:`${(0,L.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${t}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}};var mr=(0,_.bf)(["Pagination","bordered"],(e=>{const t=ur(e);return[fr(t)]}),dr);function gr(e){return(0,o.useMemo)((()=>"boolean"==typeof e?[e,{}]:e&&"object"==typeof e?[!0,e]:[void 0,void 0]),[e])}var hr=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var vr=e=>{const{align:t,prefixCls:n,selectPrefixCls:r,className:a,rootClassName:l,style:i,size:c,locale:s,responsive:d,showSizeChanger:u,selectComponentClass:p,pageSizeOptions:f}=e,m=hr(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:g}=(0,Io.A)(d),[,h]=(0,Zo.Ay)(),{getPrefixCls:v,direction:b,showSizeChanger:y,className:x,style:C}=(0,M.TP)("pagination"),S=v("pagination",n),[w,E,A]=pr(S),k=(0,T.A)(c),$="small"===k||!(!g||k||!d),[N]=(0,Jo.A)("Pagination",Qo.A),O=Object.assign(Object.assign({},N),s),[I,K]=gr(u),[R,B]=gr(y),j=null!=I?I:R,D=null!=K?K:B,L=p||z.A,H=o.useMemo((()=>f?f.map((e=>Number(e))):void 0),[f]);const _=o.useMemo((()=>{const e=o.createElement("span",{className:`${S}-item-ellipsis`},"•••");return{prevIcon:o.createElement("button",{className:`${S}-item-link`,type:"button",tabIndex:-1},"rtl"===b?o.createElement(Lo.A,null):o.createElement(Do.A,null)),nextIcon:o.createElement("button",{className:`${S}-item-link`,type:"button",tabIndex:-1},"rtl"===b?o.createElement(Do.A,null):o.createElement(Lo.A,null)),jumpPrevIcon:o.createElement("a",{className:`${S}-item-link`},o.createElement("div",{className:`${S}-item-container`},"rtl"===b?o.createElement(jo,{className:`${S}-item-link-icon`}):o.createElement(Mo,{className:`${S}-item-link-icon`}),e)),jumpNextIcon:o.createElement("a",{className:`${S}-item-link`},o.createElement("div",{className:`${S}-item-container`},"rtl"===b?o.createElement(Mo,{className:`${S}-item-link-icon`}):o.createElement(jo,{className:`${S}-item-link-icon`}),e))}}),[b,S]),F=v("select",r),W=P()({[`${S}-${t}`]:!!t,[`${S}-mini`]:$,[`${S}-rtl`]:"rtl"===b,[`${S}-bordered`]:h.wireframe},x,a,l,E,A),q=Object.assign(Object.assign({},C),i);return w(o.createElement(o.Fragment,null,h.wireframe&&o.createElement(mr,{prefixCls:S}),o.createElement(Yo,Object.assign({},_,m,{style:q,prefixCls:S,selectPrefixCls:F,className:W,locale:O,pageSizeOptions:H,showSizeChanger:j,sizeChangerRender:e=>{var t;const{disabled:n,size:r,onSizeChange:a,"aria-label":l,className:i,options:c}=e,{className:s,onChange:d}=D||{},u=null===(t=c.find((e=>String(e.value)===String(r))))||void 0===t?void 0:t.value;return o.createElement(L,Object.assign({disabled:n,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:e=>e.parentNode,"aria-label":l,options:c},D,{value:u,onChange:(e,t)=>{null==a||a(e),null==d||d(e,t)},size:$?"small":"middle",className:P()(i,s)}))}}))))},br=vr,yr=n(4716);var xr=function(e){return t=>{const{prefixCls:n,onExpand:r,record:a,expanded:l,expandable:i}=t,c=`${n}-row-expand-icon`;return o.createElement("button",{type:"button",onClick:e=>{r(a,e),e.stopPropagation()},className:P()(c,{[`${c}-spaced`]:!i,[`${c}-expanded`]:i&&l,[`${c}-collapsed`]:i&&!l}),"aria-label":l?e.collapse:e.expand,"aria-expanded":l})}};const Cr=(e,t)=>"key"in e&&void 0!==e.key&&null!==e.key?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function Sr(e,t){return t?`${t}-${e}`:`${e}`}const wr=(e,t)=>"function"==typeof e?e(t):e;var Er={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},Ar=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:Er}))};var kr=o.forwardRef(Ar);var $r=function(){const e=Object.assign({},arguments.length<=0?void 0:arguments[0]);for(let t=1;t<arguments.length;t++){const n=t<0||arguments.length<=t?void 0:arguments[t];n&&Object.keys(n).forEach((t=>{const o=n[t];void 0!==o&&(e[t]=o)}))}return e},Nr=n(7447);var Or=n(7308),Ir=n(7206),zr=n(6476),Kr=n(3029),Pr=n(2901),Rr=n(9417),Mr=n(5501),Tr=n(9426);var Br=function(e){var t=e.dropPosition,n=e.dropLevelOffset,r=e.indent,a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(t){case-1:a.top=0,a.left=-n*r;break;case 1:a.bottom=0,a.left=-n*r;break;case 0:a.bottom=0,a.left=r}return o.createElement("div",{style:a})};function jr(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var Dr=n(754);var Lr=function(e,t){var n=o.useState(!1),r=(0,se.A)(n,2),a=r[0],l=r[1];(0,ue.A)((function(){if(a)return e(),function(){t()}}),[a]),(0,ue.A)((function(){return l(!0),function(){l(!1)}}),[])},Hr=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],_r=o.forwardRef((function(e,t){var n=e.className,r=e.style,a=e.motion,l=e.motionNodes,i=e.motionType,c=e.onMotionStart,s=e.onMotionEnd,d=e.active,u=e.treeNodeRequiredProps,p=(0,Le.A)(e,Hr),f=o.useState(!0),m=(0,se.A)(f,2),g=m[0],h=m[1],v=o.useContext(dn).prefixCls,b=l&&"hide"!==i;(0,ue.A)((function(){l&&b!==g&&h(b)}),[l]);var y=o.useRef(!1),x=function(){l&&!y.current&&(y.current=!0,s())};Lr((function(){l&&c()}),x);return l?o.createElement(Dr.Ay,(0,he.A)({ref:t,visible:g},a,{motionAppear:"show"===i,onVisibleChanged:function(e){b===e&&x()}}),(function(e,t){var n=e.className,r=e.style;return o.createElement("div",{ref:t,className:P()("".concat(v,"-treenode-motion"),n),style:r},l.map((function(e){var t=Object.assign({},(jr(e.data),e.data)),n=e.title,r=e.key,a=e.isStart,l=e.isEnd;delete t.children;var i=Sn(r,u);return o.createElement(Nn,(0,he.A)({},t,i,{title:n,active:d,data:e.data,key:r,isStart:a,isEnd:l}))})))})):o.createElement(Nn,(0,he.A)({domRef:t,className:n,style:r},p,{active:d}))}));var Fr=_r;function Wr(e,t,n){var o=e.findIndex((function(e){return e.key===n})),r=e[o+1],a=t.findIndex((function(e){return e.key===n}));if(r){var l=t.findIndex((function(e){return e.key===r.key}));return t.slice(a+1,l)}return t.slice(a+1)}var qr=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],Vr={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Xr=function(){},Ur="RC_TREE_MOTION_".concat(Math.random()),Gr={key:Ur},Yr={key:Ur,level:0,index:0,pos:"0",node:Gr,nodes:[Gr]},Qr={parent:null,children:[],pos:Yr.pos,data:Gr,title:null,key:Ur,isStart:[],isEnd:[]};function Jr(e,t,n,o){return!1!==t&&n?e.slice(0,Math.ceil(n/o)+1):e}function Zr(e){return vn(e.key,e.pos)}var ea=o.forwardRef((function(e,t){var n=e.prefixCls,r=e.data,a=(e.selectable,e.checkable,e.expandedKeys),l=e.selectedKeys,i=e.checkedKeys,c=e.loadedKeys,s=e.loadingKeys,d=e.halfCheckedKeys,u=e.keyEntities,p=e.disabled,f=e.dragging,m=e.dragOverNodeKey,g=e.dropPosition,h=e.motion,v=e.height,b=e.itemHeight,y=e.virtual,x=e.scrollWidth,C=e.focusable,S=e.activeItem,w=e.focused,E=e.tabIndex,A=e.onKeyDown,k=e.onFocus,$=e.onBlur,N=e.onActiveChange,O=e.onListChangeStart,I=e.onListChangeEnd,z=(0,Le.A)(e,qr),K=o.useRef(null),P=o.useRef(null);o.useImperativeHandle(t,(function(){return{scrollTo:function(e){K.current.scrollTo(e)},getIndentWidth:function(){return P.current.offsetWidth}}}));var R=o.useState(a),M=(0,se.A)(R,2),T=M[0],B=M[1],j=o.useState(r),D=(0,se.A)(j,2),L=D[0],H=D[1],_=o.useState(r),F=(0,se.A)(_,2),W=F[0],q=F[1],V=o.useState([]),X=(0,se.A)(V,2),U=X[0],G=X[1],Y=o.useState(null),Q=(0,se.A)(Y,2),J=Q[0],Z=Q[1],ee=o.useRef(r);function te(){var e=ee.current;H(e),q(e),G([]),Z(null),I()}ee.current=r,(0,ue.A)((function(){B(a);var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e.length,o=t.length;if(1!==Math.abs(n-o))return{add:!1,key:null};function r(e,t){var n=new Map;e.forEach((function(e){n.set(e,!0)}));var o=t.filter((function(e){return!n.has(e)}));return 1===o.length?o[0]:null}return n<o?{add:!0,key:r(e,t)}:{add:!1,key:r(t,e)}}(T,a);if(null!==e.key)if(e.add){var t=L.findIndex((function(t){return t.key===e.key})),n=Jr(Wr(L,r,e.key),y,v,b),o=L.slice();o.splice(t+1,0,Qr),q(o),G(n),Z("show")}else{var l=r.findIndex((function(t){return t.key===e.key})),i=Jr(Wr(r,L,e.key),y,v,b),c=r.slice();c.splice(l+1,0,Qr),q(c),G(i),Z("hide")}else L!==r&&(H(r),q(r))}),[a,r]),o.useEffect((function(){f||te()}),[f]);var ne=h?W:r,oe={expandedKeys:a,selectedKeys:l,loadedKeys:c,loadingKeys:s,checkedKeys:i,halfCheckedKeys:d,dragOverNodeKey:m,dropPosition:g,keyEntities:u};return o.createElement(o.Fragment,null,w&&S&&o.createElement("span",{style:Vr,"aria-live":"assertive"},function(e){for(var t=String(e.data.key),n=e;n.parent;)n=n.parent,t="".concat(n.data.key," > ").concat(t);return t}(S)),o.createElement("div",null,o.createElement("input",{style:Vr,disabled:!1===C||p,tabIndex:!1!==C?E:null,onKeyDown:A,onFocus:k,onBlur:$,value:"",onChange:Xr,"aria-label":"for screen reader"})),o.createElement("div",{className:"".concat(n,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},o.createElement("div",{className:"".concat(n,"-indent")},o.createElement("div",{ref:P,className:"".concat(n,"-indent-unit")}))),o.createElement(Xt.A,(0,he.A)({},z,{data:ne,itemKey:Zr,height:v,fullHeight:!1,virtual:y,itemHeight:b,scrollWidth:x,prefixCls:"".concat(n,"-list"),ref:K,role:"tree",onVisibleChange:function(e){e.every((function(e){return Zr(e)!==Ur}))&&te()}}),(function(e){var t=e.pos,n=Object.assign({},(jr(e.data),e.data)),r=e.title,a=e.key,l=e.isStart,i=e.isEnd,c=vn(a,t);delete n.key,delete n.children;var s=Sn(c,oe);return o.createElement(Fr,(0,he.A)({},n,s,{title:r,active:!!S&&a===S.key,pos:t,data:e.data,isStart:l,isEnd:i,motion:h,motionNodes:a===Ur?U:null,motionType:J,onMotionStart:O,onMotionEnd:te,treeNodeRequiredProps:oe,onMouseMove:function(){N(null)}}))})))}));var ta=ea,na=function(e){(0,Mr.A)(n,e);var t=(0,Tr.A)(n);function n(){var e;(0,Kr.A)(this,n);for(var r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return e=t.call.apply(t,[this].concat(a)),(0,$e.A)((0,Rr.A)(e),"destroyed",!1),(0,$e.A)((0,Rr.A)(e),"delayedDragEnterLogic",void 0),(0,$e.A)((0,Rr.A)(e),"loadingRetryTimes",{}),(0,$e.A)((0,Rr.A)(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:bn()}),(0,$e.A)((0,Rr.A)(e),"dragStartMousePosition",null),(0,$e.A)((0,Rr.A)(e),"dragNodeProps",null),(0,$e.A)((0,Rr.A)(e),"currentMouseOverDroppableNodeKey",null),(0,$e.A)((0,Rr.A)(e),"listRef",o.createRef()),(0,$e.A)((0,Rr.A)(e),"onNodeDragStart",(function(t,n){var o=e.state,r=o.expandedKeys,a=o.keyEntities,l=e.props.onDragStart,i=n.eventKey;e.dragNodeProps=n,e.dragStartMousePosition={x:t.clientX,y:t.clientY};var c=On(r,i);e.setState({draggingNodeKey:i,dragChildrenKeys:Kn(i,a),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(c),window.addEventListener("dragend",e.onWindowDragEnd),null==l||l({event:t,node:wn(n)})})),(0,$e.A)((0,Rr.A)(e),"onNodeDragEnter",(function(t,n){var o=e.state,r=o.expandedKeys,a=o.keyEntities,l=o.dragChildrenKeys,i=o.flattenNodes,c=o.indent,s=e.props,d=s.onDragEnter,u=s.onExpand,p=s.allowDrop,f=s.direction,m=n.pos,g=n.eventKey;if(e.currentMouseOverDroppableNodeKey!==g&&(e.currentMouseOverDroppableNodeKey=g),e.dragNodeProps){var h=Rn(t,e.dragNodeProps,n,c,e.dragStartMousePosition,p,i,a,r,f),v=h.dropPosition,b=h.dropLevelOffset,y=h.dropTargetKey,x=h.dropContainerKey,C=h.dropTargetPos,S=h.dropAllowed,w=h.dragOverNodeKey;!l.includes(y)&&S?(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach((function(t){clearTimeout(e.delayedDragEnterLogic[t])})),e.dragNodeProps.eventKey!==n.eventKey&&(t.persist(),e.delayedDragEnterLogic[m]=window.setTimeout((function(){if(null!==e.state.draggingNodeKey){var o=(0,N.A)(r),l=mn(a,n.eventKey);l&&(l.children||[]).length&&(o=In(r,n.eventKey)),e.props.hasOwnProperty("expandedKeys")||e.setExpandedKeys(o),null==u||u(o,{node:wn(n),expanded:!0,nativeEvent:t.nativeEvent})}}),800)),e.dragNodeProps.eventKey!==y||0!==b?(e.setState({dragOverNodeKey:w,dropPosition:v,dropLevelOffset:b,dropTargetKey:y,dropContainerKey:x,dropTargetPos:C,dropAllowed:S}),null==d||d({event:t,node:wn(n),expandedKeys:r})):e.resetDragState()):e.resetDragState()}else e.resetDragState()})),(0,$e.A)((0,Rr.A)(e),"onNodeDragOver",(function(t,n){var o=e.state,r=o.dragChildrenKeys,a=o.flattenNodes,l=o.keyEntities,i=o.expandedKeys,c=o.indent,s=e.props,d=s.onDragOver,u=s.allowDrop,p=s.direction;if(e.dragNodeProps){var f=Rn(t,e.dragNodeProps,n,c,e.dragStartMousePosition,u,a,l,i,p),m=f.dropPosition,g=f.dropLevelOffset,h=f.dropTargetKey,v=f.dropContainerKey,b=f.dropTargetPos,y=f.dropAllowed,x=f.dragOverNodeKey;!r.includes(h)&&y&&(e.dragNodeProps.eventKey===h&&0===g?null===e.state.dropPosition&&null===e.state.dropLevelOffset&&null===e.state.dropTargetKey&&null===e.state.dropContainerKey&&null===e.state.dropTargetPos&&!1===e.state.dropAllowed&&null===e.state.dragOverNodeKey||e.resetDragState():m===e.state.dropPosition&&g===e.state.dropLevelOffset&&h===e.state.dropTargetKey&&v===e.state.dropContainerKey&&b===e.state.dropTargetPos&&y===e.state.dropAllowed&&x===e.state.dragOverNodeKey||e.setState({dropPosition:m,dropLevelOffset:g,dropTargetKey:h,dropContainerKey:v,dropTargetPos:b,dropAllowed:y,dragOverNodeKey:x}),null==d||d({event:t,node:wn(n)}))}})),(0,$e.A)((0,Rr.A)(e),"onNodeDragLeave",(function(t,n){e.currentMouseOverDroppableNodeKey!==n.eventKey||t.currentTarget.contains(t.relatedTarget)||(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var o=e.props.onDragLeave;null==o||o({event:t,node:wn(n)})})),(0,$e.A)((0,Rr.A)(e),"onWindowDragEnd",(function(t){e.onNodeDragEnd(t,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,$e.A)((0,Rr.A)(e),"onNodeDragEnd",(function(t,n){var o=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),null==o||o({event:t,node:wn(n)}),e.dragNodeProps=null,window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,$e.A)((0,Rr.A)(e),"onNodeDrop",(function(t,n){var o,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=e.state,l=a.dragChildrenKeys,i=a.dropPosition,c=a.dropTargetKey,s=a.dropTargetPos;if(a.dropAllowed){var d=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),null!==c){var u=(0,ke.A)((0,ke.A)({},Sn(c,e.getTreeNodeRequiredProps())),{},{active:(null===(o=e.getActiveItem())||void 0===o?void 0:o.key)===c,data:mn(e.state.keyEntities,c).node}),p=l.includes(c);(0,Ie.Ay)(!p,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var f=zn(s),m={event:t,node:wn(u),dragNode:e.dragNodeProps?wn(e.dragNodeProps):null,dragNodesKeys:[e.dragNodeProps.eventKey].concat(l),dropToGap:0!==i,dropPosition:i+Number(f[f.length-1])};r||null==d||d(m),e.dragNodeProps=null}}})),(0,$e.A)((0,Rr.A)(e),"cleanDragState",(function(){null!==e.state.draggingNodeKey&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null})),(0,$e.A)((0,Rr.A)(e),"triggerExpandActionExpand",(function(t,n){var o=e.state,r=o.expandedKeys,a=o.flattenNodes,l=n.expanded,i=n.key;if(!(n.isLeaf||t.shiftKey||t.metaKey||t.ctrlKey)){var c=a.filter((function(e){return e.key===i}))[0],s=wn((0,ke.A)((0,ke.A)({},Sn(i,e.getTreeNodeRequiredProps())),{},{data:c.data}));e.setExpandedKeys(l?On(r,i):In(r,i)),e.onNodeExpand(t,s)}})),(0,$e.A)((0,Rr.A)(e),"onNodeClick",(function(t,n){var o=e.props,r=o.onClick;"click"===o.expandAction&&e.triggerExpandActionExpand(t,n),null==r||r(t,n)})),(0,$e.A)((0,Rr.A)(e),"onNodeDoubleClick",(function(t,n){var o=e.props,r=o.onDoubleClick;"doubleClick"===o.expandAction&&e.triggerExpandActionExpand(t,n),null==r||r(t,n)})),(0,$e.A)((0,Rr.A)(e),"onNodeSelect",(function(t,n){var o=e.state.selectedKeys,r=e.state,a=r.keyEntities,l=r.fieldNames,i=e.props,c=i.onSelect,s=i.multiple,d=n.selected,u=n[l.key],p=!d,f=(o=p?s?In(o,u):[u]:On(o,u)).map((function(e){var t=mn(a,e);return t?t.node:null})).filter(Boolean);e.setUncontrolledState({selectedKeys:o}),null==c||c(o,{event:"select",selected:p,node:n,selectedNodes:f,nativeEvent:t.nativeEvent})})),(0,$e.A)((0,Rr.A)(e),"onNodeCheck",(function(t,n,o){var r,a=e.state,l=a.keyEntities,i=a.checkedKeys,c=a.halfCheckedKeys,s=e.props,d=s.checkStrictly,u=s.onCheck,p=n.key,f={event:"check",node:n,checked:o,nativeEvent:t.nativeEvent};if(d){var m=o?In(i,p):On(i,p);r={checked:m,halfChecked:On(c,p)},f.checkedNodes=m.map((function(e){return mn(l,e)})).filter(Boolean).map((function(e){return e.node})),e.setUncontrolledState({checkedKeys:m})}else{var g=Ln([].concat((0,N.A)(i),[p]),!0,l),h=g.checkedKeys,v=g.halfCheckedKeys;if(!o){var b=new Set(h);b.delete(p);var y=Ln(Array.from(b),{checked:!1,halfCheckedKeys:v},l);h=y.checkedKeys,v=y.halfCheckedKeys}r=h,f.checkedNodes=[],f.checkedNodesPositions=[],f.halfCheckedKeys=v,h.forEach((function(e){var t=mn(l,e);if(t){var n=t.node,o=t.pos;f.checkedNodes.push(n),f.checkedNodesPositions.push({node:n,pos:o})}})),e.setUncontrolledState({checkedKeys:h},!1,{halfCheckedKeys:v})}null==u||u(r,f)})),(0,$e.A)((0,Rr.A)(e),"onNodeLoad",(function(t){var n,o=t.key,r=mn(e.state.keyEntities,o);if(null==r||null===(n=r.children)||void 0===n||!n.length){var a=new Promise((function(n,r){e.setState((function(a){var l=a.loadedKeys,i=void 0===l?[]:l,c=a.loadingKeys,s=void 0===c?[]:c,d=e.props,u=d.loadData,p=d.onLoad;return!u||i.includes(o)||s.includes(o)?null:(u(t).then((function(){var r=In(e.state.loadedKeys,o);null==p||p(r,{event:"load",node:t}),e.setUncontrolledState({loadedKeys:r}),e.setState((function(e){return{loadingKeys:On(e.loadingKeys,o)}})),n()})).catch((function(t){if(e.setState((function(e){return{loadingKeys:On(e.loadingKeys,o)}})),e.loadingRetryTimes[o]=(e.loadingRetryTimes[o]||0)+1,e.loadingRetryTimes[o]>=10){var a=e.state.loadedKeys;(0,Ie.Ay)(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:In(a,o)}),n()}r(t)})),{loadingKeys:In(s,o)})}))}));return a.catch((function(){})),a}})),(0,$e.A)((0,Rr.A)(e),"onNodeMouseEnter",(function(t,n){var o=e.props.onMouseEnter;null==o||o({event:t,node:n})})),(0,$e.A)((0,Rr.A)(e),"onNodeMouseLeave",(function(t,n){var o=e.props.onMouseLeave;null==o||o({event:t,node:n})})),(0,$e.A)((0,Rr.A)(e),"onNodeContextMenu",(function(t,n){var o=e.props.onRightClick;o&&(t.preventDefault(),o({event:t,node:n}))})),(0,$e.A)((0,Rr.A)(e),"onFocus",(function(){var t=e.props.onFocus;e.setState({focused:!0});for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];null==t||t.apply(void 0,o)})),(0,$e.A)((0,Rr.A)(e),"onBlur",(function(){var t=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];null==t||t.apply(void 0,o)})),(0,$e.A)((0,Rr.A)(e),"getTreeNodeRequiredProps",(function(){var t=e.state;return{expandedKeys:t.expandedKeys||[],selectedKeys:t.selectedKeys||[],loadedKeys:t.loadedKeys||[],loadingKeys:t.loadingKeys||[],checkedKeys:t.checkedKeys||[],halfCheckedKeys:t.halfCheckedKeys||[],dragOverNodeKey:t.dragOverNodeKey,dropPosition:t.dropPosition,keyEntities:t.keyEntities}})),(0,$e.A)((0,Rr.A)(e),"setExpandedKeys",(function(t){var n=e.state,o=xn(n.treeData,t,n.fieldNames);e.setUncontrolledState({expandedKeys:t,flattenNodes:o},!0)})),(0,$e.A)((0,Rr.A)(e),"onNodeExpand",(function(t,n){var o=e.state.expandedKeys,r=e.state,a=r.listChanging,l=r.fieldNames,i=e.props,c=i.onExpand,s=i.loadData,d=n.expanded,u=n[l.key];if(!a){var p=o.includes(u),f=!d;if((0,Ie.Ay)(d&&p||!d&&!p,"Expand state not sync with index check"),o=f?In(o,u):On(o,u),e.setExpandedKeys(o),null==c||c(o,{node:n,expanded:f,nativeEvent:t.nativeEvent}),f&&s){var m=e.onNodeLoad(n);m&&m.then((function(){var t=xn(e.state.treeData,o,l);e.setUncontrolledState({flattenNodes:t})})).catch((function(){var t=On(e.state.expandedKeys,u);e.setExpandedKeys(t)}))}}})),(0,$e.A)((0,Rr.A)(e),"onListChangeStart",(function(){e.setUncontrolledState({listChanging:!0})})),(0,$e.A)((0,Rr.A)(e),"onListChangeEnd",(function(){setTimeout((function(){e.setUncontrolledState({listChanging:!1})}))})),(0,$e.A)((0,Rr.A)(e),"onActiveChange",(function(t){var n=e.state.activeKey,o=e.props,r=o.onActiveChange,a=o.itemScrollOffset,l=void 0===a?0:a;n!==t&&(e.setState({activeKey:t}),null!==t&&e.scrollTo({key:t,offset:l}),null==r||r(t))})),(0,$e.A)((0,Rr.A)(e),"getActiveItem",(function(){var t=e.state,n=t.activeKey,o=t.flattenNodes;return null===n?null:o.find((function(e){return e.key===n}))||null})),(0,$e.A)((0,Rr.A)(e),"offsetActiveKey",(function(t){var n=e.state,o=n.flattenNodes,r=n.activeKey,a=o.findIndex((function(e){return e.key===r}));-1===a&&t<0&&(a=o.length);var l=o[a=(a+t+o.length)%o.length];if(l){var i=l.key;e.onActiveChange(i)}else e.onActiveChange(null)})),(0,$e.A)((0,Rr.A)(e),"onKeyDown",(function(t){var n=e.state,o=n.activeKey,r=n.expandedKeys,a=n.checkedKeys,l=n.fieldNames,i=e.props,c=i.onKeyDown,s=i.checkable,d=i.selectable;switch(t.which){case Ho.A.UP:e.offsetActiveKey(-1),t.preventDefault();break;case Ho.A.DOWN:e.offsetActiveKey(1),t.preventDefault()}var u=e.getActiveItem();if(u&&u.data){var p=e.getTreeNodeRequiredProps(),f=!1===u.data.isLeaf||!!(u.data[l.children]||[]).length,m=wn((0,ke.A)((0,ke.A)({},Sn(o,p)),{},{data:u.data,active:!0}));switch(t.which){case Ho.A.LEFT:f&&r.includes(o)?e.onNodeExpand({},m):u.parent&&e.onActiveChange(u.parent.key),t.preventDefault();break;case Ho.A.RIGHT:f&&!r.includes(o)?e.onNodeExpand({},m):u.children&&u.children.length&&e.onActiveChange(u.children[0].key),t.preventDefault();break;case Ho.A.ENTER:case Ho.A.SPACE:!s||m.disabled||!1===m.checkable||m.disableCheckbox?s||!d||m.disabled||!1===m.selectable||e.onNodeSelect({},m):e.onNodeCheck({},m,!a.includes(o))}}null==c||c(t)})),(0,$e.A)((0,Rr.A)(e),"setUncontrolledState",(function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e.destroyed){var r=!1,a=!0,l={};Object.keys(t).forEach((function(n){e.props.hasOwnProperty(n)?a=!1:(r=!0,l[n]=t[n])})),!r||n&&!a||e.setState((0,ke.A)((0,ke.A)({},l),o))}})),(0,$e.A)((0,Rr.A)(e),"scrollTo",(function(t){e.listRef.current.scrollTo(t)})),e}return(0,Pr.A)(n,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var e=this.props,t=e.activeKey,n=e.itemScrollOffset,o=void 0===n?0:n;void 0!==t&&t!==this.state.activeKey&&(this.setState({activeKey:t}),null!==t&&this.scrollTo({key:t,offset:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var e,t=this.state,n=t.focused,r=t.flattenNodes,a=t.keyEntities,l=t.draggingNodeKey,i=t.activeKey,c=t.dropLevelOffset,s=t.dropContainerKey,d=t.dropTargetKey,u=t.dropPosition,p=t.dragOverNodeKey,f=t.indent,m=this.props,g=m.prefixCls,h=m.className,v=m.style,b=m.showLine,y=m.focusable,x=m.tabIndex,C=void 0===x?0:x,S=m.selectable,w=m.showIcon,E=m.icon,A=m.switcherIcon,k=m.draggable,$=m.checkable,N=m.checkStrictly,O=m.disabled,I=m.motion,z=m.loadData,K=m.filterTreeNode,R=m.height,M=m.itemHeight,T=m.scrollWidth,B=m.virtual,j=m.titleRender,D=m.dropIndicatorRender,L=m.onContextMenu,H=m.onScroll,_=m.direction,F=m.rootClassName,W=m.rootStyle,q=(0,Ye.A)(this.props,{aria:!0,data:!0});k&&(e="object"===(0,Ae.A)(k)?k:"function"==typeof k?{nodeDraggable:k}:{});var V={prefixCls:g,selectable:S,showIcon:w,icon:E,switcherIcon:A,draggable:e,draggingNodeKey:l,checkable:$,checkStrictly:N,disabled:O,keyEntities:a,dropLevelOffset:c,dropContainerKey:s,dropTargetKey:d,dropPosition:u,dragOverNodeKey:p,indent:f,direction:_,dropIndicatorRender:D,loadData:z,filterTreeNode:K,titleRender:j,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return o.createElement(dn.Provider,{value:V},o.createElement("div",{className:P()(g,h,F,(0,$e.A)((0,$e.A)((0,$e.A)({},"".concat(g,"-show-line"),b),"".concat(g,"-focused"),n),"".concat(g,"-active-focused"),null!==i)),style:W},o.createElement(ta,(0,he.A)({ref:this.listRef,prefixCls:g,style:v,data:r,disabled:O,selectable:S,checkable:!!$,motion:I,dragging:null!==l,height:R,itemHeight:M,virtual:B,focusable:y,focused:n,tabIndex:C,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:L,onScroll:H,scrollWidth:T},this.getTreeNodeRequiredProps(),q))))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n,o=t.prevProps,r={prevProps:e};function a(t){return!o&&e.hasOwnProperty(t)||o&&o[t]!==e[t]}var l=t.fieldNames;if(a("fieldNames")&&(l=bn(e.fieldNames),r.fieldNames=l),a("treeData")?n=e.treeData:a("children")&&((0,Ie.Ay)(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),n=yn(e.children)),n){r.treeData=n;var i=Cn(n,{fieldNames:l});r.keyEntities=(0,ke.A)((0,$e.A)({},Ur,Yr),i.keyEntities)}var c,s=r.keyEntities||t.keyEntities;if(a("expandedKeys")||o&&a("autoExpandParent"))r.expandedKeys=e.autoExpandParent||!o&&e.defaultExpandParent?Bn(e.expandedKeys,s):e.expandedKeys;else if(!o&&e.defaultExpandAll){var d=(0,ke.A)({},s);delete d[Ur];var u=[];Object.keys(d).forEach((function(e){var t=d[e];t.children&&t.children.length&&u.push(t.key)})),r.expandedKeys=u}else!o&&e.defaultExpandedKeys&&(r.expandedKeys=e.autoExpandParent||e.defaultExpandParent?Bn(e.defaultExpandedKeys,s):e.defaultExpandedKeys);if(r.expandedKeys||delete r.expandedKeys,n||r.expandedKeys){var p=xn(n||t.treeData,r.expandedKeys||t.expandedKeys,l);r.flattenNodes=p}if((e.selectable&&(a("selectedKeys")?r.selectedKeys=Mn(e.selectedKeys,e):!o&&e.defaultSelectedKeys&&(r.selectedKeys=Mn(e.defaultSelectedKeys,e))),e.checkable)&&(a("checkedKeys")?c=Tn(e.checkedKeys)||{}:!o&&e.defaultCheckedKeys?c=Tn(e.defaultCheckedKeys)||{}:n&&(c=Tn(e.checkedKeys)||{checkedKeys:t.checkedKeys,halfCheckedKeys:t.halfCheckedKeys}),c)){var f=c,m=f.checkedKeys,g=void 0===m?[]:m,h=f.halfCheckedKeys,v=void 0===h?[]:h;if(!e.checkStrictly){var b=Ln(g,!0,s);g=b.checkedKeys,v=b.halfCheckedKeys}r.checkedKeys=g,r.halfCheckedKeys=v}return a("loadedKeys")&&(r.loadedKeys=e.loadedKeys),r}}]),n}(o.Component);(0,$e.A)(na,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Br,allowDrop:function(){return!0},expandAction:!1}),(0,$e.A)(na,"TreeNode",Nn);var oa=na,ra={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},aa=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:ra}))};var la=o.forwardRef(aa),ia={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},ca=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:ia}))};var sa=o.forwardRef(ca),da={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},ua=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:da}))};var pa=o.forwardRef(ua),fa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},ma=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:fa}))};var ga=o.forwardRef(ma),ha=n(3723),va=n(7391),ba=n(977);const ya=e=>{let{treeCls:t,treeNodeCls:n,directoryNodeSelectedBg:o,directoryNodeSelectedColor:r,motionDurationMid:a,borderRadius:l,controlItemBgHover:i}=e;return{[`${t}${t}-directory ${n}`]:{[`${t}-node-content-wrapper`]:{position:"static",[`> *:not(${t}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${a}`,content:'""',borderRadius:l},"&:hover:before":{background:i}},[`${t}-switcher, ${t}-checkbox, ${t}-draggable-icon`]:{zIndex:1},"&-selected":{[`${t}-switcher, ${t}-draggable-icon`]:{color:r},[`${t}-node-content-wrapper`]:{color:r,background:"transparent","&:before, &:hover:before":{background:o}}}}}},xa=new L.Mo("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Ca=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Sa=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,L.zA)(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),wa=(e,t)=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:r,titleHeight:a,indentSize:l,nodeSelectedBg:i,nodeHoverBg:c,colorTextQuaternary:s,controlItemBgActiveDisabled:d}=t;return{[n]:Object.assign(Object.assign({},(0,H.dF)(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${n}-rtl ${n}-switcher_close ${n}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${n}-active-focused)`]:Object.assign({},(0,H.jk)(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${o}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:xa,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[o]:{display:"flex",alignItems:"flex-start",marginBottom:r,lineHeight:(0,L.zA)(a),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:r},[`&-disabled ${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${n}-checkbox-disabled + ${n}-node-selected,&${o}-disabled${o}-selected ${n}-node-content-wrapper`]:{backgroundColor:d},[`${n}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${o}-disabled)`]:{[`${n}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${n}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${o}-disabled).filter-node ${n}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${n}-draggable-icon`]:{flexShrink:0,width:a,textAlign:"center",visibility:"visible",color:s},[`&${o}-disabled ${n}-draggable-icon`]:{visibility:"hidden"}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:l}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher, ${n}-checkbox`]:{marginInlineEnd:t.calc(t.calc(a).sub(t.controlInteractiveSize)).div(2).equal()},[`${n}-switcher`]:Object.assign(Object.assign({},Ca(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:a,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:a,height:a,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${n}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${n}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(a).div(2).equal()).mul(.8).equal(),height:t.calc(a).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:a,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Sa(e,t)),{"&:hover":{backgroundColor:c},[`&${n}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:i},[`${n}-iconEle`]:{display:"inline-block",width:a,height:a,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${o}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${n}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${o}-leaf-last ${n}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${(0,L.zA)(t.calc(a).div(2).equal())} !important`}})}},Ea=(e,t)=>{const n=`.${e}`,o=`${n}-treenode`,r=t.calc(t.paddingXS).div(2).equal(),a=(0,F.oX)(t,{treeCls:n,treeNodeCls:o,treeNodePadding:r});return[wa(e,a),ya(a)]};var Aa=(0,_.OF)("Tree",((e,t)=>{let{prefixCls:n}=t;return[{[e.componentCls]:(0,va.gd)(`${n}-checkbox`,e)},Ea(n,e),(0,ba.A)(e)]}),(e=>{const{colorTextLightSolid:t,colorPrimary:n}=e;return Object.assign(Object.assign({},(e=>{const{controlHeightSM:t,controlItemBgHover:n,controlItemBgActive:o}=e;return{titleHeight:t,indentSize:t,nodeHoverBg:n,nodeHoverColor:e.colorText,nodeSelectedBg:o,nodeSelectedColor:e.colorText}})(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:n})}));var ka=function(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:r,indent:a,direction:l="ltr"}=e,i="ltr"===l?"left":"right",c="ltr"===l?"right":"left",s={[i]:-n*a+4,[c]:0};switch(t){case-1:s.top=-3;break;case 1:s.bottom=-3;break;default:s.bottom=-3,s[i]=a+4}return o.createElement("div",{style:s,className:`${r}-drop-indicator`})},$a={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},Na=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:$a}))};var Oa=o.forwardRef(Na),Ia=n(3567),za={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Ka=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:za}))};var Pa=o.forwardRef(Ka),Ra={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},Ma=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:Ra}))};var Ta=o.forwardRef(Ma),Ba=n(682);var ja=e=>{const{prefixCls:t,switcherIcon:n,treeNodeProps:r,showLine:a,switcherLoadingIcon:l}=e,{isLeaf:i,expanded:c,loading:s}=r;if(s)return o.isValidElement(l)?l:o.createElement(Ia.A,{className:`${t}-switcher-loading-icon`});let d;if(a&&"object"==typeof a&&(d=a.showLeafIcon),i){if(!a)return null;if("boolean"!=typeof d&&d){const e="function"==typeof d?d(r):d,n=`${t}-switcher-line-custom-icon`;return o.isValidElement(e)?(0,Ba.Ob)(e,{className:P()(e.props.className||"",n)}):e}return d?o.createElement(la,{className:`${t}-switcher-line-icon`}):o.createElement("span",{className:`${t}-switcher-leaf-line`})}const u=`${t}-switcher-icon`,p="function"==typeof n?n(r):n;return o.isValidElement(p)?(0,Ba.Ob)(p,{className:P()(p.props.className||"",u)}):void 0!==p?p:a?c?o.createElement(Pa,{className:`${t}-switcher-line-icon`}):o.createElement(Ta,{className:`${t}-switcher-line-icon`}):o.createElement(Oa,{className:u})};const Da=o.forwardRef(((e,t)=>{var n;const{getPrefixCls:r,direction:a,virtual:l,tree:i}=o.useContext(M.QO),{prefixCls:c,className:s,showIcon:d=!1,showLine:u,switcherIcon:p,switcherLoadingIcon:f,blockNode:m=!1,children:g,checkable:h=!1,selectable:v=!0,draggable:b,motion:y,style:x}=e,C=r("tree",c),S=r(),w=null!=y?y:Object.assign(Object.assign({},(0,ha.A)(S)),{motionAppear:!1}),E=Object.assign(Object.assign({},e),{checkable:h,selectable:v,showIcon:d,motion:w,blockNode:m,showLine:Boolean(u),dropIndicatorRender:ka}),[A,k,$]=Aa(C),[,N]=(0,Zo.Ay)(),O=N.paddingXS/2+((null===(n=N.Tree)||void 0===n?void 0:n.titleHeight)||N.controlHeightSM),I=o.useMemo((()=>{if(!b)return!1;let e={};switch(typeof b){case"function":e.nodeDraggable=b;break;case"object":e=Object.assign({},b)}return!1!==e.icon&&(e.icon=e.icon||o.createElement(ga,null)),e}),[b]);return A(o.createElement(oa,Object.assign({itemHeight:O,ref:t,virtual:l},E,{style:Object.assign(Object.assign({},null==i?void 0:i.style),x),prefixCls:C,className:P()({[`${C}-icon-hide`]:!d,[`${C}-block-node`]:m,[`${C}-unselectable`]:!v,[`${C}-rtl`]:"rtl"===a},null==i?void 0:i.className,s,k,$),direction:a,checkable:h?o.createElement("span",{className:`${C}-checkbox-inner`}):h,selectable:v,switcherIcon:e=>o.createElement(ja,{prefixCls:C,switcherIcon:p,switcherLoadingIcon:f,treeNodeProps:e,showLine:u}),draggable:I}),g))}));var La=Da;function Ha(e,t,n){const{key:o,children:r}=n;e.forEach((function(e){const a=e[o],l=e[r];!1!==t(a,e)&&Ha(l||[],t,n)}))}function _a(e){let{treeData:t,expandedKeys:n,startKey:o,endKey:r,fieldNames:a}=e;const l=[];let i=0;if(o&&o===r)return[o];if(!o||!r)return[];return Ha(t,(e=>{if(2===i)return!1;if(function(e){return e===o||e===r}(e)){if(l.push(e),0===i)i=1;else if(1===i)return i=2,!1}else 1===i&&l.push(e);return n.includes(e)}),bn(a)),l}function Fa(e,t,n){const o=(0,N.A)(t),r=[];return Ha(e,((e,t)=>{const n=o.indexOf(e);return-1!==n&&(r.push(t),o.splice(n,1)),!!o.length}),bn(n)),r}var Wa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function qa(e){const{isLeaf:t,expanded:n}=e;return t?o.createElement(la,null):n?o.createElement(sa,null):o.createElement(pa,null)}function Va(e){let{treeData:t,children:n}=e;return t||yn(n)}const Xa=(e,t)=>{var{defaultExpandAll:n,defaultExpandParent:r,defaultExpandedKeys:a}=e,l=Wa(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const i=o.useRef(null),c=o.useRef(null),[s,d]=o.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[u,p]=o.useState((()=>(()=>{const{keyEntities:e}=Cn(Va(l));let t;return t=n?Object.keys(e):r?Bn(l.expandedKeys||a||[],e):l.expandedKeys||a||[],t})()));o.useEffect((()=>{"selectedKeys"in l&&d(l.selectedKeys)}),[l.selectedKeys]),o.useEffect((()=>{"expandedKeys"in l&&p(l.expandedKeys)}),[l.expandedKeys]);const{getPrefixCls:f,direction:m}=o.useContext(M.QO),{prefixCls:g,className:h,showIcon:v=!0,expandAction:b="click"}=l,y=Wa(l,["prefixCls","className","showIcon","expandAction"]),x=f("tree",g),C=P()(`${x}-directory`,{[`${x}-directory-rtl`]:"rtl"===m},h);return o.createElement(La,Object.assign({icon:qa,ref:t,blockNode:!0},y,{showIcon:v,expandAction:b,prefixCls:x,className:C,expandedKeys:u,selectedKeys:s,onSelect:(e,t)=>{var n;const{multiple:o,fieldNames:r}=l,{node:a,nativeEvent:s}=t,{key:p=""}=a,f=Va(l),m=Object.assign(Object.assign({},t),{selected:!0}),g=(null==s?void 0:s.ctrlKey)||(null==s?void 0:s.metaKey),h=null==s?void 0:s.shiftKey;let v;o&&g?(v=e,i.current=p,c.current=v,m.selectedNodes=Fa(f,v,r)):o&&h?(v=Array.from(new Set([].concat((0,N.A)(c.current||[]),(0,N.A)(_a({treeData:f,expandedKeys:u,startKey:p,endKey:i.current,fieldNames:r}))))),m.selectedNodes=Fa(f,v,r)):(v=[p],i.current=p,c.current=v,m.selectedNodes=Fa(f,v,r)),null===(n=l.onSelect)||void 0===n||n.call(l,v,m),"selectedKeys"in l||d(v)},onExpand:(e,t)=>{var n;return"expandedKeys"in l||p(e),null===(n=l.onExpand)||void 0===n?void 0:n.call(l,e,t)}}))};var Ua=o.forwardRef(Xa);const Ga=La;Ga.DirectoryTree=Ua,Ga.TreeNode=Nn;var Ya=Ga,Qa=n(2877),Ja=n(8017);var Za=e=>{const{value:t,filterSearch:n,tablePrefixCls:r,locale:a,onChange:l}=e;return n?o.createElement("div",{className:`${r}-filter-dropdown-search`},o.createElement(Ja.A,{prefix:o.createElement(Qa.A,null),placeholder:a.filterSearchPlaceholder,onChange:l,value:t,htmlSize:1,className:`${r}-filter-dropdown-search-input`})):null};const el=e=>{const{keyCode:t}=e;t===Ho.A.ENTER&&e.stopPropagation()};var tl=o.forwardRef(((e,t)=>o.createElement("div",{className:e.className,onClick:e=>e.stopPropagation(),onKeyDown:el,ref:t},e.children)));function nl(e){let t=[];return(e||[]).forEach((e=>{let{value:n,children:o}=e;t.push(n),o&&(t=[].concat((0,N.A)(t),(0,N.A)(nl(o))))})),t}function ol(e,t){return("string"==typeof t||"number"==typeof t)&&(null==t?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()))}function rl(e){let{filters:t,prefixCls:n,filteredKeys:r,filterMultiple:a,searchValue:l,filterSearch:i}=e;return t.map(((e,t)=>{const c=String(e.value);if(e.children)return{key:c||t,label:e.text,popupClassName:`${n}-dropdown-submenu`,children:rl({filters:e.children,prefixCls:n,filteredKeys:r,filterMultiple:a,searchValue:l,filterSearch:i})};const s=a?Fn.A:bo,d={key:void 0!==e.value?c:t,label:o.createElement(o.Fragment,null,o.createElement(s,{checked:r.includes(c)}),o.createElement("span",null,e.text))};return l.trim()?"function"==typeof i?i(l,e)?d:null:ol(l,e.text)?d:null:d}))}function al(e){return e||[]}var ll=e=>{var t,n,r,a;const{tablePrefixCls:l,prefixCls:i,column:c,dropdownPrefixCls:s,columnKey:d,filterOnClose:u,filterMultiple:p,filterMode:f="menu",filterSearch:m=!1,filterState:g,triggerFilter:h,locale:b,children:y,getPopupContainer:x,rootClassName:C}=e,{filterResetToDefaultFilteredValue:S,defaultFilteredValue:w,filterDropdownProps:E={},filterDropdownOpen:A,filterDropdownVisible:k,onFilterDropdownVisibleChange:$,onFilterDropdownOpenChange:N}=c,[O,I]=o.useState(!1),z=!(!g||!(null===(t=g.filteredKeys)||void 0===t?void 0:t.length)&&!g.forceFiltered),K=e=>{var t;I(e),null===(t=E.onOpenChange)||void 0===t||t.call(E,e),null==N||N(e),null==$||$(e)};const R=null!==(a=null!==(r=null!==(n=E.open)&&void 0!==n?n:A)&&void 0!==r?r:k)&&void 0!==a?a:O,T=null==g?void 0:g.filteredKeys,[B,j]=function(e){const t=o.useRef(e),n=(0,Nr.A)();return[()=>t.current,e=>{t.current=e,n()}]}(al(T)),D=e=>{let{selectedKeys:t}=e;j(t)},L=(e,t)=>{let{node:n,checked:o}=t;D(p?{selectedKeys:e}:{selectedKeys:o&&n.key?[n.key]:[]})};o.useEffect((()=>{O&&D({selectedKeys:al(T)})}),[T]);const[H,_]=o.useState([]),F=e=>{_(e)},[W,q]=o.useState(""),V=e=>{const{value:t}=e.target;q(t)};o.useEffect((()=>{O||q("")}),[O]);const X=e=>{const t=(null==e?void 0:e.length)?e:null;return null!==t||g&&g.filteredKeys?(0,pe.A)(t,null==g?void 0:g.filteredKeys,!0)?null:void h({column:c,key:d,filteredKeys:t}):null},U=()=>{K(!1),X(B())},G=function(){let{confirm:e,closeDropdown:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{confirm:!1,closeDropdown:!1};e&&X([]),t&&K(!1),q(""),j(S?(w||[]).map((e=>String(e))):[])},Y=function(){let{closeDropdown:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{closeDropdown:!0};e&&K(!1),X(B())},Q=P()({[`${s}-menu-without-submenu`]:(J=c.filters||[],!J.some((e=>{let{children:t}=e;return t})))});var J;const Z=e=>{if(e.target.checked){const e=nl(null==c?void 0:c.filters).map((e=>String(e)));j(e)}else j([])},ee=e=>{let{filters:t}=e;return(t||[]).map(((e,t)=>{const n=String(e.value),o={title:e.text,key:void 0!==e.value?n:String(t)};return e.children&&(o.children=ee({filters:e.children})),o}))},te=e=>{var t;return Object.assign(Object.assign({},e),{text:e.title,value:e.key,children:(null===(t=e.children)||void 0===t?void 0:t.map((e=>te(e))))||[]})};let ne;const{direction:oe,renderEmpty:re}=o.useContext(M.QO);if("function"==typeof c.filterDropdown)ne=c.filterDropdown({prefixCls:`${s}-custom`,setSelectedKeys:e=>D({selectedKeys:e}),selectedKeys:B(),confirm:Y,clearFilters:G,filters:c.filters,visible:R,close:()=>{K(!1)}});else if(c.filterDropdown)ne=c.filterDropdown;else{const e=B()||[],t=()=>{var t,n;const r=null!==(t=null==re?void 0:re("Table.filter"))&&void 0!==t?t:o.createElement(Or.A,{image:Or.A.PRESENTED_IMAGE_SIMPLE,description:b.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if(0===(c.filters||[]).length)return r;if("tree"===f)return o.createElement(o.Fragment,null,o.createElement(Za,{filterSearch:m,value:W,onChange:V,tablePrefixCls:l,locale:b}),o.createElement("div",{className:`${l}-filter-dropdown-tree`},p?o.createElement(Fn.A,{checked:e.length===nl(c.filters).length,indeterminate:e.length>0&&e.length<nl(c.filters).length,className:`${l}-filter-dropdown-checkall`,onChange:Z},null!==(n=null==b?void 0:b.filterCheckall)&&void 0!==n?n:null==b?void 0:b.filterCheckAll):null,o.createElement(Ya,{checkable:!0,selectable:!1,blockNode:!0,multiple:p,checkStrictly:!p,className:`${s}-menu`,onCheck:L,checkedKeys:e,selectedKeys:e,showIcon:!1,treeData:ee({filters:c.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:W.trim()?e=>"function"==typeof m?m(W,te(e)):ol(W,e.title):void 0})));const a=rl({filters:c.filters||[],filterSearch:m,prefixCls:i,filteredKeys:B(),filterMultiple:p,searchValue:W}),d=a.every((e=>null===e));return o.createElement(o.Fragment,null,o.createElement(Za,{filterSearch:m,value:W,onChange:V,tablePrefixCls:l,locale:b}),d?r:o.createElement(Ir.A,{selectable:!0,multiple:p,prefixCls:`${s}-menu`,className:Q,onSelect:D,onDeselect:D,selectedKeys:e,getPopupContainer:x,openKeys:H,onOpenChange:F,items:a}))},n=()=>S?(0,pe.A)((w||[]).map((e=>String(e))),e,!0):0===e.length;ne=o.createElement(o.Fragment,null,t(),o.createElement("div",{className:`${i}-dropdown-btns`},o.createElement(v.Ay,{type:"link",size:"small",disabled:n(),onClick:()=>G()},b.filterReset),o.createElement(v.Ay,{type:"primary",size:"small",onClick:U},b.filterConfirm)))}c.filterDropdown&&(ne=o.createElement(zr.A,{selectable:void 0},ne)),ne=o.createElement(tl,{className:`${i}-dropdown`},ne);const ae=$r({trigger:["click"],placement:"rtl"===oe?"bottomLeft":"bottomRight",children:(()=>{let e;return e="function"==typeof c.filterIcon?c.filterIcon(z):c.filterIcon?c.filterIcon:o.createElement(kr,null),o.createElement("span",{role:"button",tabIndex:-1,className:P()(`${i}-trigger`,{active:z}),onClick:e=>{e.stopPropagation()}},e)})(),getPopupContainer:x},Object.assign(Object.assign({},E),{rootClassName:P()(C,E.rootClassName),open:R,onOpenChange:(e,t)=>{"trigger"===t.source&&(e&&void 0!==T&&j(al(T)),K(e),e||c.filterDropdown||!u||U())},dropdownRender:()=>"function"==typeof(null==E?void 0:E.dropdownRender)?E.dropdownRender(ne):ne}));return o.createElement("div",{className:`${i}-column`},o.createElement("span",{className:`${l}-column-title`},y),o.createElement(Wn.A,Object.assign({},ae)))};const il=(e,t,n)=>{let o=[];return(e||[]).forEach(((e,r)=>{var a;const l=Sr(r,n);if(e.filters||"filterDropdown"in e||"onFilter"in e)if("filteredValue"in e){let t=e.filteredValue;"filterDropdown"in e||(t=null!==(a=null==t?void 0:t.map(String))&&void 0!==a?a:t),o.push({column:e,key:Cr(e,l),filteredKeys:t,forceFiltered:e.filtered})}else o.push({column:e,key:Cr(e,l),filteredKeys:t&&e.defaultFilteredValue?e.defaultFilteredValue:void 0,forceFiltered:e.filtered});"children"in e&&(o=[].concat((0,N.A)(o),(0,N.A)(il(e.children,t,l))))})),o};function cl(e,t,n,r,a,l,i,c,s){return n.map(((n,d)=>{const u=Sr(d,c),{filterOnClose:p=!0,filterMultiple:f=!0,filterMode:m,filterSearch:g}=n;let h=n;if(h.filters||h.filterDropdown){const c=Cr(h,u),d=r.find((e=>{let{key:t}=e;return c===t}));h=Object.assign(Object.assign({},h),{title:r=>o.createElement(ll,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:h,columnKey:c,filterState:d,filterOnClose:p,filterMultiple:f,filterMode:m,filterSearch:g,triggerFilter:l,locale:a,getPopupContainer:i,rootClassName:s},wr(n.title,r))})}return"children"in h&&(h=Object.assign(Object.assign({},h),{children:cl(e,t,h.children,r,a,l,i,u,s)})),h}))}const sl=e=>{const t={};return e.forEach((e=>{let{key:n,filteredKeys:o,column:r}=e;const a=n,{filters:l,filterDropdown:i}=r;if(i)t[a]=o||null;else if(Array.isArray(o)){const e=nl(l);t[a]=e.filter((e=>o.includes(String(e))))}else t[a]=null})),t},dl=(e,t,n)=>t.reduce(((e,o)=>{const{column:{onFilter:r,filters:a},filteredKeys:l}=o;return r&&l&&l.length?e.map((e=>Object.assign({},e))).filter((e=>l.some((o=>{const l=nl(a),i=l.findIndex((e=>String(e)===String(o))),c=-1!==i?l[i]:o;return e[n]&&(e[n]=dl(e[n],t,n)),r(c,e)})))):e}),e),ul=e=>e.flatMap((e=>"children"in e?[e].concat((0,N.A)(ul(e.children||[]))):[e]));var pl=e=>{const{prefixCls:t,dropdownPrefixCls:n,mergedColumns:r,onFilterChange:a,getPopupContainer:l,locale:i,rootClassName:c}=e,s=((0,_n.rJ)("Table"),o.useMemo((()=>ul(r||[])),[r])),[d,u]=o.useState((()=>il(s,!0))),p=o.useMemo((()=>{const e=il(s,!1);if(0===e.length)return e;let t=!0,n=!0;if(e.forEach((e=>{let{filteredKeys:o}=e;void 0!==o?t=!1:n=!1})),t){const e=(s||[]).map(((e,t)=>Cr(e,Sr(t))));return d.filter((t=>{let{key:n}=t;return e.includes(n)})).map((t=>{const n=s[e.findIndex((e=>e===t.key))];return Object.assign(Object.assign({},t),{column:Object.assign(Object.assign({},t.column),n),forceFiltered:n.filtered})}))}return e}),[s,d]),f=o.useMemo((()=>sl(p)),[p]),m=e=>{const t=p.filter((t=>{let{key:n}=t;return n!==e.key}));t.push(e),u(t),a(sl(t),t)};return[e=>cl(t,n,e,p,i,m,l,void 0,c),p,f]};var fl=(e,t,n)=>{const r=o.useRef({});return[function(o){var a;if(!r.current||r.current.data!==e||r.current.childrenColumnName!==t||r.current.getRowKey!==n){const l=new Map;function i(e){e.forEach(((e,o)=>{const r=n(e,o);l.set(r,e),e&&"object"==typeof e&&t in e&&i(e[t]||[])}))}i(e),r.current={data:e,childrenColumnName:t,kvMap:l,getRowKey:n}}return null===(a=r.current.kvMap)||void 0===a?void 0:a.get(o)}]},ml=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const gl=10;var hl=function(e,t,n){const r=n&&"object"==typeof n?n:{},{total:a=0}=r,l=ml(r,["total"]),[i,c]=(0,o.useState)((()=>({current:"defaultCurrent"in l?l.defaultCurrent:1,pageSize:"defaultPageSize"in l?l.defaultPageSize:gl}))),s=$r(i,l,{total:a>0?a:e}),d=Math.ceil((a||e)/s.pageSize);s.current>d&&(s.current=d||1);const u=(e,t)=>{c({current:null!=e?e:1,pageSize:t||s.pageSize})};return!1===n?[{},()=>{}]:[Object.assign(Object.assign({},s),{onChange:(e,o)=>{var r;n&&(null===(r=n.onChange)||void 0===r||r.call(n,e,o)),u(e,o),t(e,o||(null==s?void 0:s.pageSize))}}),u]},vl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},bl=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:vl}))};var yl=o.forwardRef(bl),xl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},Cl=function(e,t){return o.createElement(Po.A,(0,he.A)({},e,{ref:t,icon:xl}))};var Sl=o.forwardRef(Cl);const wl="ascend",El="descend",Al=e=>"object"==typeof e.sorter&&"number"==typeof e.sorter.multiple&&e.sorter.multiple,kl=e=>"function"==typeof e?e:!(!e||"object"!=typeof e||!e.compare)&&e.compare,$l=(e,t,n)=>{let o=[];const r=(e,t)=>{o.push({column:e,key:Cr(e,t),multiplePriority:Al(e),sortOrder:e.sortOrder})};return(e||[]).forEach(((e,a)=>{const l=Sr(a,n);e.children?("sortOrder"in e&&r(e,l),o=[].concat((0,N.A)(o),(0,N.A)($l(e.children,t,l)))):e.sorter&&("sortOrder"in e?r(e,l):t&&e.defaultSortOrder&&o.push({column:e,key:Cr(e,l),multiplePriority:Al(e),sortOrder:e.defaultSortOrder}))})),o},Nl=(e,t,n,r,a,l,i,c)=>{const s=(t||[]).map(((t,s)=>{const d=Sr(s,c);let p=t;if(p.sorter){const c=p.sortDirections||a,s=void 0===p.showSorterTooltip?i:p.showSorterTooltip,f=Cr(p,d),m=n.find((e=>{let{key:t}=e;return t===f})),g=m?m.sortOrder:null,h=((e,t)=>t?e[e.indexOf(t)+1]:e[0])(c,g);let v;if(t.sortIcon)v=t.sortIcon({sortOrder:g});else{const t=c.includes(wl)&&o.createElement(Sl,{className:P()(`${e}-column-sorter-up`,{active:g===wl})}),n=c.includes(El)&&o.createElement(yl,{className:P()(`${e}-column-sorter-down`,{active:g===El})});v=o.createElement("span",{className:P()(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!(!t||!n)})},o.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},t,n))}const{cancelSort:b,triggerAsc:y,triggerDesc:x}=l||{};let C=b;h===El?C=x:h===wl&&(C=y);const S="object"==typeof s?Object.assign({title:C},s):{title:C};p=Object.assign(Object.assign({},p),{className:P()(p.className,{[`${e}-column-sort`]:g}),title:n=>{const r=`${e}-column-sorters`,a=o.createElement("span",{className:`${e}-column-title`},wr(t.title,n)),l=o.createElement("div",{className:r},a,v);return s?"boolean"!=typeof s&&"sorter-icon"===(null==s?void 0:s.target)?o.createElement("div",{className:`${r} ${e}-column-sorters-tooltip-target-sorter`},a,o.createElement(u.A,Object.assign({},S),v)):o.createElement(u.A,Object.assign({},S),l):l},onHeaderCell:n=>{var o;const a=(null===(o=t.onHeaderCell)||void 0===o?void 0:o.call(t,n))||{},l=a.onClick,i=a.onKeyDown;a.onClick=e=>{r({column:t,key:f,sortOrder:h,multiplePriority:Al(t)}),null==l||l(e)},a.onKeyDown=e=>{e.keyCode===Ho.A.ENTER&&(r({column:t,key:f,sortOrder:h,multiplePriority:Al(t)}),null==i||i(e))};const c=((e,t)=>{const n=wr(e,t);return"[object Object]"===Object.prototype.toString.call(n)?"":n})(t.title,{}),s=null==c?void 0:c.toString();return g&&(a["aria-sort"]="ascend"===g?"ascending":"descending"),a["aria-label"]=s||"",a.className=P()(a.className,`${e}-column-has-sorters`),a.tabIndex=0,t.ellipsis&&(a.title=(null!=c?c:"").toString()),a}})}return"children"in p&&(p=Object.assign(Object.assign({},p),{children:Nl(e,p.children,n,r,a,l,i,d)})),p}));return s},Ol=e=>{const{column:t,sortOrder:n}=e;return{column:t,order:n,field:t.dataIndex,columnKey:t.key}},Il=e=>{const t=e.filter((e=>{let{sortOrder:t}=e;return t})).map(Ol);if(0===t.length&&e.length){const t=e.length-1;return Object.assign(Object.assign({},Ol(e[t])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},zl=(e,t,n)=>{const o=t.slice().sort(((e,t)=>t.multiplePriority-e.multiplePriority)),r=e.slice(),a=o.filter((e=>{let{column:{sorter:t},sortOrder:n}=e;return kl(t)&&n}));return a.length?r.sort(((e,t)=>{for(let n=0;n<a.length;n+=1){const o=a[n],{column:{sorter:r},sortOrder:l}=o,i=kl(r);if(i&&l){const n=i(e,t,l);if(0!==n)return l===wl?n:-n}}return 0})).map((e=>{const o=e[n];return o?Object.assign(Object.assign({},e),{[n]:zl(o,t,n)}):e})):r};var Kl=e=>{const{prefixCls:t,mergedColumns:n,sortDirections:r,tableLocale:a,showSorterTooltip:l,onSorterChange:i}=e,[c,s]=o.useState($l(n,!0)),d=(e,t)=>{const n=[];return e.forEach(((e,o)=>{const r=Sr(o,t);if(n.push(Cr(e,r)),Array.isArray(e.children)){const t=d(e.children,r);n.push.apply(n,(0,N.A)(t))}})),n},u=o.useMemo((()=>{let e=!0;const t=$l(n,!1);if(!t.length){const e=d(n);return c.filter((t=>{let{key:n}=t;return e.includes(n)}))}const o=[];function r(t){e?o.push(t):o.push(Object.assign(Object.assign({},t),{sortOrder:null}))}let a=null;return t.forEach((t=>{null===a?(r(t),t.sortOrder&&(!1===t.multiplePriority?e=!1:a=!0)):(a&&!1!==t.multiplePriority||(e=!1),r(t))})),o}),[n,c]),p=o.useMemo((()=>{var e,t;const n=u.map((e=>{let{column:t,sortOrder:n}=e;return{column:t,order:n}}));return{sortColumns:n,sortColumn:null===(e=n[0])||void 0===e?void 0:e.column,sortOrder:null===(t=n[0])||void 0===t?void 0:t.order}}),[u]),f=e=>{let t;t=!1!==e.multiplePriority&&u.length&&!1!==u[0].multiplePriority?[].concat((0,N.A)(u.filter((t=>{let{key:n}=t;return n!==e.key}))),[e]):[e],s(t),i(Il(t),t)};return[e=>Nl(t,e,u,f,r,a,l),u,p,()=>Il(u)]};const Pl=(e,t)=>e.map((e=>{const n=Object.assign({},e);return n.title=wr(e.title,t),"children"in n&&(n.children=Pl(n.children,t)),n}));var Rl=e=>[o.useCallback((t=>Pl(t,e)),[e])];var Ml=Wt(((e,t)=>{const{_renderTimes:n}=e,{_renderTimes:o}=t;return n!==o}));var Tl=an(((e,t)=>{const{_renderTimes:n}=e,{_renderTimes:o}=t;return n!==o})),Bl=n(2616);var jl=e=>{const{componentCls:t,lineWidth:n,lineType:o,tableBorderColor:r,tableHeaderBg:a,tablePaddingVertical:l,tablePaddingHorizontal:i,calc:c}=e,s=`${(0,L.zA)(n)} ${o} ${r}`,d=(e,o,r)=>({[`&${t}-${e}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,L.zA)(c(o).mul(-1).equal())}\n              ${(0,L.zA)(c(c(r).add(n)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`\n            > ${t}-content,\n            > ${t}-header,\n            > ${t}-body,\n            > ${t}-summary\n          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,L.zA)(c(l).mul(-1).equal())} ${(0,L.zA)(c(c(i).add(n)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:n,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`\n                > tr${t}-expanded-row,\n                > tr${t}-placeholder\n              `]:{"> th, > td":{borderInlineEnd:0}}}}}},d("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),d("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${(0,L.zA)(n)} 0 ${(0,L.zA)(n)} ${a}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}};var Dl=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},H.L9),{wordBreak:"keep-all",[`\n          &${t}-cell-fix-left-last,\n          &${t}-cell-fix-right-first\n        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}};var Ll=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}};var Hl=e=>{const{componentCls:t,antCls:n,motionDurationSlow:o,lineWidth:r,paddingXS:a,lineType:l,tableBorderColor:i,tableExpandIconBg:c,tableExpandColumnWidth:s,borderRadius:d,tablePaddingVertical:u,tablePaddingHorizontal:p,tableExpandedRowBg:f,paddingXXS:m,expandIconMarginTop:g,expandIconSize:h,expandIconHalfInner:v,expandIconScale:b,calc:y}=e,x=`${(0,L.zA)(r)} ${l} ${i}`,C=y(m).sub(r).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},(0,H.Y1)(e)),{position:"relative",float:"left",width:h,height:h,color:"inherit",lineHeight:(0,L.zA)(h),background:c,border:x,borderRadius:d,transform:`scale(${b})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${o} ease-out`,content:'""'},"&::before":{top:v,insetInlineEnd:C,insetInlineStart:C,height:r},"&::after":{top:C,bottom:C,insetInlineStart:v,width:r,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:g,marginInlineEnd:a},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:f}},[`${n}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${(0,L.zA)(y(u).mul(-1).equal())} ${(0,L.zA)(y(p).mul(-1).equal())}`,padding:`${(0,L.zA)(u)} ${(0,L.zA)(p)}`}}}};var _l=e=>{const{componentCls:t,antCls:n,iconCls:o,tableFilterDropdownWidth:r,tableFilterDropdownSearchWidth:a,paddingXXS:l,paddingXS:i,colorText:c,lineWidth:s,lineType:d,tableBorderColor:u,headerIconColor:p,fontSizeSM:f,tablePaddingHorizontal:m,borderRadius:g,motionDurationSlow:h,colorTextDescription:v,colorPrimary:b,tableHeaderFilterActiveBg:y,colorTextDisabled:x,tableFilterDropdownBg:C,tableFilterDropdownHeight:S,controlItemBgHover:w,controlItemBgActive:E,boxShadowSecondary:A,filterDropdownMenuBg:k,calc:$}=e,N=`${n}-dropdown`,O=`${t}-filter-dropdown`,I=`${n}-tree`,z=`${(0,L.zA)(s)} ${d} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:$(l).mul(-1).equal(),marginInline:`${(0,L.zA)(l)} ${(0,L.zA)($(m).div(2).mul(-1).equal())}`,padding:`0 ${(0,L.zA)(l)}`,color:p,fontSize:f,borderRadius:g,cursor:"pointer",transition:`all ${h}`,"&:hover":{color:v,background:y},"&.active":{color:b}}}},{[`${n}-dropdown`]:{[O]:Object.assign(Object.assign({},(0,H.dF)(e)),{minWidth:r,backgroundColor:C,borderRadius:g,boxShadow:A,overflow:"hidden",[`${N}-menu`]:{maxHeight:S,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:k,"&:empty::after":{display:"block",padding:`${(0,L.zA)(i)} 0`,color:x,fontSize:f,textAlign:"center",content:'"Not Found"'}},[`${O}-tree`]:{paddingBlock:`${(0,L.zA)(i)} 0`,paddingInline:i,[I]:{padding:0},[`${I}-treenode ${I}-node-content-wrapper:hover`]:{backgroundColor:w},[`${I}-treenode-checkbox-checked ${I}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:E}}},[`${O}-search`]:{padding:i,borderBottom:z,"&-input":{input:{minWidth:a},[o]:{color:x}}},[`${O}-checkall`]:{width:"100%",marginBottom:l,marginInlineStart:l},[`${O}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${(0,L.zA)($(i).sub(s).equal())} ${(0,L.zA)(i)}`,overflow:"hidden",borderTop:z}})}},{[`${n}-dropdown ${O}, ${O}-submenu`]:{[`${n}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]};var Fl=e=>{const{componentCls:t,lineWidth:n,colorSplit:o,motionDurationSlow:r,zIndexTableFixed:a,tableBg:l,zIndexTableSticky:i,calc:c}=e,s=o;return{[`${t}-wrapper`]:{[`\n        ${t}-cell-fix-left,\n        ${t}-cell-fix-right\n      `]:{position:"sticky !important",zIndex:a,background:l},[`\n        ${t}-cell-fix-left-first::after,\n        ${t}-cell-fix-left-last::after\n      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(n).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`\n        ${t}-cell-fix-right-first::after,\n        ${t}-cell-fix-right-last::after\n      `]:{position:"absolute",top:0,bottom:c(n).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`\n          ${t}-cell-fix-left-first::after,\n          ${t}-cell-fix-left-last::after\n        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`\n          ${t}-cell-fix-right-first::after,\n          ${t}-cell-fix-right-last::after\n        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`\n        ${t}-cell-fix-left-first::after,\n        ${t}-cell-fix-left-last::after,\n        ${t}-cell-fix-right-first::after,\n        ${t}-cell-fix-right-last::after\n      `]:{boxShadow:"none"}}}}};var Wl=e=>{const{componentCls:t,antCls:n,margin:o}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${n}-pagination`]:{margin:`${(0,L.zA)(o)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}};var ql=e=>{const{componentCls:t,tableRadius:n}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${(0,L.zA)(n)} ${(0,L.zA)(n)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:n,borderStartEndRadius:n,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:n},"> *:last-child":{borderStartEndRadius:n}}},"&-footer":{borderRadius:`0 0 ${(0,L.zA)(n)} ${(0,L.zA)(n)}`}}}}};var Vl=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}};var Xl=e=>{const{componentCls:t,antCls:n,iconCls:o,fontSizeIcon:r,padding:a,paddingXS:l,headerIconColor:i,headerIconHoverColor:c,tableSelectionColumnWidth:s,tableSelectedRowBg:d,tableSelectedRowHoverBg:u,tableRowHoverBg:p,tablePaddingHorizontal:f,calc:m}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(r).add(m(a).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:m(s).add(m(l).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(r).add(m(a).div(4)).add(m(l).mul(2)).equal()}},[`\n        table tr th${t}-selection-column,\n        table tr td${t}-selection-column,\n        ${t}-selection-column\n      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${n}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:m(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:(0,L.zA)(m(f).div(4).equal()),[o]:{color:i,fontSize:r,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:d,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:p}}}}}};var Ul=e=>{const{componentCls:t,tableExpandColumnWidth:n,calc:o}=e,r=(e,r,a,l)=>({[`${t}${t}-${e}`]:{fontSize:l,[`\n        ${t}-title,\n        ${t}-footer,\n        ${t}-cell,\n        ${t}-thead > tr > th,\n        ${t}-tbody > tr > th,\n        ${t}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]:{padding:`${(0,L.zA)(r)} ${(0,L.zA)(a)}`},[`${t}-filter-trigger`]:{marginInlineEnd:(0,L.zA)(o(a).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${(0,L.zA)(o(r).mul(-1).equal())} ${(0,L.zA)(o(a).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:(0,L.zA)(o(r).mul(-1).equal()),marginInline:`${(0,L.zA)(o(n).sub(a).equal())} ${(0,L.zA)(o(a).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:(0,L.zA)(o(a).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},r("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),r("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}};var Gl=e=>{const{componentCls:t,marginXXS:n,fontSizeIcon:o,headerIconColor:r,headerIconHoverColor:a}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`\n          &${t}-cell-fix-left:hover,\n          &${t}-cell-fix-right:hover\n        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:n,color:r,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:o,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:a}}}};var Yl=e=>{const{componentCls:t,opacityLoading:n,tableScrollThumbBg:o,tableScrollThumbBgHover:r,tableScrollThumbSize:a,tableScrollBg:l,zIndexTableSticky:i,stickyScrollBarBorderRadius:c,lineWidth:s,lineType:d,tableBorderColor:u}=e,p=`${(0,L.zA)(s)} ${d} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${(0,L.zA)(a)} !important`,zIndex:i,display:"flex",alignItems:"center",background:l,borderTop:p,opacity:n,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:a,backgroundColor:o,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform none`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:r}}}}}}};var Ql=e=>{const{componentCls:t,lineWidth:n,tableBorderColor:o,calc:r}=e,a=`${(0,L.zA)(n)} ${e.lineType} ${o}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:a}}},[`div${t}-summary`]:{boxShadow:`0 ${(0,L.zA)(r(n).mul(-1).equal())} 0 ${o}`}}}};var Jl=e=>{const{componentCls:t,motionDurationMid:n,lineWidth:o,lineType:r,tableBorderColor:a,calc:l}=e,i=`${(0,L.zA)(o)} ${r} ${a}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`\n            & > ${t}-row, \n            & > div:not(${t}-row) > ${t}-row\n          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:i,transition:`background ${n}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${(0,L.zA)(o)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:l(o).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}};const Zl=e=>{const{componentCls:t,fontWeightStrong:n,tablePaddingVertical:o,tablePaddingHorizontal:r,tableExpandColumnWidth:a,lineWidth:l,lineType:i,tableBorderColor:c,tableFontSize:s,tableBg:d,tableRadius:u,tableHeaderTextColor:p,motionDurationMid:f,tableHeaderBg:m,tableHeaderCellSplitColor:g,tableFooterTextColor:h,tableFooterBg:v,calc:b}=e,y=`${(0,L.zA)(l)} ${i} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},(0,H.t6)()),{[t]:Object.assign(Object.assign({},(0,H.dF)(e)),{fontSize:s,background:d,borderRadius:`${(0,L.zA)(u)} ${(0,L.zA)(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${(0,L.zA)(u)} ${(0,L.zA)(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`\n          ${t}-cell,\n          ${t}-thead > tr > th,\n          ${t}-tbody > tr > th,\n          ${t}-tbody > tr > td,\n          tfoot > tr > th,\n          tfoot > tr > td\n        `]:{position:"relative",padding:`${(0,L.zA)(o)} ${(0,L.zA)(r)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${(0,L.zA)(o)} ${(0,L.zA)(r)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:p,fontWeight:n,textAlign:"start",background:m,borderBottom:y,transition:`background ${f} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:g,transform:"translateY(-50%)",transition:`background-color ${f}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${f}, border-color ${f}`,borderBottom:y,[`\n              > ${t}-wrapper:only-child,\n              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child\n            `]:{[t]:{marginBlock:(0,L.zA)(b(o).mul(-1).equal()),marginInline:`${(0,L.zA)(b(a).sub(r).equal())}\n                ${(0,L.zA)(b(r).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:p,fontWeight:n,textAlign:"start",background:m,borderBottom:y,transition:`background ${f} ease`}}},[`${t}-footer`]:{padding:`${(0,L.zA)(o)} ${(0,L.zA)(r)}`,color:h,background:v}})}};var ei=(0,_.OF)("Table",(e=>{const{colorTextHeading:t,colorSplit:n,colorBgContainer:o,controlInteractiveSize:r,headerBg:a,headerColor:l,headerSortActiveBg:i,headerSortHoverBg:c,bodySortBg:s,rowHoverBg:d,rowSelectedBg:u,rowSelectedHoverBg:p,rowExpandedBg:f,cellPaddingBlock:m,cellPaddingInline:g,cellPaddingBlockMD:h,cellPaddingInlineMD:v,cellPaddingBlockSM:b,cellPaddingInlineSM:y,borderColor:x,footerBg:C,footerColor:S,headerBorderRadius:w,cellFontSize:E,cellFontSizeMD:A,cellFontSizeSM:k,headerSplitColor:$,fixedHeaderSortActiveBg:N,headerFilterHoverBg:O,filterDropdownBg:I,expandIconBg:z,selectionColumnWidth:K,stickyScrollBarBg:P,calc:R}=e,M=(0,F.oX)(e,{tableFontSize:E,tableBg:o,tableRadius:w,tablePaddingVertical:m,tablePaddingHorizontal:g,tablePaddingVerticalMiddle:h,tablePaddingHorizontalMiddle:v,tablePaddingVerticalSmall:b,tablePaddingHorizontalSmall:y,tableBorderColor:x,tableHeaderTextColor:l,tableHeaderBg:a,tableFooterTextColor:S,tableFooterBg:C,tableHeaderCellSplitColor:$,tableHeaderSortBg:i,tableHeaderSortHoverBg:c,tableBodySortBg:s,tableFixedHeaderSortActiveBg:N,tableHeaderFilterActiveBg:O,tableFilterDropdownBg:I,tableRowHoverBg:d,tableSelectedRowBg:u,tableSelectedRowHoverBg:p,zIndexTableFixed:2,zIndexTableSticky:R(2).add(1).equal({unit:!1}),tableFontSizeMiddle:A,tableFontSizeSmall:k,tableSelectionColumnWidth:K,tableExpandIconBg:z,tableExpandColumnWidth:R(r).add(R(e.padding).mul(2)).equal(),tableExpandedRowBg:f,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:P,tableScrollThumbBgHover:t,tableScrollBg:n});return[Zl(M),Wl(M),Ql(M),Gl(M),_l(M),jl(M),ql(M),Hl(M),Ql(M),Ll(M),Xl(M),Fl(M),Yl(M),Dl(M),Ul(M),Vl(M),Jl(M)]}),(e=>{const{colorFillAlter:t,colorBgContainer:n,colorTextHeading:o,colorFillSecondary:r,colorFillContent:a,controlItemBgActive:l,controlItemBgActiveHover:i,padding:c,paddingSM:s,paddingXS:d,colorBorderSecondary:u,borderRadiusLG:p,controlHeight:f,colorTextPlaceholder:m,fontSize:g,fontSizeSM:h,lineHeight:v,lineWidth:b,colorIcon:y,colorIconHover:x,opacityLoading:C,controlInteractiveSize:S}=e,w=new Bl.Y(r).onBackground(n).toHexString(),E=new Bl.Y(a).onBackground(n).toHexString(),A=new Bl.Y(t).onBackground(n).toHexString(),k=new Bl.Y(y),$=new Bl.Y(x),N=S/2-b,O=2*N+3*b;return{headerBg:A,headerColor:o,headerSortActiveBg:w,headerSortHoverBg:E,bodySortBg:A,rowHoverBg:A,rowSelectedBg:l,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:s,cellPaddingInlineMD:d,cellPaddingBlockSM:d,cellPaddingInlineSM:d,borderColor:u,headerBorderRadius:p,footerBg:A,footerColor:o,cellFontSize:g,cellFontSizeMD:g,cellFontSizeSM:g,headerSplitColor:u,fixedHeaderSortActiveBg:w,headerFilterHoverBg:a,filterDropdownMenuBg:n,filterDropdownBg:n,expandIconBg:n,selectionColumnWidth:f,stickyScrollBarBg:m,stickyScrollBarBorderRadius:100,expandIconMarginTop:(g*v-3*b)/2-Math.ceil((1.4*h-3*b)/2),headerIconColor:k.clone().setA(k.a*C).toRgbString(),headerIconHoverColor:$.clone().setA($.a*C).toRgbString(),expandIconHalfInner:N,expandIconSize:O,expandIconScale:S/O}}),{unitless:{expandIconScale:!0}});const ti=[],ni=(e,t)=>{var n,r;const{prefixCls:a,className:l,rootClassName:i,style:c,size:s,bordered:d,dropdownPrefixCls:u,dataSource:p,pagination:f,rowSelection:m,rowKey:g="key",rowClassName:h,columns:v,children:b,childrenColumnName:y,onChange:x,getPopupContainer:C,loading:S,expandIcon:w,expandable:E,expandedRowRender:A,expandIconColumnIndex:k,indentSize:$,scroll:N,sortDirections:O,locale:I,showSorterTooltip:z={target:"full-header"},virtual:K}=e;(0,_n.rJ)("Table");const B=o.useMemo((()=>v||Ct(b)),[v,b]),j=o.useMemo((()=>B.some((e=>e.responsive))),[B]),D=(0,Io.A)(j),L=o.useMemo((()=>{const e=new Set(Object.keys(D).filter((e=>D[e])));return B.filter((t=>!t.responsive||t.responsive.some((t=>e.has(t)))))}),[B,D]),H=(0,R.A)(e,["className","style","columns"]),{locale:_=zo.A,direction:F,table:W,renderEmpty:q,getPrefixCls:V,getPopupContainer:X}=o.useContext(M.QO),U=(0,T.A)(s),G=Object.assign(Object.assign({},_.Table),I),Y=p||ti,Q=V("table",a),J=V("dropdown",u),[,Z]=(0,Zo.Ay)(),ee=(0,qn.A)(Q),[te,ne,oe]=ei(Q,ee),re=Object.assign(Object.assign({childrenColumnName:y,expandIconColumnIndex:k},E),{expandIcon:null!==(n=null==E?void 0:E.expandIcon)&&void 0!==n?n:null===(r=null==W?void 0:W.expandable)||void 0===r?void 0:r.expandIcon}),{childrenColumnName:ae="children"}=re,le=o.useMemo((()=>Y.some((e=>null==e?void 0:e[ae]))?"nest":A||(null==E?void 0:E.expandedRowRender)?"row":null),[Y]),ie={body:o.useRef(null)},se=function(e){return(t,n)=>{const o=t.querySelector(`.${e}-container`);let r=n;if(o){const e=getComputedStyle(o);r=n-parseInt(e.borderLeftWidth,10)-parseInt(e.borderRightWidth,10)}return r}}(Q),de=o.useRef(null),ue=o.useRef(null);ko(t,(()=>Object.assign(Object.assign({},ue.current),{nativeElement:de.current})));const pe=o.useMemo((()=>"function"==typeof g?g:e=>null==e?void 0:e[g]),[g]),[fe]=fl(Y,ae,pe),me={},ge=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var o,r,a,l;const i=Object.assign(Object.assign({},me),e);n&&(null===(o=me.resetPagination)||void 0===o||o.call(me),(null===(r=i.pagination)||void 0===r?void 0:r.current)&&(i.pagination.current=1),f&&(null===(a=f.onChange)||void 0===a||a.call(f,1,null===(l=i.pagination)||void 0===l?void 0:l.pageSize))),N&&!1!==N.scrollToFirstRowOnChange&&ie.body.current&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{getContainer:n=()=>window,callback:o,duration:r=450}=t,a=n(),l=No(a),i=Date.now(),c=()=>{const t=Date.now()-i,n=function(e,t,n,o){const r=n-t;return(e/=o/2)<1?r/2*e*e*e+t:r/2*((e-=2)*e*e+2)+t}(t>r?r:t,l,e,r);$o(a)?a.scrollTo(window.pageXOffset,n):a instanceof Document||"HTMLDocument"===a.constructor.name?a.documentElement.scrollTop=n:a.scrollTop=n,t<r?(0,zt.A)(c):"function"==typeof o&&o()};(0,zt.A)(c)}(0,{getContainer:()=>ie.body.current}),null==x||x(i.pagination,i.filters,i.sorter,{currentDataSource:dl(zl(Y,i.sorterStates,ae),i.filterStates,ae),action:t})},[he,ve,be,ye]=Kl({prefixCls:Q,mergedColumns:L,onSorterChange:(e,t)=>{ge({sorter:e,sorterStates:t},"sort",!1)},sortDirections:O||["ascend","descend"],tableLocale:G,showSorterTooltip:z}),xe=o.useMemo((()=>zl(Y,ve,ae)),[Y,ve]);me.sorter=ye(),me.sorterStates=ve;const[Ce,Se,we]=pl({prefixCls:Q,locale:G,dropdownPrefixCls:J,mergedColumns:L,onFilterChange:(e,t)=>{ge({filters:e,filterStates:t},"filter",!0)},getPopupContainer:C||X,rootClassName:P()(i,ee)}),Ee=dl(xe,Se,ae);me.filters=we,me.filterStates=Se;const Ae=o.useMemo((()=>{const e={};return Object.keys(we).forEach((t=>{null!==we[t]&&(e[t]=we[t])})),Object.assign(Object.assign({},be),{filters:e})}),[be,we]),[ke]=Rl(Ae),[$e,Ne]=hl(Ee.length,((e,t)=>{ge({pagination:Object.assign(Object.assign({},me.pagination),{current:e,pageSize:t})},"paginate")}),f);me.pagination=!1===f?{}:function(e,t){const n={current:e.current,pageSize:e.pageSize},o=t&&"object"==typeof t?t:{};return Object.keys(o).forEach((t=>{const o=e[t];"function"!=typeof o&&(n[t]=o)})),n}($e,f),me.resetPagination=Ne;const Oe=o.useMemo((()=>{if(!1===f||!$e.pageSize)return Ee;const{current:e=1,total:t,pageSize:n=gl}=$e;return Ee.length<t?Ee.length>n?Ee.slice((e-1)*n,e*n):Ee:Ee.slice((e-1)*n,e*n)}),[!!f,Ee,null==$e?void 0:$e.current,null==$e?void 0:$e.pageSize,null==$e?void 0:$e.total]),[Ie,ze]=Ao({prefixCls:Q,data:Ee,pageData:Oe,getRowKey:pe,getRecordByKey:fe,expandType:le,childrenColumnName:ae,locale:G,getPopupContainer:C||X},m);re.__PARENT_RENDER_ICON__=re.expandIcon,re.expandIcon=re.expandIcon||w||xr(G),"nest"===le&&void 0===re.expandIconColumnIndex?re.expandIconColumnIndex=m?1:0:re.expandIconColumnIndex>0&&m&&(re.expandIconColumnIndex-=1),"number"!=typeof re.indentSize&&(re.indentSize="number"==typeof $?$:15);const Ke=o.useCallback((e=>ke(Ie(Ce(he(e))))),[he,Ce,Ie]);let Pe,Re,Me;if(!1!==f&&(null==$e?void 0:$e.total)){let e;e=$e.size?$e.size:"small"===U||"middle"===U?"small":void 0;const t=t=>o.createElement(br,Object.assign({},$e,{className:P()(`${Q}-pagination ${Q}-pagination-${t}`,$e.className),size:e})),n="rtl"===F?"left":"right",{position:r}=$e;if(null!==r&&Array.isArray(r)){const e=r.find((e=>e.includes("top"))),o=r.find((e=>e.includes("bottom"))),a=r.every((e=>"none"==`${e}`));e||o||a||(Re=t(n)),e&&(Pe=t(e.toLowerCase().replace("top",""))),o&&(Re=t(o.toLowerCase().replace("bottom","")))}else Re=t(n)}"boolean"==typeof S?Me={spinning:S}:"object"==typeof S&&(Me=Object.assign({spinning:!0},S));const Te=P()(oe,ee,`${Q}-wrapper`,null==W?void 0:W.className,{[`${Q}-wrapper-rtl`]:"rtl"===F},l,i,ne),Be=Object.assign(Object.assign({},null==W?void 0:W.style),c),je=void 0!==(null==I?void 0:I.emptyText)?I.emptyText:(null==q?void 0:q("Table"))||o.createElement(Oo.A,{componentName:"Table"}),De=K?Tl:Ml,Le={},He=o.useMemo((()=>{const{fontSize:e,lineHeight:t,lineWidth:n,padding:o,paddingXS:r,paddingSM:a}=Z,l=Math.floor(e*t);switch(U){case"middle":return 2*a+l+n;case"small":return 2*r+l+n;default:return 2*o+l+n}}),[Z,U]);return K&&(Le.listItemHeight=He),te(o.createElement("div",{ref:de,className:Te,style:Be},o.createElement(yr.A,Object.assign({spinning:!1},Me),Pe,o.createElement(De,Object.assign({},Le,H,{ref:ue,columns:L,direction:F,expandable:re,prefixCls:Q,className:P()({[`${Q}-middle`]:"middle"===U,[`${Q}-small`]:"small"===U,[`${Q}-bordered`]:d,[`${Q}-empty`]:0===Y.length},oe,ee,ne),data:Oe,rowKey:pe,rowClassName:(e,t,n)=>{let o;return o="function"==typeof h?P()(h(e,t,n)):P()(h),P()({[`${Q}-row-selected`]:ze.has(pe(e,t))},o)},emptyText:je,internalHooks:ce,internalRefs:ie,transformColumns:Ke,getContainerWidth:se})),Re)))};var oi=o.forwardRef(ni);const ri=(e,t)=>{const n=o.useRef(0);return n.current+=1,o.createElement(oi,Object.assign({},e,{ref:t,_renderTimes:n.current}))},ai=o.forwardRef(ri);ai.SELECTION_COLUMN=yo,ai.EXPAND_COLUMN=ie,ai.SELECTION_ALL=xo,ai.SELECTION_INVERT=Co,ai.SELECTION_NONE=So,ai.Column=ln,ai.ColumnGroup=cn,ai.Summary=qe;var li=ai,ii=n(8188),ci=n(2708),si=n(697);const di=o.createContext(null),ui=e=>{let{index:t,...n}=e;const[r]=O.A.useForm();return o.createElement(O.A,{form:r,component:!1},o.createElement(di.Provider,{value:r},o.createElement("tr",n)))},pi=e=>{let{title:t,editable:n,children:r,dataIndex:a,record:l,handleSave:i,inputType:c,options:s,...d}=e;const{0:u,1:p}=(0,o.useState)(!1),f=(0,o.useRef)(null),m=(0,o.useRef)(null),g=(0,o.useContext)(di);(0,o.useEffect)((()=>{var e,t;u&&("select"===c?null===(e=m.current)||void 0===e||e.focus():null===(t=f.current)||void 0===t||t.focus())}),[u,c]);const h=()=>{p(!u),g.setFieldsValue({[a]:l[a]})},v=async()=>{try{const e=await g.validateFields();h(),i({...l,...e})}catch(e){console.log("Save failed:",e)}};let b=r;var y;n&&(b=u?o.createElement(O.A.Item,{style:{margin:0},name:a,rules:"name"===a?[{required:!0,message:`${t} is required.`}]:[]},"text"===c?o.createElement(I.A,{ref:f,onPressEnter:v,onBlur:v}):"select"===c?o.createElement(z.A,{ref:m,options:s,onBlur:v,onChange:()=>setTimeout(v,100),open:u,style:{width:"100%"}}):"password"===c?o.createElement(I.A.Password,{ref:f,onPressEnter:v,onBlur:v,visibilityToggle:!0}):o.createElement(I.A,{ref:f,onPressEnter:v,onBlur:v})):o.createElement("div",{className:"editable-cell-value-wrap group cursor-pointer",style:{paddingRight:24,minHeight:20},onClick:h},o.createElement("div",{className:"flex items-center"},o.createElement("span",{className:"flex-grow"},"password"===c?"••••••••":"select"===c&&s?(null===(y=s.find((e=>e.value===r)))||void 0===y?void 0:y.label)||r:r||" "),o.createElement(ii.A,{className:"w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity"}))));return o.createElement("td",d,b)},fi=e=>{let{serverSettings:t,loading:n,userId:r,initializeSettings:a}=e;const{0:l,1:i}=(0,o.useState)([]),{0:c,1:s}=(0,o.useState)(!1),[d,p]=h.Ay.useMessage();(0,o.useEffect)((()=>{if(t){const e=t.config.environment.map(((e,t)=>({key:`${t}`,name:e.name,value:e.value,type:e.type})));i(e),s(!1)}}),[t]);const f=async e=>{const t=(0,N.A)(l),n=t.findIndex((t=>e.key===t.key)),o=t[n];t.splice(n,1,{...o,...e}),i(t),s(!0)},m=(["string","number","boolean","secret"].map((e=>({value:e,label:e.charAt(0).toUpperCase()+e.slice(1)}))),[{title:"Name",dataIndex:"name",width:"55%",editable:!0,inputType:"text"},{title:"Value",dataIndex:"value",width:"45%",editable:!0,inputType:"password"},{title:"Actions",dataIndex:"operation",render:(e,t)=>o.createElement(u.A,{title:"Delete variable"},o.createElement(v.Ay,{type:"text",danger:!0,icon:o.createElement(ci.A,{className:"w-4 h-4"}),onClick:()=>(async e=>{const t=l.filter((t=>t.key!==e));i(t),s(!0)})(t.key)}))}].map((e=>e.editable?{...e,onCell:t=>({record:t,editable:e.editable,dataIndex:e.dataIndex,title:e.title,inputType:e.inputType,options:e.options,handleSave:f})}:e))),g={body:{row:ui,cell:pi}};return o.createElement(le,{className:"shadow-sm"},p,o.createElement("div",{className:"flex justify-between items-center mb-4"},o.createElement("h3",{className:"text-lg font-medium"},"Environment Variables"),o.createElement("div",{className:"space-x-2 inline-flex"},o.createElement(u.A,{title:"Add new variable"},o.createElement(v.Ay,{type:"default",icon:o.createElement(si.A,{className:"w-4 h-4"}),onClick:async()=>{const e={key:`${l.length+1}`,name:"",value:"",type:"string"},t=[].concat((0,N.A)(l),[e]);i(t),s(!0)}},"Add")),o.createElement(u.A,{title:c?"Save your changes":"No unsaved changes"},o.createElement(v.Ay,{type:"primary",icon:o.createElement("div",{className:"relative"},o.createElement(y.A,{className:"w-4 h-4"}),c&&o.createElement("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"})),onClick:async()=>{try{const e=l.map((e=>({name:e.name,value:e.value,type:e.type,required:!1})));if(!t)return void d.error("Settings not loaded yet");const n={...t,config:{...t.config,environment:e}},o={id:n.id,config:n.config,user_id:r};await C.Y.updateSettings(o,r),await a(r),s(!1),d.success("Environment variables saved successfully")}catch(e){console.error("Failed to save settings:",e),d.error("Failed to save environment variables")}},disabled:!c},"Save")))),o.createElement(li,{components:g,rowClassName:()=>"editable-row",bordered:!0,dataSource:l,columns:m,pagination:!1,loading:n}),o.createElement("div",{className:"mt-6 pt-4 border-t border-secondary"},o.createElement("p",{className:"text-xs text-secondary"},o.createElement(x.A,{strokeWidth:1.5,className:"inline-block mr-1 h-4 w-4"})," ","Note: Environment variables are currently available to all processes on the server.")))},mi=()=>{const{0:e,1:t}=(0,o.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("settingsSidebar");return null===e||JSON.parse(e)}return!0})),{user:n}=(0,o.useContext)(g.v),r=(null==n?void 0:n.id)||"",{serverSettings:l,resetUISettings:u,initializeSettings:p,isLoading:f}=(0,d.C)();if((0,o.useEffect)((()=>{r&&p(r)}),[r,p]),(0,o.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("settingsSidebar",JSON.stringify(e))}),[e]),f)return o.createElement("div",{className:"p-8 text-center"},"Loading settings...");const h=(null==l?void 0:l.config.default_model_client)||{provider:"openai",component_type:"model",label:"Default Model Client",description:"Default model client for this environment",config:{model:"gpt-3.5-turbo",temperature:.7,max_tokens:1e3}},v=[{key:"ui",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(i,{className:"w-4 h-4"}),"UI Settings"),children:o.createElement(w,{userId:r})},{key:"model",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(c.A,{className:"w-4 h-4"}),"Default Model"),children:o.createElement("div",{className:"mt-4"},o.createElement($,{modelComponent:h,onModelUpdate:async()=>{}}))},{key:"environment",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(s,{className:"w-4 h-4"}),"Environment Variables"),children:o.createElement("div",{className:"mt-4"},o.createElement(fi,{serverSettings:l,loading:!1,userId:r,initializeSettings:p}))}];return o.createElement("div",{className:"relative flex h-full w-full"},o.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(e?"w-64":"w-12")},o.createElement(m,{isOpen:e,sections:[{id:"settings",title:"Settings",icon:s,content:()=>o.createElement(o.Fragment,null)}],currentSection:{id:"settings",title:"Settings",icon:s,content:()=>o.createElement(o.Fragment,null)},onToggle:()=>t(!e),onSelectSection:()=>{}})),o.createElement("div",{className:"flex-1 transition-all max-w-5xl -mr-6 duration-200 "+(e?"ml-64":"ml-12")},o.createElement("div",{className:"p-4 pt-2"},o.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},o.createElement("span",{className:"text-primary font-medium"},"Settings")),o.createElement("div",{className:"flex items-center gap-2 mb-8 text-sm"},o.createElement("span",{className:"text-secondary"},"Manage your settings and preferences")),o.createElement("div",{className:"  rounded-lg shadow-sm"},o.createElement(a.A,{defaultActiveKey:"ui",items:v,size:"large",className:"settings-tabs"})))))};var gi=e=>{let{data:t}=e;return o.createElement(r.A,{meta:t.site.siteMetadata,title:"Home",link:"/settings"},o.createElement("main",{style:{height:"100%"},className:" h-full "},o.createElement(mi,null)))}},8603:function(e,t,n){n.d(t,{A:function(){return h}});var o=n(7550),r=n(6540),a=n(2318),l=n(6942),i=n.n(l),c=n(2941),s=n(2279),d=n(2702),u=n(6327),p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const f=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:l}=r.useContext(s.QO),{prefixCls:f,type:m="default",danger:g,disabled:h,loading:v,onClick:b,htmlType:y,children:x,className:C,menu:S,arrow:w,autoFocus:E,overlay:A,trigger:k,align:$,open:N,onOpenChange:O,placement:I,getPopupContainer:z,href:K,icon:P=r.createElement(a.A,null),title:R,buttonsRender:M=e=>e,mouseEnterDelay:T,mouseLeaveDelay:B,overlayClassName:j,overlayStyle:D,destroyPopupOnHide:L,dropdownRender:H}=e,_=p(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),F=n("dropdown",f),W=`${F}-button`,q={menu:S,arrow:w,autoFocus:E,align:$,disabled:h,trigger:h?[]:k,onOpenChange:O,getPopupContainer:z||t,mouseEnterDelay:T,mouseLeaveDelay:B,overlayClassName:j,overlayStyle:D,destroyPopupOnHide:L,dropdownRender:H},{compactSize:V,compactItemClassnames:X}=(0,u.RQ)(F,l),U=i()(W,X,C);"overlay"in e&&(q.overlay=A),"open"in e&&(q.open=N),q.placement="placement"in e?I:"rtl"===l?"bottomLeft":"bottomRight";const G=r.createElement(c.Ay,{type:m,danger:g,disabled:h,loading:v,onClick:b,htmlType:y,href:K,title:R},x),Y=r.createElement(c.Ay,{type:m,danger:g,icon:P}),[Q,J]=M([G,Y]);return r.createElement(d.A.Compact,Object.assign({className:U,size:V,block:!0},_),Q,r.createElement(o.A,Object.assign({},q),J))};f.__ANT_BUTTON=!0;var m=f;const g=o.A;g.Button=m;var h=g},9492:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},9777:function(e,t,n){n.d(t,{F:function(){return l}});var o=n(998),r=function(e){if((0,o.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some((function(e){return e in n.style}))}return!1},a=function(e,t){if(!r(e))return!1;var n=document.createElement("div"),o=n.style[e];return n.style[e]=t,n.style[e]!==o};function l(e,t){return Array.isArray(e)||void 0===t?r(e):a(e,t)}}}]);
//# sourceMappingURL=component---src-pages-settings-tsx-10244ca44e6bbe7613a0.js.map