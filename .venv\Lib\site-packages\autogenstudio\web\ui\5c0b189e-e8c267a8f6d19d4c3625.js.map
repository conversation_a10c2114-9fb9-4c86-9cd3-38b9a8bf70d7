{"version": 3, "file": "5c0b189e-e8c267a8f6d19d4c3625.js", "mappings": "6SAiCA,MAAMA,EAAY,IACb,iBAICC,EADqBD,EAAUE,oBACgB,CAACC,GAAMA,KAC5D,SAASC,EAAeC,GACtB,MAAMC,EAAM,UAAa,KACnB,CAAwC,IAO9C,OAHAL,GAAuB,KACrBK,EAAIC,QAAUF,CAAQ,IAEjB,eAAkB,WACvB,IAAK,IAAIG,EAAOC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,GAAOK,EAAO,EAAGA,EAAOL,EAAMK,IAC/EF,EAAKE,GAAQJ,UAAUI,GAEzB,OAAsB,MAAfP,EAAIC,aAAkB,EAASD,EAAIC,WAAWI,EACvD,GAAG,GACL,CAEA,MAAMG,EAAW,UACXC,EAAa,YACbC,EAAa,YACbC,EAAc,aA8OpB,IAAIC,EAA4B,oBAAbC,SAA2B,EAAAC,gBAAkB,EAAAC,UAsIhE,MAIMC,EAAiB,CAACN,EAAYC,GAC9BM,EAAe,CAACT,EAAUC,GAuLhC,IAAIS,GAAwB,EACxBC,EAAQ,EACZ,MAAMC,EAAQ,IAEd,eAAiBC,KAAKC,SAASC,SAAS,IAAIC,MAAM,EAAG,GAAKL,IAc1D,MAQMM,EARa/B,EAAU+B,OAb7B,WACE,MAAOC,EAAIC,GAAS,YAAe,IAAMT,EAAwBE,SAAUQ,IAU3E,OATAhB,GAAM,KACM,MAANc,GACFC,EAAMP,IACR,GAEC,IACH,aAAgB,KACdF,GAAwB,CAAI,GAC3B,IACIQ,CACT,EAwJA,SAASG,IACP,MAAMC,EAAM,IAAIC,IAChB,MAAO,CACL,IAAAC,CAAKC,EAAOC,GACV,IAAIC,EAC2B,OAA9BA,EAAWL,EAAIM,IAAIH,KAAmBE,EAASE,SAAQC,GAAWA,EAAQJ,IAC7E,EACA,EAAAK,CAAGN,EAAOO,GACRV,EAAIW,IAAIR,EAAO,IAAKH,EAAIM,IAAIH,IAAU,GAAKO,GAC7C,EACA,GAAAE,CAAIT,EAAOO,GACT,IAAIG,EACJb,EAAIW,IAAIR,GAAwC,OAA/BU,EAAYb,EAAIM,IAAIH,SAAkB,EAASU,EAAUC,QAAOC,GAAKA,IAAML,MAAc,GAC5G,EAEJ,CAEA,MAAMM,EAAmC,gBAAoB,MACvDC,EAAmC,gBAAoB,MAMvDC,EAA0B,KAC9B,IAAIC,EACJ,OAAuE,OAA9DA,EAAoB,aAAiBH,SAAgC,EAASG,EAAkBvB,KAAO,IAAI,EAMhHwB,EAAkB,IAAM,aAAiBH,GA89B/C,MAEMI,EAAsB,6BAynC5B,SAASC,EAAYC,QACH,IAAZA,IACFA,EAAU,CAAC,GAEb,MAAM,OACJC,GACED,EACEE,EAzDR,SAAgCF,GAC9B,MAAM,KACJG,GAAO,EACPC,aAAcC,EACdC,SAAUC,GACRP,EACEQ,EAAapC,IACbqC,EAAU,SAAa,CAAC,IACvBC,GAAU,YAAe,IAAMlC,MAChCmC,EAAsC,MAA7BhB,KAORiB,EAAmBC,GAAwB,WAAeN,EAAaO,WACxEV,EAAe3D,GAAe,CAAC0D,EAAMvB,EAAOmC,KAChDN,EAAQ7D,QAAQoE,UAAYb,EAAOvB,OAAQL,EAC3CmC,EAAO/B,KAAK,aAAc,CACxBwB,OACAvB,QACAmC,SACAJ,WAEkB,MAApBN,GAA4BA,EAAiBF,EAAMvB,EAAOmC,EAAO,IAE7DE,EAAO,WAAc,KAAM,CAC/BJ,0BACE,IACEP,EAAW,WAAc,KAAM,CACnCQ,UAAWF,GAAqBL,EAAaO,WAAa,KAC1DI,SAAUX,EAAaW,UAAY,KACnCC,aAAcZ,EAAaO,aACzB,CAACF,EAAmBL,EAAaO,UAAWP,EAAaW,WAC7D,OAAO,WAAc,KAAM,CACzBT,UACAN,OACAC,eACAE,WACAI,SACAF,aACAS,UACE,CAACd,EAAMC,EAAcE,EAAUI,EAAQF,EAAYS,GACzD,CAa8BG,CAAuB,IAC9CpB,EACHM,SAAU,CACRQ,UAAW,KACXI,SAAU,QACPlB,EAAQM,YAGTe,EAAcrB,EAAQqB,aAAenB,EACrCoB,EAAmBD,EAAYf,UAC9BiB,EAAeC,GAAmB,WAAe,OACjDZ,EAAmBa,GAAyB,WAAe,MAE5DN,GADyC,MAApBG,OAA2B,EAASA,EAAiBH,eACrCI,EACrCG,EAAkB,SAAa,MAC/BC,EAAO9B,IACbtC,GAAM,KACA4D,IACFO,EAAgB9E,QAAUuE,EAC5B,GACC,CAACA,IACJ,MAAMS,GAAW,QAAc,IAC1B5B,EACHM,SAAU,IACLgB,KACCV,GAAqB,CACvBE,UAAWF,MAIXC,EAAuB,eAAkBgB,IAC7C,MAAMC,GAA4B,QAAUD,GAAQ,CAClDE,sBAAuB,IAAMF,EAAKE,wBAClCC,eAAgBH,GACdA,EAGJJ,EAAsBK,GACtBF,EAASX,KAAKgB,aAAaH,EAA0B,GACpD,CAACF,EAASX,OACPgB,EAAe,eAAkBJ,MACjC,QAAUA,IAAkB,OAATA,KACrBH,EAAgB9E,QAAUiF,EAC1BL,EAAgBK,MAKd,QAAUD,EAASX,KAAKH,UAAUlE,UAAgD,OAApCgF,EAASX,KAAKH,UAAUlE,SAIjE,OAATiF,KAAkB,QAAUA,KAC1BD,EAASX,KAAKgB,aAAaJ,EAC7B,GACC,CAACD,EAASX,OACPA,EAAO,WAAc,KAAM,IAC5BW,EAASX,KACZgB,eACApB,uBACAM,aAAcO,KACZ,CAACE,EAASX,KAAMgB,EAAcpB,IAC5BP,EAAW,WAAc,KAAM,IAChCsB,EAAStB,SACZa,aAAcA,KACZ,CAACS,EAAStB,SAAUa,IAClBe,EAAU,WAAc,KAAM,IAC/BN,KACAP,EACHJ,OACAX,WACAL,YACE,CAAC2B,EAAUX,EAAMX,EAAUL,EAAQoB,IAQvC,OAPA9D,GAAM,KACJ8D,EAAYZ,QAAQ7D,QAAQuF,gBAAkBD,EAC9C,MAAML,EAAe,MAARF,OAAe,EAASA,EAAKS,SAASxF,QAAQyF,MAAKR,GAAQA,EAAKxD,KAAO4B,IAChF4B,IACFA,EAAKK,QAAUA,EACjB,IAEK,WAAc,KAAM,IACtBN,EACHM,UACAjB,OACAX,cACE,CAACsB,EAAUX,EAAMX,EAAU4B,GACjC,CAkIA,MAAMI,EAAa,SACbC,EAAe,WACrB,SAASC,EAAWC,EAAWC,EAAWC,GACxC,MAAMlE,EAAM,IAAIC,IACVkE,EAAwB,SAAfD,EACf,IAAIE,EAAeJ,EACnB,GAAIG,GAAUH,EAAW,CACvB,MACE,CAACH,GAAaQ,EACd,CAACP,GAAeQ,KACbC,GACDP,EACJI,EAAeG,CACjB,CACA,MAAO,IACc,aAAfL,GAA6B,CAC/BM,UAAW,EACX,CAACnD,GAAsB,OAEtB+C,KACAH,EAAUjE,KAAIyE,IACf,MAAMC,EAAkBD,EAAQA,EAAMP,GAAc,KACpD,MAA+B,mBAApBQ,EACFV,EAAYU,EAAgBV,GAAa,KAE3CU,CAAe,IACrBC,OAAOX,GAAWY,QAAO,CAACC,EAAKC,IAC3BA,GAGLC,OAAOC,QAAQF,GAAOvE,SAAQ0E,IAC5B,IAAKC,EAAKT,GAASQ,EASf,IAAI5E,EARJ8D,GAAU,CAACN,EAAYC,GAAcqB,SAASD,KAGxB,IAAtBA,EAAIE,QAAQ,OACTpF,EAAIqF,IAAIH,IACXlF,EAAIW,IAAIuE,EAAK,IAEM,mBAAVT,IAEoB,OAA5BpE,EAAWL,EAAIM,IAAI4E,KAAiB7E,EAASiF,KAAKb,GACnDI,EAAIK,GAAO,WAET,IADA,IAAIrE,EACKzC,EAAOC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,GAAOK,EAAO,EAAGA,EAAOL,EAAMK,IAC/EF,EAAKE,GAAQJ,UAAUI,GAEzB,OAAqC,OAA7BoC,EAAYb,EAAIM,IAAI4E,SAAgB,EAASrE,EAAUb,KAAIjC,GAAMA,KAAMQ,KAAOqF,MAAK2B,QAAezF,IAARyF,GACpG,IAGFV,EAAIK,GAAOT,EACb,IAEKI,GA1BEA,GA2BR,CAAC,GAER,CAOA,SAASW,EAAgBvB,QACL,IAAdA,IACFA,EAAY,IAEd,MAAMwB,EAAgBxB,EAAUjE,KAAIkF,GAAc,MAAPA,OAAc,EAASA,EAAI7C,YAChEqD,EAAezB,EAAUjE,KAAIkF,GAAc,MAAPA,OAAc,EAASA,EAAIzC,WAC/DkD,EAAW1B,EAAUjE,KAAIkF,GAAc,MAAPA,OAAc,EAASA,EAAIU,OAC3DC,EAAoB,eAAkB7B,GAAaD,EAAWC,EAAWC,EAAW,cAE1FwB,GACMK,EAAmB,eAAkB9B,GAAaD,EAAWC,EAAWC,EAAW,aAEzFyB,GACMK,EAAe,eAAkB/B,GAAaD,EAAWC,EAAWC,EAAW,SAErF0B,GACA,OAAO,WAAc,KAAM,CACzBE,oBACAC,mBACAC,kBACE,CAACF,EAAmBC,EAAkBC,GAC5C,CA07BA,SAASC,EAAgCC,EAAOC,GAC9C,MAAO,IACFD,EACHE,MAAO,IACFF,EAAME,MACT1D,SAAU,IACLwD,EAAME,MAAM1D,SACfyD,WAIR,CAMA,MAAME,EAAQtB,IAAS,CACrBuB,KAAM,QACN9E,QAASuD,EACT,QAAM/G,CAAGkI,GACP,MAAM,QACJK,EAAO,YACPC,EAAW,iBACXC,EACAC,OAAQC,EAAc,EAAC,MACvB5H,EAAQ,EAAC,gBACT6H,EAAkB,EAAC,2BACnBC,EAA6B,EAAC,UAC9BC,KACGC,IACD,QAAShC,EAAOmB,IACd,MACJE,EACAtE,UAAU,SACRY,IAEAwD,EACEL,EAAOU,EAAQnI,QAAQW,GACvBiI,GAAyB,MAAbF,OAAoB,EAASA,EAAU1I,UAAYsE,EAQ/DuE,EAAYvE,EAASuE,WAAaD,EAASC,UAC3CC,EAA4C,IAAvBxE,EAASuE,UAC9BE,EAA4C,IAAvBH,EAASC,UAC9BG,EAAqB1E,IAAasE,EAMxC,IAAKnB,EACH,MAAO,CAAC,EAEV,MAAMwB,EAAW,IACZnB,WACO,SAAQL,EAAKyB,UAAY5E,EAASuE,UAAYb,EAAM9D,UAAU6D,OAAS,EAAIN,EAAK0B,aAAe,EAAIZ,GAAa3I,GAAGkI,IAEzHsB,QAAiB,QAAevB,EAAgCoB,EAAUL,EAASS,aAAeR,EAAYvE,EAASuE,WAAYF,GACnIW,QAAoB,QAAeL,EAAU,IAC9CN,EACHY,eAAgB,cAEZC,GAAQ,QAAI,EAAGJ,EAASK,KACxBC,EAAQT,EAASU,EAAIH,EAGrBI,GAFehB,EAASS,aAAeT,EAASiB,aACvBC,GAAKA,EAAI,OACd,QAAI,EAAGlB,EAASS,cAAgBP,GAAsBE,GAAsBD,EAAiC,EAAZF,EAAgB,GAAKW,GAAQ,QAAI,EAAGJ,EAASW,UAKxK,GAJAnB,EAASoB,MAAMJ,UAAYA,EAAY,KACvChB,EAASqB,UAAYT,EAGjBnB,EAAkB,CACpB,MAAM6B,EAAiBtB,EAASO,aAAe1B,EAAK0B,cAAe,QAAIX,EAAiBL,EAAQnI,QAAQG,QAAU,GAAKmJ,EAAYG,MAAQhB,GAA8Ba,EAAYS,SAAWtB,EAChM,aAAmB,IAAMJ,EAAiB6B,IAC5C,CAOA,OANI9B,IACFA,EAAYpI,cAAgB,QAAe6H,EAAgC,IACtEoB,EACHU,EAAGD,GACFd,EAASO,aAAeN,EAAYvE,EAASuE,WAAYF,IAEvD,CACLgB,EAAGD,EAEP,IAOF,SAASS,EAAe7E,EAASqB,GAC/B,MAAM,KACJpD,EAAI,SACJG,GACE4B,GACE,QACJ8E,GAAU,EAAI,YACdhC,EAAW,UACXM,EACA2B,SAAUC,GACR3D,EACE0D,EAAWxK,EAAeyK,GAC1BC,EAAyB,UAAa,GACtCC,EAAmB,SAAa,MAChCC,EAAqB,SAAa,MACxC,aAAgB,KACd,IAAKL,EAAS,OACd,SAASM,EAAQC,GACf,GAAIA,EAAEC,UAAYC,GAA6B,MAAvBzC,EAAYpI,QAClC,OAEF,MAAM8K,EAAKH,EAAEI,OACPC,EAAU5C,EAAYpI,QAAQyJ,MAAQ,GACtCwB,EAAa7C,EAAYpI,QAAQ+J,SAAW,GAC5CmB,EAAkBL,EAAGxB,aAAewB,EAAGhB,aACvCsB,EAAOL,EAAK,GAAK,EAAI,EACrBM,EAASN,EAAK,EAAI,MAAQ,MAC5BD,EAAGxB,cAAgBwB,EAAGhB,gBAGrBmB,GAAWF,EAAK,IAAMG,GAAcH,EAAK,GAC5CH,EAAEU,iBACF,aAAmB,KACjBhB,GAASiB,GAAKA,EAAIlK,KAAKgK,GAAQN,EAAII,EAAkBC,IAAM,KAEpD,WAAWI,MAAK,aAGzBV,EAAGZ,WAAaa,GAEpB,CACA,MAAMD,GAAmB,MAAbnC,OAAoB,EAASA,EAAU1I,UAAY0D,EAASY,SACxE,OAAIf,GAAQsH,GACVA,EAAGW,iBAAiB,QAASd,GAG7Be,uBAAsB,KACpBjB,EAAiBxK,QAAU6K,EAAGZ,UACH,MAAvB7B,EAAYpI,UACdyK,EAAmBzK,QAAU,IACxBoI,EAAYpI,SAEnB,IAEK,KACLwK,EAAiBxK,QAAU,KAC3ByK,EAAmBzK,QAAU,KAC7B6K,EAAGa,oBAAoB,QAAShB,EAAQ,QAf5C,CAiBA,GACC,CAACN,EAAS7G,EAAMG,EAASY,SAAU8D,EAAaM,EAAW2B,IAC9D,MAAM/F,EAAW,WAAc,KAAM,CACnC,SAAAqH,GACEpB,EAAuBvK,SAAU,CACnC,EACA,OAAA0K,GACEH,EAAuBvK,SAAU,CACnC,EACA,aAAA4L,GACErB,EAAuBvK,SAAU,CACnC,EACA,QAAA6L,GACE,MAAMhB,GAAmB,MAAbnC,OAAoB,EAASA,EAAU1I,UAAY0D,EAASY,SACxE,GAAK8D,EAAYpI,SAAY6K,GAAON,EAAuBvK,QAA3D,CAGA,GAAiC,OAA7BwK,EAAiBxK,QAAkB,CACrC,MAAM8L,EAAajB,EAAGZ,UAAYO,EAAiBxK,SAC/CoI,EAAYpI,QAAQ+J,QAAU,IAAO+B,GAAc,GAAK1D,EAAYpI,QAAQyJ,KAAO,IAAOqC,EAAa,IACzG,aAAmB,IAAMzB,GAASiB,GAAKA,EAAIQ,KAE/C,CAGAL,uBAAsB,KACpBjB,EAAiBxK,QAAU6K,EAAGZ,SAAS,GAVzC,CAYF,KACE,CAACvG,EAASY,SAAU+F,EAAUjC,EAAaM,IAC/C,OAAO,WAAc,IAAM0B,EAAU,CACnC9F,YACE,CAAC,GAAG,CAAC8F,EAAS9F,GACpB,C", "sources": ["webpack://autogentstudio/./node_modules/@floating-ui/react/dist/floating-ui.react.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect, useEffect, useRef } from 'react';\nimport { stopEvent, getDocument, isMouseLikePointerType, contains, activeElement, isSafari, isTypeableCombobox, isVirtualClick, isVirtualPointerEvent, getTarget, getPlatform, isTypeableElement, isReactEvent, isRootElement, isEventTargetWithin, isMac, getUserAgent } from '@floating-ui/react/utils';\nimport { floor, evaluate, max, min, round } from '@floating-ui/utils';\nimport { getComputedStyle, isElement, getNodeName, isHTMLElement, getWindow, isLastTraversableNode, getParentNode, isWebKit } from '@floating-ui/utils/dom';\nimport { tabbable, isTabbable } from 'tabbable';\nimport * as ReactDOM from 'react-dom';\nimport { getOverflowAncestors, useFloating as useFloating$1, offset, detectOverflow } from '@floating-ui/react-dom';\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/react-dom';\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/react-utils#usemergerefs\n */\nfunction useMergeRefs(refs) {\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      refs.forEach(ref => {\n        if (typeof ref === 'function') {\n          ref(value);\n        } else if (ref != null) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nfunction isDifferentRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  const list = listRef.current;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= list.length - 1 && isDisabled(list, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(elementsRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction buildCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isDisabled(list, index, disabledIndices) {\n  if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = list[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\nfunction sortByDocumentPosition(a, b) {\n  const position = a.compareDocumentPosition(b);\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}\nfunction areMapsEqual(map1, map2) {\n  if (map1.size !== map2.size) {\n    return false;\n  }\n  for (const [key, value] of map1.entries()) {\n    if (value !== map2.get(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nconst FloatingListContext = /*#__PURE__*/React.createContext({\n  register: () => {},\n  unregister: () => {},\n  map: /*#__PURE__*/new Map(),\n  elementsRef: {\n    current: []\n  }\n});\n/**\n * Provides context for a list of items within the floating element.\n * @see https://floating-ui.com/docs/FloatingList\n */\nfunction FloatingList(props) {\n  const {\n    children,\n    elementsRef,\n    labelsRef\n  } = props;\n  const [map, setMap] = React.useState(() => new Map());\n  const register = React.useCallback(node => {\n    setMap(prevMap => new Map(prevMap).set(node, null));\n  }, []);\n  const unregister = React.useCallback(node => {\n    setMap(prevMap => {\n      const map = new Map(prevMap);\n      map.delete(node);\n      return map;\n    });\n  }, []);\n  index(() => {\n    const newMap = new Map(map);\n    const nodes = Array.from(newMap.keys()).sort(sortByDocumentPosition);\n    nodes.forEach((node, index) => {\n      newMap.set(node, index);\n    });\n    if (!areMapsEqual(map, newMap)) {\n      setMap(newMap);\n    }\n  }, [map]);\n  return /*#__PURE__*/React.createElement(FloatingListContext.Provider, {\n    value: React.useMemo(() => ({\n      register,\n      unregister,\n      map,\n      elementsRef,\n      labelsRef\n    }), [register, unregister, map, elementsRef, labelsRef])\n  }, children);\n}\n/**\n * Used to register a list item and its index (DOM position) in the\n * `FloatingList`.\n * @see https://floating-ui.com/docs/FloatingList#uselistitem\n */\nfunction useListItem(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    label\n  } = props;\n  const {\n    register,\n    unregister,\n    map,\n    elementsRef,\n    labelsRef\n  } = React.useContext(FloatingListContext);\n  const [index$1, setIndex] = React.useState(null);\n  const componentRef = React.useRef(null);\n  const ref = React.useCallback(node => {\n    componentRef.current = node;\n    if (index$1 !== null) {\n      elementsRef.current[index$1] = node;\n      if (labelsRef) {\n        var _node$textContent;\n        const isLabelDefined = label !== undefined;\n        labelsRef.current[index$1] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;\n      }\n    }\n  }, [index$1, elementsRef, labelsRef, label]);\n  index(() => {\n    const node = componentRef.current;\n    if (node) {\n      register(node);\n      return () => {\n        unregister(node);\n      };\n    }\n  }, [register, unregister]);\n  index(() => {\n    const index = componentRef.current ? map.get(componentRef.current) : null;\n    if (index != null) {\n      setIndex(index);\n    }\n  }, [map]);\n  return React.useMemo(() => ({\n    ref,\n    index: index$1 == null ? -1 : index$1\n  }), [index$1, ref]);\n}\n\nfunction renderJsx(render, computedProps) {\n  if (typeof render === 'function') {\n    return render(computedProps);\n  }\n  if (render) {\n    return /*#__PURE__*/React.cloneElement(render, computedProps);\n  }\n  return /*#__PURE__*/React.createElement(\"div\", computedProps);\n}\nconst CompositeContext = /*#__PURE__*/React.createContext({\n  activeIndex: 0,\n  onNavigate: () => {}\n});\nconst horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];\nconst verticalKeys = [ARROW_UP, ARROW_DOWN];\nconst allKeys = [...horizontalKeys, ...verticalKeys];\n\n/**\n * Creates a single tab stop whose items are navigated by arrow keys, which\n * provides list navigation outside of floating element contexts.\n *\n * This is useful to enable navigation of a list of items that aren’t part of a\n * floating element. A menubar is an example of a composite, with each reference\n * element being an item.\n * @see https://floating-ui.com/docs/Composite\n */\nconst Composite = /*#__PURE__*/React.forwardRef(function Composite(props, forwardedRef) {\n  const {\n    render,\n    orientation = 'both',\n    loop = true,\n    rtl = false,\n    cols = 1,\n    disabledIndices,\n    activeIndex: externalActiveIndex,\n    onNavigate: externalSetActiveIndex,\n    itemSizes,\n    dense = false,\n    ...domProps\n  } = props;\n  const [internalActiveIndex, internalSetActiveIndex] = React.useState(0);\n  const activeIndex = externalActiveIndex != null ? externalActiveIndex : internalActiveIndex;\n  const onNavigate = useEffectEvent(externalSetActiveIndex != null ? externalSetActiveIndex : internalSetActiveIndex);\n  const elementsRef = React.useRef([]);\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const contextValue = React.useMemo(() => ({\n    activeIndex,\n    onNavigate\n  }), [activeIndex, onNavigate]);\n  const isGrid = cols > 1;\n  function handleKeyDown(event) {\n    if (!allKeys.includes(event.key)) return;\n    let nextIndex = activeIndex;\n    const minIndex = getMinIndex(elementsRef, disabledIndices);\n    const maxIndex = getMaxIndex(elementsRef, disabledIndices);\n    const horizontalEndKey = rtl ? ARROW_LEFT : ARROW_RIGHT;\n    const horizontalStartKey = rtl ? ARROW_RIGHT : ARROW_LEFT;\n    if (isGrid) {\n      const sizes = itemSizes || Array.from({\n        length: elementsRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = buildCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isDisabled(elementsRef.current, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isDisabled(elementsRef.current, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const maybeNextIndex = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex ? elementsRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getCellIndices([...(disabledIndices || elementsRef.current.map((_, index) => isDisabled(elementsRef.current, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getCellIndexOfCorner(activeIndex > maxIndex ? minIndex : activeIndex, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction we're\n        // moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === horizontalEndKey ? 'tr' : 'tl')\n      })];\n      if (maybeNextIndex != null) {\n        nextIndex = maybeNextIndex;\n      }\n    }\n    const toEndKeys = {\n      horizontal: [horizontalEndKey],\n      vertical: [ARROW_DOWN],\n      both: [horizontalEndKey, ARROW_DOWN]\n    }[orientation];\n    const toStartKeys = {\n      horizontal: [horizontalStartKey],\n      vertical: [ARROW_UP],\n      both: [horizontalStartKey, ARROW_UP]\n    }[orientation];\n    const preventedKeys = isGrid ? allKeys : {\n      horizontal: horizontalKeys,\n      vertical: verticalKeys,\n      both: allKeys\n    }[orientation];\n    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {\n      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {\n        nextIndex = minIndex;\n      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {\n        nextIndex = maxIndex;\n      } else {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: nextIndex,\n          decrement: toStartKeys.includes(event.key),\n          disabledIndices\n        });\n      }\n    }\n    if (nextIndex !== activeIndex && !isIndexOutOfBounds(elementsRef, nextIndex)) {\n      var _elementsRef$current$;\n      event.stopPropagation();\n      if (preventedKeys.includes(event.key)) {\n        event.preventDefault();\n      }\n      onNavigate(nextIndex);\n      (_elementsRef$current$ = elementsRef.current[nextIndex]) == null || _elementsRef$current$.focus();\n    }\n  }\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: forwardedRef,\n    'aria-orientation': orientation === 'both' ? undefined : orientation,\n    onKeyDown(e) {\n      domProps.onKeyDown == null || domProps.onKeyDown(e);\n      renderElementProps.onKeyDown == null || renderElementProps.onKeyDown(e);\n      handleKeyDown(e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(CompositeContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(FloatingList, {\n    elementsRef: elementsRef\n  }, renderJsx(render, computedProps)));\n});\n/**\n * @see https://floating-ui.com/docs/Composite\n */\nconst CompositeItem = /*#__PURE__*/React.forwardRef(function CompositeItem(props, forwardedRef) {\n  const {\n    render,\n    ...domProps\n  } = props;\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const {\n    activeIndex,\n    onNavigate\n  } = React.useContext(CompositeContext);\n  const {\n    ref,\n    index\n  } = useListItem();\n  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);\n  const isActive = activeIndex === index;\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: mergedRef,\n    tabIndex: isActive ? 0 : -1,\n    'data-active': isActive ? '' : undefined,\n    onFocus(e) {\n      domProps.onFocus == null || domProps.onFocus(e);\n      renderElementProps.onFocus == null || renderElementProps.onFocus(e);\n      onNavigate(index);\n    }\n  };\n  return renderJsx(render, computedProps);\n});\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => // Ensure the id is unique with multiple independent versions of Floating UI\n// on <React 18\n\"floating-ui-\" + Math.random().toString(36).slice(2, 6) + count++;\nfunction useFloatingId() {\n  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : undefined);\n  index(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  React.useEffect(() => {\n    serverHandoffComplete = true;\n  }, []);\n  return id;\n}\nconst useReactId = SafeReact.useId;\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/react-utils#useid\n */\nconst useId = useReactId || useFloatingId;\n\nlet devMessageSet;\nif (process.env.NODE_ENV !== \"production\") {\n  devMessageSet = /*#__PURE__*/new Set();\n}\nfunction warn() {\n  var _devMessageSet;\n  for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {\n    messages[_key] = arguments[_key];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(message))) {\n    var _devMessageSet2;\n    (_devMessageSet2 = devMessageSet) == null || _devMessageSet2.add(message);\n    console.warn(message);\n  }\n}\nfunction error() {\n  var _devMessageSet3;\n  for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    messages[_key2] = arguments[_key2];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet3 = devMessageSet) != null && _devMessageSet3.has(message))) {\n    var _devMessageSet4;\n    (_devMessageSet4 = devMessageSet) == null || _devMessageSet4.add(message);\n    console.error(message);\n  }\n}\n\n/**\n * Renders a pointing arrow triangle.\n * @see https://floating-ui.com/docs/FloatingArrow\n */\nconst FloatingArrow = /*#__PURE__*/React.forwardRef(function FloatingArrow(props, ref) {\n  const {\n    context: {\n      placement,\n      elements: {\n        floating\n      },\n      middlewareData: {\n        arrow,\n        shift\n      }\n    },\n    width = 14,\n    height = 7,\n    tipRadius = 0,\n    strokeWidth = 0,\n    staticOffset,\n    stroke,\n    d,\n    style: {\n      transform,\n      ...restStyle\n    } = {},\n    ...rest\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!ref) {\n      warn('The `ref` prop is required for `FloatingArrow`.');\n    }\n  }\n  const clipPathId = useId();\n  const [isRTL, setIsRTL] = React.useState(false);\n\n  // https://github.com/floating-ui/floating-ui/issues/2932\n  index(() => {\n    if (!floating) return;\n    const isRTL = getComputedStyle(floating).direction === 'rtl';\n    if (isRTL) {\n      setIsRTL(true);\n    }\n  }, [floating]);\n  if (!floating) {\n    return null;\n  }\n  const [side, alignment] = placement.split('-');\n  const isVerticalSide = side === 'top' || side === 'bottom';\n  let computedStaticOffset = staticOffset;\n  if (isVerticalSide && shift != null && shift.x || !isVerticalSide && shift != null && shift.y) {\n    computedStaticOffset = null;\n  }\n\n  // Strokes must be double the border width, this ensures the stroke's width\n  // works as you'd expect.\n  const computedStrokeWidth = strokeWidth * 2;\n  const halfStrokeWidth = computedStrokeWidth / 2;\n  const svgX = width / 2 * (tipRadius / -8 + 1);\n  const svgY = height / 2 * tipRadius / 4;\n  const isCustomShape = !!d;\n  const yOffsetProp = computedStaticOffset && alignment === 'end' ? 'bottom' : 'top';\n  let xOffsetProp = computedStaticOffset && alignment === 'end' ? 'right' : 'left';\n  if (computedStaticOffset && isRTL) {\n    xOffsetProp = alignment === 'end' ? 'left' : 'right';\n  }\n  const arrowX = (arrow == null ? void 0 : arrow.x) != null ? computedStaticOffset || arrow.x : '';\n  const arrowY = (arrow == null ? void 0 : arrow.y) != null ? computedStaticOffset || arrow.y : '';\n  const dValue = d || 'M0,0' + (\" H\" + width) + (\" L\" + (width - svgX) + \",\" + (height - svgY)) + (\" Q\" + width / 2 + \",\" + height + \" \" + svgX + \",\" + (height - svgY)) + ' Z';\n  const rotation = {\n    top: isCustomShape ? 'rotate(180deg)' : '',\n    left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',\n    bottom: isCustomShape ? '' : 'rotate(180deg)',\n    right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'\n  }[side];\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    \"aria-hidden\": true,\n    ref: ref,\n    width: isCustomShape ? width : width + computedStrokeWidth,\n    height: width,\n    viewBox: \"0 0 \" + width + \" \" + (height > width ? height : width),\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      [xOffsetProp]: arrowX,\n      [yOffsetProp]: arrowY,\n      [side]: isVerticalSide || isCustomShape ? '100%' : \"calc(100% - \" + computedStrokeWidth / 2 + \"px)\",\n      transform: [rotation, transform].filter(t => !!t).join(' '),\n      ...restStyle\n    }\n  }), computedStrokeWidth > 0 && /*#__PURE__*/React.createElement(\"path\", {\n    clipPath: \"url(#\" + clipPathId + \")\",\n    fill: \"none\",\n    stroke: stroke\n    // Account for the stroke on the fill path rendered below.\n    ,\n    strokeWidth: computedStrokeWidth + (d ? 0 : 1),\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    stroke: computedStrokeWidth && !d ? rest.fill : 'none',\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: -halfStrokeWidth,\n    y: halfStrokeWidth * (isCustomShape ? -1 : 1),\n    width: width + computedStrokeWidth,\n    height: width\n  })));\n});\n\nfunction createPubSub() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null || _map$get.forEach(handler => handler(data));\n    },\n    on(event, listener) {\n      map.set(event, [...(map.get(event) || []), listener]);\n    },\n    off(event, listener) {\n      var _map$get2;\n      map.set(event, ((_map$get2 = map.get(event)) == null ? void 0 : _map$get2.filter(l => l !== listener)) || []);\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/React.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the parent node id for nested floating elements, if available.\n * Returns `null` for top-level floating elements.\n */\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\n\n/**\n * Returns the nearest floating tree context, if available.\n */\nconst useFloatingTree = () => React.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the `FloatingTree`, returning its id.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction useFloatingNodeId(customParentId) {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  index(() => {\n    const node = {\n      id,\n      parentId\n    };\n    tree == null || tree.addNode(node);\n    return () => {\n      tree == null || tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n}\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingNode(props) {\n  const {\n    children,\n    id\n  } = props;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/React.createElement(FloatingNodeContext.Provider, {\n    value: React.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId])\n  }, children);\n}\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM.\n * This is not necessary in all cases, except when there must be explicit communication between parent and child floating elements. It is necessary for:\n * - The `bubbles` option in the `useDismiss()` Hook\n * - Nested virtual list navigation\n * - Nested floating elements that each open on hover\n * - Custom communication between parent and child floating elements\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingTree(props) {\n  const {\n    children\n  } = props;\n  const nodesRef = React.useRef([]);\n  const addNode = React.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = React.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const events = React.useState(() => createPubSub())[0];\n  return /*#__PURE__*/React.createElement(FloatingTreeContext.Provider, {\n    value: React.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [addNode, removeNode, events])\n  }, children);\n}\n\nfunction createAttribute(name) {\n  return \"data-floating-ui-\" + name;\n}\n\nfunction useLatestRef(value) {\n  const ref = useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\nconst safePolygonIdentifier = /*#__PURE__*/createAttribute('safe-polygon');\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  return value == null ? void 0 : value[prop];\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nfunction useHover(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = props;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const openRef = useLatestRef(open);\n  const pointerTypeRef = React.useRef();\n  const timeoutRef = React.useRef(-1);\n  const handlerRef = React.useRef();\n  const restTimeoutRef = React.useRef(-1);\n  const blockMouseMoveRef = React.useRef(true);\n  const performedPointerEventsMutationRef = React.useRef(false);\n  const unbindMouseMoveRef = React.useRef(() => {});\n  const restTimeoutPendingRef = React.useRef(false);\n  const isHoverOpen = React.useCallback(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  }, [dataRef]);\n\n  // When closing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        open\n      } = _ref;\n      if (!open) {\n        clearTimeout(timeoutRef.current);\n        clearTimeout(restTimeoutRef.current);\n        blockMouseMoveRef.current = true;\n        restTimeoutPendingRef.current = false;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [enabled, events]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    if (!handleCloseRef.current) return;\n    if (!open) return;\n    function onLeave(event) {\n      if (isHoverOpen()) {\n        onOpenChange(false, event, 'hover');\n      }\n    }\n    const html = getDocument(elements.floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [elements.floating, open, onOpenChange, enabled, handleCloseRef, isHoverOpen]);\n  const closeWithDelay = React.useCallback(function (event, runElseBranch, reason) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    if (reason === void 0) {\n      reason = 'hover';\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = window.setTimeout(() => onOpenChange(false, event, reason), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeout(timeoutRef.current);\n      onOpenChange(false, event, reason);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = useEffectEvent(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  });\n  const clearPointerEvents = useEffectEvent(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument(elements.floating).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  });\n  const isClickLikeOpenEvent = useEffectEvent(() => {\n    return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n  });\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onMouseEnter(event) {\n      clearTimeout(timeoutRef.current);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || restMs > 0 && !getDelay(delayRef.current, 'open')) {\n        return;\n      }\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = window.setTimeout(() => {\n          if (!openRef.current) {\n            onOpenChange(true, event, 'hover');\n          }\n        }, openDelay);\n      } else if (!open) {\n        onOpenChange(true, event, 'hover');\n      }\n    }\n    function onMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      unbindMouseMoveRef.current();\n      const doc = getDocument(elements.floating);\n      clearTimeout(restTimeoutRef.current);\n      restTimeoutPendingRef.current = false;\n      if (handleCloseRef.current && dataRef.current.floatingContext) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeout(timeoutRef.current);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...dataRef.current.floatingContext,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            if (!isClickLikeOpenEvent()) {\n              closeWithDelay(event, true, 'safe-polygon');\n            }\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n\n      // Allow interactivity without `safePolygon` on touch devices. With a\n      // pointer, a short close delay is an alternative, so it should work\n      // consistently.\n      const shouldClose = pointerTypeRef.current === 'touch' ? !contains(elements.floating, event.relatedTarget) : true;\n      if (shouldClose) {\n        closeWithDelay(event);\n      }\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      if (!dataRef.current.floatingContext) return;\n      handleCloseRef.current == null || handleCloseRef.current({\n        ...dataRef.current.floatingContext,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          if (!isClickLikeOpenEvent()) {\n            closeWithDelay(event);\n          }\n        }\n      })(event);\n    }\n    if (isElement(elements.domReference)) {\n      var _elements$floating;\n      const ref = elements.domReference;\n      open && ref.addEventListener('mouseleave', onScrollMouseLeave);\n      (_elements$floating = elements.floating) == null || _elements$floating.addEventListener('mouseleave', onScrollMouseLeave);\n      move && ref.addEventListener('mousemove', onMouseEnter, {\n        once: true\n      });\n      ref.addEventListener('mouseenter', onMouseEnter);\n      ref.addEventListener('mouseleave', onMouseLeave);\n      return () => {\n        var _elements$floating2;\n        open && ref.removeEventListener('mouseleave', onScrollMouseLeave);\n        (_elements$floating2 = elements.floating) == null || _elements$floating2.removeEventListener('mouseleave', onScrollMouseLeave);\n        move && ref.removeEventListener('mousemove', onMouseEnter);\n        ref.removeEventListener('mouseenter', onMouseEnter);\n        ref.removeEventListener('mouseleave', onMouseLeave);\n      };\n    }\n  }, [elements, enabled, context, mouseOnly, restMs, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, openRef, tree, delayRef, handleCloseRef, dataRef, isClickLikeOpenEvent]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  index(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) return;\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {\n      performedPointerEventsMutationRef.current = true;\n      const floatingEl = elements.floating;\n      if (isElement(elements.domReference) && floatingEl) {\n        var _tree$nodesRef$curren;\n        const body = getDocument(elements.floating).body;\n        body.setAttribute(safePolygonIdentifier, '');\n        const ref = elements.domReference;\n        const parentFloating = tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        body.style.pointerEvents = 'none';\n        ref.style.pointerEvents = 'auto';\n        floatingEl.style.pointerEvents = 'auto';\n        return () => {\n          body.style.pointerEvents = '';\n          ref.style.pointerEvents = '';\n          floatingEl.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, elements, tree, handleCloseRef, isHoverOpen]);\n  index(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      restTimeoutPendingRef.current = false;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  React.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      clearPointerEvents();\n    };\n  }, [enabled, elements.domReference, cleanupMouseMoveHandler, clearPointerEvents]);\n  const reference = React.useMemo(() => {\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      onPointerDown: setPointerRef,\n      onPointerEnter: setPointerRef,\n      onMouseMove(event) {\n        const {\n          nativeEvent\n        } = event;\n        function handleMouseMove() {\n          if (!blockMouseMoveRef.current && !openRef.current) {\n            onOpenChange(true, nativeEvent, 'hover');\n          }\n        }\n        if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current)) {\n          return;\n        }\n        if (open || restMs === 0) {\n          return;\n        }\n\n        // Ignore insignificant movements to account for tremors.\n        if (restTimeoutPendingRef.current && event.movementX ** 2 + event.movementY ** 2 < 2) {\n          return;\n        }\n        clearTimeout(restTimeoutRef.current);\n        if (pointerTypeRef.current === 'touch') {\n          handleMouseMove();\n        } else {\n          restTimeoutPendingRef.current = true;\n          restTimeoutRef.current = window.setTimeout(handleMouseMove, restMs);\n        }\n      }\n    };\n  }, [mouseOnly, onOpenChange, open, openRef, restMs]);\n  const floating = React.useMemo(() => ({\n    onMouseEnter() {\n      clearTimeout(timeoutRef.current);\n    },\n    onMouseLeave(event) {\n      if (!isClickLikeOpenEvent()) {\n        closeWithDelay(event.nativeEvent, false);\n      }\n    }\n  }), [closeWithDelay, isClickLikeOpenEvent]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nconst NOOP = () => {};\nconst FloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: NOOP,\n  setState: NOOP,\n  isInstantPhase: false\n});\n\n/**\n * @deprecated\n * Use the return value of `useDelayGroup()` instead.\n */\nconst useDelayGroupContext = () => React.useContext(FloatingDelayGroupContext);\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction FloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const [state, setState] = React.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = React.useRef(null);\n  const setCurrentId = React.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  index(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else if (!state.isInstantPhase) {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      if (state.isInstantPhase) {\n        setState({\n          isInstantPhase: false\n        });\n      }\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId, state.isInstantPhase]);\n  return /*#__PURE__*/React.createElement(FloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setCurrentId])\n  }, children);\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `FloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    id: optionId,\n    enabled = true\n  } = options;\n  const id = optionId != null ? optionId : floatingId;\n  const groupContext = useDelayGroupContext();\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = groupContext;\n  index(() => {\n    if (!enabled) return;\n    if (!currentId) return;\n    setState({\n      delay: {\n        open: 1,\n        close: getDelay(initialDelay, 'close')\n      }\n    });\n    if (currentId !== id) {\n      onOpenChange(false);\n    }\n  }, [enabled, id, onOpenChange, setState, currentId, initialDelay]);\n  index(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!enabled) return;\n    if (!currentId) return;\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  index(() => {\n    if (!enabled) return;\n    if (setCurrentId === NOOP || !open) return;\n    setCurrentId(id);\n  }, [enabled, open, setCurrentId, id]);\n  return groupContext;\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nfunction getAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction getChildren(nodes, id) {\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    });\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\n\n// Modified to add conditional `aria-hidden` support:\n// https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts\nlet counterMap = /*#__PURE__*/new WeakMap();\nlet uncontrolledElementsSet = /*#__PURE__*/new WeakSet();\nlet markerMap = {};\nlet lockCount$1 = 0;\nconst supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\nconst unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));\nconst correctElements = (parent, targets) => targets.map(target => {\n  if (parent.contains(target)) {\n    return target;\n  }\n  const correctedTarget = unwrapHost(target);\n  if (parent.contains(correctedTarget)) {\n    return correctedTarget;\n  }\n  return null;\n}).filter(x => x != null);\nfunction applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {\n  const markerName = 'data-floating-ui-inert';\n  const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;\n  const avoidElements = correctElements(body, uncorrectedAvoidElements);\n  const elementsToKeep = new Set();\n  const elementsToStop = new Set(avoidElements);\n  const hiddenElements = [];\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  const markerCounter = markerMap[markerName];\n  avoidElements.forEach(keep);\n  deep(body);\n  elementsToKeep.clear();\n  function keep(el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    el.parentNode && keep(el.parentNode);\n  }\n  function deep(parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    [].forEach.call(parent.children, node => {\n      if (getNodeName(node) === 'script') return;\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;\n        const alreadyHidden = attr !== null && attr !== 'false';\n        const counterValue = (counterMap.get(node) || 0) + 1;\n        const markerValue = (markerCounter.get(node) || 0) + 1;\n        counterMap.set(node, counterValue);\n        markerCounter.set(node, markerValue);\n        hiddenElements.push(node);\n        if (counterValue === 1 && alreadyHidden) {\n          uncontrolledElementsSet.add(node);\n        }\n        if (markerValue === 1) {\n          node.setAttribute(markerName, '');\n        }\n        if (!alreadyHidden && controlAttribute) {\n          node.setAttribute(controlAttribute, 'true');\n        }\n      }\n    });\n  }\n  lockCount$1++;\n  return () => {\n    hiddenElements.forEach(element => {\n      const counterValue = (counterMap.get(element) || 0) - 1;\n      const markerValue = (markerCounter.get(element) || 0) - 1;\n      counterMap.set(element, counterValue);\n      markerCounter.set(element, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledElementsSet.has(element) && controlAttribute) {\n          element.removeAttribute(controlAttribute);\n        }\n        uncontrolledElementsSet.delete(element);\n      }\n      if (!markerValue) {\n        element.removeAttribute(markerName);\n      }\n    });\n    lockCount$1--;\n    if (!lockCount$1) {\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledElementsSet = new WeakSet();\n      markerMap = {};\n    }\n  };\n}\nfunction markOthers(avoidElements, ariaHidden, inert) {\n  if (ariaHidden === void 0) {\n    ariaHidden = false;\n  }\n  if (inert === void 0) {\n    inert = false;\n  }\n  const body = getDocument(avoidElements[0]).body;\n  return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live]'))), body, ariaHidden, inert);\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, direction) {\n  const allTabbable = tabbable(container, getTabbableOptions());\n  if (direction === 'prev') {\n    allTabbable.reverse();\n  }\n  const activeIndex = allTabbable.indexOf(activeElement(getDocument(container)));\n  const nextTabbableElements = allTabbable.slice(activeIndex + 1);\n  return nextTabbableElements[0];\n}\nfunction getNextTabbable() {\n  return getTabbableIn(document.body, 'next');\n}\nfunction getPreviousTabbable() {\n  return getTabbableIn(document.body, 'prev');\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\n// See Diego Haz's Sandbox for making this logic work well on Safari/iOS:\n// https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/FocusTrap.tsx\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nlet timeoutId;\nfunction setActiveElementOnTab(event) {\n  if (event.key === 'Tab') {\n    event.target;\n    clearTimeout(timeoutId);\n  }\n}\nconst FocusGuard = /*#__PURE__*/React.forwardRef(function FocusGuard(props, ref) {\n  const [role, setRole] = React.useState();\n  index(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n    document.addEventListener('keydown', setActiveElementOnTab);\n    return () => {\n      document.removeEventListener('keydown', setActiveElementOnTab);\n    };\n  }, []);\n  const restProps = {\n    ref,\n    tabIndex: 0,\n    // Role is only for VoiceOver\n    role,\n    'aria-hidden': role ? undefined : true,\n    [createAttribute('focus-guard')]: '',\n    style: HIDDEN_STYLES\n  };\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, props, restProps));\n});\n\nconst PortalContext = /*#__PURE__*/React.createContext(null);\nconst attr = /*#__PURE__*/createAttribute('portal');\n/**\n * @see https://floating-ui.com/docs/FloatingPortal#usefloatingportalnode\n */\nfunction useFloatingPortalNode(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    id,\n    root\n  } = props;\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  const [portalNode, setPortalNode] = React.useState(null);\n  const portalNodeRef = React.useRef(null);\n  index(() => {\n    return () => {\n      portalNode == null || portalNode.remove();\n      // Allow the subsequent layout effects to create a new node on updates.\n      // The portal node will still be cleaned up on unmount.\n      // https://github.com/floating-ui/floating-ui/issues/2454\n      queueMicrotask(() => {\n        portalNodeRef.current = null;\n      });\n    };\n  }, [portalNode]);\n  index(() => {\n    // Wait for the uniqueId to be generated before creating the portal node in\n    // React <18 (using `useFloatingId` instead of the native `useId`).\n    // https://github.com/floating-ui/floating-ui/issues/2778\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    const existingIdRoot = id ? document.getElementById(id) : null;\n    if (!existingIdRoot) return;\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    existingIdRoot.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, uniqueId]);\n  index(() => {\n    // Wait for the root to exist before creating the portal node. The root must\n    // be stored in state, not a ref, for this to work reactively.\n    if (root === null) return;\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    let container = root || (portalContext == null ? void 0 : portalContext.portalNode);\n    if (container && !isElement(container)) container = container.current;\n    container = container || document.body;\n    let idWrapper = null;\n    if (id) {\n      idWrapper = document.createElement('div');\n      idWrapper.id = id;\n      container.appendChild(idWrapper);\n    }\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    container = idWrapper || container;\n    container.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, root, uniqueId, portalContext]);\n  return portalNode;\n}\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * This is necessary to ensure the floating element can appear outside any\n * potential parent containers that cause clipping (such as `overflow: hidden`),\n * while retaining its location in the React tree.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nfunction FloatingPortal(props) {\n  const {\n    children,\n    id,\n    root,\n    preserveTabOrder = true\n  } = props;\n  const portalNode = useFloatingPortalNode({\n    id,\n    root\n  });\n  const [focusManagerState, setFocusManagerState] = React.useState(null);\n  const beforeOutsideRef = React.useRef(null);\n  const afterOutsideRef = React.useRef(null);\n  const beforeInsideRef = React.useRef(null);\n  const afterInsideRef = React.useRef(null);\n  const modal = focusManagerState == null ? void 0 : focusManagerState.modal;\n  const open = focusManagerState == null ? void 0 : focusManagerState.open;\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal &&\n  // Don't render if unmount is transitioning.\n  focusManagerState.open && preserveTabOrder && !!(root || portalNode);\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  React.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, modal]);\n  React.useEffect(() => {\n    if (!portalNode) return;\n    if (open) return;\n    enableFocusInside(portalNode);\n  }, [open, portalNode]);\n  return /*#__PURE__*/React.createElement(PortalContext.Provider, {\n    value: React.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode])\n  }, shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: beforeOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _beforeInsideRef$curr;\n        (_beforeInsideRef$curr = beforeInsideRef.current) == null || _beforeInsideRef$curr.focus();\n      } else {\n        const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        prevTabbable == null || prevTabbable.focus();\n      }\n    }\n  }), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-owns\": portalNode.id,\n    style: HIDDEN_STYLES\n  }), portalNode && /*#__PURE__*/ReactDOM.createPortal(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: afterOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _afterInsideRef$curre;\n        (_afterInsideRef$curre = afterInsideRef.current) == null || _afterInsideRef$curre.focus();\n      } else {\n        const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        nextTabbable == null || nextTabbable.focus();\n        (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent, 'focus-out'));\n      }\n    }\n  }));\n}\nconst usePortalContext = () => React.useContext(PortalContext);\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nconst LIST_LIMIT = 20;\nlet previouslyFocusedElements = [];\nfunction addPreviouslyFocusedElement(element) {\n  previouslyFocusedElements = previouslyFocusedElements.filter(el => el.isConnected);\n  let tabbableEl = element;\n  if (!tabbableEl || getNodeName(tabbableEl) === 'body') return;\n  if (!isTabbable(tabbableEl, getTabbableOptions())) {\n    const tabbableChild = tabbable(tabbableEl, getTabbableOptions())[0];\n    if (tabbableChild) {\n      tabbableEl = tabbableChild;\n    }\n  }\n  previouslyFocusedElements.push(tabbableEl);\n  if (previouslyFocusedElements.length > LIST_LIMIT) {\n    previouslyFocusedElements = previouslyFocusedElements.slice(-LIST_LIMIT);\n  }\n}\nfunction getPreviouslyFocusedElement() {\n  return previouslyFocusedElements.slice().reverse().find(el => el.isConnected);\n}\nconst VisuallyHiddenDismiss = /*#__PURE__*/React.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/React.createElement(\"button\", _extends({}, props, {\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  }));\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(props) {\n  const {\n    context,\n    children,\n    disabled = false,\n    order = ['content'],\n    guards: _guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    restoreFocus = false,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true\n  } = props;\n  const {\n    open,\n    refs,\n    nodeId,\n    onOpenChange,\n    events,\n    dataRef,\n    floatingId,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isUntrappedTypeableCombobox = isTypeableCombobox(domReference) && ignoreInitialFocus;\n\n  // Force the guards to be rendered if the `inert` attribute is not supported.\n  const guards = supportsInert() ? _guards : true;\n  const orderRef = useLatestRef(order);\n  const initialFocusRef = useLatestRef(initialFocus);\n  const returnFocusRef = useLatestRef(returnFocus);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n  const startDismissButtonRef = React.useRef(null);\n  const endDismissButtonRef = React.useRef(null);\n  const preventReturnFocusRef = React.useRef(false);\n  const isPointerDownRef = React.useRef(false);\n  const tabbableIndexRef = React.useRef(-1);\n  const isInsidePortal = portalContext != null;\n  const floatingFocusElement = getFloatingFocusElement(floating);\n  const getTabbableContent = useEffectEvent(function (container) {\n    if (container === void 0) {\n      container = floatingFocusElement;\n    }\n    return container ? tabbable(container, getTabbableOptions()) : [];\n  });\n  const getTabbableElements = useEffectEvent(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floatingFocusElement && type === 'floating') {\n        return floatingFocusElement;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  });\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!modal) return;\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (contains(floatingFocusElement, activeElement(getDocument(floatingFocusElement))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floatingFocusElement && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument(floatingFocusElement);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [disabled, domReference, floatingFocusElement, modal, orderRef, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!floating) return;\n    function handleFocusIn(event) {\n      const target = getTarget(event);\n      const tabbableContent = getTabbableContent();\n      const tabbableIndex = tabbableContent.indexOf(target);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n    }\n    floating.addEventListener('focusin', handleFocusIn);\n    return () => {\n      floating.removeEventListener('focusin', handleFocusIn);\n    };\n  }, [disabled, floating, getTabbableContent]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!closeOnFocusOut) return;\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      queueMicrotask(() => {\n        const movedToUnrelatedNode = !(contains(domReference, relatedTarget) || contains(floating, relatedTarget) || contains(relatedTarget, floating) || contains(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getChildren(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context, _node$context2;\n          return contains((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n        }) || getAncestors(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context3, _node$context4;\n          return ((_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating) === relatedTarget || ((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.domReference) === relatedTarget;\n        })));\n\n        // Restore focus to the previous tabbable element index to prevent\n        // focus from being lost outside the floating tree.\n        if (restoreFocus && movedToUnrelatedNode && activeElement(getDocument(floatingFocusElement)) === getDocument(floatingFocusElement).body) {\n          // Let `FloatingPortal` effect knows that focus is still inside the\n          // floating tree.\n          if (isHTMLElement(floatingFocusElement)) {\n            floatingFocusElement.focus();\n          }\n          const prevTabbableIndex = tabbableIndexRef.current;\n          const tabbableContent = getTabbableContent();\n          const nodeToFocus = tabbableContent[prevTabbableIndex] || tabbableContent[tabbableContent.length - 1] || floatingFocusElement;\n          if (isHTMLElement(nodeToFocus)) {\n            nodeToFocus.focus();\n          }\n        }\n\n        // Focus did not move inside the floating tree, and there are no tabbable\n        // portal guards to handle closing.\n        if ((isUntrappedTypeableCombobox ? true : !modal) && relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n        // Fix React 18 Strict Mode returnFocus due to double rendering.\n        relatedTarget !== getPreviouslyFocusedElement()) {\n          preventReturnFocusRef.current = true;\n          onOpenChange(false, event, 'focus-out');\n        }\n      });\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [disabled, domReference, floating, floatingFocusElement, modal, nodeId, tree, portalContext, onOpenChange, closeOnFocusOut, restoreFocus, getTabbableContent, isUntrappedTypeableCombobox]);\n  React.useEffect(() => {\n    var _portalContext$portal;\n    if (disabled) return;\n\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null || (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll(\"[\" + createAttribute('portal') + \"]\")) || []);\n    if (floating) {\n      const insideElements = [floating, ...portalNodes, startDismissButtonRef.current, endDismissButtonRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);\n      const cleanup = modal || isUntrappedTypeableCombobox ? markOthers(insideElements, guards, !guards) : markOthers(insideElements);\n      return () => {\n        cleanup();\n      };\n    }\n  }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards]);\n  index(() => {\n    if (disabled || !isHTMLElement(floatingFocusElement)) return;\n    const doc = getDocument(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n\n    // Wait for any layout effect state setters to execute to set `tabIndex`.\n    queueMicrotask(() => {\n      const focusableElements = getTabbableElements(floatingFocusElement);\n      const initialFocusValue = initialFocusRef.current;\n      const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floatingFocusElement;\n      const focusAlreadyInsideFloatingEl = contains(floatingFocusElement, previouslyFocusedElement);\n      if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {\n        enqueueFocus(elToFocus, {\n          preventScroll: elToFocus === floatingFocusElement\n        });\n      }\n    });\n  }, [disabled, open, floatingFocusElement, ignoreInitialFocus, getTabbableElements, initialFocusRef]);\n  index(() => {\n    if (disabled || !floatingFocusElement) return;\n    let preventReturnFocusScroll = false;\n    const doc = getDocument(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n    const contextData = dataRef.current;\n    let openEvent = contextData.openEvent;\n    addPreviouslyFocusedElement(previouslyFocusedElement);\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onOpenChange(_ref) {\n      let {\n        open,\n        reason,\n        event,\n        nested\n      } = _ref;\n      if (open) {\n        openEvent = event;\n      }\n      if (reason === 'escape-key' && refs.domReference.current) {\n        addPreviouslyFocusedElement(refs.domReference.current);\n      }\n      if (reason === 'hover' && event.type === 'mouseleave') {\n        preventReturnFocusRef.current = true;\n      }\n      if (reason !== 'outside-press') return;\n      if (nested) {\n        preventReturnFocusRef.current = false;\n        preventReturnFocusScroll = true;\n      } else {\n        preventReturnFocusRef.current = !(isVirtualClick(event) || isVirtualPointerEvent(event));\n      }\n    }\n    events.on('openchange', onOpenChange);\n    const fallbackEl = doc.createElement('span');\n    fallbackEl.setAttribute('tabindex', '-1');\n    fallbackEl.setAttribute('aria-hidden', 'true');\n    Object.assign(fallbackEl.style, HIDDEN_STYLES);\n    if (isInsidePortal && domReference) {\n      domReference.insertAdjacentElement('afterend', fallbackEl);\n    }\n    function getReturnElement() {\n      if (typeof returnFocusRef.current === 'boolean') {\n        return getPreviouslyFocusedElement() || fallbackEl;\n      }\n      return returnFocusRef.current.current || fallbackEl;\n    }\n    return () => {\n      events.off('openchange', onOpenChange);\n      const activeEl = activeElement(doc);\n      const isFocusInsideFloatingTree = contains(floating, activeEl) || tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n        var _node$context5;\n        return contains((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.floating, activeEl);\n      });\n      const shouldFocusReference = isFocusInsideFloatingTree || openEvent && ['click', 'mousedown'].includes(openEvent.type);\n      if (shouldFocusReference && refs.domReference.current) {\n        addPreviouslyFocusedElement(refs.domReference.current);\n      }\n      const returnElement = getReturnElement();\n      queueMicrotask(() => {\n        if (\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        returnFocusRef.current && !preventReturnFocusRef.current && isHTMLElement(returnElement) && (\n        // If the focus moved somewhere else after mount, avoid returning focus\n        // since it likely entered a different element which should be\n        // respected: https://github.com/floating-ui/floating-ui/issues/2607\n        returnElement !== activeEl && activeEl !== doc.body ? isFocusInsideFloatingTree : true)) {\n          returnElement.focus({\n            preventScroll: preventReturnFocusScroll\n          });\n        }\n        fallbackEl.remove();\n      });\n    };\n  }, [disabled, floating, floatingFocusElement, returnFocusRef, dataRef, refs, events, tree, nodeId, isInsidePortal, domReference]);\n  React.useEffect(() => {\n    // The `returnFocus` cleanup behavior is inside a microtask; ensure we\n    // wait for it to complete before resetting the flag.\n    queueMicrotask(() => {\n      preventReturnFocusRef.current = false;\n    });\n  }, [disabled]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  index(() => {\n    if (disabled) return;\n    if (!portalContext) return;\n    portalContext.setFocusManagerState({\n      modal,\n      closeOnFocusOut,\n      open,\n      onOpenChange,\n      refs\n    });\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [disabled, portalContext, modal, open, onOpenChange, refs, closeOnFocusOut]);\n  index(() => {\n    if (disabled) return;\n    if (!floatingFocusElement) return;\n    if (typeof MutationObserver !== 'function') return;\n    if (ignoreInitialFocus) return;\n    const handleMutation = () => {\n      const tabIndex = floatingFocusElement.getAttribute('tabindex');\n      const tabbableContent = getTabbableContent();\n      const activeEl = activeElement(getDocument(floating));\n      const tabbableIndex = tabbableContent.indexOf(activeEl);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n      if (orderRef.current.includes('floating') || activeEl !== refs.domReference.current && tabbableContent.length === 0) {\n        if (tabIndex !== '0') {\n          floatingFocusElement.setAttribute('tabindex', '0');\n        }\n      } else if (tabIndex !== '-1') {\n        floatingFocusElement.setAttribute('tabindex', '-1');\n      }\n    };\n    handleMutation();\n    const observer = new MutationObserver(handleMutation);\n    observer.observe(floatingFocusElement, {\n      childList: true,\n      subtree: true,\n      attributes: true\n    });\n    return () => {\n      observer.disconnect();\n    };\n  }, [disabled, floating, floatingFocusElement, refs, orderRef, getTabbableContent, ignoreInitialFocus]);\n  function renderDismissButton(location) {\n    if (disabled || !visuallyHiddenDismiss || !modal) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: event => onOpenChange(false, event.nativeEvent)\n    }, typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss');\n  }\n  const shouldRenderGuards = !disabled && guards && (modal ? !isUntrappedTypeableCombobox : true) && (isInsidePortal || modal);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.beforeInsideRef,\n    onFocus: event => {\n      if (modal) {\n        const els = getTabbableElements();\n        enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        preventReturnFocusRef.current = false;\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const nextTabbable = getNextTabbable() || domReference;\n          nextTabbable == null || nextTabbable.focus();\n        } else {\n          var _portalContext$before;\n          (_portalContext$before = portalContext.beforeOutsideRef.current) == null || _portalContext$before.focus();\n        }\n      }\n    }\n  }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.afterInsideRef,\n    onFocus: event => {\n      if (modal) {\n        enqueueFocus(getTabbableElements()[0]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        if (closeOnFocusOut) {\n          preventReturnFocusRef.current = true;\n        }\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const prevTabbable = getPreviousTabbable() || domReference;\n          prevTabbable == null || prevTabbable.focus();\n        } else {\n          var _portalContext$afterO;\n          (_portalContext$afterO = portalContext.afterOutsideRef.current) == null || _portalContext$afterO.focus();\n        }\n      }\n    }\n  }));\n}\n\nlet lockCount = 0;\nfunction enableScrollLock() {\n  const isIOS = /iP(hone|ad|od)|iOS/.test(getPlatform());\n  const bodyStyle = document.body.style;\n  // RTL <body> scrollbar\n  const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n  const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.scrollX;\n  const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.scrollY;\n  bodyStyle.overflow = 'hidden';\n  if (scrollbarWidth) {\n    bodyStyle[paddingProp] = scrollbarWidth + \"px\";\n  }\n\n  // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n  // technique has fewer side effects.\n  if (isIOS) {\n    var _window$visualViewpor, _window$visualViewpor2;\n    // iOS 12 does not support `visualViewport`.\n    const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n    const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n    Object.assign(bodyStyle, {\n      position: 'fixed',\n      top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n      left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n      right: '0'\n    });\n  }\n  return () => {\n    Object.assign(bodyStyle, {\n      overflow: '',\n      [paddingProp]: ''\n    });\n    if (isIOS) {\n      Object.assign(bodyStyle, {\n        position: '',\n        top: '',\n        left: '',\n        right: ''\n      });\n      window.scrollTo(scrollX, scrollY);\n    }\n  };\n}\nlet cleanup = () => {};\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/React.forwardRef(function FloatingOverlay(props, ref) {\n  const {\n    lockScroll = false,\n    ...rest\n  } = props;\n  index(() => {\n    if (!lockScroll) return;\n    lockCount++;\n    if (lockCount === 1) {\n      cleanup = enableScrollLock();\n    }\n    return () => {\n      lockCount--;\n      if (lockCount === 0) {\n        cleanup();\n      }\n    };\n  }, [lockScroll]);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref\n  }, rest, {\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  }));\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nfunction useClick(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true,\n    stickIfOpen = true\n  } = props;\n  const pointerTypeRef = React.useRef();\n  const didKeyDownRef = React.useRef(false);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      pointerTypeRef.current = event.pointerType;\n    },\n    onMouseDown(event) {\n      const pointerType = pointerTypeRef.current;\n\n      // Ignore all buttons except for the \"main\" button.\n      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n      if (event.button !== 0) return;\n      if (eventOption === 'click') return;\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        // Prevent stealing focus from the floating element\n        event.preventDefault();\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onClick(event) {\n      const pointerType = pointerTypeRef.current;\n      if (eventOption === 'mousedown' && pointerTypeRef.current) {\n        pointerTypeRef.current = undefined;\n        return;\n      }\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'click' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onKeyDown(event) {\n      pointerTypeRef.current = undefined;\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {\n        return;\n      }\n      if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n        // Prevent scrolling\n        event.preventDefault();\n        didKeyDownRef.current = true;\n      }\n      if (event.key === 'Enter') {\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    },\n    onKeyUp(event) {\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {\n        return;\n      }\n      if (event.key === ' ' && didKeyDownRef.current) {\n        didKeyDownRef.current = false;\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    }\n  }), [dataRef, domReference, eventOption, ignoreMouse, keyboardHandlers, onOpenChange, open, stickIfOpen, toggle]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction createVirtualElement(domElement, data) {\n  let offsetX = null;\n  let offsetY = null;\n  let isAutoUpdateEvent = false;\n  return {\n    contextElement: domElement || undefined,\n    getBoundingClientRect() {\n      var _data$dataRef$current;\n      const domRect = (domElement == null ? void 0 : domElement.getBoundingClientRect()) || {\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n      };\n      const isXAxis = data.axis === 'x' || data.axis === 'both';\n      const isYAxis = data.axis === 'y' || data.axis === 'both';\n      const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';\n      let width = domRect.width;\n      let height = domRect.height;\n      let x = domRect.x;\n      let y = domRect.y;\n      if (offsetX == null && data.x && isXAxis) {\n        offsetX = domRect.x - data.x;\n      }\n      if (offsetY == null && data.y && isYAxis) {\n        offsetY = domRect.y - data.y;\n      }\n      x -= offsetX || 0;\n      y -= offsetY || 0;\n      width = 0;\n      height = 0;\n      if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {\n        width = data.axis === 'y' ? domRect.width : 0;\n        height = data.axis === 'x' ? domRect.height : 0;\n        x = isXAxis && data.x != null ? data.x : x;\n        y = isYAxis && data.y != null ? data.y : y;\n      } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {\n        height = data.axis === 'x' ? domRect.height : height;\n        width = data.axis === 'y' ? domRect.width : width;\n      }\n      isAutoUpdateEvent = true;\n      return {\n        width,\n        height,\n        x,\n        y,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x\n      };\n    }\n  };\n}\nfunction isMouseBasedEvent(event) {\n  return event != null && event.clientX != null;\n}\n/**\n * Positions the floating element relative to a client point (in the viewport),\n * such as the mouse position. By default, it follows the mouse cursor.\n * @see https://floating-ui.com/docs/useClientPoint\n */\nfunction useClientPoint(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    dataRef,\n    elements: {\n      floating,\n      domReference\n    },\n    refs\n  } = context;\n  const {\n    enabled = true,\n    axis = 'both',\n    x = null,\n    y = null\n  } = props;\n  const initialRef = React.useRef(false);\n  const cleanupListenerRef = React.useRef(null);\n  const [pointerType, setPointerType] = React.useState();\n  const [reactive, setReactive] = React.useState([]);\n  const setReference = useEffectEvent((x, y) => {\n    if (initialRef.current) return;\n\n    // Prevent setting if the open event was not a mouse-like one\n    // (e.g. focus to open, then hover over the reference element).\n    // Only apply if the event exists.\n    if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {\n      return;\n    }\n    refs.setPositionReference(createVirtualElement(domReference, {\n      x,\n      y,\n      axis,\n      dataRef,\n      pointerType\n    }));\n  });\n  const handleReferenceEnterOrMove = useEffectEvent(event => {\n    if (x != null || y != null) return;\n    if (!open) {\n      setReference(event.clientX, event.clientY);\n    } else if (!cleanupListenerRef.current) {\n      // If there's no cleanup, there's no listener, but we want to ensure\n      // we add the listener if the cursor landed on the floating element and\n      // then back on the reference (i.e. it's interactive).\n      setReactive([]);\n    }\n  });\n\n  // If the pointer is a mouse-like pointer, we want to continue following the\n  // mouse even if the floating element is transitioning out. On touch\n  // devices, this is undesirable because the floating element will move to\n  // the dismissal touch point.\n  const openCheck = isMouseLikePointerType(pointerType) ? floating : open;\n  const addListener = React.useCallback(() => {\n    // Explicitly specified `x`/`y` coordinates shouldn't add a listener.\n    if (!openCheck || !enabled || x != null || y != null) return;\n    const win = getWindow(floating);\n    function handleMouseMove(event) {\n      const target = getTarget(event);\n      if (!contains(floating, target)) {\n        setReference(event.clientX, event.clientY);\n      } else {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      }\n    }\n    if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {\n      win.addEventListener('mousemove', handleMouseMove);\n      const cleanup = () => {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      };\n      cleanupListenerRef.current = cleanup;\n      return cleanup;\n    }\n    refs.setPositionReference(domReference);\n  }, [openCheck, enabled, x, y, floating, dataRef, refs, domReference, setReference]);\n  React.useEffect(() => {\n    return addListener();\n  }, [addListener, reactive]);\n  React.useEffect(() => {\n    if (enabled && !floating) {\n      initialRef.current = false;\n    }\n  }, [enabled, floating]);\n  React.useEffect(() => {\n    if (!enabled && open) {\n      initialRef.current = true;\n    }\n  }, [enabled, open]);\n  index(() => {\n    if (enabled && (x != null || y != null)) {\n      initialRef.current = false;\n      setReference(x, y);\n    }\n  }, [enabled, x, y, setReference]);\n  const reference = React.useMemo(() => {\n    function setPointerTypeRef(_ref) {\n      let {\n        pointerType\n      } = _ref;\n      setPointerType(pointerType);\n    }\n    return {\n      onPointerDown: setPointerTypeRef,\n      onPointerEnter: setPointerTypeRef,\n      onMouseMove: handleReferenceEnterOrMove,\n      onMouseEnter: handleReferenceEnterOrMove\n    };\n  }, [handleReferenceEnterOrMove]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeProp = normalizable => {\n  var _normalizable$escapeK, _normalizable$outside;\n  return {\n    escapeKey: typeof normalizable === 'boolean' ? normalizable : (_normalizable$escapeK = normalizable == null ? void 0 : normalizable.escapeKey) != null ? _normalizable$escapeK : false,\n    outsidePress: typeof normalizable === 'boolean' ? normalizable : (_normalizable$outside = normalizable == null ? void 0 : normalizable.outsidePress) != null ? _normalizable$outside : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nfunction useDismiss(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    elements,\n    dataRef\n  } = context;\n  const {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles,\n    capture\n  } = props;\n  const tree = useFloatingTree();\n  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const insideReactTreeRef = React.useRef(false);\n  const endedOrStartedInsideRef = React.useRef(false);\n  const {\n    escapeKey: escapeKeyBubbles,\n    outsidePress: outsidePressBubbles\n  } = normalizeProp(bubbles);\n  const {\n    escapeKey: escapeKeyCapture,\n    outsidePress: outsidePressCapture\n  } = normalizeProp(capture);\n  const isComposingRef = React.useRef(false);\n  const closeOnEscapeKeyDown = useEffectEvent(event => {\n    var _dataRef$current$floa;\n    if (!open || !enabled || !escapeKey || event.key !== 'Escape') {\n      return;\n    }\n\n    // Wait until IME is settled. Pressing `Escape` while composing should\n    // close the compose menu, but not the floating element.\n    if (isComposingRef.current) {\n      return;\n    }\n    const nodeId = (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (!escapeKeyBubbles) {\n      event.stopPropagation();\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context;\n          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n    }\n    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event, 'escape-key');\n  });\n  const closeOnEscapeKeyDownCapture = useEffectEvent(event => {\n    var _getTarget2;\n    const callback = () => {\n      var _getTarget;\n      closeOnEscapeKeyDown(event);\n      (_getTarget = getTarget(event)) == null || _getTarget.removeEventListener('keydown', callback);\n    };\n    (_getTarget2 = getTarget(event)) == null || _getTarget2.addEventListener('keydown', callback);\n  });\n  const closeOnPressOutside = useEffectEvent(event => {\n    var _dataRef$current$floa2;\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = insideReactTreeRef.current;\n    insideReactTreeRef.current = false;\n\n    // When click outside is lazy (`click` event), handle dragging.\n    // Don't close if:\n    // - The click started inside the floating element.\n    // - The click ended inside the floating element.\n    const endedOrStartedInside = endedOrStartedInsideRef.current;\n    endedOrStartedInsideRef.current = false;\n    if (outsidePressEvent === 'click' && endedOrStartedInside) {\n      return;\n    }\n    if (insideReactTree) {\n      return;\n    }\n    if (typeof outsidePress === 'function' && !outsidePress(event)) {\n      return;\n    }\n    const target = getTarget(event);\n    const inertSelector = \"[\" + createAttribute('inert') + \"]\";\n    const markers = getDocument(elements.floating).querySelectorAll(inertSelector);\n    let targetRootAncestor = isElement(target) ? target : null;\n    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {\n      const nextParent = getParentNode(targetRootAncestor);\n      if (isLastTraversableNode(nextParent) || !isElement(nextParent)) {\n        break;\n      }\n      targetRootAncestor = nextParent;\n    }\n\n    // Check if the click occurred on a third-party element injected after the\n    // floating element rendered.\n    if (markers.length && isElement(target) && !isRootElement(target) &&\n    // Clicked on a direct ancestor (e.g. FloatingOverlay).\n    !contains(target, elements.floating) &&\n    // If the target root element contains none of the markers, then the\n    // element was injected after the floating element rendered.\n    Array.from(markers).every(marker => !contains(targetRootAncestor, marker))) {\n      return;\n    }\n\n    // Check if the click occurred on the scrollbar\n    if (isHTMLElement(target) && floating) {\n      // In Firefox, `target.scrollWidth > target.clientWidth` for inline\n      // elements.\n      const canScrollX = target.clientWidth > 0 && target.scrollWidth > target.clientWidth;\n      const canScrollY = target.clientHeight > 0 && target.scrollHeight > target.clientHeight;\n      let xCond = canScrollY && event.offsetX > target.clientWidth;\n\n      // In some browsers it is possible to change the <body> (or window)\n      // scrollbar to the left side, but is very rare and is difficult to\n      // check for. Plus, for modal dialogs with backdrops, it is more\n      // important that the backdrop is checked but not so much the window.\n      if (canScrollY) {\n        const isRTL = getComputedStyle(target).direction === 'rtl';\n        if (isRTL) {\n          xCond = event.offsetX <= target.offsetWidth - target.clientWidth;\n        }\n      }\n      if (xCond || canScrollX && event.offsetY > target.clientHeight) {\n        return;\n      }\n    }\n    const nodeId = (_dataRef$current$floa2 = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa2.nodeId;\n    const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n      var _node$context;\n      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n    });\n    if (isEventTargetWithin(event, elements.floating) || isEventTargetWithin(event, elements.domReference) || targetIsInsideChildren) {\n      return;\n    }\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (children.length > 0) {\n      let shouldDismiss = true;\n      children.forEach(child => {\n        var _child$context2;\n        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n          shouldDismiss = false;\n          return;\n        }\n      });\n      if (!shouldDismiss) {\n        return;\n      }\n    }\n    onOpenChange(false, event, 'outside-press');\n  });\n  const closeOnPressOutsideCapture = useEffectEvent(event => {\n    var _getTarget4;\n    const callback = () => {\n      var _getTarget3;\n      closeOnPressOutside(event);\n      (_getTarget3 = getTarget(event)) == null || _getTarget3.removeEventListener(outsidePressEvent, callback);\n    };\n    (_getTarget4 = getTarget(event)) == null || _getTarget4.addEventListener(outsidePressEvent, callback);\n  });\n  React.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    let compositionTimeout = -1;\n    function onScroll(event) {\n      onOpenChange(false, event, 'ancestor-scroll');\n    }\n    function handleCompositionStart() {\n      window.clearTimeout(compositionTimeout);\n      isComposingRef.current = true;\n    }\n    function handleCompositionEnd() {\n      // Safari fires `compositionend` before `keydown`, so we need to wait\n      // until the next tick to set `isComposing` to `false`.\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      compositionTimeout = window.setTimeout(() => {\n        isComposingRef.current = false;\n      },\n      // 0ms or 1ms don't work in Safari. 5ms appears to consistently work.\n      // Only apply to WebKit for the test to remain 0ms.\n      isWebKit() ? 5 : 0);\n    }\n    const doc = getDocument(elements.floating);\n    if (escapeKey) {\n      doc.addEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n      doc.addEventListener('compositionstart', handleCompositionStart);\n      doc.addEventListener('compositionend', handleCompositionEnd);\n    }\n    outsidePress && doc.addEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(elements.domReference)) {\n        ancestors = getOverflowAncestors(elements.domReference);\n      }\n      if (isElement(elements.floating)) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.floating));\n      }\n      if (!isElement(elements.reference) && elements.reference && elements.reference.contextElement) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      if (escapeKey) {\n        doc.removeEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n        doc.removeEventListener('compositionstart', handleCompositionStart);\n        doc.removeEventListener('compositionend', handleCompositionEnd);\n      }\n      outsidePress && doc.removeEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n      window.clearTimeout(compositionTimeout);\n    };\n  }, [dataRef, elements, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, escapeKeyCapture, closeOnEscapeKeyDownCapture, closeOnPressOutside, outsidePressCapture, closeOnPressOutsideCapture]);\n  React.useEffect(() => {\n    insideReactTreeRef.current = false;\n  }, [outsidePress, outsidePressEvent]);\n  const reference = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    [bubbleHandlerKeys[referencePressEvent]]: event => {\n      if (referencePress) {\n        onOpenChange(false, event.nativeEvent, 'reference-press');\n      }\n    }\n  }), [closeOnEscapeKeyDown, onOpenChange, referencePress, referencePressEvent]);\n  const floating = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    onMouseDown() {\n      endedOrStartedInsideRef.current = true;\n    },\n    onMouseUp() {\n      endedOrStartedInsideRef.current = true;\n    },\n    [captureHandlerKeys[outsidePressEvent]]: () => {\n      insideReactTreeRef.current = true;\n    }\n  }), [closeOnEscapeKeyDown, outsidePressEvent]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction useFloatingRootContext(options) {\n  const {\n    open = false,\n    onOpenChange: onOpenChangeProp,\n    elements: elementsProp\n  } = options;\n  const floatingId = useId();\n  const dataRef = React.useRef({});\n  const [events] = React.useState(() => createPubSub());\n  const nested = useFloatingParentNodeId() != null;\n  if (process.env.NODE_ENV !== \"production\") {\n    const optionDomReference = elementsProp.reference;\n    if (optionDomReference && !isElement(optionDomReference)) {\n      error('Cannot pass a virtual element to the `elements.reference` option,', 'as it must be a real DOM element. Use `refs.setPositionReference()`', 'instead.');\n    }\n  }\n  const [positionReference, setPositionReference] = React.useState(elementsProp.reference);\n  const onOpenChange = useEffectEvent((open, event, reason) => {\n    dataRef.current.openEvent = open ? event : undefined;\n    events.emit('openchange', {\n      open,\n      event,\n      reason,\n      nested\n    });\n    onOpenChangeProp == null || onOpenChangeProp(open, event, reason);\n  });\n  const refs = React.useMemo(() => ({\n    setPositionReference\n  }), []);\n  const elements = React.useMemo(() => ({\n    reference: positionReference || elementsProp.reference || null,\n    floating: elementsProp.floating || null,\n    domReference: elementsProp.reference\n  }), [positionReference, elementsProp.reference, elementsProp.floating]);\n  return React.useMemo(() => ({\n    dataRef,\n    open,\n    onOpenChange,\n    elements,\n    events,\n    floatingId,\n    refs\n  }), [open, onOpenChange, elements, events, floatingId, refs]);\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    nodeId\n  } = options;\n  const internalRootContext = useFloatingRootContext({\n    ...options,\n    elements: {\n      reference: null,\n      floating: null,\n      ...options.elements\n    }\n  });\n  const rootContext = options.rootContext || internalRootContext;\n  const computedElements = rootContext.elements;\n  const [_domReference, setDomReference] = React.useState(null);\n  const [positionReference, _setPositionReference] = React.useState(null);\n  const optionDomReference = computedElements == null ? void 0 : computedElements.domReference;\n  const domReference = optionDomReference || _domReference;\n  const domReferenceRef = React.useRef(null);\n  const tree = useFloatingTree();\n  index(() => {\n    if (domReference) {\n      domReferenceRef.current = domReference;\n    }\n  }, [domReference]);\n  const position = useFloating$1({\n    ...options,\n    elements: {\n      ...computedElements,\n      ...(positionReference && {\n        reference: positionReference\n      })\n    }\n  });\n  const setPositionReference = React.useCallback(node => {\n    const computedPositionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      contextElement: node\n    } : node;\n    // Store the positionReference in state if the DOM reference is specified externally via the\n    // `elements.reference` option. This ensures that it won't be overridden on future renders.\n    _setPositionReference(computedPositionReference);\n    position.refs.setReference(computedPositionReference);\n  }, [position.refs]);\n  const setReference = React.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = React.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = React.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const context = React.useMemo(() => ({\n    ...position,\n    ...rootContext,\n    refs,\n    elements,\n    nodeId\n  }), [position, refs, elements, nodeId, rootContext]);\n  index(() => {\n    rootContext.dataRef.current.floatingContext = context;\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return React.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    elements\n  }), [position, refs, elements, context]);\n}\n\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nfunction useFocus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    visibleOnly = true\n  } = props;\n  const blockFocusRef = React.useRef(false);\n  const timeoutRef = React.useRef();\n  const keyboardModalityRef = React.useRef(true);\n  React.useEffect(() => {\n    if (!enabled) return;\n    const win = getWindow(elements.domReference);\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(elements.domReference) && elements.domReference === activeElement(getDocument(elements.domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    function onKeyDown() {\n      keyboardModalityRef.current = true;\n    }\n    win.addEventListener('blur', onBlur);\n    win.addEventListener('keydown', onKeyDown, true);\n    return () => {\n      win.removeEventListener('blur', onBlur);\n      win.removeEventListener('keydown', onKeyDown, true);\n    };\n  }, [elements.domReference, open, enabled]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        reason\n      } = _ref;\n      if (reason === 'reference-press' || reason === 'escape-key') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [events, enabled]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      if (isVirtualPointerEvent(event.nativeEvent)) return;\n      keyboardModalityRef.current = false;\n    },\n    onMouseLeave() {\n      blockFocusRef.current = false;\n    },\n    onFocus(event) {\n      if (blockFocusRef.current) return;\n      const target = getTarget(event.nativeEvent);\n      if (visibleOnly && isElement(target)) {\n        try {\n          // Mac Safari unreliably matches `:focus-visible` on the reference\n          // if focus was outside the page initially - use the fallback\n          // instead.\n          if (isSafari() && isMac()) throw Error();\n          if (!target.matches(':focus-visible')) return;\n        } catch (e) {\n          // Old browsers will throw an error when using `:focus-visible`.\n          if (!keyboardModalityRef.current && !isTypeableElement(target)) {\n            return;\n          }\n        }\n      }\n      onOpenChange(true, event.nativeEvent, 'focus');\n    },\n    onBlur(event) {\n      blockFocusRef.current = false;\n      const relatedTarget = event.relatedTarget;\n      const nativeEvent = event.nativeEvent;\n\n      // Hit the non-modal focus management portal guard. Focus will be\n      // moved into the floating element immediately after.\n      const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';\n\n      // Wait for the window blur listener to fire.\n      timeoutRef.current = window.setTimeout(() => {\n        var _dataRef$current$floa;\n        const activeEl = activeElement(elements.domReference ? elements.domReference.ownerDocument : document);\n\n        // Focus left the page, keep it open.\n        if (!relatedTarget && activeEl === elements.domReference) return;\n\n        // When focusing the reference element (e.g. regular click), then\n        // clicking into the floating element, prevent it from hiding.\n        // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n        // We can not rely on relatedTarget to point to the correct element\n        // as it will only point to the shadow host of the newly focused element\n        // and not the element that actually has received focus if it is located\n        // inside a shadow root.\n        if (contains((_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.refs.floating.current, activeEl) || contains(elements.domReference, activeEl) || movedToFocusGuard) {\n          return;\n        }\n        onOpenChange(false, nativeEvent, 'focus');\n      });\n    }\n  }), [dataRef, elements.domReference, onOpenChange, visibleOnly]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst ACTIVE_KEY = 'active';\nconst SELECTED_KEY = 'selected';\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  const isItem = elementKey === 'item';\n  let domUserProps = userProps;\n  if (isItem && userProps) {\n    const {\n      [ACTIVE_KEY]: _,\n      [SELECTED_KEY]: __,\n      ...validProps\n    } = userProps;\n    domUserProps = validProps;\n  }\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1,\n      [FOCUSABLE_ATTRIBUTE]: ''\n    }),\n    ...domUserProps,\n    ...propsList.map(value => {\n      const propsOrGetProps = value ? value[elementKey] : null;\n      if (typeof propsOrGetProps === 'function') {\n        return userProps ? propsOrGetProps(userProps) : null;\n      }\n      return propsOrGetProps;\n    }).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (isItem && [ACTIVE_KEY, SELECTED_KEY].includes(key)) {\n          return;\n        }\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null || _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\n/**\n * Merges an array of interaction hooks' props into prop getters, allowing\n * event handler functions to be composed together without overwriting one\n * another.\n * @see https://floating-ui.com/docs/useInteractions\n */\nfunction useInteractions(propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  const referenceDeps = propsList.map(key => key == null ? void 0 : key.reference);\n  const floatingDeps = propsList.map(key => key == null ? void 0 : key.floating);\n  const itemDeps = propsList.map(key => key == null ? void 0 : key.item);\n  const getReferenceProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  referenceDeps);\n  const getFloatingProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  floatingDeps);\n  const getItemProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  itemDeps);\n  return React.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n}\n\nlet isPreventScrollSupported = false;\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key === ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  return doSwitch(orientation, vertical, horizontal);\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nfunction useListNavigation(context, props) {\n  const {\n    open,\n    onOpenChange,\n    elements\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    cols = 1,\n    scrollItemIntoView = true,\n    virtualItemRef,\n    itemSizes,\n    dense = false\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (allowEscape) {\n      if (!loop) {\n        warn('`useListNavigation` looping must be enabled to allow escaping.');\n      }\n      if (!virtual) {\n        warn('`useListNavigation` must be virtual to allow escaping.');\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      warn('In grid list navigation mode (`cols` > 1), the `orientation` should', 'be either \"horizontal\" or \"both\".');\n    }\n  }\n  const floatingFocusElement = getFloatingFocusElement(elements.floating);\n  const floatingFocusElementRef = useLatestRef(floatingFocusElement);\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  const onNavigate = useEffectEvent(unstable_onNavigate);\n  const typeableComboboxReference = isTypeableCombobox(elements.domReference);\n  const focusItemOnOpenRef = React.useRef(focusItemOnOpen);\n  const indexRef = React.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = React.useRef(null);\n  const isPointerModalityRef = React.useRef(true);\n  const previousOnNavigateRef = React.useRef(onNavigate);\n  const previousMountedRef = React.useRef(!!elements.floating);\n  const previousOpenRef = React.useRef(open);\n  const forceSyncFocus = React.useRef(false);\n  const forceScrollIntoViewRef = React.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const selectedIndexRef = useLatestRef(selectedIndex);\n  const [activeId, setActiveId] = React.useState();\n  const [virtualId, setVirtualId] = React.useState();\n  const focusItem = useEffectEvent(function (listRef, indexRef, forceScrollIntoView) {\n    if (forceScrollIntoView === void 0) {\n      forceScrollIntoView = false;\n    }\n    function runFocus(item) {\n      if (virtual) {\n        setActiveId(item.id);\n        tree == null || tree.events.emit('virtualfocus', item);\n        if (virtualItemRef) {\n          virtualItemRef.current = item;\n        }\n      } else {\n        enqueueFocus(item, {\n          preventScroll: true,\n          // Mac Safari does not move the virtual cursor unless the focus call\n          // is sync. However, for the very first focus call, we need to wait\n          // for the position to be ready in order to prevent unwanted\n          // scrolling. This means the virtual cursor will not move to the first\n          // item when first opening the floating element, but will on\n          // subsequent calls. `preventScroll` is supported in modern Safari,\n          // so we can use that instead.\n          // iOS Safari must be async or the first item will not be focused.\n          sync: isMac() && isSafari() ? isPreventScrollSupported || forceSyncFocus.current : false\n        });\n      }\n    }\n    const initialItem = listRef.current[indexRef.current];\n    if (initialItem) {\n      runFocus(initialItem);\n    }\n    requestAnimationFrame(() => {\n      const waitedItem = listRef.current[indexRef.current] || initialItem;\n      if (!waitedItem) return;\n      if (!initialItem) {\n        runFocus(waitedItem);\n      }\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        waitedItem.scrollIntoView == null || waitedItem.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  });\n  index(() => {\n    document.createElement('div').focus({\n      get preventScroll() {\n        isPreventScrollSupported = true;\n        return false;\n      }\n    });\n  }, []);\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  index(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        indexRef.current = selectedIndex;\n        onNavigate(selectedIndex);\n      }\n    } else if (previousMountedRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current(null);\n    }\n  }, [enabled, open, elements.floating, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  index(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (activeIndex == null) {\n        forceSyncFocus.current = false;\n        if (selectedIndexRef.current != null) {\n          return;\n        }\n\n        // Reset while the floating element was open (e.g. the list changed).\n        if (previousMountedRef.current) {\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n        }\n\n        // Initial sync.\n        if ((!previousOpenRef.current || !previousMountedRef.current) && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n          let runs = 0;\n          const waitForListPopulated = () => {\n            if (listRef.current[0] == null) {\n              // Avoid letting the browser paint if possible on the first try,\n              // otherwise use rAF. Don't try more than twice, since something\n              // is wrong otherwise.\n              if (runs < 2) {\n                const scheduler = runs ? requestAnimationFrame : queueMicrotask;\n                scheduler(waitForListPopulated);\n              }\n              runs++;\n            } else {\n              indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinIndex(listRef, disabledIndicesRef.current) : getMaxIndex(listRef, disabledIndicesRef.current);\n              keyRef.current = null;\n              onNavigate(indexRef.current);\n            }\n          };\n          waitForListPopulated();\n        }\n      } else if (!isIndexOutOfBounds(listRef, activeIndex)) {\n        indexRef.current = activeIndex;\n        focusItem(listRef, indexRef, forceScrollIntoViewRef.current);\n        forceScrollIntoViewRef.current = false;\n      }\n    }\n  }, [enabled, open, elements.floating, activeIndex, selectedIndexRef, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  index(() => {\n    var _nodes$find;\n    if (!enabled || elements.floating || !tree || virtual || !previousMountedRef.current) {\n      return;\n    }\n    const nodes = tree.nodesRef.current;\n    const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null || (_nodes$find = _nodes$find.context) == null ? void 0 : _nodes$find.elements.floating;\n    const activeEl = activeElement(getDocument(elements.floating));\n    const treeContainsActiveEl = nodes.some(node => node.context && contains(node.context.elements.floating, activeEl));\n    if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {\n      parent.focus({\n        preventScroll: true\n      });\n    }\n  }, [enabled, elements.floating, tree, parentId, virtual]);\n  index(() => {\n    if (!enabled) return;\n    if (!tree) return;\n    if (!virtual) return;\n    if (parentId) return;\n    function handleVirtualFocus(item) {\n      setVirtualId(item.id);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    }\n    tree.events.on('virtualfocus', handleVirtualFocus);\n    return () => {\n      tree.events.off('virtualfocus', handleVirtualFocus);\n    };\n  }, [enabled, tree, virtual, parentId, virtualItemRef]);\n  index(() => {\n    previousOnNavigateRef.current = onNavigate;\n    previousMountedRef.current = !!elements.floating;\n  });\n  index(() => {\n    if (!open) {\n      keyRef.current = null;\n    }\n  }, [open]);\n  index(() => {\n    previousOpenRef.current = open;\n  }, [open]);\n  const hasActiveIndex = activeIndex != null;\n  const item = React.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!open) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1) {\n        onNavigate(index);\n      }\n    }\n    const props = {\n      onFocus(_ref) {\n        let {\n          currentTarget\n        } = _ref;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref2 => {\n        let {\n          currentTarget\n        } = _ref2;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref3) {\n          let {\n            currentTarget\n          } = _ref3;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave(_ref4) {\n          let {\n            pointerType\n          } = _ref4;\n          if (!isPointerModalityRef.current || pointerType === 'touch') {\n            return;\n          }\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n          onNavigate(null);\n          if (!virtual) {\n            enqueueFocus(floatingFocusElementRef.current, {\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [open, floatingFocusElementRef, focusItem, focusItemOnHover, listRef, onNavigate, virtual]);\n  const commonOnKeyDown = useEffectEvent(event => {\n    isPointerModalityRef.current = false;\n    forceSyncFocus.current = true;\n\n    // When composing a character, Chrome fires ArrowDown twice. Firefox/Safari\n    // don't appear to suffer from this. `event.isComposing` is avoided due to\n    // Safari not supporting it properly (although it's not needed in the first\n    // place for Safari, just avoiding any possible issues).\n    if (event.which === 229) {\n      return;\n    }\n\n    // If the floating element is animating out, ignore navigation. Otherwise,\n    // the `activeIndex` gets set to 0 despite not being open so the next time\n    // the user ArrowDowns, the first item won't be focused.\n    if (!latestOpenRef.current && event.currentTarget === floatingFocusElementRef.current) {\n      return;\n    }\n    if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl)) {\n      stopEvent(event);\n      onOpenChange(false, event.nativeEvent, 'list-navigation');\n      if (isHTMLElement(elements.domReference)) {\n        if (virtual) {\n          tree == null || tree.events.emit('virtualfocus', elements.domReference);\n        } else {\n          elements.domReference.focus();\n        }\n      }\n      return;\n    }\n    const currentIndex = indexRef.current;\n    const minIndex = getMinIndex(listRef, disabledIndices);\n    const maxIndex = getMaxIndex(listRef, disabledIndices);\n    if (!typeableComboboxReference) {\n      if (event.key === 'Home') {\n        stopEvent(event);\n        indexRef.current = minIndex;\n        onNavigate(indexRef.current);\n      }\n      if (event.key === 'End') {\n        stopEvent(event);\n        indexRef.current = maxIndex;\n        onNavigate(indexRef.current);\n      }\n    }\n\n    // Grid navigation.\n    if (cols > 1) {\n      const sizes = itemSizes || Array.from({\n        length: listRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = buildCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isDisabled(listRef.current, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isDisabled(listRef.current, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const index = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex != null ? listRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getCellIndices([...(disabledIndices || listRef.current.map((_, index) => isDisabled(listRef.current, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getCellIndexOfCorner(indexRef.current > maxIndex ? minIndex : indexRef.current, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction\n        // we're moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT) ? 'tr' : 'tl'),\n        stopEvent: true\n      })];\n      if (index != null) {\n        indexRef.current = index;\n        onNavigate(indexRef.current);\n      }\n      if (orientation === 'both') {\n        return;\n      }\n    }\n    if (isMainOrientationKey(event.key, orientation)) {\n      stopEvent(event);\n\n      // Reset the index if no item is focused.\n      if (open && !virtual && activeElement(event.currentTarget.ownerDocument) === event.currentTarget) {\n        indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n        onNavigate(indexRef.current);\n        return;\n      }\n      if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n        if (loop) {\n          indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.min(maxIndex, findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          }));\n        }\n      } else {\n        if (loop) {\n          indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.max(minIndex, findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          }));\n        }\n      }\n      if (isIndexOutOfBounds(listRef, indexRef.current)) {\n        onNavigate(null);\n      } else {\n        onNavigate(indexRef.current);\n      }\n    }\n  });\n  const ariaActiveDescendantProp = React.useMemo(() => {\n    return virtual && open && hasActiveIndex && {\n      'aria-activedescendant': virtualId || activeId\n    };\n  }, [virtual, open, hasActiveIndex, virtualId, activeId]);\n  const floating = React.useMemo(() => {\n    return {\n      'aria-orientation': orientation === 'both' ? undefined : orientation,\n      ...(!isTypeableCombobox(elements.domReference) && ariaActiveDescendantProp),\n      onKeyDown: commonOnKeyDown,\n      onPointerMove() {\n        isPointerModalityRef.current = true;\n      }\n    };\n  }, [ariaActiveDescendantProp, commonOnKeyDown, elements.domReference, orientation]);\n  const reference = React.useMemo(() => {\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    return {\n      ...ariaActiveDescendantProp,\n      onKeyDown(event) {\n        isPointerModalityRef.current = false;\n        const isArrowKey = event.key.startsWith('Arrow');\n        const isHomeOrEndKey = ['Home', 'End'].includes(event.key);\n        const isMoveKey = isArrowKey || isHomeOrEndKey;\n        const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);\n        const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl);\n        const isMainKey = isMainOrientationKey(event.key, orientation);\n        const isNavigationKey = (nested ? isCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';\n        if (virtual && open) {\n          const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);\n          const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;\n          if (isMoveKey && deepestNode && virtualItemRef) {\n            const eventObject = new KeyboardEvent('keydown', {\n              key: event.key,\n              bubbles: true\n            });\n            if (isCrossOpenKey || isCrossCloseKey) {\n              var _deepestNode$context, _deepestNode$context2;\n              const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;\n              const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? listRef.current.find(item => (item == null ? void 0 : item.id) === activeId) : null;\n              if (dispatchItem) {\n                stopEvent(event);\n                dispatchItem.dispatchEvent(eventObject);\n                setVirtualId(undefined);\n              }\n            }\n            if ((isMainKey || isHomeOrEndKey) && deepestNode.context) {\n              if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {\n                var _deepestNode$context$;\n                stopEvent(event);\n                (_deepestNode$context$ = deepestNode.context.elements.domReference) == null || _deepestNode$context$.dispatchEvent(eventObject);\n                return;\n              }\n            }\n          }\n          return commonOnKeyDown(event);\n        }\n\n        // If a floating element should not open on arrow key down, avoid\n        // setting `activeIndex` while it's closed.\n        if (!open && !openOnArrowKeyDown && isArrowKey) {\n          return;\n        }\n        if (isNavigationKey) {\n          keyRef.current = nested && isMainKey ? null : event.key;\n        }\n        if (nested) {\n          if (isCrossOpenKey) {\n            stopEvent(event);\n            if (open) {\n              indexRef.current = getMinIndex(listRef, disabledIndicesRef.current);\n              onNavigate(indexRef.current);\n            } else {\n              onOpenChange(true, event.nativeEvent, 'list-navigation');\n            }\n          }\n          return;\n        }\n        if (isMainKey) {\n          if (selectedIndex != null) {\n            indexRef.current = selectedIndex;\n          }\n          stopEvent(event);\n          if (!open && openOnArrowKeyDown) {\n            onOpenChange(true, event.nativeEvent, 'list-navigation');\n          } else {\n            commonOnKeyDown(event);\n          }\n          if (open) {\n            onNavigate(indexRef.current);\n          }\n        }\n      },\n      onFocus() {\n        if (open && !virtual) {\n          onNavigate(null);\n        }\n      },\n      onPointerDown: checkVirtualPointer,\n      onMouseDown: checkVirtualMouse,\n      onClick: checkVirtualMouse\n    };\n  }, [activeId, ariaActiveDescendantProp, commonOnKeyDown, disabledIndicesRef, focusItemOnOpen, listRef, nested, onNavigate, onOpenChange, open, openOnArrowKeyDown, orientation, rtl, selectedIndex, tree, virtual, virtualItemRef]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\nconst componentRoleToAriaRoleMap = /*#__PURE__*/new Map([['select', 'listbox'], ['combobox', 'listbox'], ['label', false]]);\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nfunction useRole(context, props) {\n  var _componentRoleToAriaR;\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    floatingId\n  } = context;\n  const {\n    enabled = true,\n    role = 'dialog'\n  } = props;\n  const ariaRole = (_componentRoleToAriaR = componentRoleToAriaRoleMap.get(role)) != null ? _componentRoleToAriaR : role;\n  const referenceId = useId();\n  const parentId = useFloatingParentNodeId();\n  const isNested = parentId != null;\n  const reference = React.useMemo(() => {\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return {\n        [\"aria-\" + (role === 'label' ? 'labelledby' : 'describedby')]: open ? floatingId : undefined\n      };\n    }\n    return {\n      'aria-expanded': open ? 'true' : 'false',\n      'aria-haspopup': ariaRole === 'alertdialog' ? 'dialog' : ariaRole,\n      'aria-controls': open ? floatingId : undefined,\n      ...(ariaRole === 'listbox' && {\n        role: 'combobox'\n      }),\n      ...(ariaRole === 'menu' && {\n        id: referenceId\n      }),\n      ...(ariaRole === 'menu' && isNested && {\n        role: 'menuitem'\n      }),\n      ...(role === 'select' && {\n        'aria-autocomplete': 'none'\n      }),\n      ...(role === 'combobox' && {\n        'aria-autocomplete': 'list'\n      })\n    };\n  }, [ariaRole, floatingId, isNested, open, referenceId, role]);\n  const floating = React.useMemo(() => {\n    const floatingProps = {\n      id: floatingId,\n      ...(ariaRole && {\n        role: ariaRole\n      })\n    };\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return floatingProps;\n    }\n    return {\n      ...floatingProps,\n      ...(ariaRole === 'menu' && {\n        'aria-labelledby': referenceId\n      })\n    };\n  }, [ariaRole, floatingId, referenceId, role]);\n  const item = React.useCallback(_ref => {\n    let {\n      active,\n      selected\n    } = _ref;\n    const commonProps = {\n      role: 'option',\n      ...(active && {\n        id: floatingId + \"-option\"\n      })\n    };\n\n    // For `menu`, we are unable to tell if the item is a `menuitemradio`\n    // or `menuitemcheckbox`. For backwards-compatibility reasons, also\n    // avoid defaulting to `menuitem` as it may overwrite custom role props.\n    switch (role) {\n      case 'select':\n        return {\n          ...commonProps,\n          'aria-selected': active && selected\n        };\n      case 'combobox':\n        {\n          return {\n            ...commonProps,\n            ...(active && {\n              'aria-selected': true\n            })\n          };\n        }\n    }\n    return {};\n  }, [floatingId, role]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction execWithArgsOrReturn(valueOrFn, args) {\n  return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;\n}\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = React.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  React.useEffect(() => {\n    if (!open && isMounted) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, isMounted, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    duration = 250\n  } = props;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [status, setStatus] = React.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n  if (!isMounted && status === 'close') {\n    setStatus('unmounted');\n  }\n  index(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        setStatus('open');\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    }\n    setStatus('close');\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = props;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const fnArgs = React.useMemo(() => ({\n    side,\n    placement\n  }), [side, placement]);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [styles, setStyles] = React.useState(() => ({\n    ...execWithArgsOrReturn(unstable_common, fnArgs),\n    ...execWithArgsOrReturn(unstable_initial, fnArgs)\n  }));\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  index(() => {\n    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);\n    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);\n    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);\n    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nfunction useTypeahead(context, props) {\n  var _ref;\n  const {\n    open,\n    dataRef\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch,\n    onTypingChange: unstable_onTypingChange,\n    enabled = true,\n    findMatch = null,\n    resetMs = 750,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = props;\n  const timeoutIdRef = React.useRef();\n  const stringRef = React.useRef('');\n  const prevIndexRef = React.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);\n  const matchIndexRef = React.useRef(null);\n  const onMatch = useEffectEvent(unstable_onMatch);\n  const onTypingChange = useEffectEvent(unstable_onTypingChange);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  index(() => {\n    if (open) {\n      clearTimeout(timeoutIdRef.current);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  index(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref2;\n      prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  const setTypingChange = useEffectEvent(value => {\n    if (value) {\n      if (!dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    } else {\n      if (dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    }\n  });\n  const onKeyDown = useEffectEvent(event => {\n    function getMatchingIndex(list, orderedList, string) {\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);\n      return str ? list.indexOf(str) : -1;\n    }\n    const listContent = listRef.current;\n    if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n      if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {\n        setTypingChange(false);\n      } else if (event.key === ' ') {\n        stopEvent(event);\n      }\n    }\n    if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n    // Character key.\n    event.key.length !== 1 ||\n    // Modifier key.\n    event.ctrlKey || event.metaKey || event.altKey) {\n      return;\n    }\n    if (open && event.key !== ' ') {\n      stopEvent(event);\n      setTypingChange(true);\n    }\n\n    // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n    // allow it in this case, too.\n    const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n      var _text$, _text$2;\n      return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n    });\n\n    // Allows the user to cycle through items that start with the same letter\n    // in rapid succession.\n    if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n    }\n    stringRef.current += event.key;\n    clearTimeout(timeoutIdRef.current);\n    timeoutIdRef.current = setTimeout(() => {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n      setTypingChange(false);\n    }, resetMs);\n    const prevIndex = prevIndexRef.current;\n    const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);\n    if (index !== -1) {\n      onMatch(index);\n      matchIndexRef.current = index;\n    } else if (event.key !== ' ') {\n      stringRef.current = '';\n      setTypingChange(false);\n    }\n  });\n  const reference = React.useMemo(() => ({\n    onKeyDown\n  }), [onKeyDown]);\n  const floating = React.useMemo(() => {\n    return {\n      onKeyDown,\n      onKeyUp(event) {\n        if (event.key === ' ') {\n          setTypingChange(false);\n        }\n      }\n    };\n  }, [onKeyDown, setTypingChange]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside of it is\n * anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = evaluate(props, state);\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    const scrollEl = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n\n    // Valid combinations:\n    // 1. Floating element is the scrollRef and has a border (default)\n    // 2. Floating element is not the scrollRef, floating element has a border\n    // 3. Floating element is not the scrollRef, scrollRef has a border\n    // Floating > {...getFloatingProps()} wrapper > scrollRef > items is not\n    // allowed as VoiceOver doesn't work.\n    const clientTop = floating.clientTop || scrollEl.clientTop;\n    const floatingIsBordered = floating.clientTop !== 0;\n    const scrollElIsBordered = scrollEl.clientTop !== 0;\n    const floatingIsScrollEl = floating === scrollEl;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!state.placement.startsWith('bottom')) {\n        warn('`placement` side must be \"bottom\" when using the `inner`', 'middleware.');\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await offset(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const overflow = await detectOverflow(getArgsWithCustomFloatingHeight(nextArgs, scrollEl.scrollHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    const refOverflow = await detectOverflow(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const isScrollable = scrollEl.scrollHeight > scrollEl.clientHeight;\n    const rounder = isScrollable ? v => v : round;\n    const maxHeight = rounder(max(0, scrollEl.scrollHeight + (floatingIsBordered && floatingIsScrollEl || scrollElIsBordered ? clientTop * 2 : 0) - diffY - max(0, overflow.bottom)));\n    scrollEl.style.maxHeight = maxHeight + \"px\";\n    scrollEl.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      const shouldFallback = scrollEl.offsetHeight < item.offsetHeight * min(minItemsVisible, listRef.current.length) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold;\n      ReactDOM.flushSync(() => onFallbackChange(shouldFallback));\n    }\n    if (overflowRef) {\n      overflowRef.current = await detectOverflow(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, scrollEl.offsetHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n */\nfunction useInnerOffset(context, props) {\n  const {\n    open,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = props;\n  const onChange = useEffectEvent(unstable_onChange);\n  const controlledScrollingRef = React.useRef(false);\n  const prevScrollTopRef = React.useRef(null);\n  const initialOverflowRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        ReactDOM.flushSync(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  const floating = React.useMemo(() => ({\n    onKeyDown() {\n      controlledScrollingRef.current = true;\n    },\n    onWheel() {\n      controlledScrollingRef.current = false;\n    },\n    onPointerMove() {\n      controlledScrollingRef.current = false;\n    },\n    onScroll() {\n      const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n      if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n        return;\n      }\n      if (prevScrollTopRef.current !== null) {\n        const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n        if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n          ReactDOM.flushSync(() => onChange(d => d + scrollDiff));\n        }\n      }\n\n      // [Firefox] Wait for the height change to have been applied.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n      });\n    }\n  }), [elements.floating, onChange, overflowRef, scrollRef]);\n  return React.useMemo(() => enabled ? {\n    floating\n  } : {}, [enabled, floating]);\n}\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\n/**\n * Generates a safe polygon area that the user can traverse without closing the\n * floating element once leaving the reference element.\n * @see https://floating-ui.com/docs/useHover#safepolygon\n */\nfunction safePolygon(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    buffer = 0.5,\n    blockPointerEvents = false,\n    requireIntent = true\n  } = options;\n  let timeoutId;\n  let hasLanded = false;\n  let lastX = null;\n  let lastY = null;\n  let lastCursorTime = performance.now();\n  function getCursorSpeed(x, y) {\n    const currentTime = performance.now();\n    const elapsedTime = currentTime - lastCursorTime;\n    if (lastX === null || lastY === null || elapsedTime === 0) {\n      lastX = x;\n      lastY = y;\n      lastCursorTime = currentTime;\n      return null;\n    }\n    const deltaX = x - lastX;\n    const deltaY = y - lastY;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const speed = distance / elapsedTime; // px / ms\n\n    lastX = x;\n    lastY = y;\n    lastCursorTime = currentTime;\n    return speed;\n  }\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeout(timeoutId);\n        onClose();\n      }\n      clearTimeout(timeoutId);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      const isFloatingWider = rect.width > refRect.width;\n      const isFloatingTaller = rect.height > refRect.height;\n      const left = (isFloatingWider ? refRect : rect).left;\n      const right = (isFloatingWider ? refRect : rect).right;\n      const top = (isFloatingTaller ? refRect : rect).top;\n      const bottom = (isFloatingTaller ? refRect : rect).bottom;\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getChildren(tree.nodesRef.current, nodeId).some(_ref2 => {\n        let {\n          context\n        } = _ref2;\n        return context == null ? void 0 : context.open;\n      })) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[left, refRect.top + 1], [left, rect.bottom - 1], [right, rect.bottom - 1], [right, refRect.top + 1]];\n          break;\n        case 'bottom':\n          rectPoly = [[left, rect.top + 1], [left, refRect.bottom - 1], [right, refRect.bottom - 1], [right, rect.top + 1]];\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, bottom], [rect.right - 1, top], [refRect.left + 1, top], [refRect.left + 1, bottom]];\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, bottom], [refRect.right - 1, top], [rect.left + 1, top], [rect.left + 1, bottom]];\n          break;\n      }\n      function getPolygon(_ref3) {\n        let [x, y] = _ref3;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      if (isPointInPolygon([clientX, clientY], rectPoly)) {\n        return;\n      }\n      if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isLeave && requireIntent) {\n        const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);\n        const cursorSpeedThreshold = 0.1;\n        if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {\n          return close();\n        }\n      }\n      if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {\n        close();\n      } else if (!hasLanded && requireIntent) {\n        timeoutId = window.setTimeout(close, 40);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\nexport { Composite, CompositeItem, FloatingArrow, FloatingDelayGroup, FloatingFocusManager, FloatingList, FloatingNode, FloatingOverlay, FloatingPortal, FloatingTree, inner, safePolygon, useClick, useClientPoint, useDelayGroup, useDelayGroupContext, useDismiss, useFloating, useFloatingNodeId, useFloatingParentNodeId, useFloatingPortalNode, useFloatingRootContext, useFloatingTree, useFocus, useHover, useId, useInnerOffset, useInteractions, useListItem, useListNavigation, useMergeRefs, useRole, useTransitionStatus, useTransitionStyles, useTypeahead };\n"], "names": ["SafeReact", "useSafeInsertionEffect", "useInsertionEffect", "fn", "useEffectEvent", "callback", "ref", "current", "_len", "arguments", "length", "args", "Array", "_key", "ARROW_UP", "ARROW_DOWN", "ARROW_LEFT", "ARROW_RIGHT", "index", "document", "useLayoutEffect", "useEffect", "horizontalKeys", "verticalKeys", "serverHandoffComplete", "count", "genId", "Math", "random", "toString", "slice", "useId", "id", "setId", "undefined", "createPubSub", "map", "Map", "emit", "event", "data", "_map$get", "get", "for<PERSON>ach", "handler", "on", "listener", "set", "off", "_map$get2", "filter", "l", "FloatingNodeContext", "FloatingTreeContext", "useFloatingParentNodeId", "_React$useContext", "useFloatingTree", "FOCUSABLE_ATTRIBUTE", "useFloating", "options", "nodeId", "internalRootContext", "open", "onOpenChange", "onOpenChangeProp", "elements", "elementsProp", "floatingId", "dataRef", "events", "nested", "positionReference", "setPositionReference", "reference", "reason", "openEvent", "refs", "floating", "domReference", "useFloatingRootContext", "rootContext", "computedElements", "_domReference", "setDomReference", "_setPositionReference", "domReferenceRef", "tree", "position", "node", "computedPositionReference", "getBoundingClientRect", "contextElement", "setReference", "context", "floatingContext", "nodesRef", "find", "ACTIVE_KEY", "SELECTED_KEY", "mergeProps", "userProps", "propsList", "elementKey", "isItem", "domUserProps", "_", "__", "validProps", "tabIndex", "value", "propsOrGetProps", "concat", "reduce", "acc", "props", "Object", "entries", "_ref", "key", "includes", "indexOf", "has", "push", "val", "useInteractions", "referenceDeps", "floatingDeps", "itemDeps", "item", "getReferenceProps", "getFloatingProps", "getItemProps", "getArgsWithCustomFloatingHeight", "state", "height", "rects", "inner", "name", "listRef", "overflowRef", "onFallbackChange", "offset", "innerOffset", "minItemsVisible", "referenceOverflowThreshold", "scrollRef", "detectOverflowOptions", "scrollEl", "clientTop", "floatingIsBordered", "scrollElIsBordered", "floatingIsScrollEl", "nextArgs", "offsetTop", "offsetHeight", "overflow", "scrollHeight", "refOverflow", "elementContext", "diffY", "top", "nextY", "y", "maxHeight", "clientHeight", "v", "bottom", "style", "scrollTop", "<PERSON><PERSON><PERSON><PERSON>", "useInnerOffset", "enabled", "onChange", "unstable_onChange", "controlledScrollingRef", "prevScrollTopRef", "initialOverflowRef", "onWheel", "e", "ctrl<PERSON>ey", "el", "dY", "deltaY", "isAtTop", "isAtBottom", "remainingScroll", "sign", "method", "preventDefault", "d", "test", "addEventListener", "requestAnimationFrame", "removeEventListener", "onKeyDown", "onPointerMove", "onScroll", "scrollDiff"], "sourceRoot": ""}