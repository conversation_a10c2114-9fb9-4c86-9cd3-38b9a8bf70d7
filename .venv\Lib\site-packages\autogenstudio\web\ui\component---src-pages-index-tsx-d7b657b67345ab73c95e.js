"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[245],{2323:function(e,t,a){a.r(t),a.d(t,{default:function(){return z}});var s=a(6540),n=a(5312),l=a(436),o=a(9036),r=a(5625),i=a(2744),c=a(9850),m=a(2605),d=a(8458),u=a(9957),f=a(5319),p=a(4716),E=a(2941),y=a(418),g=a(4810);const h=e=>{let{session:t,onSave:a,onCancel:n,isOpen:l,teams:r}=e;const[i]=m.A.useForm(),{0:c,1:h}=(0,s.useState)(!1),[v,w]=o.Ay.useMessage();(0,s.useEffect)((()=>{l?i.setFieldsValue({name:(null==t?void 0:t.name)||"",team_id:(null==t?void 0:t.team_id)||void 0}):i.resetFields()}),[i,t,l]);const S=!c&&0===r.length;return s.createElement(d.A,{title:t?"Edit Session":"Create Session",open:l,onCancel:n,footer:null,className:"text-primary",forceRender:!0},w,s.createElement(m.A,{form:i,name:"session-form",layout:"vertical",onFinish:async e=>{try{await a({...e,id:null==t?void 0:t.id}),v.success(`Session ${t?"updated":"created"} successfully`)}catch(s){s instanceof Error&&v.error(s.message)}},onFinishFailed:e=>{v.error("Please check the form for errors"),console.error("Form validation failed:",e)},autoComplete:"off"},s.createElement(m.A.Item,{label:"Session Name",name:"name",rules:[{required:!0,message:"Please enter a session name"},{max:100,message:"Session name cannot exceed 100 characters"}]},s.createElement(u.A,null)),s.createElement("div",{className:"space-y-2   w-full"},s.createElement(m.A.Item,{className:"w-full",label:"Team",name:"team_id",rules:[{required:!0,message:"Please select a team"}]},s.createElement(f.A,{placeholder:"Select a team",loading:c,disabled:c||S,showSearch:!0,optionFilterProp:"children",filterOption:(e,t)=>{var a;return(null!==(a=null==t?void 0:t.label)&&void 0!==a?a:"").toLowerCase().includes(e.toLowerCase())},options:r.map((e=>({value:e.id,label:`${e.component.label} (${e.component.component_type})`}))),notFoundContent:c?s.createElement(p.A,{size:"small"}):null}))),s.createElement("div",{className:"text-sm text-accent "},s.createElement(g.Link,{to:"/build"},"view all teams")),S&&s.createElement("div",{className:"flex border p-1 rounded -mt-2 mb-4 items-center gap-1.5 text-sm text-yellow-600"},s.createElement(y.A,{className:"h-4 w-4"}),s.createElement("span",null,"No teams found. Please create a team first.")),s.createElement(m.A.Item,{className:"flex justify-end mb-0"},s.createElement("div",{className:"flex gap-2"},s.createElement(E.Ay,{onClick:n},"Cancel"),s.createElement(E.Ay,{type:"primary",htmlType:"submit",disabled:S},t?"Update":"Create")))))};var v=a(208),w=a(955),S=a(9910),x=a(697),N=a(9644),b=a(85),A=a(4060),C=a(7213),k=a(8188),L=a(2708),I=a(2206),G=a(8603),j=a(7015),P=a(2640),F=a(5107),T=a(180),O=a(8017);var _=e=>{let{teams:t,isLoading:a,onStartSession:n}=e;const{0:o,1:r}=(0,s.useState)(),{0:i,1:c}=(0,s.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("lastUsedTeamId");return e?parseInt(e):void 0}})),{0:m,1:d}=(0,s.useState)(""),u=t.filter((e=>{var t,a;return(null===(t=e.component.label)||void 0===t?void 0:t.toLowerCase().includes(m.toLowerCase()))||(null===(a=e.component.description)||void 0===a?void 0:a.toLowerCase().includes(m.toLowerCase()))}));(0,s.useEffect)((()=>{i&&t.some((e=>e.id===i))?r(i):t.length>0&&r(t[0].id)}),[t,i]);const f=!a&&0===t.length,p={items:[{type:"group",label:s.createElement("div",null,s.createElement("div",{className:"text-xs text-secondary mb-1"},"Select a team"),s.createElement(O.A,{prefix:s.createElement(j.A,{className:"w-4 h-4"}),placeholder:"Search teams",onChange:e=>d(e.target.value)})),key:"from-team"},{type:"divider"}].concat((0,l.A)(u.map((e=>{var t;return{label:s.createElement("div",null,s.createElement("div",null,(0,T.EJ)(e.component.label||"",20)),s.createElement("div",{className:"text-xs text-secondary"},e.component.component_type)),key:(null==e||null===(t=e.id)||void 0===t?void 0:t.toString())||"",icon:s.createElement(P.A,{className:"w-4 h-4"})}})))),onClick:async e=>{const a=parseInt(e.key);t.find((e=>e.id===a))?r(a):console.error("Selected team not found:",a)}},E=t.find((e=>e.id===o));return s.createElement("div",{className:"space-y-2 w-full"},s.createElement(G.A.Button,{menu:p,type:"primary",className:"w-full",placement:"bottomRight",icon:s.createElement(F.A,{className:"w-4 h-4"}),onClick:async()=>{if(!o)return;"undefined"!=typeof window&&localStorage.setItem("lastUsedTeamId",o.toString());const e=t.find((e=>e.id===o));e&&(await new Promise((e=>setTimeout(e,100))),n(o,e.component.label||""))},disabled:!o||a},s.createElement("div",{className:"",style:{width:"183px"}},s.createElement(x.A,{className:"w-4 h-4 inline-block -mt-1"})," New Session")),s.createElement("div",{className:"text-xs text-secondary",title:null==E?void 0:E.component.label},(0,T.EJ)((null==E?void 0:E.component.label)||"",30)),f&&s.createElement("div",{className:"flex items-center gap-1.5 text-xs text-yellow-600 mt-1"},s.createElement(C.A,{className:"h-3 w-3"}),s.createElement("span",null,"Create a team to get started")))};const D=e=>{let{isOpen:t,sessions:a,currentSession:n,onToggle:l,onSelectSession:o,onEditSession:r,onDeleteSession:i,isLoading:c=!1,onStartSession:m,teams:d}=e;return t?s.createElement("div",{className:"h-full border-r border-secondary "},s.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},s.createElement("div",{className:"flex items-center gap-2"},s.createElement("span",{className:"text-primary font-medium"},"Sessions"),s.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},a.length)),s.createElement(w.A,{title:"Close Sidebar"},s.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s.createElement(N.A,{strokeWidth:1.5,className:"h-6 w-6"})))),s.createElement("div",{className:"my-4 flex text-sm  "},s.createElement("div",{className:" mr-2 w-full pr-2"},t&&s.createElement(_,{teams:d,isLoading:c,onStartSession:m}))),s.createElement("div",{className:"py-2 flex text-sm text-secondary"},s.createElement(b.A,{className:"w-4 h-4 inline-block mr-1.5"}),s.createElement("div",{className:"inline-block -mt-0.5"},"Recents"," ",s.createElement("span",{className:"text-accent text-xs mx-1 mt-0.5"}," ","(",a.length,")"," ")," "),c&&s.createElement(A.A,{className:"w-4 h-4 inline-block ml-2 animate-spin"})),!c&&0===a.length&&s.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded "},s.createElement(C.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No recent sessions found"),s.createElement("div",{className:"overflow-y-auto   scroll   h-[calc(100%-181px)]"},a.map((e=>s.createElement("div",{key:e.id,className:"relative"},s.createElement("div",{className:"bg-accent absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80  rounded "+((null==n?void 0:n.id)===e.id?"bg-accent":"bg-tertiary")}," "),s.createElement("div",{className:"group ml-1 flex items-center justify-between rounded-l p-2 py-1 text-sm cursor-pointer hover:bg-tertiary "+((null==n?void 0:n.id)===e.id?"border-accent bg-secondary":""),onClick:()=>o(e)},s.createElement("div",{className:"flex flex-col min-w-0 flex-1 mr-2"},s.createElement("div",{className:"truncate text-sm"},e.name),s.createElement("span",{className:"truncate text-xs text-secondary"},(0,I.vq)(e.updated_at||""))),s.createElement("div",{className:"py-3 flex gap-1 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"},s.createElement(w.A,{title:"Edit session"},s.createElement(E.Ay,{type:"text",size:"small",className:"p-1 min-w-[24px] h-6",icon:s.createElement(k.A,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation(),r(e)}})),s.createElement(w.A,{title:"Delete session"},s.createElement(E.Ay,{type:"text",size:"small",className:"p-1 min-w-[24px] h-6",danger:!0,icon:s.createElement(L.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),e.id&&i(e.id)}}))))))))):s.createElement("div",{className:"h-full  border-r border-secondary"},s.createElement("div",{className:"p-2 -ml-2 "},s.createElement(w.A,{title:s.createElement("span",null,"Sessions"," ",s.createElement("span",{className:"text-accent mx-1"}," ",a.length," ")," ")},s.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s.createElement(S.A,{strokeWidth:1.5,className:"h-6 w-6"})))),s.createElement("div",{className:"mt-4 px-2 -ml-1"},s.createElement(w.A,{title:"Create new session"},s.createElement(E.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:()=>r(),icon:s.createElement(x.A,{className:"w-4 h-4"})}))))};var R=a(2197),U=a(1511),$=a(2571);const J=(0,U.v)(((e,t)=>({galleries:[],selectedGallery:null,isLoading:!1,error:null,fetchGalleries:async a=>{try{e({isLoading:!0,error:null});const s=await $.f.listGalleries(a);e({galleries:s,selectedGallery:t().selectedGallery||s[0]||null,isLoading:!1})}catch(s){e({error:s instanceof Error?s.message:"Failed to fetch galleries",isLoading:!1})}},selectGallery:t=>{e({selectedGallery:t})},getSelectedGallery:()=>t().selectedGallery}))),M=()=>{const{0:e,1:t}=(0,s.useState)([]),{0:a,1:n}=(0,s.useState)(!1),{0:m,1:d}=(0,s.useState)(!1),{0:u,1:f}=(0,s.useState)(),{0:p,1:E}=(0,s.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("sessionSidebar");return null===e||JSON.parse(e)}return!0})),[y,g]=o.Ay.useMessage(),{user:w}=(0,s.useContext)(i.v),{session:S,setSession:x,sessions:N,setSessions:b}=(0,r.J)(),{0:A,1:C}=(0,s.useState)(!1),{0:k,1:L}=(0,s.useState)(null),I=J();(0,s.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("sessionSidebar",JSON.stringify(p))}),[p]);const G=(0,s.useCallback)((async()=>{if(null!=w&&w.id)try{n(!0);const e=await c.j.listSessions(w.id);b(e);const t=new URLSearchParams(window.location.search).get("sessionId");!S&&e.length>0&&!t&&x(e[0])}catch(e){console.error("Error fetching sessions:",e),y.error("Error loading sessions")}finally{n(!1)}}),[null==w?void 0:w.id,b,S,x]);(0,s.useEffect)((()=>{const e=new URLSearchParams(window.location.search).get("sessionId");e&&!S&&j({id:parseInt(e)})}),[]),(0,s.useEffect)((()=>{const e=()=>{!new URLSearchParams(window.location.search).get("sessionId")&&S&&x(null)};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)}),[S]);const j=async e=>{if(null!=w&&w.id&&e.id)try{n(!0);const t=await c.j.getSession(e.id,w.id);if(!t)return y.error("Session not found"),window.history.pushState({},"",window.location.pathname),void(N.length>0?x(N[0]):x(null));x(t),window.history.pushState({},"",`?sessionId=${e.id}`)}catch(t){console.error("Error loading session:",t),y.error("Error loading session")}finally{n(!1)}};(0,s.useEffect)((()=>{G()}),[G]);const P=(0,s.useCallback)((async()=>{if(null!=w&&w.id)try{n(!0);const e=await R.CG.listTeams(w.id);if(e.length>0)t(e);else{console.log("No teams found, creating default team"),await I.fetchGalleries(w.id);const e=I.getSelectedGallery(),a=null==e?void 0:e.config.components.teams[0];if(console.log("Default Gallery .. manager fetching ",a),a){const e={component:a},s=await R.CG.createTeam(e,w.id);console.log("Default team created:",e),t([s])}}}catch(e){console.error("Error fetching teams:",e),y.error("Error loading teams")}finally{n(!1)}}),[null==w?void 0:w.id,y]);return(0,s.useEffect)((()=>{P()}),[P]),s.createElement("div",{className:"relative flex h-full w-full"},g,s.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(p?"w-64":"w-12")},s.createElement(D,{isOpen:p,teams:e,onStartSession:async(e,t)=>{if(null!=w&&w.id)try{const a=`${t.substring(0,20)} - ${(new Date).toLocaleString()} Session`,s=await c.j.createSession({name:a,team_id:e},w.id);b([s].concat((0,l.A)(N))),x(s),y.success("Session created!")}catch(a){y.error("Error creating session")}},sessions:N,currentSession:S,onToggle:()=>E(!p),onSelectSession:j,onEditSession:e=>{f(e),d(!0)},onDeleteSession:async e=>{if(null!=w&&w.id)try{await c.j.deleteSession(e,w.id);b(N.filter((t=>t.id!==e))),(null==S?void 0:S.id)!==e&&0!==N.length||(x(N[0]||null),window.history.pushState({},"",window.location.pathname)),y.success("Session deleted")}catch(t){console.error("Error deleting session:",t),y.error("Error deleting session")}},isLoading:a})),s.createElement("div",{className:"flex-1 transition-all duration-200 "+(p?"ml-64":"ml-12")},S&&N.length>0?s.createElement("div",{className:"pl-4 flex gap-4"},s.createElement("div",{className:"flex-1 "+(A?"w-1/2":"w-full")},s.createElement(v.A,{session:S,isCompareMode:A,onCompareClick:()=>{if(N.length>1){const e=N.find((e=>e.id!==(null==S?void 0:S.id)));L(e||S)}else L(S);C(!0)},onSessionChange:j,availableSessions:N})),A&&s.createElement("div",{className:"flex-1 w-1/2 border-l border-secondary/20 pl-4"},s.createElement(v.A,{session:k,isCompareMode:!0,isSecondaryView:!0,onExitCompare:()=>{C(!1),L(null)},onSessionChange:L,availableSessions:N}))):s.createElement("div",{className:"flex items-center justify-center h-full text-secondary"},"No session selected. Create or select a session from the sidebar.")),s.createElement(h,{teams:e,session:u,isOpen:m,onSave:async e=>{if(null!=w&&w.id)try{if(e.id){const t=await c.j.updateSession(e.id,e,w.id);b(N.map((e=>e.id===t.id?t:e))),(null==S?void 0:S.id)===t.id&&x(t)}else{const t=await c.j.createSession(e,w.id);b([t].concat((0,l.A)(N))),x(t)}d(!1),f(void 0)}catch(t){y.error("Error saving session"),console.error(t)}},onCancel:()=>{d(!1),f(void 0)}}))};var z=e=>{let{data:t}=e;return s.createElement(n.A,{meta:t.site.siteMetadata,title:"Home",link:"/"},s.createElement("main",{style:{height:"100%"},className:" h-full "},s.createElement(M,null)))}}}]);
//# sourceMappingURL=component---src-pages-index-tsx-d7b657b67345ab73c95e.js.map