/*! For license information please see ac9637fc46bc1f6d812d954ce915b60ead6677c4-11523dfd34cc8047a009.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[634],{64:function(e,t,n){n.d(t,{A:function(){return u},d:function(){return l}});var o=n(6540),r=n(7852),i=n(2065);function l(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function a(e){const{closable:t,closeIcon:n}=e||{};return o.useMemo((()=>{if(!t&&(!1===t||!1===n||null===n))return!1;if(void 0===t&&void 0===n)return null;let e={closeIcon:"boolean"!=typeof n&&null!==n?n:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e}),[t,n])}function c(){const e={};for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return n.forEach((t=>{t&&Object.keys(t).forEach((n=>{void 0!==t[n]&&(e[n]=t[n])}))})),e}const s={};function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;const l=a(e),u=a(t),d="boolean"!=typeof l&&!!(null==l?void 0:l.disabled),m=o.useMemo((()=>Object.assign({closeIcon:o.createElement(r.A,null)},n)),[n]),p=o.useMemo((()=>!1!==l&&(l?c(m,u,l):!1!==u&&(u?c(m,u):!!m.closable&&m))),[l,u,m]);return o.useMemo((()=>{if(!1===p)return[!1,null,d];const{closeIconRender:e}=m,{closeIcon:t}=p;let n=t;if(null!=n){e&&(n=e(t));const r=(0,i.A)(p,!0);Object.keys(r).length&&(n=o.isValidElement(n)?o.cloneElement(n,r):o.createElement("span",Object.assign({},r),n))}return[!0,n,d]}),[p,m])}},234:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},329:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},551:function(e,t,n){n.d(t,{A:function(){return T}});var o=n(8168),r=n(2284),i=n(9379),l=n(4467),a=n(5544),c=n(3986),s=n(6942),u=n.n(s),d=n(8462),m=n(1470),p=n(981),f=n(6540),g=n(961),v=f.forwardRef((function(e,t){var n=e.height,r=e.offsetY,a=e.offsetX,c=e.children,s=e.prefixCls,m=e.onInnerResize,p=e.innerProps,g=e.rtl,v=e.extra,h={},b={display:"flex",flexDirection:"column"};return void 0!==r&&(h={height:n,position:"relative",overflow:"hidden"},b=(0,i.A)((0,i.A)({},b),{},(0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)({transform:"translateY(".concat(r,"px)")},g?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),f.createElement("div",{style:h},f.createElement(d.A,{onResize:function(e){e.offsetHeight&&m&&m()}},f.createElement("div",(0,o.A)({style:b,className:u()((0,l.A)({},"".concat(s,"-holder-inner"),s)),ref:t},p),c,v)))}));v.displayName="Filler";var h=v;function b(e){var t=e.children,n=e.setRef,o=f.useCallback((function(e){n(e)}),[]);return f.cloneElement(t,{ref:o})}function y(e,t,n){var o=f.useState(e),r=(0,a.A)(o,2),i=r[0],l=r[1],c=f.useState(null),s=(0,a.A)(c,2),u=s[0],d=s[1];return f.useEffect((function(){var o=function(e,t,n){var o,r,i=e.length,l=t.length;if(0===i&&0===l)return null;i<l?(o=e,r=t):(o=t,r=e);var a={__EMPTY_ITEM__:!0};function c(e){return void 0!==e?n(e):a}for(var s=null,u=1!==Math.abs(i-l),d=0;d<r.length;d+=1){var m=c(o[d]);if(m!==c(r[d])){s=d,u=u||m!==c(r[d+1]);break}}return null===s?null:{index:s,multiple:u}}(i||[],e||[],t);void 0!==(null==o?void 0:o.index)&&(null==n||n(o.index),d(e[o.index])),l(e)}),[e]),[u]}var $=n(5371),C="object"===("undefined"==typeof navigator?"undefined":(0,r.A)(navigator))&&/Firefox/i.test(navigator.userAgent),A=function(e,t,n,o){var r=(0,f.useRef)(!1),i=(0,f.useRef)(null);var l=(0,f.useRef)({top:e,bottom:t,left:n,right:o});return l.current.top=e,l.current.bottom=t,l.current.left=n,l.current.right=o,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e?t<0&&l.current.left||t>0&&l.current.right:t<0&&l.current.top||t>0&&l.current.bottom;return n&&o?(clearTimeout(i.current),r.current=!1):o&&!r.current||(clearTimeout(i.current),r.current=!0,i.current=setTimeout((function(){r.current=!1}),50)),!r.current&&o}};function w(e,t,n,o,r,i,l){var a=(0,f.useRef)(0),c=(0,f.useRef)(null),s=(0,f.useRef)(null),u=(0,f.useRef)(!1),d=A(t,n,o,r);var m=(0,f.useRef)(null),p=(0,f.useRef)(null);return[function(t){if(e){$.A.cancel(p.current),p.current=(0,$.A)((function(){m.current=null}),2);var n=t.deltaX,o=t.deltaY,r=t.shiftKey,f=n,g=o;("sx"===m.current||!m.current&&r&&o&&!n)&&(f=o,g=0,m.current="sx");var v=Math.abs(f),h=Math.abs(g);null===m.current&&(m.current=i&&v>h?"x":"y"),"y"===m.current?function(e,t){if($.A.cancel(c.current),!d(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,a.current+=t,s.current=t,C||n.preventDefault(),c.current=(0,$.A)((function(){var e=u.current?10:1;l(a.current*e,!1),a.current=0})))}}(t,g):function(e,t){l(t,!0),C||e.preventDefault()}(t,f)}},function(t){e&&(u.current=t.detail===s.current)}]}var x=n(3029),S=n(2901),E=function(){function e(){(0,x.A)(this,e),(0,l.A)(this,"maps",void 0),(0,l.A)(this,"id",0),(0,l.A)(this,"diffKeys",new Set),this.maps=Object.create(null)}return(0,S.A)(e,[{key:"set",value:function(e,t){this.maps[e]=t,this.id+=1,this.diffKeys.add(e)}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffKeys.clear()}},{key:"getRecord",value:function(){return this.diffKeys}}]),e}();function I(e){var t=parseFloat(e);return isNaN(t)?0:t}var O=14/15;function M(e){return Math.floor(Math.pow(e,.5))}function k(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var R=f.forwardRef((function(e,t){var n=e.prefixCls,o=e.rtl,r=e.scrollOffset,c=e.scrollRange,s=e.onStartMove,d=e.onStopMove,m=e.onScroll,p=e.horizontal,g=e.spinSize,v=e.containerSize,h=e.style,b=e.thumbStyle,y=e.showScrollBar,C=f.useState(!1),A=(0,a.A)(C,2),w=A[0],x=A[1],S=f.useState(null),E=(0,a.A)(S,2),I=E[0],O=E[1],M=f.useState(null),R=(0,a.A)(M,2),z=R[0],B=R[1],N=!o,j=f.useRef(),P=f.useRef(),H=f.useState(y),T=(0,a.A)(H,2),D=T[0],L=T[1],F=f.useRef(),W=function(){!0!==y&&!1!==y&&(clearTimeout(F.current),L(!0),F.current=setTimeout((function(){L(!1)}),3e3))},K=c-v||0,_=v-g||0,V=f.useMemo((function(){return 0===r||0===K?0:r/K*_}),[r,K,_]),X=f.useRef({top:V,dragging:w,pageY:I,startTop:z});X.current={top:V,dragging:w,pageY:I,startTop:z};var q=function(e){x(!0),O(k(e,p)),B(X.current.top),s(),e.stopPropagation(),e.preventDefault()};f.useEffect((function(){var e=function(e){e.preventDefault()},t=j.current,n=P.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",q,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",q)}}),[]);var G=f.useRef();G.current=K;var Y=f.useRef();Y.current=_,f.useEffect((function(){if(w){var e,t=function(t){var n=X.current,o=n.dragging,r=n.pageY,i=n.startTop;$.A.cancel(e);var l=j.current.getBoundingClientRect(),a=v/(p?l.width:l.height);if(o){var c=(k(t,p)-r)*a,s=i;!N&&p?s-=c:s+=c;var u=G.current,d=Y.current,f=d?s/d:0,g=Math.ceil(f*u);g=Math.max(g,0),g=Math.min(g,u),e=(0,$.A)((function(){m(g,p)}))}},n=function(){x(!1),d()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),$.A.cancel(e)}}}),[w]),f.useEffect((function(){return W(),function(){clearTimeout(F.current)}}),[r]),f.useImperativeHandle(t,(function(){return{delayHidden:W}}));var U="".concat(n,"-scrollbar"),Q={position:"absolute",visibility:D?null:"hidden"},J={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return p?(Q.height=8,Q.left=0,Q.right=0,Q.bottom=0,J.height="100%",J.width=g,N?J.left=V:J.right=V):(Q.width=8,Q.top=0,Q.bottom=0,N?Q.right=0:Q.left=0,J.width="100%",J.height=g,J.top=V),f.createElement("div",{ref:j,className:u()(U,(0,l.A)((0,l.A)((0,l.A)({},"".concat(U,"-horizontal"),p),"".concat(U,"-vertical"),!p),"".concat(U,"-visible"),D)),style:(0,i.A)((0,i.A)({},Q),h),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:W},f.createElement("div",{ref:P,className:u()("".concat(U,"-thumb"),(0,l.A)({},"".concat(U,"-thumb-moving"),w)),style:(0,i.A)((0,i.A)({},J),b),onMouseDown:q}))}));function z(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var B=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],N=[],j={overflowY:"auto",overflowAnchor:"none"};function P(e,t){var n=e.prefixCls,s=void 0===n?"rc-virtual-list":n,v=e.className,C=e.height,x=e.itemHeight,S=e.fullHeight,P=void 0===S||S,H=e.style,T=e.data,D=e.children,L=e.itemKey,F=e.virtual,W=e.direction,K=e.scrollWidth,_=e.component,V=void 0===_?"div":_,X=e.onScroll,q=e.onVirtualScroll,G=e.onVisibleChange,Y=e.innerProps,U=e.extraRender,Q=e.styles,J=e.showScrollBar,Z=void 0===J?"optional":J,ee=(0,c.A)(e,B),te=f.useCallback((function(e){return"function"==typeof L?L(e):null==e?void 0:e[L]}),[L]),ne=function(e,t,n){var o=f.useState(0),r=(0,a.A)(o,2),i=r[0],l=r[1],c=(0,f.useRef)(new Map),s=(0,f.useRef)(new E),u=(0,f.useRef)(0);function d(){u.current+=1}function m(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];d();var t=function(){var e=!1;c.current.forEach((function(t,n){if(t&&t.offsetParent){var o=t.offsetHeight,r=getComputedStyle(t),i=r.marginTop,l=r.marginBottom,a=o+I(i)+I(l);s.current.get(n)!==a&&(s.current.set(n,a),e=!0)}})),e&&l((function(e){return e+1}))};if(e)t();else{u.current+=1;var n=u.current;Promise.resolve().then((function(){n===u.current&&t()}))}}return(0,f.useEffect)((function(){return d}),[]),[function(o,r){var i=e(o),l=c.current.get(i);r?(c.current.set(i,r),m()):c.current.delete(i),!l!=!r&&(r?null==t||t(o):null==n||n(o))},m,s.current,i]}(te,null,null),oe=(0,a.A)(ne,4),re=oe[0],ie=oe[1],le=oe[2],ae=oe[3],ce=!(!1===F||!C||!x),se=f.useMemo((function(){return Object.values(le.maps).reduce((function(e,t){return e+t}),0)}),[le.id,le.maps]),ue=ce&&T&&(Math.max(x*T.length,se)>C||!!K),de="rtl"===W,me=u()(s,(0,l.A)({},"".concat(s,"-rtl"),de),v),pe=T||N,fe=(0,f.useRef)(),ge=(0,f.useRef)(),ve=(0,f.useRef)(),he=(0,f.useState)(0),be=(0,a.A)(he,2),ye=be[0],$e=be[1],Ce=(0,f.useState)(0),Ae=(0,a.A)(Ce,2),we=Ae[0],xe=Ae[1],Se=(0,f.useState)(!1),Ee=(0,a.A)(Se,2),Ie=Ee[0],Oe=Ee[1],Me=function(){Oe(!0)},ke=function(){Oe(!1)},Re={getKey:te};function ze(e){$e((function(t){var n=function(e){var t=e;Number.isNaN(Qe.current)||(t=Math.min(t,Qe.current));return t=Math.max(t,0),t}("function"==typeof e?e(t):e);return fe.current.scrollTop=n,n}))}var Be=(0,f.useRef)({start:0,end:pe.length}),Ne=(0,f.useRef)(),je=y(pe,te),Pe=(0,a.A)(je,1)[0];Ne.current=Pe;var He=f.useMemo((function(){if(!ce)return{scrollHeight:void 0,start:0,end:pe.length-1,offset:void 0};var e;if(!ue)return{scrollHeight:(null===(e=ge.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:pe.length-1,offset:void 0};for(var t,n,o,r=0,i=pe.length,l=0;l<i;l+=1){var a=pe[l],c=te(a),s=le.get(c),u=r+(void 0===s?x:s);u>=ye&&void 0===t&&(t=l,n=r),u>ye+C&&void 0===o&&(o=l),r=u}return void 0===t&&(t=0,n=0,o=Math.ceil(C/x)),void 0===o&&(o=pe.length-1),{scrollHeight:r,start:t,end:o=Math.min(o+1,pe.length-1),offset:n}}),[ue,ce,ye,pe,ae,C]),Te=He.scrollHeight,De=He.start,Le=He.end,Fe=He.offset;Be.current.start=De,Be.current.end=Le,f.useLayoutEffect((function(){var e=le.getRecord();if(1===e.size){var t=Array.from(e)[0],n=pe[De];if(n)if(te(n)===t){var o=le.get(t)-x;ze((function(e){return e+o}))}}le.resetRecord()}),[Te]);var We=f.useState({width:0,height:C}),Ke=(0,a.A)(We,2),_e=Ke[0],Ve=Ke[1],Xe=(0,f.useRef)(),qe=(0,f.useRef)(),Ge=f.useMemo((function(){return z(_e.width,K)}),[_e.width,K]),Ye=f.useMemo((function(){return z(_e.height,Te)}),[_e.height,Te]),Ue=Te-C,Qe=(0,f.useRef)(Ue);Qe.current=Ue;var Je=ye<=0,Ze=ye>=Ue,et=we<=0,tt=we>=K,nt=A(Je,Ze,et,tt),ot=function(){return{x:de?-we:we,y:ye}},rt=(0,f.useRef)(ot()),it=(0,m._q)((function(e){if(q){var t=(0,i.A)((0,i.A)({},ot()),e);rt.current.x===t.x&&rt.current.y===t.y||(q(t),rt.current=t)}}));function lt(e,t){var n=e;t?((0,g.flushSync)((function(){xe(n)})),it()):ze(n)}var at=function(e){var t=e,n=K?K-_e.width:0;return t=Math.max(t,0),t=Math.min(t,n)},ct=(0,m._q)((function(e,t){t?((0,g.flushSync)((function(){xe((function(t){return at(t+(de?-e:e))}))})),it()):ze((function(t){return t+e}))})),st=w(ce,Je,Ze,et,tt,!!K,ct),ut=(0,a.A)(st,2),dt=ut[0],mt=ut[1];!function(e,t,n){var o,r=(0,f.useRef)(!1),i=(0,f.useRef)(0),l=(0,f.useRef)(0),a=(0,f.useRef)(null),c=(0,f.useRef)(null),s=function(e){if(r.current){var t=Math.ceil(e.touches[0].pageX),o=Math.ceil(e.touches[0].pageY),a=i.current-t,s=l.current-o,u=Math.abs(a)>Math.abs(s);u?i.current=t:l.current=o;var d=n(u,u?a:s,!1,e);d&&e.preventDefault(),clearInterval(c.current),d&&(c.current=setInterval((function(){u?a*=O:s*=O;var e=Math.floor(u?a:s);(!n(u,e,!0)||Math.abs(e)<=.1)&&clearInterval(c.current)}),16))}},u=function(){r.current=!1,o()},d=function(e){o(),1!==e.touches.length||r.current||(r.current=!0,i.current=Math.ceil(e.touches[0].pageX),l.current=Math.ceil(e.touches[0].pageY),a.current=e.target,a.current.addEventListener("touchmove",s,{passive:!1}),a.current.addEventListener("touchend",u,{passive:!0}))};o=function(){a.current&&(a.current.removeEventListener("touchmove",s),a.current.removeEventListener("touchend",u))},(0,p.A)((function(){return e&&t.current.addEventListener("touchstart",d,{passive:!0}),function(){var e;null===(e=t.current)||void 0===e||e.removeEventListener("touchstart",d),o(),clearInterval(c.current)}}),[e])}(ce,fe,(function(e,t,n,o){var r=o;return!nt(e,t,n)&&((!r||!r._virtualHandled)&&(r&&(r._virtualHandled=!0),dt({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0))})),function(e,t,n){f.useEffect((function(){var o=t.current;if(e&&o){var r,i,l=!1,a=function(){$.A.cancel(r)},c=function e(){a(),r=(0,$.A)((function(){n(i),e()}))},s=function(e){if(!e.target.draggable){var t=e;t._virtualHandled||(t._virtualHandled=!0,l=!0)}},u=function(){l=!1,a()},d=function(e){if(l){var t=k(e,!1),n=o.getBoundingClientRect(),r=n.top,s=n.bottom;t<=r?(i=-M(r-t),c()):t>=s?(i=M(t-s),c()):a()}};return o.addEventListener("mousedown",s),o.ownerDocument.addEventListener("mouseup",u),o.ownerDocument.addEventListener("mousemove",d),function(){o.removeEventListener("mousedown",s),o.ownerDocument.removeEventListener("mouseup",u),o.ownerDocument.removeEventListener("mousemove",d),a()}}}),[e])}(ue,fe,(function(e){ze((function(t){return t+e}))})),(0,p.A)((function(){function e(e){var t=Je&&e.detail<0,n=Ze&&e.detail>0;!ce||t||n||e.preventDefault()}var t=fe.current;return t.addEventListener("wheel",dt,{passive:!1}),t.addEventListener("DOMMouseScroll",mt,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",dt),t.removeEventListener("DOMMouseScroll",mt),t.removeEventListener("MozMousePixelScroll",e)}}),[ce,Je,Ze]),(0,p.A)((function(){if(K){var e=at(we);xe(e),it({x:e})}}),[_e.width,K]);var pt=function(){var e,t;null===(e=Xe.current)||void 0===e||e.delayHidden(),null===(t=qe.current)||void 0===t||t.delayHidden()},ft=function(e,t,n,o,l,c,s,u){var d=f.useRef(),m=f.useState(null),g=(0,a.A)(m,2),v=g[0],h=g[1];return(0,p.A)((function(){if(v&&v.times<10){if(!e.current)return void h((function(e){return(0,i.A)({},e)}));c();var r=v.targetAlign,a=v.originAlign,u=v.index,d=v.offset,m=e.current.clientHeight,p=!1,f=r,g=null;if(m){for(var b=r||a,y=0,$=0,C=0,A=Math.min(t.length-1,u),w=0;w<=A;w+=1){var x=l(t[w]);$=y;var S=n.get(x);y=C=$+(void 0===S?o:S)}for(var E="top"===b?d:m-d,I=A;I>=0;I-=1){var O=l(t[I]),M=n.get(O);if(void 0===M){p=!0;break}if((E-=M)<=0)break}switch(b){case"top":g=$-d;break;case"bottom":g=C-m+d;break;default:var k=e.current.scrollTop;$<k?f="top":C>k+m&&(f="bottom")}null!==g&&s(g),g!==v.lastTop&&(p=!0)}p&&h((0,i.A)((0,i.A)({},v),{},{times:v.times+1,targetAlign:f,lastTop:g}))}}),[v,e.current]),function(e){if(null!=e){if($.A.cancel(d.current),"number"==typeof e)s(e);else if(e&&"object"===(0,r.A)(e)){var n,o=e.align;n="index"in e?e.index:t.findIndex((function(t){return l(t)===e.key}));var i=e.offset;h({times:0,index:n,offset:void 0===i?0:i,originAlign:o})}}else u()}}(fe,pe,le,x,te,(function(){return ie(!0)}),ze,pt);f.useImperativeHandle(t,(function(){return{nativeElement:ve.current,getScrollInfo:ot,scrollTo:function(e){var t;(t=e)&&"object"===(0,r.A)(t)&&("left"in t||"top"in t)?(void 0!==e.left&&xe(at(e.left)),ft(e.top)):ft(e)}}})),(0,p.A)((function(){if(G){var e=pe.slice(De,Le+1);G(e,pe)}}),[De,Le,pe]);var gt=function(e,t,n,o){var r=f.useMemo((function(){return[new Map,[]]}),[e,n.id,o]),i=(0,a.A)(r,2),l=i[0],c=i[1];return function(r){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r,a=l.get(r),s=l.get(i);if(void 0===a||void 0===s)for(var u=e.length,d=c.length;d<u;d+=1){var m,p=e[d],f=t(p);l.set(f,d);var g=null!==(m=n.get(f))&&void 0!==m?m:o;if(c[d]=(c[d-1]||0)+g,f===r&&(a=d),f===i&&(s=d),void 0!==a&&void 0!==s)break}return{top:c[a-1]||0,bottom:c[s]}}}(pe,te,le,x),vt=null==U?void 0:U({start:De,end:Le,virtual:ue,offsetX:we,offsetY:Fe,rtl:de,getSize:gt}),ht=function(e,t,n,o,r,i,l,a){var c=a.getKey;return e.slice(t,n+1).map((function(e,n){var a=l(e,t+n,{style:{width:o},offsetX:r}),s=c(e);return f.createElement(b,{key:s,setRef:function(t){return i(e,t)}},a)}))}(pe,De,Le,K,we,re,D,Re),bt=null;C&&(bt=(0,i.A)((0,l.A)({},P?"height":"maxHeight",C),j),ce&&(bt.overflowY="hidden",K&&(bt.overflowX="hidden"),Ie&&(bt.pointerEvents="none")));var yt={};return de&&(yt.dir="rtl"),f.createElement("div",(0,o.A)({ref:ve,style:(0,i.A)((0,i.A)({},H),{},{position:"relative"}),className:me},yt,ee),f.createElement(d.A,{onResize:function(e){Ve({width:e.offsetWidth,height:e.offsetHeight})}},f.createElement(V,{className:"".concat(s,"-holder"),style:bt,ref:fe,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==ye&&ze(t),null==X||X(e),it()},onMouseEnter:pt},f.createElement(h,{prefixCls:s,height:Te,offsetX:we,offsetY:Fe,scrollWidth:K,onInnerResize:ie,ref:ge,innerProps:Y,rtl:de,extra:vt},ht))),ue&&Te>C&&f.createElement(R,{ref:Xe,prefixCls:s,scrollOffset:ye,scrollRange:Te,rtl:de,onScroll:lt,onStartMove:Me,onStopMove:ke,spinSize:Ye,containerSize:_e.height,style:null==Q?void 0:Q.verticalScrollBar,thumbStyle:null==Q?void 0:Q.verticalScrollBarThumb,showScrollBar:Z}),ue&&K>_e.width&&f.createElement(R,{ref:qe,prefixCls:s,scrollOffset:we,scrollRange:K,rtl:de,onScroll:lt,onStartMove:Me,onStopMove:ke,spinSize:Ge,containerSize:_e.width,horizontal:!0,style:null==Q?void 0:Q.horizontalScrollBar,thumbStyle:null==Q?void 0:Q.horizontalScrollBarThumb,showScrollBar:Z}))}var H=f.forwardRef(P);H.displayName="List";var T=H},697:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},977:function(e,t){t.A=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}})},2102:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2197:function(e,t,n){n.d(t,{CG:function(){return c},gw:function(){return a}});var o=n(7387),r=n(3838);let i=function(e){function t(){return e.apply(this,arguments)||this}(0,o.A)(t,e);var n=t.prototype;return n.listTeams=async function(e){const t=await fetch(`${this.getBaseUrl()}/teams/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch teams");return n.data},n.getTeam=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/teams/${e}?user_id=${t}`,{headers:this.getHeaders()}),o=await n.json();if(!o.status)throw new Error(o.message||"Failed to fetch team");return o.data},n.createTeam=async function(e,t){const n={...e,user_id:t},o=await fetch(`${this.getBaseUrl()}/teams/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify(n)}),r=await o.json();if(!r.status)throw new Error(r.message||"Failed to create team");return r.data},n.deleteTeam=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/teams/${e}?user_id=${t}`,{method:"DELETE",headers:this.getHeaders()}),o=await n.json();if(!o.status)throw new Error(o.message||"Failed to delete team")},t}(r.y),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o.A)(t,e);var n=t.prototype;return n.validateComponent=async function(e){const t=await fetch(`${this.getBaseUrl()}/validate/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({component:e})}),n=await t.json();if(!t.ok)throw new Error(n.message||"Failed to validate component");return n},n.testComponent=async function(e,t){void 0===t&&(t=60);const n=await fetch(`${this.getBaseUrl()}/validate/test`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({component:e,timeout:t})}),o=await n.json();if(!n.ok)throw new Error(o.message||"Failed to test component");return o},t}(r.y);const a=new l,c=new i},2318:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},2708:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2877:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},3425:function(e,t,n){n.d(t,{U:function(){return a}});var o=n(6540),r=n(2533),i=n(867),l=n(2279);function a(e){return t=>o.createElement(i.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},o.createElement(e,Object.assign({},t)))}t.A=(e,t,n,i,c)=>a((a=>{const{prefixCls:s,style:u}=a,d=o.useRef(null),[m,p]=o.useState(0),[f,g]=o.useState(0),[v,h]=(0,r.A)(!1,{value:a.open}),{getPrefixCls:b}=o.useContext(l.QO),y=b(i||"select",s);o.useEffect((()=>{if(h(!0),"undefined"!=typeof ResizeObserver){const e=new ResizeObserver((e=>{const t=e[0].target;p(t.offsetHeight+8),g(t.offsetWidth)})),t=setInterval((()=>{var n;const o=c?`.${c(y)}`:`.${y}-dropdown`,r=null===(n=d.current)||void 0===n?void 0:n.querySelector(o);r&&(clearInterval(t),e.observe(r))}),10);return()=>{clearInterval(t),e.disconnect()}}}),[]);let $=Object.assign(Object.assign({},a),{style:Object.assign(Object.assign({},u),{margin:0}),open:v,visible:v,getPopupContainer:()=>d.current});n&&($=n($)),t&&Object.assign($,{[t]:{overflow:{adjustX:!1,adjustY:!1}}});const C={paddingBottom:m,position:"relative",minWidth:f};return o.createElement("div",{ref:d,style:C},o.createElement(e,Object.assign({},$)))}))},3497:function(e,t,n){n.d(t,{A:function(){return A}});var o=n(8168),r=n(4467),i=n(5544),l=n(3986),a=n(2427),c=n(6942),s=n.n(c),u=n(8719),d=n(6540),m=n(6928),p=n(5371),f=m.A.ESC,g=m.A.TAB;var v=(0,d.forwardRef)((function(e,t){var n=e.overlay,o=e.arrow,r=e.prefixCls,i=(0,d.useMemo)((function(){return"function"==typeof n?n():n}),[n]),l=(0,u.K4)(t,(0,u.A9)(i));return d.createElement(d.Fragment,null,o&&d.createElement("div",{className:"".concat(r,"-arrow")}),d.cloneElement(i,{ref:(0,u.f3)(i)?l:void 0}))})),h={adjustX:1,adjustY:1},b=[0,0],y={topLeft:{points:["bl","tl"],overflow:h,offset:[0,-4],targetOffset:b},top:{points:["bc","tc"],overflow:h,offset:[0,-4],targetOffset:b},topRight:{points:["br","tr"],overflow:h,offset:[0,-4],targetOffset:b},bottomLeft:{points:["tl","bl"],overflow:h,offset:[0,4],targetOffset:b},bottom:{points:["tc","bc"],overflow:h,offset:[0,4],targetOffset:b},bottomRight:{points:["tr","br"],overflow:h,offset:[0,4],targetOffset:b}},$=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function C(e,t){var n,c=e.arrow,m=void 0!==c&&c,h=e.prefixCls,b=void 0===h?"rc-dropdown":h,C=e.transitionName,A=e.animation,w=e.align,x=e.placement,S=void 0===x?"bottomLeft":x,E=e.placements,I=void 0===E?y:E,O=e.getPopupContainer,M=e.showAction,k=e.hideAction,R=e.overlayClassName,z=e.overlayStyle,B=e.visible,N=e.trigger,j=void 0===N?["hover"]:N,P=e.autoFocus,H=e.overlay,T=e.children,D=e.onVisibleChange,L=(0,l.A)(e,$),F=d.useState(),W=(0,i.A)(F,2),K=W[0],_=W[1],V="visible"in e?B:K,X=d.useRef(null),q=d.useRef(null),G=d.useRef(null);d.useImperativeHandle(t,(function(){return X.current}));var Y=function(e){_(e),null==D||D(e)};!function(e){var t=e.visible,n=e.triggerRef,o=e.onVisibleChange,r=e.autoFocus,i=e.overlayRef,l=d.useRef(!1),a=function(){var e,r;t&&(null===(e=n.current)||void 0===e||null===(r=e.focus)||void 0===r||r.call(e),null==o||o(!1))},c=function(){var e;return!(null===(e=i.current)||void 0===e||!e.focus||(i.current.focus(),l.current=!0,0))},s=function(e){switch(e.keyCode){case f:a();break;case g:var t=!1;l.current||(t=c()),t?e.preventDefault():a()}};d.useEffect((function(){return t?(window.addEventListener("keydown",s),r&&(0,p.A)(c,3),function(){window.removeEventListener("keydown",s),l.current=!1}):function(){l.current=!1}}),[t])}({visible:V,triggerRef:G,onVisibleChange:Y,autoFocus:P,overlayRef:q});var U,Q,J,Z=function(){return d.createElement(v,{ref:q,overlay:H,prefixCls:b,arrow:m})},ee=d.cloneElement(T,{className:s()(null===(n=T.props)||void 0===n?void 0:n.className,V&&(U=e.openClassName,void 0!==U?U:"".concat(b,"-open"))),ref:(0,u.f3)(T)?(0,u.K4)(G,(0,u.A9)(T)):void 0}),te=k;return te||-1===j.indexOf("contextMenu")||(te=["click"]),d.createElement(a.A,(0,o.A)({builtinPlacements:I},L,{prefixCls:b,ref:X,popupClassName:s()(R,(0,r.A)({},"".concat(b,"-show-arrow"),m)),popupStyle:z,action:j,showAction:M,hideAction:te,popupPlacement:S,popupAlign:w,popupTransitionName:C,popupAnimation:A,popupVisible:V,stretch:(Q=e.minOverlayWidthMatchTrigger,J=e.alignPoint,("minOverlayWidthMatchTrigger"in e?Q:!J)?"minWidth":""),popup:"function"==typeof H?Z:Z(),onPopupVisibleChange:Y,onPopupClick:function(t){var n=e.onOverlayClick;_(!1),n&&n(t)},getPopupContainer:O}),ee)}var A=d.forwardRef(C)},3561:function(e,t,n){n.d(t,{YU:function(){return c},_j:function(){return f},nP:function(){return a},ox:function(){return i},vR:function(){return l}});var o=n(2187),r=n(4980);const i=new o.Mo("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),l=new o.Mo("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),a=new o.Mo("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),c=new o.Mo("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),s=new o.Mo("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),u=new o.Mo("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),d=new o.Mo("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),m=new o.Mo("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),p={"slide-up":{inKeyframes:i,outKeyframes:l},"slide-down":{inKeyframes:a,outKeyframes:c},"slide-left":{inKeyframes:s,outKeyframes:u},"slide-right":{inKeyframes:d,outKeyframes:m}},f=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:i,outKeyframes:l}=p[t];return[(0,r.b)(o,i,l,e.motionDurationMid),{[`\n      ${o}-enter,\n      ${o}-appear\n    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]}},3838:function(e,t,n){n.d(t,{y:function(){return r}});var o=n(180);let r=function(){function e(){}var t=e.prototype;return t.getBaseUrl=function(){return(0,o.Tt)()},t.getHeaders=function(){const e=localStorage.getItem("auth_token"),t={"Content-Type":"application/json"};return e&&(t.Authorization=`Bearer ${e}`),t},e}()},4103:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},4129:function(e,t,n){n.d(t,{M:function(){return o}});const o=n(6540).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},4211:function(e,t,n){n.d(t,{Mh:function(){return m}});var o=n(2187),r=n(4980);const i=new o.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new o.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),a=new o.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new o.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),s=new o.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),u=new o.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d={"move-up":{inKeyframes:new o.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:l},"move-left":{inKeyframes:a,outKeyframes:c},"move-right":{inKeyframes:s,outKeyframes:u}},m=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:i,outKeyframes:l}=d[t];return[(0,r.b)(o,i,l,e.motionDurationMid),{[`\n        ${o}-enter,\n        ${o}-appear\n      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},4440:function(e,t,n){n.d(t,{cH:function(){return l},lB:function(){return a}});var o=n(2187),r=n(7358);const i=e=>{const{antCls:t,componentCls:n,colorText:r,footerBg:i,headerHeight:l,headerPadding:a,headerColor:c,footerPadding:s,fontSize:u,bodyBg:d,headerBg:m}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:l,padding:a,color:c,lineHeight:(0,o.zA)(l),background:m,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:s,color:r,fontSize:u,background:i},[`${n}-content`]:{flex:"auto",color:r,minHeight:0}}},l=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:i,marginXXS:l,colorTextLightSolid:a,colorBgContainer:c}=e,s=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:`0 ${s}px`,headerColor:r,footerPadding:`${i}px ${s}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+2*l,triggerBg:"#002140",triggerColor:a,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:r}},a=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]];t.Ay=(0,r.OF)("Layout",(e=>[i(e)]),l,{deprecatedTokens:a})},4471:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5006:function(e,t,n){n.d(t,{L3:function(){return c},i4:function(){return s},xV:function(){return u}});var o=n(2187),r=n(7358),i=n(4277);const l=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},a=(e,t)=>((e,t)=>{const{prefixCls:n,componentCls:o,gridColumns:r}=e,i={};for(let l=r;l>=0;l--)0===l?(i[`${o}${t}-${l}`]={display:"none"},i[`${o}-push-${l}`]={insetInlineStart:"auto"},i[`${o}-pull-${l}`]={insetInlineEnd:"auto"},i[`${o}${t}-push-${l}`]={insetInlineStart:"auto"},i[`${o}${t}-pull-${l}`]={insetInlineEnd:"auto"},i[`${o}${t}-offset-${l}`]={marginInlineStart:0},i[`${o}${t}-order-${l}`]={order:0}):(i[`${o}${t}-${l}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${l/r*100}%`,maxWidth:l/r*100+"%"}],i[`${o}${t}-push-${l}`]={insetInlineStart:l/r*100+"%"},i[`${o}${t}-pull-${l}`]={insetInlineEnd:l/r*100+"%"},i[`${o}${t}-offset-${l}`]={marginInlineStart:l/r*100+"%"},i[`${o}${t}-order-${l}`]={order:l});return i[`${o}${t}-flex`]={flex:`var(--${n}${t}-flex)`},i})(e,t),c=(0,r.OF)("Grid",(e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}}),(()=>({}))),s=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),u=(0,r.OF)("Grid",(e=>{const t=(0,i.oX)(e,{gridColumns:24}),n=s(t);return delete n.xs,[l(t),a(t,""),a(t,"-xs"),Object.keys(n).map((e=>((e,t,n)=>({[`@media (min-width: ${(0,o.zA)(t)})`]:Object.assign({},a(e,n))}))(t,n[e],`-${e}`))).reduce(((e,t)=>Object.assign(Object.assign({},e),t)),{})]}),(()=>({})))},5128:function(e,t,n){var o=n(6540),r=n(2279),i=n(7308);t.A=e=>{const{componentName:t}=e,{getPrefixCls:n}=(0,o.useContext)(r.QO),l=n("empty");switch(t){case"Table":case"List":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE,className:`${l}-small`});case"Table.filter":return null;default:return o.createElement(i.A,null)}}},5254:function(e,t,n){n.d(t,{A:function(){return r}});var o=n(6540);function r(e,t){const n=(0,o.useRef)([]),r=()=>{n.current.push(setTimeout((()=>{var t,n,o,r;(null===(t=e.current)||void 0===t?void 0:t.input)&&"password"===(null===(n=e.current)||void 0===n?void 0:n.input.getAttribute("type"))&&(null===(o=e.current)||void 0===o?void 0:o.input.hasAttribute("value"))&&(null===(r=e.current)||void 0===r||r.input.removeAttribute("value"))})))};return(0,o.useEffect)((()=>(t&&r(),()=>n.current.forEach((e=>{e&&clearTimeout(e)})))),[]),r}},5319:function(e,t,n){n.d(t,{A:function(){return It}});var o=n(6540),r=n(6942),i=n.n(r),l=n(8168),a=n(436),c=n(4467),s=n(9379),u=n(5544),d=n(3986),m=n(2284),p=n(2533),f=n(8210),g=n(981),v=n(8430),h=n(8719),b=function(e){var t=e.className,n=e.customizeIcon,r=e.customizeIconProps,l=e.children,a=e.onMouseDown,c=e.onClick,s="function"==typeof n?n(r):n;return o.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null==a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},void 0!==s?s:o.createElement("span",{className:i()(t.split(/\s+/).map((function(e){return"".concat(e,"-icon")})))},l))},y=o.createContext(null);function $(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,t=o.useRef(null),n=o.useRef(null);return o.useEffect((function(){return function(){window.clearTimeout(n.current)}}),[]),[function(){return t.current},function(o){(o||null===t.current)&&(t.current=o),window.clearTimeout(n.current),n.current=window.setTimeout((function(){t.current=null}),e)}]}var C=n(6928);var A=n(2065),w=n(9591),x=function(e,t){var n,r=e.prefixCls,l=e.id,a=e.inputElement,c=e.disabled,u=e.tabIndex,d=e.autoFocus,m=e.autoComplete,p=e.editable,g=e.activeDescendantId,v=e.value,b=e.maxLength,y=e.onKeyDown,$=e.onMouseDown,C=e.onChange,A=e.onPaste,w=e.onCompositionStart,x=e.onCompositionEnd,S=e.onBlur,E=e.open,I=e.attrs,O=a||o.createElement("input",null),M=O,k=M.ref,R=M.props,z=R.onKeyDown,B=R.onChange,N=R.onMouseDown,j=R.onCompositionStart,P=R.onCompositionEnd,H=R.onBlur,T=R.style;return(0,f.$e)(!("maxLength"in O.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),O=o.cloneElement(O,(0,s.A)((0,s.A)((0,s.A)({type:"search"},R),{},{id:l,ref:(0,h.K4)(t,k),disabled:c,tabIndex:u,autoComplete:m||"off",autoFocus:d,className:i()("".concat(r,"-selection-search-input"),null===(n=O)||void 0===n||null===(n=n.props)||void 0===n?void 0:n.className),role:"combobox","aria-expanded":E||!1,"aria-haspopup":"listbox","aria-owns":"".concat(l,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(l,"_list"),"aria-activedescendant":E?g:void 0},I),{},{value:p?v:"",maxLength:b,readOnly:!p,unselectable:p?null:"on",style:(0,s.A)((0,s.A)({},T),{},{opacity:p?null:0}),onKeyDown:function(e){y(e),z&&z(e)},onMouseDown:function(e){$(e),N&&N(e)},onChange:function(e){C(e),B&&B(e)},onCompositionStart:function(e){w(e),j&&j(e)},onCompositionEnd:function(e){x(e),P&&P(e)},onPaste:A,onBlur:function(e){S(e),H&&H(e)}}))};var S=o.forwardRef(x);function E(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var I="undefined"!=typeof window&&window.document&&window.document.documentElement;function O(e){return["string","number"].includes((0,m.A)(e))}function M(e){var t=void 0;return e&&(O(e.title)?t=e.title.toString():O(e.label)&&(t=e.label.toString())),t}function k(e){var t;return null!==(t=e.key)&&void 0!==t?t:e.value}var R=function(e){e.preventDefault(),e.stopPropagation()},z=function(e){var t,n,r=e.id,l=e.prefixCls,a=e.values,s=e.open,d=e.searchValue,m=e.autoClearSearchValue,p=e.inputRef,f=e.placeholder,g=e.disabled,v=e.mode,h=e.showSearch,y=e.autoFocus,$=e.autoComplete,C=e.activeDescendantId,x=e.tabIndex,E=e.removeIcon,O=e.maxTagCount,z=e.maxTagTextLength,B=e.maxTagPlaceholder,N=void 0===B?function(e){return"+ ".concat(e.length," ...")}:B,j=e.tagRender,P=e.onToggleOpen,H=e.onRemove,T=e.onInputChange,D=e.onInputPaste,L=e.onInputKeyDown,F=e.onInputMouseDown,W=e.onInputCompositionStart,K=e.onInputCompositionEnd,_=e.onInputBlur,V=o.useRef(null),X=(0,o.useState)(0),q=(0,u.A)(X,2),G=q[0],Y=q[1],U=(0,o.useState)(!1),Q=(0,u.A)(U,2),J=Q[0],Z=Q[1],ee="".concat(l,"-selection"),te=s||"multiple"===v&&!1===m||"tags"===v?d:"",ne="tags"===v||"multiple"===v&&!1===m||h&&(s||J);t=function(){Y(V.current.scrollWidth)},n=[te],I?o.useLayoutEffect(t,n):o.useEffect(t,n);var oe=function(e,t,n,r,l){return o.createElement("span",{title:M(e),className:i()("".concat(ee,"-item"),(0,c.A)({},"".concat(ee,"-item-disabled"),n))},o.createElement("span",{className:"".concat(ee,"-item-content")},t),r&&o.createElement(b,{className:"".concat(ee,"-item-remove"),onMouseDown:R,onClick:l,customizeIcon:E},"×"))},re=function(e,t,n,r,i,l){return o.createElement("span",{onMouseDown:function(e){R(e),P(!s)}},j({label:t,value:e,disabled:n,closable:r,onClose:i,isMaxTag:!!l}))},ie=o.createElement("div",{className:"".concat(ee,"-search"),style:{width:G},onFocus:function(){Z(!0)},onBlur:function(){Z(!1)}},o.createElement(S,{ref:p,open:s,prefixCls:l,id:r,inputElement:null,disabled:g,autoFocus:y,autoComplete:$,editable:ne,activeDescendantId:C,value:te,onKeyDown:L,onMouseDown:F,onChange:T,onPaste:D,onCompositionStart:W,onCompositionEnd:K,onBlur:_,tabIndex:x,attrs:(0,A.A)(e,!0)}),o.createElement("span",{ref:V,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},te," ")),le=o.createElement(w.A,{prefixCls:"".concat(ee,"-overflow"),data:a,renderItem:function(e){var t=e.disabled,n=e.label,o=e.value,r=!g&&!t,i=n;if("number"==typeof z&&("string"==typeof n||"number"==typeof n)){var l=String(i);l.length>z&&(i="".concat(l.slice(0,z),"..."))}var a=function(t){t&&t.stopPropagation(),H(e)};return"function"==typeof j?re(o,i,t,r,a):oe(e,i,t,r,a)},renderRest:function(e){if(!a.length)return null;var t="function"==typeof N?N(e):N;return"function"==typeof j?re(void 0,t,!1,!1,void 0,!0):oe({title:t},t,!1)},suffix:ie,itemKey:k,maxCount:O});return o.createElement("span",{className:"".concat(ee,"-wrap")},le,!a.length&&!te&&o.createElement("span",{className:"".concat(ee,"-placeholder")},f))},B=function(e){var t=e.inputElement,n=e.prefixCls,r=e.id,i=e.inputRef,l=e.disabled,a=e.autoFocus,c=e.autoComplete,s=e.activeDescendantId,d=e.mode,m=e.open,p=e.values,f=e.placeholder,g=e.tabIndex,v=e.showSearch,h=e.searchValue,b=e.activeValue,y=e.maxLength,$=e.onInputKeyDown,C=e.onInputMouseDown,w=e.onInputChange,x=e.onInputPaste,E=e.onInputCompositionStart,I=e.onInputCompositionEnd,O=e.onInputBlur,k=e.title,R=o.useState(!1),z=(0,u.A)(R,2),B=z[0],N=z[1],j="combobox"===d,P=j||v,H=p[0],T=h||"";j&&b&&!B&&(T=b),o.useEffect((function(){j&&N(!1)}),[j,b]);var D=!("combobox"!==d&&!m&&!v)&&!!T,L=void 0===k?M(H):k,F=o.useMemo((function(){return H?null:o.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:D?{visibility:"hidden"}:void 0},f)}),[H,D,f,n]);return o.createElement("span",{className:"".concat(n,"-selection-wrap")},o.createElement("span",{className:"".concat(n,"-selection-search")},o.createElement(S,{ref:i,prefixCls:n,id:r,open:m,inputElement:t,disabled:l,autoFocus:a,autoComplete:c,editable:P,activeDescendantId:s,value:T,onKeyDown:$,onMouseDown:C,onChange:function(e){N(!0),w(e)},onPaste:x,onCompositionStart:E,onCompositionEnd:I,onBlur:O,tabIndex:g,attrs:(0,A.A)(e,!0),maxLength:j?y:void 0})),!j&&H?o.createElement("span",{className:"".concat(n,"-selection-item"),title:L,style:D?{visibility:"hidden"}:void 0},H.label):null,F)},N=function(e,t){var n=(0,o.useRef)(null),r=(0,o.useRef)(!1),i=e.prefixCls,a=e.open,c=e.mode,s=e.showSearch,d=e.tokenWithEnter,m=e.disabled,p=e.prefix,f=e.autoClearSearchValue,g=e.onSearch,v=e.onSearchSubmit,h=e.onToggleOpen,b=e.onInputKeyDown,y=e.onInputBlur,A=e.domRef;o.useImperativeHandle(t,(function(){return{focus:function(e){n.current.focus(e)},blur:function(){n.current.blur()}}}));var w=$(0),x=(0,u.A)(w,2),S=x[0],E=x[1],I=(0,o.useRef)(null),O=function(e){!1!==g(e,!0,r.current)&&h(!0)},M={inputRef:n,onInputKeyDown:function(e){var t,o=e.which,i=n.current instanceof HTMLTextAreaElement;(i||!a||o!==C.A.UP&&o!==C.A.DOWN||e.preventDefault(),b&&b(e),o!==C.A.ENTER||"tags"!==c||r.current||a||null==v||v(e.target.value),i&&!a&&~[C.A.UP,C.A.DOWN,C.A.LEFT,C.A.RIGHT].indexOf(o))||(t=o)&&![C.A.ESC,C.A.SHIFT,C.A.BACKSPACE,C.A.TAB,C.A.WIN_KEY,C.A.ALT,C.A.META,C.A.WIN_KEY_RIGHT,C.A.CTRL,C.A.SEMICOLON,C.A.EQUALS,C.A.CAPS_LOCK,C.A.CONTEXT_MENU,C.A.F1,C.A.F2,C.A.F3,C.A.F4,C.A.F5,C.A.F6,C.A.F7,C.A.F8,C.A.F9,C.A.F10,C.A.F11,C.A.F12].includes(t)&&h(!0)},onInputMouseDown:function(){E(!0)},onInputChange:function(e){var t=e.target.value;if(d&&I.current&&/[\r\n]/.test(I.current)){var n=I.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");t=t.replace(n,I.current)}I.current=null,O(t)},onInputPaste:function(e){var t=e.clipboardData,n=null==t?void 0:t.getData("text");I.current=n||""},onInputCompositionStart:function(){r.current=!0},onInputCompositionEnd:function(e){r.current=!1,"combobox"!==c&&O(e.target.value)},onInputBlur:y},k="multiple"===c||"tags"===c?o.createElement(z,(0,l.A)({},e,M)):o.createElement(B,(0,l.A)({},e,M));return o.createElement("div",{ref:A,className:"".concat(i,"-selector"),onClick:function(e){e.target!==n.current&&(void 0!==document.body.style.msTouchAction?setTimeout((function(){n.current.focus()})):n.current.focus())},onMouseDown:function(e){var t=S();e.target===n.current||t||"combobox"===c&&m||e.preventDefault(),("combobox"===c||s&&t)&&a||(a&&!1!==f&&g("",!0,!1),h())}},p&&o.createElement("div",{className:"".concat(i,"-prefix")},p),k)};var j=o.forwardRef(N),P=n(2427),H=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],T=function(e,t){var n=e.prefixCls,r=(e.disabled,e.visible),a=e.children,u=e.popupElement,m=e.animation,p=e.transitionName,f=e.dropdownStyle,g=e.dropdownClassName,v=e.direction,h=void 0===v?"ltr":v,b=e.placement,y=e.builtinPlacements,$=e.dropdownMatchSelectWidth,C=e.dropdownRender,A=e.dropdownAlign,w=e.getPopupContainer,x=e.empty,S=e.getTriggerDOMNode,E=e.onPopupVisibleChange,I=e.onPopupMouseEnter,O=(0,d.A)(e,H),M="".concat(n,"-dropdown"),k=u;C&&(k=C(u));var R=o.useMemo((function(){return y||function(e){var t=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"}}}($)}),[y,$]),z=m?"".concat(M,"-").concat(m):p,B="number"==typeof $,N=o.useMemo((function(){return B?null:!1===$?"minWidth":"width"}),[$,B]),j=f;B&&(j=(0,s.A)((0,s.A)({},j),{},{width:$}));var T=o.useRef(null);return o.useImperativeHandle(t,(function(){return{getPopupElement:function(){var e;return null===(e=T.current)||void 0===e?void 0:e.popupElement}}})),o.createElement(P.A,(0,l.A)({},O,{showAction:E?["click"]:[],hideAction:E?["click"]:[],popupPlacement:b||("rtl"===h?"bottomRight":"bottomLeft"),builtinPlacements:R,prefixCls:M,popupTransitionName:z,popup:o.createElement("div",{onMouseEnter:I},k),ref:T,stretch:N,popupAlign:A,popupVisible:r,getPopupContainer:w,popupClassName:i()(g,(0,c.A)({},"".concat(M,"-empty"),x)),popupStyle:j,getTriggerDOMNode:S,onPopupVisibleChange:E}),a)};var D=o.forwardRef(T),L=n(7695);function F(e,t){var n,o=e.key;return"value"in e&&(n=e.value),null!=o?o:void 0!==n?n:"rc-index-key-".concat(t)}function W(e){return void 0!==e&&!Number.isNaN(e)}function K(e,t){var n=e||{},o=n.label||(t?"children":"label");return{label:o,value:n.value||"value",options:n.options||"options",groupLabel:n.groupLabel||o}}function _(e){var t=(0,s.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,f.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var V=o.createContext(null);function X(e){var t=e.visible,n=e.values;if(!t)return null;return o.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,50).map((function(e){var t=e.label,n=e.value;return["number","string"].includes((0,m.A)(t))?t:n})).join(", ")),n.length>50?", ...":null)}var q=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],G=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Y=function(e){return"tags"===e||"multiple"===e},U=o.forwardRef((function(e,t){var n,r=e.id,f=e.prefixCls,C=e.className,A=e.showSearch,w=e.tagRender,x=e.direction,S=e.omitDomProps,E=e.displayValues,I=e.onDisplayValuesChange,O=e.emptyOptions,M=e.notFoundContent,k=void 0===M?"Not Found":M,R=e.onClear,z=e.mode,B=e.disabled,N=e.loading,P=e.getInputElement,H=e.getRawInputElement,T=e.open,F=e.defaultOpen,K=e.onDropdownVisibleChange,_=e.activeValue,U=e.onActiveValueChange,Q=e.activeDescendantId,J=e.searchValue,Z=e.autoClearSearchValue,ee=e.onSearch,te=e.onSearchSplit,ne=e.tokenSeparators,oe=e.allowClear,re=e.prefix,ie=e.suffixIcon,le=e.clearIcon,ae=e.OptionList,ce=e.animation,se=e.transitionName,ue=e.dropdownStyle,de=e.dropdownClassName,me=e.dropdownMatchSelectWidth,pe=e.dropdownRender,fe=e.dropdownAlign,ge=e.placement,ve=e.builtinPlacements,he=e.getPopupContainer,be=e.showAction,ye=void 0===be?[]:be,$e=e.onFocus,Ce=e.onBlur,Ae=e.onKeyUp,we=e.onKeyDown,xe=e.onMouseDown,Se=(0,d.A)(e,q),Ee=Y(z),Ie=(void 0!==A?A:Ee)||"combobox"===z,Oe=(0,s.A)({},Se);G.forEach((function(e){delete Oe[e]})),null==S||S.forEach((function(e){delete Oe[e]}));var Me=o.useState(!1),ke=(0,u.A)(Me,2),Re=ke[0],ze=ke[1];o.useEffect((function(){ze((0,v.A)())}),[]);var Be=o.useRef(null),Ne=o.useRef(null),je=o.useRef(null),Pe=o.useRef(null),He=o.useRef(null),Te=o.useRef(!1),De=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=o.useState(!1),n=(0,u.A)(t,2),r=n[0],i=n[1],l=o.useRef(null),a=function(){window.clearTimeout(l.current)};return o.useEffect((function(){return a}),[]),[r,function(t,n){a(),l.current=window.setTimeout((function(){i(t),n&&n()}),e)},a]}(),Le=(0,u.A)(De,3),Fe=Le[0],We=Le[1],Ke=Le[2];o.useImperativeHandle(t,(function(){var e,t;return{focus:null===(e=Pe.current)||void 0===e?void 0:e.focus,blur:null===(t=Pe.current)||void 0===t?void 0:t.blur,scrollTo:function(e){var t;return null===(t=He.current)||void 0===t?void 0:t.scrollTo(e)},nativeElement:Be.current||Ne.current}}));var _e=o.useMemo((function(){var e;if("combobox"!==z)return J;var t=null===(e=E[0])||void 0===e?void 0:e.value;return"string"==typeof t||"number"==typeof t?String(t):""}),[J,z,E]),Ve="combobox"===z&&"function"==typeof P&&P()||null,Xe="function"==typeof H&&H(),qe=(0,h.xK)(Ne,null==Xe||null===(n=Xe.props)||void 0===n?void 0:n.ref),Ge=o.useState(!1),Ye=(0,u.A)(Ge,2),Ue=Ye[0],Qe=Ye[1];(0,g.A)((function(){Qe(!0)}),[]);var Je=(0,p.A)(!1,{defaultValue:F,value:T}),Ze=(0,u.A)(Je,2),et=Ze[0],tt=Ze[1],nt=!!Ue&&et,ot=!k&&O;(B||ot&&nt&&"combobox"===z)&&(nt=!1);var rt=!ot&&nt,it=o.useCallback((function(e){var t=void 0!==e?e:!nt;B||(tt(t),nt!==t&&(null==K||K(t)))}),[B,nt,tt,K]),lt=o.useMemo((function(){return(ne||[]).some((function(e){return["\n","\r\n"].includes(e)}))}),[ne]),at=o.useContext(V)||{},ct=at.maxCount,st=at.rawValues,ut=function(e,t,n){if(!(Ee&&W(ct)&&(null==st?void 0:st.size)>=ct)){var o=!0,r=e;null==U||U(null);var i=function(e,t,n){if(!t||!t.length)return null;var o=!1,r=function e(t,n){var r=(0,L.A)(n),i=r[0],l=r.slice(1);if(!i)return[t];var c=t.split(i);return o=o||c.length>1,c.reduce((function(t,n){return[].concat((0,a.A)(t),(0,a.A)(e(n,l)))}),[]).filter(Boolean)}(e,t);return o?void 0!==n?r.slice(0,n):r:null}(e,ne,W(ct)?ct-st.size:void 0),l=n?null:i;return"combobox"!==z&&l&&(r="",null==te||te(l),it(!1),o=!1),ee&&_e!==r&&ee(r,{source:t?"typing":"effect"}),o}};o.useEffect((function(){nt||Ee||"combobox"===z||ut("",!1,!1)}),[nt]),o.useEffect((function(){et&&B&&tt(!1),B&&!Te.current&&We(!1)}),[B]);var dt=$(),mt=(0,u.A)(dt,2),pt=mt[0],ft=mt[1],gt=o.useRef(!1),vt=o.useRef(!1),ht=[];o.useEffect((function(){return function(){ht.forEach((function(e){return clearTimeout(e)})),ht.splice(0,ht.length)}}),[]);var bt,yt=o.useState({}),$t=(0,u.A)(yt,2)[1];Xe&&(bt=function(e){it(e)}),function(e,t,n,r){var i=o.useRef(null);i.current={open:t,triggerOpen:n,customizedTrigger:r},o.useEffect((function(){function t(t){var n;if(null===(n=i.current)||void 0===n||!n.customizedTrigger){var o=t.target;o.shadowRoot&&t.composed&&(o=t.composedPath()[0]||o),i.current.open&&e().filter((function(e){return e})).every((function(e){return!e.contains(o)&&e!==o}))&&i.current.triggerOpen(!1)}}return window.addEventListener("mousedown",t),function(){return window.removeEventListener("mousedown",t)}}),[])}((function(){var e;return[Be.current,null===(e=je.current)||void 0===e?void 0:e.getPopupElement()]}),rt,it,!!Xe);var Ct,At=o.useMemo((function(){return(0,s.A)((0,s.A)({},e),{},{notFoundContent:k,open:nt,triggerOpen:rt,id:r,showSearch:Ie,multiple:Ee,toggleOpen:it})}),[e,k,rt,nt,r,Ie,Ee,it]),wt=!!ie||N;wt&&(Ct=o.createElement(b,{className:i()("".concat(f,"-arrow"),(0,c.A)({},"".concat(f,"-arrow-loading"),N)),customizeIcon:ie,customizeIconProps:{loading:N,searchValue:_e,open:nt,focused:Fe,showSearch:Ie}}));var xt,St=function(e,t,n,r,i){var l=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,s=o.useMemo((function(){return"object"===(0,m.A)(r)?r.clearIcon:i||void 0}),[r,i]);return{allowClear:o.useMemo((function(){return!(l||!r||!n.length&&!a||"combobox"===c&&""===a)}),[r,l,n.length,a,c]),clearIcon:o.createElement(b,{className:"".concat(e,"-clear"),onMouseDown:t,customizeIcon:s},"×")}}(f,(function(){var e;null==R||R(),null===(e=Pe.current)||void 0===e||e.focus(),I([],{type:"clear",values:E}),ut("",!1,!1)}),E,oe,le,B,_e,z),Et=St.allowClear,It=St.clearIcon,Ot=o.createElement(ae,{ref:He}),Mt=i()(f,C,(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(f,"-focused"),Fe),"".concat(f,"-multiple"),Ee),"".concat(f,"-single"),!Ee),"".concat(f,"-allow-clear"),oe),"".concat(f,"-show-arrow"),wt),"".concat(f,"-disabled"),B),"".concat(f,"-loading"),N),"".concat(f,"-open"),nt),"".concat(f,"-customize-input"),Ve),"".concat(f,"-show-search"),Ie)),kt=o.createElement(D,{ref:je,disabled:B,prefixCls:f,visible:rt,popupElement:Ot,animation:ce,transitionName:se,dropdownStyle:ue,dropdownClassName:de,direction:x,dropdownMatchSelectWidth:me,dropdownRender:pe,dropdownAlign:fe,placement:ge,builtinPlacements:ve,getPopupContainer:he,empty:O,getTriggerDOMNode:function(e){return Ne.current||e},onPopupVisibleChange:bt,onPopupMouseEnter:function(){$t({})}},Xe?o.cloneElement(Xe,{ref:qe}):o.createElement(j,(0,l.A)({},e,{domRef:Ne,prefixCls:f,inputElement:Ve,ref:Pe,id:r,prefix:re,showSearch:Ie,autoClearSearchValue:Z,mode:z,activeDescendantId:Q,tagRender:w,values:E,open:nt,onToggleOpen:it,activeValue:_,searchValue:_e,onSearch:ut,onSearchSubmit:function(e){e&&e.trim()&&ee(e,{source:"submit"})},onRemove:function(e){var t=E.filter((function(t){return t!==e}));I(t,{type:"remove",values:[e]})},tokenWithEnter:lt,onInputBlur:function(){gt.current=!1}})));return xt=Xe?kt:o.createElement("div",(0,l.A)({className:Mt},Oe,{ref:Be,onMouseDown:function(e){var t,n=e.target,o=null===(t=je.current)||void 0===t?void 0:t.getPopupElement();if(o&&o.contains(n)){var r=setTimeout((function(){var e,t=ht.indexOf(r);-1!==t&&ht.splice(t,1),Ke(),Re||o.contains(document.activeElement)||null===(e=Pe.current)||void 0===e||e.focus()}));ht.push(r)}for(var i=arguments.length,l=new Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];null==xe||xe.apply(void 0,[e].concat(l))},onKeyDown:function(e){var t,n=pt(),o=e.key,r="Enter"===o;if(r&&("combobox"!==z&&e.preventDefault(),nt||it(!0)),ft(!!_e),"Backspace"===o&&!n&&Ee&&!_e&&E.length){for(var i=(0,a.A)(E),l=null,c=i.length-1;c>=0;c-=1){var s=i[c];if(!s.disabled){i.splice(c,1),l=s;break}}l&&I(i,{type:"remove",values:[l]})}for(var u=arguments.length,d=new Array(u>1?u-1:0),m=1;m<u;m++)d[m-1]=arguments[m];!nt||r&&gt.current||(r&&(gt.current=!0),null===(t=He.current)||void 0===t||t.onKeyDown.apply(t,[e].concat(d))),null==we||we.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var r;nt&&(null===(r=He.current)||void 0===r||r.onKeyUp.apply(r,[e].concat(n))),"Enter"===e.key&&(gt.current=!1),null==Ae||Ae.apply(void 0,[e].concat(n))},onFocus:function(){We(!0),B||($e&&!vt.current&&$e.apply(void 0,arguments),ye.includes("focus")&&it(!0)),vt.current=!0},onBlur:function(){Te.current=!0,We(!1,(function(){vt.current=!1,Te.current=!1,it(!1)})),B||(_e&&("tags"===z?ee(_e,{source:"submit"}):"multiple"===z&&ee("",{source:"blur"})),Ce&&Ce.apply(void 0,arguments))}}),o.createElement(X,{visible:Fe&&!nt,values:E}),kt,Ct,Et&&It),o.createElement(y.Provider,{value:At},xt)}));var Q=U,J=function(){return null};J.isSelectOptGroup=!0;var Z=J,ee=function(){return null};ee.isSelectOption=!0;var te=ee,ne=n(8104),oe=n(9853),re=n(551);var ie=["disabled","title","children","style","className"];function le(e){return"string"==typeof e||"number"==typeof e}var ae=function(e,t){var n=o.useContext(y),r=n.prefixCls,s=n.id,m=n.open,p=n.multiple,f=n.mode,g=n.searchValue,v=n.toggleOpen,h=n.notFoundContent,$=n.onPopupScroll,w=o.useContext(V),x=w.maxCount,S=w.flattenOptions,E=w.onActiveValue,I=w.defaultActiveFirstOption,O=w.onSelect,M=w.menuItemSelectedIcon,k=w.rawValues,R=w.fieldNames,z=w.virtual,B=w.direction,N=w.listHeight,j=w.listItemHeight,P=w.optionRender,H="".concat(r,"-item"),T=(0,ne.A)((function(){return S}),[m,S],(function(e,t){return t[0]&&e[1]!==t[1]})),D=o.useRef(null),L=o.useMemo((function(){return p&&W(x)&&(null==k?void 0:k.size)>=x}),[p,x,null==k?void 0:k.size]),F=function(e){e.preventDefault()},K=function(e){var t;null===(t=D.current)||void 0===t||t.scrollTo("number"==typeof e?{index:e}:e)},_=o.useCallback((function(e){return"combobox"!==f&&k.has(e)}),[f,(0,a.A)(k).toString(),k.size]),X=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=T.length,o=0;o<n;o+=1){var r=(e+o*t+n)%n,i=T[r]||{},l=i.group,a=i.data;if(!l&&(null==a||!a.disabled)&&(_(a.value)||!L))return r}return-1},q=o.useState((function(){return X(0)})),G=(0,u.A)(q,2),Y=G[0],U=G[1],Q=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];U(e);var n={source:t?"keyboard":"mouse"},o=T[e];o?E(o.value,e,n):E(null,-1,n)};(0,o.useEffect)((function(){Q(!1!==I?X(0):-1)}),[T.length,g]);var J=o.useCallback((function(e){return"combobox"===f?String(e).toLowerCase()===g.toLowerCase():k.has(e)}),[f,g,(0,a.A)(k).toString(),k.size]);(0,o.useEffect)((function(){var e,t=setTimeout((function(){if(!p&&m&&1===k.size){var e=Array.from(k)[0],t=T.findIndex((function(t){return t.data.value===e}));-1!==t&&(Q(t),K(t))}}));m&&(null===(e=D.current)||void 0===e||e.scrollTo(void 0));return function(){return clearTimeout(t)}}),[m,g]);var Z=function(e){void 0!==e&&O(e,{selected:!k.has(e)}),p||v(!1)};if(o.useImperativeHandle(t,(function(){return{onKeyDown:function(e){var t=e.which,n=e.ctrlKey;switch(t){case C.A.N:case C.A.P:case C.A.UP:case C.A.DOWN:var o=0;if(t===C.A.UP?o=-1:t===C.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&n&&(t===C.A.N?o=1:t===C.A.P&&(o=-1)),0!==o){var r=X(Y+o,o);K(r),Q(r,!0)}break;case C.A.TAB:case C.A.ENTER:var i,l=T[Y];!l||null!=l&&null!==(i=l.data)&&void 0!==i&&i.disabled||L?Z(void 0):Z(l.value),m&&e.preventDefault();break;case C.A.ESC:v(!1),m&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){K(e)}}})),0===T.length)return o.createElement("div",{role:"listbox",id:"".concat(s,"_list"),className:"".concat(H,"-empty"),onMouseDown:F},h);var ee=Object.keys(R).map((function(e){return R[e]})),te=function(e){return e.label};function ae(e,t){return{role:e.group?"presentation":"option",id:"".concat(s,"_list_").concat(t)}}var ce=function(e){var t=T[e];if(!t)return null;var n=t.data||{},r=n.value,i=t.group,a=(0,A.A)(n,!0),c=te(t);return t?o.createElement("div",(0,l.A)({"aria-label":"string"!=typeof c||i?null:c},a,{key:e},ae(t,e),{"aria-selected":J(r)}),r):null},se={role:"listbox",id:"".concat(s,"_list")};return o.createElement(o.Fragment,null,z&&o.createElement("div",(0,l.A)({},se,{style:{height:0,width:0,overflow:"hidden"}}),ce(Y-1),ce(Y),ce(Y+1)),o.createElement(re.A,{itemKey:"key",ref:D,data:T,height:N,itemHeight:j,fullHeight:!1,onMouseDown:F,onScroll:$,virtual:z,direction:B,innerProps:z?null:se},(function(e,t){var n=e.group,r=e.groupOption,a=e.data,s=e.label,u=e.value,m=a.key;if(n){var p,f=null!==(p=a.title)&&void 0!==p?p:le(s)?s.toString():void 0;return o.createElement("div",{className:i()(H,"".concat(H,"-group"),a.className),title:f},void 0!==s?s:m)}var g=a.disabled,v=a.title,h=(a.children,a.style),y=a.className,$=(0,d.A)(a,ie),C=(0,oe.A)($,ee),w=_(u),x=g||!w&&L,S="".concat(H,"-option"),E=i()(H,S,y,(0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(S,"-grouped"),r),"".concat(S,"-active"),Y===t&&!x),"".concat(S,"-disabled"),x),"".concat(S,"-selected"),w)),I=te(e),O=!M||"function"==typeof M||w,k="number"==typeof I?I:I||u,R=le(k)?k.toString():void 0;return void 0!==v&&(R=v),o.createElement("div",(0,l.A)({},(0,A.A)(C),z?{}:ae(e,t),{"aria-selected":J(u),className:E,title:R,onMouseMove:function(){Y===t||x||Q(t)},onClick:function(){x||Z(u)},style:h}),o.createElement("div",{className:"".concat(S,"-content")},"function"==typeof P?P(e,{index:t}):k),o.isValidElement(M)||w,O&&o.createElement(b,{className:"".concat(H,"-option-state"),customizeIcon:M,customizeIconProps:{value:u,disabled:x,isSelected:w}},w?"✓":null))})))};var ce=o.forwardRef(ae);function se(e,t){return E(e).join("").toUpperCase().includes(t)}var ue=n(998),de=0,me=(0,ue.A)();function pe(e){var t=o.useState(),n=(0,u.A)(t,2),r=n[0],i=n[1];return o.useEffect((function(){var e;i("rc_select_".concat((me?(e=de,de+=1):e="TEST_OR_SSR",e)))}),[]),e||r}var fe=n(2546),ge=["children","value"],ve=["children"];function he(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,fe.A)(e).map((function(e,n){if(!o.isValidElement(e)||!e.type)return null;var r=e,i=r.type.isSelectOptGroup,l=r.key,a=r.props,c=a.children,u=(0,d.A)(a,ve);return t||!i?function(e){var t=e,n=t.key,o=t.props,r=o.children,i=o.value,l=(0,d.A)(o,ge);return(0,s.A)({key:n,value:void 0!==i?i:n,children:r},l)}(e):(0,s.A)((0,s.A)({key:"__RC_SELECT_GRP__".concat(null===l?n:l,"__"),label:l},u),{},{options:he(c)})})).filter((function(e){return e}))}var be=function(e,t,n,r,i){return o.useMemo((function(){var o=e;!e&&(o=he(t));var l=new Map,a=new Map,c=function(e,t,n){n&&"string"==typeof n&&e.set(t[n],t)};return function e(t){for(var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=0;s<t.length;s+=1){var u=t[s];!u[n.options]||o?(l.set(u[n.value],u),c(a,u,n.label),c(a,u,r),c(a,u,i)):e(u[n.options],!0)}}(o),{options:o,valueOptions:l,labelOptions:a}}),[e,t,n,r,i])};function ye(e){var t=o.useRef();t.current=e;var n=o.useCallback((function(){return t.current.apply(t,arguments)}),[]);return n}var $e=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Ce=["inputValue"];var Ae=o.forwardRef((function(e,t){var n=e.id,r=e.mode,i=e.prefixCls,f=void 0===i?"rc-select":i,g=e.backfill,v=e.fieldNames,h=e.inputValue,b=e.searchValue,y=e.onSearch,$=e.autoClearSearchValue,C=void 0===$||$,A=e.onSelect,w=e.onDeselect,x=e.dropdownMatchSelectWidth,S=void 0===x||x,I=e.filterOption,O=e.filterSort,M=e.optionFilterProp,k=e.optionLabelProp,R=e.options,z=e.optionRender,B=e.children,N=e.defaultActiveFirstOption,j=e.menuItemSelectedIcon,P=e.virtual,H=e.direction,T=e.listHeight,D=void 0===T?200:T,L=e.listItemHeight,W=void 0===L?20:L,X=e.labelRender,q=e.value,G=e.defaultValue,U=e.labelInValue,J=e.onChange,Z=e.maxCount,ee=(0,d.A)(e,$e),te=pe(n),ne=Y(r),oe=!(R||!B),re=o.useMemo((function(){return(void 0!==I||"combobox"!==r)&&I}),[I,r]),ie=o.useMemo((function(){return K(v,oe)}),[JSON.stringify(v),oe]),le=(0,p.A)("",{value:void 0!==b?b:h,postState:function(e){return e||""}}),ae=(0,u.A)(le,2),ue=ae[0],de=ae[1],me=be(R,B,ie,M,k),fe=me.valueOptions,ge=me.labelOptions,ve=me.options,he=o.useCallback((function(e){return E(e).map((function(e){var t,n,o,r,i,l;(function(e){return!e||"object"!==(0,m.A)(e)})(e)?t=e:(o=e.key,n=e.label,t=null!==(l=e.value)&&void 0!==l?l:o);var a,c=fe.get(t);c&&(void 0===n&&(n=null==c?void 0:c[k||ie.label]),void 0===o&&(o=null!==(a=null==c?void 0:c.key)&&void 0!==a?a:t),r=null==c?void 0:c.disabled,i=null==c?void 0:c.title);return{label:n,value:t,key:o,disabled:r,title:i}}))}),[ie,k,fe]),Ae=(0,p.A)(G,{value:q}),we=(0,u.A)(Ae,2),xe=we[0],Se=we[1],Ee=o.useMemo((function(){var e,t=he(ne&&null===xe?[]:xe);return"combobox"===r&&function(e){return!e&&0!==e}(null===(e=t[0])||void 0===e?void 0:e.value)?[]:t}),[xe,he,r,ne]),Ie=function(e,t){var n=o.useRef({values:new Map,options:new Map});return[o.useMemo((function(){var o=n.current,r=o.values,i=o.options,l=e.map((function(e){var t;return void 0===e.label?(0,s.A)((0,s.A)({},e),{},{label:null===(t=r.get(e.value))||void 0===t?void 0:t.label}):e})),a=new Map,c=new Map;return l.forEach((function(e){a.set(e.value,e),c.set(e.value,t.get(e.value)||i.get(e.value))})),n.current.values=a,n.current.options=c,l}),[e,t]),o.useCallback((function(e){return t.get(e)||n.current.options.get(e)}),[t])]}(Ee,fe),Oe=(0,u.A)(Ie,2),Me=Oe[0],ke=Oe[1],Re=o.useMemo((function(){if(!r&&1===Me.length){var e=Me[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return Me.map((function(e){var t;return(0,s.A)((0,s.A)({},e),{},{label:null!==(t="function"==typeof X?X(e):e.label)&&void 0!==t?t:e.value})}))}),[r,Me,X]),ze=o.useMemo((function(){return new Set(Me.map((function(e){return e.value})))}),[Me]);o.useEffect((function(){if("combobox"===r){var e,t=null===(e=Me[0])||void 0===e?void 0:e.value;de(function(e){return null!=e}(t)?String(t):"")}}),[Me]);var Be=ye((function(e,t){var n=null!=t?t:e;return(0,c.A)((0,c.A)({},ie.value,e),ie.label,n)})),Ne=function(e,t,n,r,i){return o.useMemo((function(){if(!n||!1===r)return e;var o=t.options,l=t.label,a=t.value,u=[],d="function"==typeof r,m=n.toUpperCase(),p=d?r:function(e,t){return i?se(t[i],m):t[o]?se(t["children"!==l?l:"label"],m):se(t[a],m)},f=d?function(e){return _(e)}:function(e){return e};return e.forEach((function(e){if(e[o])if(p(n,f(e)))u.push(e);else{var t=e[o].filter((function(e){return p(n,f(e))}));t.length&&u.push((0,s.A)((0,s.A)({},e),{},(0,c.A)({},o,t)))}else p(n,f(e))&&u.push(e)})),u}),[e,r,i,n,t])}(o.useMemo((function(){if("tags"!==r)return ve;var e=(0,a.A)(ve);return(0,a.A)(Me).sort((function(e,t){return e.value<t.value?-1:1})).forEach((function(t){var n=t.value;(function(e){return fe.has(e)})(n)||e.push(Be(n,t.label))})),e}),[Be,ve,fe,Me,r]),ie,ue,re,M),je=o.useMemo((function(){return"tags"!==r||!ue||Ne.some((function(e){return e[M||"value"]===ue}))||Ne.some((function(e){return e[ie.value]===ue}))?Ne:[Be(ue)].concat((0,a.A)(Ne))}),[Be,M,r,Ne,ue,ie]),Pe=function e(t){return(0,a.A)(t).sort((function(e,t){return O(e,t,{searchValue:ue})})).map((function(t){return Array.isArray(t.options)?(0,s.A)((0,s.A)({},t),{},{options:t.options.length>0?e(t.options):t.options}):t}))},He=o.useMemo((function(){return O?Pe(je):je}),[je,O,ue]),Te=o.useMemo((function(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],i=K(n,!1),l=i.label,a=i.value,c=i.options,s=i.groupLabel;return function e(t,n){Array.isArray(t)&&t.forEach((function(t){if(n||!(c in t)){var i=t[a];r.push({key:F(t,r.length),groupOption:n,data:t,label:t[l],value:i})}else{var u=t[s];void 0===u&&o&&(u=t.label),r.push({key:F(t,r.length),group:!0,data:t,label:u}),e(t[c],!0)}}))}(e,!1),r}(He,{fieldNames:ie,childrenAsData:oe})}),[He,ie,oe]),De=function(e){var t=he(e);if(Se(t),J&&(t.length!==Me.length||t.some((function(e,t){var n;return(null===(n=Me[t])||void 0===n?void 0:n.value)!==(null==e?void 0:e.value)})))){var n=U?t:t.map((function(e){return e.value})),o=t.map((function(e){return _(ke(e.value))}));J(ne?n:n[0],ne?o:o[0])}},Le=o.useState(null),Fe=(0,u.A)(Le,2),We=Fe[0],Ke=Fe[1],_e=o.useState(0),Ve=(0,u.A)(_e,2),Xe=Ve[0],qe=Ve[1],Ge=void 0!==N?N:"combobox"!==r,Ye=o.useCallback((function(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).source,o=void 0===n?"keyboard":n;qe(t),g&&"combobox"===r&&null!==e&&"keyboard"===o&&Ke(String(e))}),[g,r]),Ue=function(e,t,n){var o=function(){var t,n=ke(e);return[U?{label:null==n?void 0:n[ie.label],value:e,key:null!==(t=null==n?void 0:n.key)&&void 0!==t?t:e}:e,_(n)]};if(t&&A){var r=o(),i=(0,u.A)(r,2),l=i[0],a=i[1];A(l,a)}else if(!t&&w&&"clear"!==n){var c=o(),s=(0,u.A)(c,2),d=s[0],m=s[1];w(d,m)}},Qe=ye((function(e,t){var n,o=!ne||t.selected;n=o?ne?[].concat((0,a.A)(Me),[e]):[e]:Me.filter((function(t){return t.value!==e})),De(n),Ue(e,o),"combobox"===r?Ke(""):Y&&!C||(de(""),Ke(""))})),Je=o.useMemo((function(){var e=!1!==P&&!1!==S;return(0,s.A)((0,s.A)({},me),{},{flattenOptions:Te,onActiveValue:Ye,defaultActiveFirstOption:Ge,onSelect:Qe,menuItemSelectedIcon:j,rawValues:ze,fieldNames:ie,virtual:e,direction:H,listHeight:D,listItemHeight:W,childrenAsData:oe,maxCount:Z,optionRender:z})}),[Z,me,Te,Ye,Ge,Qe,j,ze,ie,P,S,H,D,W,oe,z]);return o.createElement(V.Provider,{value:Je},o.createElement(Q,(0,l.A)({},ee,{id:te,prefixCls:f,ref:t,omitDomProps:Ce,mode:r,displayValues:Re,onDisplayValuesChange:function(e,t){De(e);var n=t.type,o=t.values;"remove"!==n&&"clear"!==n||o.forEach((function(e){Ue(e.value,!1,n)}))},direction:H,searchValue:ue,onSearch:function(e,t){if(de(e),Ke(null),"submit"!==t.source)"blur"!==t.source&&("combobox"===r&&De(e),null==y||y(e));else{var n=(e||"").trim();if(n){var o=Array.from(new Set([].concat((0,a.A)(ze),[n])));De(o),Ue(n,!0),de("")}}},autoClearSearchValue:C,onSearchSplit:function(e){var t=e;"tags"!==r&&(t=e.map((function(e){var t=ge.get(e);return null==t?void 0:t.value})).filter((function(e){return void 0!==e})));var n=Array.from(new Set([].concat((0,a.A)(ze),(0,a.A)(t))));De(n),n.forEach((function(e){Ue(e,!0)}))},dropdownMatchSelectWidth:S,OptionList:ce,emptyOptions:!Te.length,activeValue:We,activeDescendantId:"".concat(te,"_list_").concat(Xe)})))}));var we=Ae;we.Option=te,we.OptGroup=Z;var xe=we,Se=n(275),Ee=n(3723),Ie=n(3425),Oe=n(8182),Me=n(2279),ke=n(5128),Re=n(8119),ze=n(934),Be=n(829),Ne=n(4241),je=n(124),Pe=n(6327),He=n(1320);var Te=function(e,t){return e||(e=>{const t={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},t),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},t),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},t),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},t),{points:["br","tr"],offset:[0,-4]})}})(t)},De=n(5905),Le=n(5974),Fe=n(7358),We=n(4277),Ke=n(3561),_e=n(4211);const Ve=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:t,padding:r,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}};var Xe=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`,r=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,a=`${n}-dropdown-placement-`,c=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},(0,De.dF)(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`\n          ${r}${a}bottomLeft,\n          ${i}${a}bottomLeft\n        `]:{animationName:Ke.ox},[`\n          ${r}${a}topLeft,\n          ${i}${a}topLeft,\n          ${r}${a}topRight,\n          ${i}${a}topRight\n        `]:{animationName:Ke.nP},[`${l}${a}bottomLeft`]:{animationName:Ke.vR},[`\n          ${l}${a}topLeft,\n          ${l}${a}topRight\n        `]:{animationName:Ke.YU},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},Ve(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},De.L9),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},Ve(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},(0,Ke._j)(e,"slide-up"),(0,Ke._j)(e,"slide-down"),(0,_e.Mh)(e,"move-up"),(0,_e.Mh)(e,"move-down")]},qe=n(2187);const Ge=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:r,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:a,colorIcon:c,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:u}=e,d=`${t}-selection-overflow`;return{[d]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:u,borderRadius:o,cursor:"default",transition:`font-size ${r}, line-height ${r}, height ${r}`,marginInlineEnd:e.calc(u).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${t}-disabled&`]:{color:l,borderColor:a,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},(0,De.Nk)()),{display:"inline-flex",alignItems:"center",color:c,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},Ye=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,r=`${n}-selection-overflow`,i=e.multipleSelectItemHeight,l=(e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()})(e),a=t?`${n}-${t}`:"",c=(e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:o,INTERNAL_FIXED_ITEM_MARGIN:r}=e,i=e.max(e.calc(n).sub(o).equal(),0);return{basePadding:i,containerPadding:e.max(e.calc(i).sub(r).equal(),0),itemHeight:(0,qe.zA)(t),itemLineHeight:(0,qe.zA)(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}})(e);return{[`${n}-multiple${a}`]:Object.assign(Object.assign({},Ge(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:c.basePadding,paddingBlock:c.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${(0,qe.zA)(o)} 0`,lineHeight:(0,qe.zA)(i),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:c.itemHeight,lineHeight:(0,qe.zA)(c.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:(0,qe.zA)(i),marginBlock:o}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal()},[`${r}-item + ${r}-item,\n        ${n}-prefix + ${n}-selection-wrap\n      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${r}-item-suffix`]:{minHeight:c.itemHeight,marginBlock:o},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:(0,qe.zA)(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function Ue(e,t){const{componentCls:n}=e,o=t?`${n}-${t}`:"",r={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`\n        &${n}-show-arrow ${n}-selector,\n        &${n}-allow-clear ${n}-selector\n      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[Ye(e,t),r]}var Qe=e=>{const{componentCls:t}=e,n=(0,We.oX)(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=(0,We.oX)(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[Ue(e),Ue(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},Ue(o,"lg")]};function Je(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:r}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${n}-${t}`:"";return{[`${n}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},(0,De.dF)(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:(0,qe.zA)(i)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`\n          ${n}-selection-item,\n          ${n}-selection-placeholder\n        `]:{display:"block",padding:0,lineHeight:(0,qe.zA)(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`\n        &${n}-show-arrow ${n}-selection-item,\n        &${n}-show-arrow ${n}-selection-search,\n        &${n}-show-arrow ${n}-selection-placeholder\n      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${(0,qe.zA)(o)}`,[`${n}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:(0,qe.zA)(i)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${(0,qe.zA)(o)}`,"&:after":{display:"none"}}}}}}}function Ze(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[Je(e),Je((0,We.oX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${(0,qe.zA)(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`\n            &${t}-show-arrow ${t}-selection-item,\n            &${t}-show-arrow ${t}-selection-placeholder\n          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},Je((0,We.oX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const et=(e,t)=>{const{componentCls:n,antCls:o,controlOutlineWidth:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${(0,qe.zA)(r)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},tt=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},et(e,t))}),nt=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},et(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),tt(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),tt(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),ot=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},rt=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},ot(e,t))}),it=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},ot(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),rt(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),rt(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),lt=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),at=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${(0,qe.zA)(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},ct=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},at(e,t))}),st=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},at(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),ct(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),ct(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,qe.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})});var ut=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},nt(e)),it(e)),lt(e)),st(e))});const dt=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},mt=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none","-webkit-appearance":"none"}}}},pt=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:r}=e;return{[n]:Object.assign(Object.assign({},(0,De.dF)(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},dt(e)),mt(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},De.L9),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},De.L9),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},(0,De.Nk)()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[r]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},[`&:hover ${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},ft=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},pt(e),Ze(e),Qe(e),Xe(e),{[`${t}-rtl`]:{direction:"rtl"}},(0,Le.G)(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]};var gt=(0,Fe.OF)("Select",((e,t)=>{let{rootPrefixCls:n}=t;const o=(0,We.oX)(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[ft(o),ut(o)]}),(e=>{const{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:r,controlHeightSM:i,controlHeightLG:l,paddingXXS:a,controlPaddingHorizontal:c,zIndexPopupBase:s,colorText:u,fontWeightStrong:d,controlItemBgActive:m,controlItemBgHover:p,colorBgContainer:f,colorFillSecondary:g,colorBgContainerDisabled:v,colorTextDisabled:h,colorPrimaryHover:b,colorPrimary:y,controlOutline:$}=e,C=2*a,A=2*o,w=Math.min(r-C,r-A),x=Math.min(i-C,i-A),S=Math.min(l-C,l-A);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(a/2),zIndexPopup:s+50,optionSelectedColor:u,optionSelectedFontWeight:d,optionSelectedBg:m,optionActiveBg:p,optionPadding:`${(r-t*n)/2}px ${c}px`,optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:f,clearBg:f,singleItemHeightLG:l,multipleItemBg:g,multipleItemBorderColor:"transparent",multipleItemHeight:w,multipleItemHeightSM:x,multipleItemHeightLG:S,multipleSelectorBgDisabled:v,multipleItemColorDisabled:h,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:y,activeOutlineColor:$,selectAffixPadding:a}}),{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}}),vt=n(6067),ht=n(6029),bt=n(7852),yt=n(4103),$t=n(3567),Ct=n(2877);var At=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const wt="SECRET_COMBOBOX_MODE_DO_NOT_USE",xt=(e,t)=>{var n;const{prefixCls:r,bordered:l,className:a,rootClassName:c,getPopupContainer:s,popupClassName:u,dropdownClassName:d,listHeight:m=256,placement:p,listItemHeight:f,size:g,disabled:v,notFoundContent:h,status:b,builtinPlacements:y,dropdownMatchSelectWidth:$,popupMatchSelectWidth:C,direction:A,style:w,allowClear:x,variant:S,dropdownStyle:E,transitionName:I,tagRender:O,maxCount:M,prefix:k}=e,R=At(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix"]),{getPopupContainer:z,getPrefixCls:B,renderEmpty:N,direction:j,virtual:P,popupMatchSelectWidth:H,popupOverflow:T}=o.useContext(Me.QO),D=(0,Me.TP)("select"),[,L]=(0,He.Ay)(),F=null!=f?f:null==L?void 0:L.controlHeight,W=B("select",r),K=B(),_=null!=A?A:j,{compactSize:V,compactItemClassnames:X}=(0,Pe.RQ)(W,_),[q,G]=(0,je.A)("select",S,l),Y=(0,ze.A)(W),[U,Q,J]=gt(W,Y),Z=o.useMemo((()=>{const{mode:t}=e;if("combobox"!==t)return t===wt?"combobox":t}),[e.mode]),ee="multiple"===Z||"tags"===Z,te=function(e,t){return void 0!==t?t:null!==e}(e.suffixIcon,e.showArrow),ne=null!==(n=null!=C?C:$)&&void 0!==n?n:H,{status:re,hasFeedback:ie,isFormItemInput:le,feedbackIcon:ae}=o.useContext(Ne.$W),ce=(0,Oe.v)(re,b);let se;se=void 0!==h?h:"combobox"===Z?null:(null==N?void 0:N("Select"))||o.createElement(ke.A,{componentName:"Select"});const{suffixIcon:ue,itemIcon:de,removeIcon:me,clearIcon:pe}=function(e){let{suffixIcon:t,clearIcon:n,menuItemSelectedIcon:r,removeIcon:i,loading:l,multiple:a,hasFeedback:c,prefixCls:s,showSuffixIcon:u,feedbackIcon:d,showArrow:m,componentName:p}=e;const f=null!=n?n:o.createElement(ht.A,null),g=e=>null!==t||c||m?o.createElement(o.Fragment,null,!1!==u&&e,c&&d):null;let v=null;if(void 0!==t)v=g(t);else if(l)v=g(o.createElement($t.A,{spin:!0}));else{const e=`${s}-suffix`;v=t=>{let{open:n,showSearch:r}=t;return g(n&&r?o.createElement(Ct.A,{className:e}):o.createElement(yt.A,{className:e}))}}let h=null;h=void 0!==r?r:a?o.createElement(vt.A,null):null;let b=null;return b=void 0!==i?i:o.createElement(bt.A,null),{clearIcon:f,suffixIcon:v,itemIcon:h,removeIcon:b}}(Object.assign(Object.assign({},R),{multiple:ee,hasFeedback:ie,feedbackIcon:ae,showSuffixIcon:te,prefixCls:W,componentName:"Select"})),fe=!0===x?{clearIcon:pe}:x,ge=(0,oe.A)(R,["suffixIcon","itemIcon"]),ve=i()(u||d,{[`${W}-dropdown-${_}`]:"rtl"===_},c,J,Y,Q),he=(0,Be.A)((e=>{var t;return null!==(t=null!=g?g:V)&&void 0!==t?t:e})),be=o.useContext(Re.A),ye=null!=v?v:be,$e=i()({[`${W}-lg`]:"large"===he,[`${W}-sm`]:"small"===he,[`${W}-rtl`]:"rtl"===_,[`${W}-${q}`]:G,[`${W}-in-form-item`]:le},(0,Oe.L)(W,ce,ie),X,D.className,a,c,J,Y,Q),Ce=o.useMemo((()=>void 0!==p?p:"rtl"===_?"bottomRight":"bottomLeft"),[p,_]);const[Ae]=(0,Se.YK)("SelectLike",null==E?void 0:E.zIndex);return U(o.createElement(xe,Object.assign({ref:t,virtual:P,showSearch:D.showSearch},ge,{style:Object.assign(Object.assign({},D.style),w),dropdownMatchSelectWidth:ne,transitionName:(0,Ee.b)(K,"slide-up",I),builtinPlacements:Te(y,T),listHeight:m,listItemHeight:F,mode:Z,prefixCls:W,placement:Ce,direction:_,prefix:k,suffixIcon:ue,menuItemSelectedIcon:de,removeIcon:me,allowClear:fe,notFoundContent:se,className:$e,getPopupContainer:s||z,dropdownClassName:ve,disabled:ye,dropdownStyle:Object.assign(Object.assign({},E),{zIndex:Ae}),maxCount:ee?M:void 0,tagRender:ee?O:void 0})))};const St=o.forwardRef(xt),Et=(0,Ie.A)(St,"dropdownAlign");St.SECRET_COMBOBOX_MODE_DO_NOT_USE=wt,St.Option=te,St.OptGroup=Z,St._InternalPanelDoNotUseOrYouWillBeFired=Et;var It=St},6476:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(6540),r=n(8719),i=n(2897),l=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const a=o.createContext(null),c=o.forwardRef(((e,t)=>{const{children:n}=e,c=l(e,["children"]),s=o.useContext(a),u=o.useMemo((()=>Object.assign(Object.assign({},s),c)),[s,c.prefixCls,c.mode,c.selectable,c.rootClassName]),d=(0,r.H3)(n),m=(0,r.xK)(t,d?(0,r.A9)(n):null);return o.createElement(a.Provider,{value:u},o.createElement(i.A,{space:!0},d?o.cloneElement(n,{ref:m}):n))}));t.h=a},6813:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},7072:function(e,t,n){n.d(t,{A:function(){return j}});var o=n(6540),r=n(6942),i=n.n(r),l=n(2279),a=n(9853);var c=e=>{const{prefixCls:t,className:n,style:r,size:l,shape:a}=e,c=i()({[`${t}-lg`]:"large"===l,[`${t}-sm`]:"small"===l}),s=i()({[`${t}-circle`]:"circle"===a,[`${t}-square`]:"square"===a,[`${t}-round`]:"round"===a}),u=o.useMemo((()=>"number"==typeof l?{width:l,height:l,lineHeight:`${l}px`}:{}),[l]);return o.createElement("span",{className:i()(t,c,s,n),style:Object.assign(Object.assign({},u),r)})},s=n(2187),u=n(7358),d=n(4277);const m=new s.Mo("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),p=e=>({height:e,lineHeight:(0,s.zA)(e)}),f=e=>Object.assign({width:e},p(e)),g=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:m,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),v=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},p(e)),h=e=>{const{skeletonAvatarCls:t,gradientFromColor:n,controlHeight:o,controlHeightLG:r,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:n},f(o)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},f(r)),[`${t}${t}-sm`]:Object.assign({},f(i))}},b=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:o,controlHeightLG:r,controlHeightSM:i,gradientFromColor:l,calc:a}=e;return{[o]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:n},v(t,a)),[`${o}-lg`]:Object.assign({},v(r,a)),[`${o}-sm`]:Object.assign({},v(i,a))}},y=e=>Object.assign({width:e},p(e)),$=e=>{const{skeletonImageCls:t,imageSizeBase:n,gradientFromColor:o,borderRadiusSM:r,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:o,borderRadius:r},y(i(n).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},y(n)),{maxWidth:i(n).mul(4).equal(),maxHeight:i(n).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},C=(e,t,n)=>{const{skeletonButtonCls:o}=e;return{[`${n}${o}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${o}-round`]:{borderRadius:t}}},A=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},p(e)),w=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:o,controlHeightLG:r,controlHeightSM:i,gradientFromColor:l,calc:a}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:t,width:a(o).mul(2).equal(),minWidth:a(o).mul(2).equal()},A(o,a))},C(e,o,n)),{[`${n}-lg`]:Object.assign({},A(r,a))}),C(e,r,`${n}-lg`)),{[`${n}-sm`]:Object.assign({},A(i,a))}),C(e,i,`${n}-sm`))},x=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:o,skeletonParagraphCls:r,skeletonButtonCls:i,skeletonInputCls:l,skeletonImageCls:a,controlHeight:c,controlHeightLG:s,controlHeightSM:u,gradientFromColor:d,padding:m,marginSM:p,borderRadius:v,titleHeight:y,blockRadius:C,paragraphLiHeight:A,controlHeightXS:x,paragraphMarginTop:S}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:m,verticalAlign:"top",[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:d},f(c)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:Object.assign({},f(s)),[`${n}-sm`]:Object.assign({},f(u))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[o]:{width:"100%",height:y,background:d,borderRadius:C,[`+ ${r}`]:{marginBlockStart:u}},[r]:{padding:0,"> li":{width:"100%",height:A,listStyle:"none",background:d,borderRadius:C,"+ li":{marginBlockStart:x}}},[`${r}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${o}, ${r} > li`]:{borderRadius:v}}},[`${t}-with-avatar ${t}-content`]:{[o]:{marginBlockStart:p,[`+ ${r}`]:{marginBlockStart:S}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},w(e)),h(e)),b(e)),$(e)),[`${t}${t}-block`]:{width:"100%",[i]:{width:"100%"},[l]:{width:"100%"}},[`${t}${t}-active`]:{[`\n        ${o},\n        ${r} > li,\n        ${n},\n        ${i},\n        ${l},\n        ${a}\n      `]:Object.assign({},g(e))}}};var S=(0,u.OF)("Skeleton",(e=>{const{componentCls:t,calc:n}=e,o=(0,d.oX)(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:n(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[x(o)]}),(e=>{const{colorFillContent:t,colorFill:n}=e;return{color:t,colorGradientEnd:n,gradientFromColor:t,gradientToColor:n,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}}),{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]});var E=e=>{const{prefixCls:t,className:n,rootClassName:r,active:s,shape:u="circle",size:d="default"}=e,{getPrefixCls:m}=o.useContext(l.QO),p=m("skeleton",t),[f,g,v]=S(p),h=(0,a.A)(e,["prefixCls","className"]),b=i()(p,`${p}-element`,{[`${p}-active`]:s},n,r,g,v);return f(o.createElement("div",{className:b},o.createElement(c,Object.assign({prefixCls:`${p}-avatar`,shape:u,size:d},h))))};var I=e=>{const{prefixCls:t,className:n,rootClassName:r,style:a,active:c}=e,{getPrefixCls:s}=o.useContext(l.QO),u=s("skeleton",t),[d,m,p]=S(u),f=i()(u,`${u}-element`,{[`${u}-active`]:c},n,r,m,p);return d(o.createElement("div",{className:f},o.createElement("div",{className:i()(`${u}-image`,n),style:a},o.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${u}-image-svg`},o.createElement("title",null,"Image placeholder"),o.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:`${u}-image-path`})))))};var O=e=>{const{prefixCls:t,className:n,rootClassName:r,active:s,block:u,size:d="default"}=e,{getPrefixCls:m}=o.useContext(l.QO),p=m("skeleton",t),[f,g,v]=S(p),h=(0,a.A)(e,["prefixCls"]),b=i()(p,`${p}-element`,{[`${p}-active`]:s,[`${p}-block`]:u},n,r,g,v);return f(o.createElement("div",{className:b},o.createElement(c,Object.assign({prefixCls:`${p}-input`,size:d},h))))};var M=e=>{const{prefixCls:t,className:n,rootClassName:r,style:a,active:c,children:s}=e,{getPrefixCls:u}=o.useContext(l.QO),d=u("skeleton",t),[m,p,f]=S(d),g=i()(d,`${d}-element`,{[`${d}-active`]:c},p,n,r,f);return m(o.createElement("div",{className:g},o.createElement("div",{className:i()(`${d}-image`,n),style:a},s)))};const k=(e,t)=>{const{width:n,rows:o=2}=t;return Array.isArray(n)?n[e]:o-1===e?n:void 0};var R=e=>{const{prefixCls:t,className:n,style:r,rows:l=0}=e,a=Array.from({length:l}).map(((t,n)=>o.createElement("li",{key:n,style:{width:k(n,e)}})));return o.createElement("ul",{className:i()(t,n),style:r},a)};var z=e=>{let{prefixCls:t,className:n,width:r,style:l}=e;return o.createElement("h3",{className:i()(t,n),style:Object.assign({width:r},l)})};function B(e){return e&&"object"==typeof e?e:{}}const N=e=>{const{prefixCls:t,loading:n,className:r,rootClassName:a,style:s,children:u,avatar:d=!1,title:m=!0,paragraph:p=!0,active:f,round:g}=e,{getPrefixCls:v,direction:h,className:b,style:y}=(0,l.TP)("skeleton"),$=v("skeleton",t),[C,A,w]=S($);if(n||!("loading"in e)){const e=!!d,t=!!m,n=!!p;let l,u;if(e){const e=Object.assign(Object.assign({prefixCls:`${$}-avatar`},function(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}(t,n)),B(d));l=o.createElement("div",{className:`${$}-header`},o.createElement(c,Object.assign({},e)))}if(t||n){let r,i;if(t){const t=Object.assign(Object.assign({prefixCls:`${$}-title`},function(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}(e,n)),B(m));r=o.createElement(z,Object.assign({},t))}if(n){const n=Object.assign(Object.assign({prefixCls:`${$}-paragraph`},function(e,t){const n={};return e&&t||(n.width="61%"),n.rows=!e&&t?3:2,n}(e,t)),B(p));i=o.createElement(R,Object.assign({},n))}u=o.createElement("div",{className:`${$}-content`},r,i)}const v=i()($,{[`${$}-with-avatar`]:e,[`${$}-active`]:f,[`${$}-rtl`]:"rtl"===h,[`${$}-round`]:g},b,r,a,A,w);return C(o.createElement("div",{className:v,style:Object.assign(Object.assign({},y),s)},l,u))}return null!=u?u:null};N.Button=e=>{const{prefixCls:t,className:n,rootClassName:r,active:s,block:u=!1,size:d="default"}=e,{getPrefixCls:m}=o.useContext(l.QO),p=m("skeleton",t),[f,g,v]=S(p),h=(0,a.A)(e,["prefixCls"]),b=i()(p,`${p}-element`,{[`${p}-active`]:s,[`${p}-block`]:u},n,r,g,v);return f(o.createElement("div",{className:b},o.createElement(c,Object.assign({prefixCls:`${p}-button`,size:d},h))))},N.Avatar=E,N.Input=O,N.Image=I,N.Node=M;var j=N},7206:function(e,t,n){n.d(t,{A:function(){return Y}});var o=n(6540),r=n(8810),i=n(8414),l=n(2318),a=n(6942),c=n.n(a),s=n(6956),u=n(9853),d=n(3723),m=n(682),p=n(2279),f=n(934);var g=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1}),v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var h=e=>{const{prefixCls:t,className:n,dashed:i}=e,l=v(e,["prefixCls","className","dashed"]),{getPrefixCls:a}=o.useContext(p.QO),s=a("menu",t),u=c()({[`${s}-item-divider-dashed`]:!!i},n);return o.createElement(r.cG,Object.assign({className:u},l))},b=n(2546),y=n(955);var $=e=>{var t;const{className:n,children:l,icon:a,title:s,danger:d,extra:p}=e,{prefixCls:f,firstLevel:v,direction:h,disableMenuItemTitleTooltip:$,inlineCollapsed:C}=o.useContext(g),{siderCollapsed:A}=o.useContext(i.P);let w=s;void 0===s?w=v?l:"":!1===s&&(w="");const x={title:w};A||C||(x.title=null,x.open=!1);const S=(0,b.A)(l).length;let E=o.createElement(r.q7,Object.assign({},(0,u.A)(e,["title","icon","danger"]),{className:c()({[`${f}-item-danger`]:d,[`${f}-item-only-child`]:1===(a?S+1:S)},n),title:"string"==typeof s?s:void 0}),(0,m.Ob)(a,{className:c()(o.isValidElement(a)?null===(t=a.props)||void 0===t?void 0:t.className:"",`${f}-item-icon`)}),(e=>{const t=null==l?void 0:l[0],n=o.createElement("span",{className:c()(`${f}-title-content`,{[`${f}-title-content-with-extra`]:!!p||0===p})},l);return(!a||o.isValidElement(l)&&"span"===l.type)&&l&&e&&v&&"string"==typeof t?o.createElement("div",{className:`${f}-inline-collapsed-noicon`},t.charAt(0)):n})(C));return $||(E=o.createElement(y.A,Object.assign({},x,{placement:"rtl"===h?"left":"right",classNames:{root:`${f}-inline-collapsed-tooltip`}}),E)),E},C=n(6476),A=n(2187),w=n(2616),x=n(5905),S=n(977),E=n(3561),I=n(9077),O=n(7358),M=n(4277);var k=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:i,lineType:l,itemPaddingInline:a}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${(0,A.zA)(i)} ${l} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:a},[`> ${t}-item:hover,\n        > ${t}-item-active,\n        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}};var R=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,\n    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,A.zA)(o(n).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,A.zA)(n)})`}}}}};const z=e=>Object.assign({},(0,x.jk)(e));var B=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:i,groupTitleColor:l,itemBg:a,subMenuItemBg:c,itemSelectedBg:s,activeBarHeight:u,activeBarWidth:d,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:f,motionEaseOut:g,itemPaddingInline:v,motionDurationMid:h,itemHoverColor:b,lineType:y,colorSplit:$,itemDisabledColor:C,dangerItemColor:w,dangerItemHoverColor:x,dangerItemSelectedColor:S,dangerItemActiveBg:E,dangerItemSelectedBg:I,popupBg:O,itemHoverBg:M,itemActiveBg:k,menuSubMenuBg:R,horizontalItemSelectedColor:B,horizontalItemSelectedBg:N,horizontalItemBorderRadius:j,horizontalItemHoverBg:P}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:a,[`&${n}-root:focus-visible`]:Object.assign({},z(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:l}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:i},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},z(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${C} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:b}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:M},"&:active":{backgroundColor:k}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:M},"&:active":{backgroundColor:k}}},[`${n}-item-danger`]:{color:w,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:x}},[`&${n}-item:active`]:{background:E}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:S},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:s,[`&${n}-item-danger`]:{backgroundColor:I}},[`&${n}-submenu > ${n}`]:{backgroundColor:R},[`&${n}-popup > ${n}`]:{backgroundColor:O},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:O},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:j,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:`${(0,A.zA)(u)} solid transparent`,transition:`border-color ${p} ${f}`,content:'""'},"&:hover, &-active, &-open":{background:P,"&::after":{borderBottomWidth:u,borderBottomColor:B}},"&-selected":{color:B,backgroundColor:N,"&:hover":{backgroundColor:N},"&::after":{borderBottomWidth:u,borderBottomColor:B}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${(0,A.zA)(m)} ${y} ${$}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:c},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,A.zA)(d)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${h} ${g}`,`opacity ${h} ${g}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:S}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${h} ${f}`,`opacity ${h} ${f}`].join(",")}}}}}};const N=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:i,marginXS:l,itemMarginBlock:a,itemWidth:c,itemPaddingInline:s}=e,u=e.calc(i).add(r).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:(0,A.zA)(n),paddingInline:s,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:a,width:c},[`> ${t}-item,\n            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:(0,A.zA)(n)},[`${t}-item-group-list ${t}-submenu-title,\n            ${t}-submenu-title`]:{paddingInlineEnd:u}}};var j=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:i,controlHeightLG:l,motionEaseOut:a,paddingXL:c,itemMarginInline:s,fontSizeLG:u,motionDurationFast:d,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:f,collapsedWidth:g,collapsedIconSize:v}=e,h={height:o,lineHeight:(0,A.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},N(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},N(e)),{boxShadow:f})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${(0,A.zA)(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${m}`,`background ${m}`,`padding ${d} ${a}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:h,[`& ${t}-item-group-title`]:{paddingInlineStart:c}},[`${t}-item`]:h}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:u,textAlign:"center"}}},[`> ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,\n          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,A.zA)(e.calc(v).div(2).equal())} - ${(0,A.zA)(s)})`,textOverflow:"clip",[`\n            ${t}-submenu-arrow,\n            ${t}-submenu-expand-icon\n          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:v,lineHeight:(0,A.zA)(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},x.L9),{paddingInline:p})}}]};const P=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:i,iconCls:l,iconSize:a,iconMarginInlineEnd:c}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${r}`].join(","),[`${t}-item-icon, ${l}`]:{minWidth:a,fontSize:a,transition:[`font-size ${o} ${i}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:c,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},(0,x.Nk)()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},H=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:i,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,A.zA)(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,A.zA)(l)})`}}}}},T=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:i,motionEaseInOut:l,paddingXS:a,padding:c,colorSplit:s,lineWidth:u,zIndexPopup:d,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:f,menuArrowOffset:g,lineType:v,groupTitleLineHeight:h,groupTitleFontSize:b}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,x.t6)()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,x.dF)(e)),(0,x.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${(0,A.zA)(a)} ${(0,A.zA)(c)}`,fontSize:b,lineHeight:h,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`,`padding ${i} ${l}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${l}`,`padding ${r} ${l}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:v,borderWidth:0,borderTopWidth:u,marginBlock:u,padding:0,"&-dashed":{borderStyle:"dashed"}}}),P(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${(0,A.zA)(e.calc(o).mul(2).equal())} ${(0,A.zA)(c)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},P(e)),H(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${l}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),H(e)),{[`&-inline-collapsed ${n}-submenu-arrow,\n        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,A.zA)(g)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,A.zA)(e.calc(g).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${(0,A.zA)(e.calc(f).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,A.zA)(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,A.zA)(g)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},D=e=>{var t,n,o;const{colorPrimary:r,colorError:i,colorTextDisabled:l,colorErrorBg:a,colorText:c,colorTextDescription:s,colorBgContainer:u,colorFillAlter:d,colorFillContent:m,lineWidth:p,lineWidthBold:f,controlItemBgActive:g,colorBgTextHover:v,controlHeightLG:h,lineHeight:b,colorBgElevated:y,marginXXS:$,padding:C,fontSize:A,controlHeightSM:x,fontSizeLG:S,colorTextLightSolid:E,colorErrorHover:I}=e,O=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,M=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,k=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,R=new w.Y(E).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:c,itemColor:c,colorItemTextHover:c,itemHoverColor:c,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:s,groupTitleColor:s,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:u,itemBg:u,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:m,itemActiveBg:g,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:O,colorActiveBarHeight:f,activeBarHeight:f,colorActiveBarBorderSize:p,activeBarBorderWidth:M,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:a,dangerItemActiveBg:a,colorDangerItemBgSelected:a,dangerItemSelectedBg:a,itemMarginInline:k,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:h,groupTitleLineHeight:b,collapsedWidth:2*h,popupBg:y,itemMarginBlock:$,itemPaddingInline:C,horizontalLineHeight:1.15*h+"px",iconSize:A,iconMarginInlineEnd:x-A,collapsedIconSize:S,groupTitleFontSize:A,darkItemDisabledColor:new w.Y(E).setA(.25).toRgbString(),darkItemColor:R,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:E,darkItemSelectedBg:r,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:R,darkItemHoverColor:E,darkDangerItemHoverColor:I,darkDangerItemSelectedColor:E,darkDangerItemActiveBg:i,itemWidth:O?`calc(100% + ${M}px)`:`calc(100% - ${2*k}px)`}};var L=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return(0,O.OF)("Menu",(e=>{const{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:r,darkDangerItemColor:i,darkItemBg:l,darkSubMenuItemBg:a,darkItemSelectedColor:c,darkItemSelectedBg:s,darkDangerItemSelectedBg:u,darkItemHoverBg:d,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:f,darkDangerItemHoverColor:g,darkDangerItemSelectedColor:v,darkDangerItemActiveBg:h,popupBg:b,darkPopupBg:y}=e,$=e.calc(o).div(7).mul(5).equal(),C=(0,M.oX)(e,{menuArrowSize:$,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc($).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:b}),A=(0,M.oX)(C,{itemColor:r,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:c,subMenuItemSelectedColor:c,itemBg:l,popupBg:y,subMenuItemBg:a,itemActiveBg:"transparent",itemSelectedBg:s,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:d,itemDisabledColor:f,dangerItemColor:i,dangerItemHoverColor:g,dangerItemSelectedColor:v,dangerItemActiveBg:h,dangerItemSelectedBg:u,menuSubMenuBg:a,horizontalItemSelectedColor:c,horizontalItemSelectedBg:s});return[T(C),k(C),j(C),B(C,"light"),B(A,"dark"),R(C),(0,S.A)(C),(0,E._j)(C,"slide-up"),(0,E._j)(C,"slide-down"),(0,I.aB)(C,"zoom-big")]}),D,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)},F=n(275);var W=e=>{var t;const{popupClassName:n,icon:i,title:l,theme:a}=e,s=o.useContext(g),{prefixCls:d,inlineCollapsed:p,theme:f}=s,v=(0,r.Wj)();let h;if(i){const e=o.isValidElement(l)&&"span"===l.type;h=o.createElement(o.Fragment,null,(0,m.Ob)(i,{className:c()(o.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:"",`${d}-item-icon`)}),e?l:o.createElement("span",{className:`${d}-title-content`},l))}else h=p&&!v.length&&l&&"string"==typeof l?o.createElement("div",{className:`${d}-inline-collapsed-noicon`},l.charAt(0)):o.createElement("span",{className:`${d}-title-content`},l);const b=o.useMemo((()=>Object.assign(Object.assign({},s),{firstLevel:!1})),[s]),[y]=(0,F.YK)("Menu");return o.createElement(g.Provider,{value:b},o.createElement(r.g8,Object.assign({},(0,u.A)(e,["icon"]),{title:h,popupClassName:c()(d,n,`${d}-${a||f}`),popupStyle:Object.assign({zIndex:y},e.popupStyle)})))},K=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function _(e){return null===e||!1===e}const V={item:$,submenu:W,divider:h},X=(0,o.forwardRef)(((e,t)=>{var n;const i=o.useContext(C.h),a=i||{},{getPrefixCls:v,getPopupContainer:h,direction:b,menu:y}=o.useContext(p.QO),$=v(),{prefixCls:A,className:w,style:x,theme:S="light",expandIcon:E,_internalDisableMenuItemTitleTooltip:I,inlineCollapsed:O,siderCollapsed:M,rootClassName:k,mode:R,selectable:z,onClick:B,overflowedIndicatorPopupClassName:N}=e,j=K(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),P=(0,u.A)(j,["collapsedWidth"]);null===(n=a.validator)||void 0===n||n.call(a,{mode:R});const H=(0,s.A)((function(){var e;null==B||B.apply(void 0,arguments),null===(e=a.onClick)||void 0===e||e.call(a)})),T=a.mode||R,D=null!=z?z:a.selectable,F=null!=O?O:M,W={horizontal:{motionName:`${$}-slide-up`},inline:(0,d.A)($),other:{motionName:`${$}-zoom-big`}},X=v("menu",A||a.prefixCls),q=(0,f.A)(X),[G,Y,U]=L(X,q,!i),Q=c()(`${X}-${S}`,null==y?void 0:y.className,w),J=o.useMemo((()=>{var e,t;if("function"==typeof E||_(E))return E||null;if("function"==typeof a.expandIcon||_(a.expandIcon))return a.expandIcon||null;if("function"==typeof(null==y?void 0:y.expandIcon)||_(null==y?void 0:y.expandIcon))return(null==y?void 0:y.expandIcon)||null;const n=null!==(e=null!=E?E:null==a?void 0:a.expandIcon)&&void 0!==e?e:null==y?void 0:y.expandIcon;return(0,m.Ob)(n,{className:c()(`${X}-submenu-expand-icon`,o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})}),[E,null==a?void 0:a.expandIcon,null==y?void 0:y.expandIcon,X]),Z=o.useMemo((()=>({prefixCls:X,inlineCollapsed:F||!1,direction:b,firstLevel:!0,theme:S,mode:T,disableMenuItemTitleTooltip:I})),[X,F,b,I,S]);return G(o.createElement(C.h.Provider,{value:null},o.createElement(g.Provider,{value:Z},o.createElement(r.Ay,Object.assign({getPopupContainer:h,overflowedIndicator:o.createElement(l.A,null),overflowedIndicatorPopupClassName:c()(X,`${X}-${S}`,N),mode:T,selectable:D,onClick:H},P,{inlineCollapsed:F,style:Object.assign(Object.assign({},null==y?void 0:y.style),x),className:Q,prefixCls:X,direction:b,defaultMotions:W,expandIcon:J,ref:t,rootClassName:c()(k,Y,a.rootClassName,U,q),_internalComponents:V})))))}));var q=X;const G=(0,o.forwardRef)(((e,t)=>{const n=(0,o.useRef)(null),r=o.useContext(i.P);return(0,o.useImperativeHandle)(t,(()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}}))),o.createElement(q,Object.assign({ref:n},e,r))}));G.Item=$,G.SubMenu=W,G.Divider=h,G.ItemGroup=r.te;var Y=G},7308:function(e,t,n){n.d(t,{A:function(){return $}});var o=n(6540),r=n(6942),i=n.n(r),l=n(9155),a=n(2616),c=n(1320);var s=()=>{const[,e]=(0,c.Ay)(),[t]=(0,l.A)("Empty"),n=new a.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return o.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{fill:"none",fillRule:"evenodd"},o.createElement("g",{transform:"translate(24 31.67)"},o.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),o.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),o.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),o.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),o.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),o.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),o.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},o.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),o.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))};var u=()=>{const[,e]=(0,c.Ay)(),[t]=(0,l.A)("Empty"),{colorFill:n,colorFillTertiary:r,colorFillQuaternary:i,colorBgContainer:s}=e,{borderColor:u,shadowColor:d,contentColor:m}=(0,o.useMemo)((()=>({borderColor:new a.Y(n).onBackground(s).toHexString(),shadowColor:new a.Y(r).onBackground(s).toHexString(),contentColor:new a.Y(i).onBackground(s).toHexString()})),[n,r,i,s]);return o.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},o.createElement("ellipse",{fill:d,cx:"32",cy:"33",rx:"32",ry:"7"}),o.createElement("g",{fillRule:"nonzero",stroke:u},o.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),o.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:m}))))},d=n(7358),m=n(4277);const p=e=>{const{componentCls:t,margin:n,marginXS:o,marginXL:r,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:o,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:r,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}};var f=(0,d.OF)("Empty",(e=>{const{componentCls:t,controlHeightLG:n,calc:o}=e,r=(0,m.oX)(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[p(r)]})),g=n(2279),v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const h=o.createElement(s,null),b=o.createElement(u,null),y=e=>{const{className:t,rootClassName:n,prefixCls:r,image:a=h,description:c,children:s,imageStyle:u,style:d,classNames:m,styles:p}=e,y=v(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:$,direction:C,className:A,style:w,classNames:x,styles:S}=(0,g.TP)("empty"),E=$("empty",r),[I,O,M]=f(E),[k]=(0,l.A)("Empty"),R=void 0!==c?c:null==k?void 0:k.description,z="string"==typeof R?R:"empty";let B=null;return B="string"==typeof a?o.createElement("img",{alt:z,src:a}):a,I(o.createElement("div",Object.assign({className:i()(O,M,E,A,{[`${E}-normal`]:a===b,[`${E}-rtl`]:"rtl"===C},t,n,x.root,null==m?void 0:m.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},S.root),w),null==p?void 0:p.root),d)},y),o.createElement("div",{className:i()(`${E}-image`,x.image,null==m?void 0:m.image),style:Object.assign(Object.assign(Object.assign({},u),S.image),null==p?void 0:p.image)},B),R&&o.createElement("div",{className:i()(`${E}-description`,x.description,null==m?void 0:m.description),style:Object.assign(Object.assign({},S.description),null==p?void 0:p.description)},R),s&&o.createElement("div",{className:i()(`${E}-footer`,x.footer,null==m?void 0:m.footer),style:Object.assign(Object.assign({},S.footer),null==p?void 0:p.footer)},s)))};y.PRESENTED_IMAGE_DEFAULT=h,y.PRESENTED_IMAGE_SIMPLE=b;var $=y},7447:function(e,t,n){n.d(t,{A:function(){return r}});var o=n(6540);function r(){const[,e]=o.useReducer((e=>e+1),0);return e}},7550:function(e,t,n){n.d(t,{A:function(){return T}});var o=n(6540),r=n(329),i=n(8e3),l=n(6942),a=n.n(l),c=n(3497),s=n(6956),u=n(2533),d=n(9853),m=n(275);var p=e=>"object"!=typeof e&&"function"!=typeof e||null===e,f=n(3257),g=n(3425),v=n(682),h=n(8877),b=n(235),y=n(2279),$=n(934),C=n(7206),A=n(6476),w=n(1320),x=n(2187),S=n(5905),E=n(3561),I=n(4211),O=n(9077),M=n(5201),k=n(791),R=n(7358),z=n(4277);var B=e=>{const{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:r}=e,i=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}};const N=e=>{const{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:i,antCls:l,iconCls:a,motionDurationMid:c,paddingBlock:s,fontSize:u,dropdownEdgeChildPadding:d,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:f,colorBgElevated:g}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${l}-btn`]:{[`& > ${a}-down, & > ${l}-btn-icon > ${a}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${l}-btn > ${a}-down`]:{fontSize:p},[`${a}-down::before`]:{transition:`transform ${c}`}},[`${t}-wrap-open`]:{[`${a}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomLeft,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomLeft,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottom,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottom,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomRight,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:E.ox},[`&${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topLeft,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topLeft,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-top,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-top,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topRight,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topRight`]:{animationName:E.nP},[`&${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomLeft,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottom,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:E.vR},[`&${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topLeft,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-top,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topRight`]:{animationName:E.YU}}},(0,M.Ay)(e,g,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,S.dF)(e)),{[n]:Object.assign(Object.assign({padding:d,listStyleType:"none",backgroundColor:g,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,S.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${(0,x.zA)(s)} ${(0,x.zA)(f)}`,color:e.colorTextDescription,transition:`all ${c}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:u,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${c}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,x.zA)(s)} ${(0,x.zA)(f)}`,color:e.colorText,fontWeight:"normal",fontSize:u,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${c}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,S.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:g,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,x.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:p,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${(0,x.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(f).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:g,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,E._j)(e,"slide-up"),(0,E._j)(e,"slide-down"),(0,I.Mh)(e,"move-up"),(0,I.Mh)(e,"move-down"),(0,O.aB)(e,"zoom-big")]]};var j=(0,R.OF)("Dropdown",(e=>{const{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:r}=e,i=(0,z.oX)(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[N(i),B(i)]}),(e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,M.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,k.n)(e))),{resetStyle:!1});const P=e=>{var t;const{menu:n,arrow:l,prefixCls:g,children:x,trigger:S,disabled:E,dropdownRender:I,getPopupContainer:O,overlayClassName:M,rootClassName:k,overlayStyle:R,open:z,onOpenChange:B,visible:N,onVisibleChange:P,mouseEnterDelay:H=.15,mouseLeaveDelay:T=.1,autoAdjustOverflow:D=!0,placement:L="",overlay:F,transitionName:W}=e,{getPopupContainer:K,getPrefixCls:_,direction:V,dropdown:X}=o.useContext(y.QO);(0,h.rJ)("Dropdown");const q=o.useMemo((()=>{const e=_();return void 0!==W?W:L.includes("top")?`${e}-slide-down`:`${e}-slide-up`}),[_,L,W]),G=o.useMemo((()=>L?L.includes("Center")?L.slice(0,L.indexOf("Center")):L:"rtl"===V?"bottomRight":"bottomLeft"),[L,V]);const Y=_("dropdown",g),U=(0,$.A)(Y),[Q,J,Z]=j(Y,U),[,ee]=(0,w.Ay)(),te=o.Children.only(p(x)?o.createElement("span",null,x):x),ne=(0,v.Ob)(te,{className:a()(`${Y}-trigger`,{[`${Y}-rtl`]:"rtl"===V},te.props.className),disabled:null!==(t=te.props.disabled)&&void 0!==t?t:E}),oe=E?[]:S,re=!!(null==oe?void 0:oe.includes("contextMenu")),[ie,le]=(0,u.A)(!1,{value:null!=z?z:N}),ae=(0,s.A)((e=>{null==B||B(e,{source:"trigger"}),null==P||P(e),le(e)})),ce=a()(M,k,J,Z,U,null==X?void 0:X.className,{[`${Y}-rtl`]:"rtl"===V}),se=(0,f.A)({arrowPointAtCenter:"object"==typeof l&&l.pointAtCenter,autoAdjustOverflow:D,offset:ee.marginXXS,arrowWidth:l?ee.sizePopupArrow:0,borderRadius:ee.borderRadius}),ue=o.useCallback((()=>{(null==n?void 0:n.selectable)&&(null==n?void 0:n.multiple)||(null==B||B(!1,{source:"menu"}),le(!1))}),[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[de,me]=(0,m.YK)("Dropdown",null==R?void 0:R.zIndex);let pe=o.createElement(c.A,Object.assign({alignPoint:re},(0,d.A)(e,["rootClassName"]),{mouseEnterDelay:H,mouseLeaveDelay:T,visible:ie,builtinPlacements:se,arrow:!!l,overlayClassName:ce,prefixCls:Y,getPopupContainer:O||K,transitionName:q,trigger:oe,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(C.A,Object.assign({},n)):"function"==typeof F?F():F,I&&(e=I(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(A.A,{prefixCls:`${Y}-menu`,rootClassName:a()(Z,U),expandIcon:o.createElement("span",{className:`${Y}-menu-submenu-arrow`},"rtl"===V?o.createElement(r.A,{className:`${Y}-menu-submenu-arrow-icon`}):o.createElement(i.A,{className:`${Y}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:ue,validator:e=>{let{mode:t}=e}},e)},placement:G,onVisibleChange:ae,overlayStyle:Object.assign(Object.assign(Object.assign({},null==X?void 0:X.style),R),{zIndex:de})}),ne);return de&&(pe=o.createElement(b.A.Provider,{value:me},pe)),Q(pe)},H=(0,g.A)(P,"align",void 0,"dropdown",(e=>e));P._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(H,Object.assign({},e),o.createElement("span",null));var T=P},8e3:function(e,t,n){n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=r.forwardRef(a)},8017:function(e,t,n){n.d(t,{A:function(){return C}});var o=n(6540),r=n(6942),i=n.n(r),l=n(8491),a=n(8719),c=n(2897),s=n(6311),u=n(8182),d=n(2279),m=n(8119),p=n(934),f=n(829),g=n(4241),v=n(124),h=n(6327),b=n(5254),y=n(1594);var $=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var C=(0,o.forwardRef)(((e,t)=>{const{prefixCls:n,bordered:r=!0,status:C,size:A,disabled:w,onBlur:x,onFocus:S,suffix:E,allowClear:I,addonAfter:O,addonBefore:M,className:k,style:R,styles:z,rootClassName:B,onChange:N,classNames:j,variant:P}=e,H=$(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]);const{getPrefixCls:T,direction:D,allowClear:L,autoComplete:F,className:W,style:K,classNames:_,styles:V}=(0,d.TP)("input"),X=T("input",n),q=(0,o.useRef)(null),G=(0,p.A)(X),[Y,U,Q]=(0,y.MG)(X,B),[J]=(0,y.Ay)(X,G),{compactSize:Z,compactItemClassnames:ee}=(0,h.RQ)(X,D),te=(0,f.A)((e=>{var t;return null!==(t=null!=A?A:Z)&&void 0!==t?t:e})),ne=o.useContext(m.A),oe=null!=w?w:ne,{status:re,hasFeedback:ie,feedbackIcon:le}=(0,o.useContext)(g.$W),ae=(0,u.v)(re,C),ce=function(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}(e)||!!ie;(0,o.useRef)(ce);const se=(0,b.A)(q,!0),ue=(ie||E)&&o.createElement(o.Fragment,null,E,ie&&le),de=(0,s.A)(null!=I?I:L),[me,pe]=(0,v.A)("input",P,r);return Y(J(o.createElement(l.A,Object.assign({ref:(0,a.K4)(t,q),prefixCls:X,autoComplete:F},H,{disabled:oe,onBlur:e=>{se(),null==x||x(e)},onFocus:e=>{se(),null==S||S(e)},style:Object.assign(Object.assign({},K),R),styles:Object.assign(Object.assign({},V),z),suffix:ue,allowClear:de,className:i()(k,B,Q,G,ee,W),onChange:e=>{se(),null==N||N(e)},addonBefore:M&&o.createElement(c.A,{form:!0,space:!0},M),addonAfter:O&&o.createElement(c.A,{form:!0,space:!0},O),classNames:Object.assign(Object.assign(Object.assign({},j),_),{input:i()({[`${X}-sm`]:"small"===te,[`${X}-lg`]:"large"===te,[`${X}-rtl`]:"rtl"===D},null==j?void 0:j.input,_.input,U),variant:i()({[`${X}-${me}`]:pe},(0,u.L)(X,ae)),affixWrapper:i()({[`${X}-affix-wrapper-sm`]:"small"===te,[`${X}-affix-wrapper-lg`]:"large"===te,[`${X}-affix-wrapper-rtl`]:"rtl"===D},U),wrapper:i()({[`${X}-group-rtl`]:"rtl"===D},U),groupWrapper:i()({[`${X}-group-wrapper-sm`]:"small"===te,[`${X}-group-wrapper-lg`]:"large"===te,[`${X}-group-wrapper-rtl`]:"rtl"===D,[`${X}-group-wrapper-${me}`]:pe},(0,u.L)(`${X}-group-wrapper`,ae,ie),U)})}))))}))},8188:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},8414:function(e,t,n){n.d(t,{P:function(){return w},A:function(){return E}});var o=n(6540),r=n(8168),i={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},l=n(7064),a=function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:i}))};var c=o.forwardRef(a),s=n(329),u=n(8e3),d=n(6942),m=n.n(d),p=n(9853),f=n(2279),g=n(4129),v=n(2187),h=n(4440),b=n(7358);const y=e=>{const{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:r,antCls:i,triggerHeight:l,triggerColor:a,triggerBg:c,headerHeight:s,zeroTriggerWidth:u,zeroTriggerHeight:d,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:f,lightTriggerBg:g,bodyBg:h}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:l},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:l,color:a,lineHeight:(0,v.zA)(l),textAlign:"center",background:c,cursor:"pointer",transition:`all ${o}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:s,insetInlineEnd:e.calc(u).mul(-1).equal(),zIndex:1,width:u,height:d,color:a,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${(0,v.zA)(m)} ${(0,v.zA)(m)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(u).mul(-1).equal(),borderRadius:`${(0,v.zA)(m)} 0 0 ${(0,v.zA)(m)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:f,background:g},[`${t}-zero-width-trigger`]:{color:f,background:g,border:`1px solid ${h}`,borderInlineStart:0}}}}};var $=(0,b.OF)(["Layout","Sider"],(e=>[y(e)]),h.cH,{deprecatedTokens:h.lB}),C=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const A={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},w=o.createContext({}),x=(()=>{let e=0;return function(){return e+=1,`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:""}${e}`}})(),S=o.forwardRef(((e,t)=>{const{prefixCls:n,className:r,trigger:i,children:l,defaultCollapsed:a=!1,theme:d="dark",style:v={},collapsible:h=!1,reverseArrow:b=!1,width:y=200,collapsedWidth:S=80,zeroWidthTriggerStyle:E,breakpoint:I,onCollapse:O,onBreakpoint:M}=e,k=C(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:R}=(0,o.useContext)(g.M),[z,B]=(0,o.useState)("collapsed"in e?e.collapsed:a),[N,j]=(0,o.useState)(!1);(0,o.useEffect)((()=>{"collapsed"in e&&B(e.collapsed)}),[e.collapsed]);const P=(t,n)=>{"collapsed"in e||B(t),null==O||O(t,n)},{getPrefixCls:H,direction:T}=(0,o.useContext)(f.QO),D=H("layout-sider",n),[L,F,W]=$(D),K=(0,o.useRef)(null);K.current=e=>{j(e.matches),null==M||M(e.matches),z!==e.matches&&P(e.matches,"responsive")},(0,o.useEffect)((()=>{function e(e){return K.current(e)}let t;if("undefined"!=typeof window){const{matchMedia:o}=window;if(o&&I&&I in A){t=o(`screen and (max-width: ${A[I]})`);try{t.addEventListener("change",e)}catch(n){t.addListener(e)}e(t)}}return()=>{try{null==t||t.removeEventListener("change",e)}catch(n){null==t||t.removeListener(e)}}}),[I]),(0,o.useEffect)((()=>{const e=x("ant-sider-");return R.addSider(e),()=>R.removeSider(e)}),[]);const _=()=>{P(!z,"clickTrigger")},V=(0,p.A)(k,["collapsed"]),X=z?S:y,q=(G=X,!Number.isNaN(Number.parseFloat(G))&&isFinite(G)?`${X}px`:String(X));var G;const Y=0===parseFloat(String(S||0))?o.createElement("span",{onClick:_,className:m()(`${D}-zero-width-trigger`,`${D}-zero-width-trigger-${b?"right":"left"}`),style:E},i||o.createElement(c,null)):null,U="rtl"===T==!b,Q={expanded:U?o.createElement(u.A,null):o.createElement(s.A,null),collapsed:U?o.createElement(s.A,null):o.createElement(u.A,null)}[z?"collapsed":"expanded"],J=null!==i?Y||o.createElement("div",{className:`${D}-trigger`,onClick:_,style:{width:q}},i||Q):null,Z=Object.assign(Object.assign({},v),{flex:`0 0 ${q}`,maxWidth:q,minWidth:q,width:q}),ee=m()(D,`${D}-${d}`,{[`${D}-collapsed`]:!!z,[`${D}-has-trigger`]:h&&null!==i&&!Y,[`${D}-below`]:!!N,[`${D}-zero-width`]:0===parseFloat(q)},r,F,W),te=o.useMemo((()=>({siderCollapsed:z})),[z]);return L(o.createElement(w.Provider,{value:te},o.createElement("aside",Object.assign({className:ee},V,{style:Z,ref:t}),o.createElement("div",{className:`${D}-children`},l),h||N&&Y?J:null)))}));var E=S},8557:function(e,t,n){n.d(t,{f:function(){return a}});var o=n(6540),r=n(6956);function i(){}const l=o.createContext({add:i,remove:i});function a(e){const t=o.useContext(l),n=o.useRef(null);return(0,r.A)((o=>{if(o){const r=e?o.querySelector(e):o;t.add(r),n.current=r}else t.remove(n.current)}))}},8810:function(e,t,n){n.d(t,{cG:function(){return Le},q7:function(){return ye},te:function(){return Ke},Dr:function(){return ye},g8:function(){return Te},Ay:function(){return Qe},Wj:function(){return O}});var o=n(8168),r=n(4467),i=n(9379),l=n(436),a=n(5544),c=n(3986),s=n(6942),u=n.n(s),d=n(9591),m=n(2533),p=n(3210),f=n(8210),g=n(6540),v=n(961),h=g.createContext(null);function b(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function y(e){return b(g.useContext(h),e)}var $=n(8104),C=["children","locked"],A=g.createContext(null);function w(e){var t=e.children,n=e.locked,o=(0,c.A)(e,C),r=g.useContext(A),l=(0,$.A)((function(){return e=r,t=o,n=(0,i.A)({},e),Object.keys(t).forEach((function(e){var o=t[e];void 0!==o&&(n[e]=o)})),n;var e,t,n}),[r,o],(function(e,t){return!(n||e[0]===t[0]&&(0,p.A)(e[1],t[1],!0))}));return g.createElement(A.Provider,{value:l},t)}var x=[],S=g.createContext(null);function E(){return g.useContext(S)}var I=g.createContext(x);function O(e){var t=g.useContext(I);return g.useMemo((function(){return void 0!==e?[].concat((0,l.A)(t),[e]):t}),[t,e])}var M=g.createContext(null),k=g.createContext({}),R=n(2467);function z(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,R.A)(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||"a"===n&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),i=Number(r),l=null;return r&&!Number.isNaN(i)?l=i:o&&null===l&&(l=0),o&&e.disabled&&(l=null),null!==l&&(l>=0||t&&l<0)}return!1}function B(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(0,l.A)(e.querySelectorAll("*")).filter((function(e){return z(e,t)}));return z(e,t)&&n.unshift(e),n}var N=n(6928),j=n(5371),P=N.A.LEFT,H=N.A.RIGHT,T=N.A.UP,D=N.A.DOWN,L=N.A.ENTER,F=N.A.ESC,W=N.A.HOME,K=N.A.END,_=[T,D,P,H];function V(e,t){return B(e,!0).filter((function(e){return t.has(e)}))}function X(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var r=V(e,t),i=r.length,l=r.findIndex((function(e){return n===e}));return o<0?-1===l?l=i-1:l-=1:o>0&&(l+=1),r[l=(l+i)%i]}var q=function(e,t){var n=new Set,o=new Map,r=new Map;return e.forEach((function(e){var i=document.querySelector("[data-menu-id='".concat(b(t,e),"']"));i&&(n.add(i),r.set(i,e),o.set(e,i))})),{elements:n,key2element:o,element2key:r}};function G(e,t,n,o,i,l,a,c,s,u){var d=g.useRef(),m=g.useRef();m.current=t;var p=function(){j.A.cancel(d.current)};return g.useEffect((function(){return function(){p()}}),[]),function(f){var g=f.which;if([].concat(_,[L,F,W,K]).includes(g)){var v=l(),h=q(v,o),b=h,y=b.elements,$=b.key2element,C=b.element2key,A=function(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}($.get(t),y),w=C.get(A),x=function(e,t,n,o){var i,l="prev",a="next",c="children",s="parent";if("inline"===e&&o===L)return{inlineTrigger:!0};var u=(0,r.A)((0,r.A)({},T,l),D,a),d=(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},P,n?a:l),H,n?l:a),D,c),L,c),m=(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({},T,l),D,a),L,c),F,s),P,n?c:s),H,n?s:c);switch(null===(i={inline:u,horizontal:d,vertical:m,inlineSub:u,horizontalSub:m,verticalSub:m}["".concat(e).concat(t?"":"Sub")])||void 0===i?void 0:i[o]){case l:return{offset:-1,sibling:!0};case a:return{offset:1,sibling:!0};case s:return{offset:-1,sibling:!1};case c:return{offset:1,sibling:!1};default:return null}}(e,1===a(w,!0).length,n,g);if(!x&&g!==W&&g!==K)return;(_.includes(g)||[W,K].includes(g))&&f.preventDefault();var S=function(e){if(e){var t=e,n=e.querySelector("a");null!=n&&n.getAttribute("href")&&(t=n);var o=C.get(e);c(o),p(),d.current=(0,j.A)((function(){m.current===o&&t.focus()}))}};if([W,K].includes(g)||x.sibling||!A){var E,I,O=V(E=A&&"inline"!==e?function(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}(A):i.current,y);I=g===W?O[0]:g===K?O[O.length-1]:X(E,y,A,x.offset),S(I)}else if(x.inlineTrigger)s(w);else if(x.offset>0)s(w,!0),p(),d.current=(0,j.A)((function(){h=q(v,o);var e=A.getAttribute("aria-controls"),t=X(document.getElementById(e),h.elements);S(t)}),5);else if(x.offset<0){var M=a(w,!0),k=M[M.length-2],R=$.get(k);s(k,!1),S(R)}}null==u||u(f)}}var Y="__RC_UTIL_PATH_SPLIT__",U=function(e){return e.join(Y)},Q="rc-menu-more";function J(){var e=g.useState({}),t=(0,a.A)(e,2)[1],n=(0,g.useRef)(new Map),o=(0,g.useRef)(new Map),r=g.useState([]),i=(0,a.A)(r,2),c=i[0],s=i[1],u=(0,g.useRef)(0),d=(0,g.useRef)(!1),m=(0,g.useCallback)((function(e,r){var i=U(r);o.current.set(i,e),n.current.set(e,i),u.current+=1;var l,a=u.current;l=function(){a===u.current&&(d.current||t({}))},Promise.resolve().then(l)}),[]),p=(0,g.useCallback)((function(e,t){var r=U(t);o.current.delete(r),n.current.delete(e)}),[]),f=(0,g.useCallback)((function(e){s(e)}),[]),v=(0,g.useCallback)((function(e,t){var o=n.current.get(e)||"",r=o.split(Y);return t&&c.includes(r[0])&&r.unshift(Q),r}),[c]),h=(0,g.useCallback)((function(e,t){return e.filter((function(e){return void 0!==e})).some((function(e){return v(e,!0).includes(t)}))}),[v]),b=(0,g.useCallback)((function(e){var t="".concat(n.current.get(e)).concat(Y),r=new Set;return(0,l.A)(o.current.keys()).forEach((function(e){e.startsWith(t)&&r.add(o.current.get(e))})),r}),[]);return g.useEffect((function(){return function(){d.current=!0}}),[]),{registerPath:m,unregisterPath:p,refreshOverflowKeys:f,isSubPathKey:h,getKeyPath:v,getKeys:function(){var e=(0,l.A)(n.current.keys());return c.length&&e.push(Q),e},getSubPathKeys:b}}function Z(e){var t=g.useRef(e);t.current=e;var n=g.useCallback((function(){for(var e,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(o))}),[]);return e?n:void 0}var ee=Math.random().toFixed(5).toString().slice(2),te=0;var ne=n(3029),oe=n(2901),re=n(5501),ie=n(9426),le=n(9853),ae=n(8719);function ce(e,t,n,o){var r=g.useContext(A),i=r.activeKey,l=r.onActive,a=r.onInactive,c={active:i===e};return t||(c.onMouseEnter=function(t){null==n||n({key:e,domEvent:t}),l(e)},c.onMouseLeave=function(t){null==o||o({key:e,domEvent:t}),a(e)}),c}function se(e){var t=g.useContext(A),n=t.mode,o=t.rtl,r=t.inlineIndent;if("inline"!==n)return null;return o?{paddingRight:e*r}:{paddingLeft:e*r}}function ue(e){var t,n=e.icon,o=e.props,r=e.children;return null===n||!1===n?null:("function"==typeof n?t=g.createElement(n,(0,i.A)({},o)):"boolean"!=typeof n&&(t=n),t||r||null)}var de=["item"];function me(e){var t=e.item,n=(0,c.A)(e,de);return Object.defineProperty(n,"item",{get:function(){return(0,f.Ay)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var pe=["title","attribute","elementRef"],fe=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],ge=["active"],ve=function(e){(0,re.A)(n,e);var t=(0,ie.A)(n);function n(){return(0,ne.A)(this,n),t.apply(this,arguments)}return(0,oe.A)(n,[{key:"render",value:function(){var e=this.props,t=e.title,n=e.attribute,r=e.elementRef,i=(0,c.A)(e,pe),l=(0,le.A)(i,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,f.Ay)(!n,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),g.createElement(d.A.Item,(0,o.A)({},n,{title:"string"==typeof t?t:void 0},l,{ref:r}))}}]),n}(g.Component),he=g.forwardRef((function(e,t){var n=e.style,a=e.className,s=e.eventKey,d=(e.warnKey,e.disabled),m=e.itemIcon,p=e.children,f=e.role,v=e.onMouseEnter,h=e.onMouseLeave,b=e.onClick,$=e.onKeyDown,C=e.onFocus,w=(0,c.A)(e,fe),x=y(s),S=g.useContext(A),E=S.prefixCls,I=S.onItemClick,M=S.disabled,R=S.overflowDisabled,z=S.itemIcon,B=S.selectedKeys,j=S.onActive,P=g.useContext(k)._internalRenderMenuItem,H="".concat(E,"-item"),T=g.useRef(),D=g.useRef(),L=M||d,F=(0,ae.xK)(t,D),W=O(s);var K=function(e){return{key:s,keyPath:(0,l.A)(W).reverse(),item:T.current,domEvent:e}},_=m||z,V=ce(s,L,v,h),X=V.active,q=(0,c.A)(V,ge),G=B.includes(s),Y=se(W.length),U={};"option"===e.role&&(U["aria-selected"]=G);var Q=g.createElement(ve,(0,o.A)({ref:T,elementRef:F,role:null===f?"none":f||"menuitem",tabIndex:d?null:-1,"data-menu-id":R&&x?null:x},(0,le.A)(w,["extra"]),q,U,{component:"li","aria-disabled":d,style:(0,i.A)((0,i.A)({},Y),n),className:u()(H,(0,r.A)((0,r.A)((0,r.A)({},"".concat(H,"-active"),X),"".concat(H,"-selected"),G),"".concat(H,"-disabled"),L),a),onClick:function(e){if(!L){var t=K(e);null==b||b(me(t)),I(t)}},onKeyDown:function(e){if(null==$||$(e),e.which===N.A.ENTER){var t=K(e);null==b||b(me(t)),I(t)}},onFocus:function(e){j(s),null==C||C(e)}}),p,g.createElement(ue,{props:(0,i.A)((0,i.A)({},e),{},{isSelected:G}),icon:_}));return P&&(Q=P(Q,e,{selected:G})),Q}));function be(e,t){var n=e.eventKey,r=E(),i=O(n);return g.useEffect((function(){if(r)return r.registerPath(n,i),function(){r.unregisterPath(n,i)}}),[i]),r?null:g.createElement(he,(0,o.A)({},e,{ref:t}))}var ye=g.forwardRef(be),$e=["className","children"],Ce=function(e,t){var n=e.className,r=e.children,i=(0,c.A)(e,$e),l=g.useContext(A),a=l.prefixCls,s=l.mode,d=l.rtl;return g.createElement("ul",(0,o.A)({className:u()(a,d&&"".concat(a,"-rtl"),"".concat(a,"-sub"),"".concat(a,"-").concat("inline"===s?"inline":"vertical"),n),role:"menu"},i,{"data-menu-list":!0,ref:t}),r)},Ae=g.forwardRef(Ce);Ae.displayName="SubMenuList";var we=Ae,xe=n(2546);function Se(e,t){return(0,xe.A)(e).map((function(e,n){if(g.isValidElement(e)){var o,r,i=e.key,a=null!==(o=null===(r=e.props)||void 0===r?void 0:r.eventKey)&&void 0!==o?o:i;null==a&&(a="tmp_key-".concat([].concat((0,l.A)(t),[n]).join("-")));var c={key:a,eventKey:a};return g.cloneElement(e,c)}return e}))}var Ee=n(2427),Ie={adjustX:1,adjustY:1},Oe={topLeft:{points:["bl","tl"],overflow:Ie},topRight:{points:["br","tr"],overflow:Ie},bottomLeft:{points:["tl","bl"],overflow:Ie},bottomRight:{points:["tr","br"],overflow:Ie},leftTop:{points:["tr","tl"],overflow:Ie},leftBottom:{points:["br","bl"],overflow:Ie},rightTop:{points:["tl","tr"],overflow:Ie},rightBottom:{points:["bl","br"],overflow:Ie}},Me={topLeft:{points:["bl","tl"],overflow:Ie},topRight:{points:["br","tr"],overflow:Ie},bottomLeft:{points:["tl","bl"],overflow:Ie},bottomRight:{points:["tr","br"],overflow:Ie},rightTop:{points:["tr","tl"],overflow:Ie},rightBottom:{points:["br","bl"],overflow:Ie},leftTop:{points:["tl","tr"],overflow:Ie},leftBottom:{points:["bl","br"],overflow:Ie}};function ke(e,t,n){return t||(n?n[e]||n.other:void 0)}var Re={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ze(e){var t=e.prefixCls,n=e.visible,o=e.children,l=e.popup,c=e.popupStyle,s=e.popupClassName,d=e.popupOffset,m=e.disabled,p=e.mode,f=e.onVisibleChange,v=g.useContext(A),h=v.getPopupContainer,b=v.rtl,y=v.subMenuOpenDelay,$=v.subMenuCloseDelay,C=v.builtinPlacements,w=v.triggerSubMenuAction,x=v.forceSubMenuRender,S=v.rootClassName,E=v.motion,I=v.defaultMotions,O=g.useState(!1),M=(0,a.A)(O,2),k=M[0],R=M[1],z=b?(0,i.A)((0,i.A)({},Me),C):(0,i.A)((0,i.A)({},Oe),C),B=Re[p],N=ke(p,E,I),P=g.useRef(N);"inline"!==p&&(P.current=N);var H=(0,i.A)((0,i.A)({},P.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),T=g.useRef();return g.useEffect((function(){return T.current=(0,j.A)((function(){R(n)})),function(){j.A.cancel(T.current)}}),[n]),g.createElement(Ee.A,{prefixCls:t,popupClassName:u()("".concat(t,"-popup"),(0,r.A)({},"".concat(t,"-rtl"),b),s,S),stretch:"horizontal"===p?"minWidth":null,getPopupContainer:h,builtinPlacements:z,popupPlacement:B,popupVisible:k,popup:l,popupStyle:c,popupAlign:d&&{offset:d},action:m?[]:[w],mouseEnterDelay:y,mouseLeaveDelay:$,onPopupVisibleChange:f,forceRender:x,popupMotion:H,fresh:!0},o)}var Be=n(754);function Ne(e){var t=e.id,n=e.open,r=e.keyPath,l=e.children,c="inline",s=g.useContext(A),u=s.prefixCls,d=s.forceSubMenuRender,m=s.motion,p=s.defaultMotions,f=s.mode,v=g.useRef(!1);v.current=f===c;var h=g.useState(!v.current),b=(0,a.A)(h,2),y=b[0],$=b[1],C=!!v.current&&n;g.useEffect((function(){v.current&&$(!1)}),[f]);var x=(0,i.A)({},ke(c,m,p));r.length>1&&(x.motionAppear=!1);var S=x.onVisibleChanged;return x.onVisibleChanged=function(e){return v.current||e||$(!0),null==S?void 0:S(e)},y?null:g.createElement(w,{mode:c,locked:!v.current},g.createElement(Be.Ay,(0,o.A)({visible:C},x,{forceRender:d,removeOnLeave:!1,leavedClassName:"".concat(u,"-hidden")}),(function(e){var n=e.className,o=e.style;return g.createElement(we,{id:t,className:n,style:o},l)})))}var je=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Pe=["active"],He=g.forwardRef((function(e,t){var n=e.style,l=e.className,s=e.title,m=e.eventKey,p=(e.warnKey,e.disabled),f=e.internalPopupClose,v=e.children,h=e.itemIcon,b=e.expandIcon,$=e.popupClassName,C=e.popupOffset,x=e.popupStyle,S=e.onClick,E=e.onMouseEnter,I=e.onMouseLeave,R=e.onTitleClick,z=e.onTitleMouseEnter,B=e.onTitleMouseLeave,N=(0,c.A)(e,je),j=y(m),P=g.useContext(A),H=P.prefixCls,T=P.mode,D=P.openKeys,L=P.disabled,F=P.overflowDisabled,W=P.activeKey,K=P.selectedKeys,_=P.itemIcon,V=P.expandIcon,X=P.onItemClick,q=P.onOpenChange,G=P.onActive,Y=g.useContext(k)._internalRenderSubMenuItem,U=g.useContext(M).isSubPathKey,Q=O(),J="".concat(H,"-submenu"),ee=L||p,te=g.useRef(),ne=g.useRef();var oe=null!=h?h:_,re=null!=b?b:V,ie=D.includes(m),le=!F&&ie,ae=U(K,m),de=ce(m,ee,z,B),pe=de.active,fe=(0,c.A)(de,Pe),ge=g.useState(!1),ve=(0,a.A)(ge,2),he=ve[0],be=ve[1],ye=function(e){ee||be(e)},$e=g.useMemo((function(){return pe||"inline"!==T&&(he||U([W],m))}),[T,pe,W,he,m,U]),Ce=se(Q.length),Ae=Z((function(e){null==S||S(me(e)),X(e)})),xe=j&&"".concat(j,"-popup"),Se=g.useMemo((function(){return g.createElement(ue,{icon:"horizontal"!==T?re:void 0,props:(0,i.A)((0,i.A)({},e),{},{isOpen:le,isSubMenu:!0})},g.createElement("i",{className:"".concat(J,"-arrow")}))}),[T,re,e,le,J]),Ee=g.createElement("div",(0,o.A)({role:"menuitem",style:Ce,className:"".concat(J,"-title"),tabIndex:ee?null:-1,ref:te,title:"string"==typeof s?s:null,"data-menu-id":F&&j?null:j,"aria-expanded":le,"aria-haspopup":!0,"aria-controls":xe,"aria-disabled":ee,onClick:function(e){ee||(null==R||R({key:m,domEvent:e}),"inline"===T&&q(m,!ie))},onFocus:function(){G(m)}},fe),s,Se),Ie=g.useRef(T);if("inline"!==T&&Q.length>1?Ie.current="vertical":Ie.current=T,!F){var Oe=Ie.current;Ee=g.createElement(ze,{mode:Oe,prefixCls:J,visible:!f&&le&&"inline"!==T,popupClassName:$,popupOffset:C,popupStyle:x,popup:g.createElement(w,{mode:"horizontal"===Oe?"vertical":Oe},g.createElement(we,{id:xe,ref:ne},v)),disabled:ee,onVisibleChange:function(e){"inline"!==T&&q(m,e)}},Ee)}var Me=g.createElement(d.A.Item,(0,o.A)({ref:t,role:"none"},N,{component:"li",style:n,className:u()(J,"".concat(J,"-").concat(T),l,(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},"".concat(J,"-open"),le),"".concat(J,"-active"),$e),"".concat(J,"-selected"),ae),"".concat(J,"-disabled"),ee)),onMouseEnter:function(e){ye(!0),null==E||E({key:m,domEvent:e})},onMouseLeave:function(e){ye(!1),null==I||I({key:m,domEvent:e})}}),Ee,!F&&g.createElement(Ne,{id:xe,open:le,keyPath:Q},v));return Y&&(Me=Y(Me,e,{selected:ae,active:$e,open:le,disabled:ee})),g.createElement(w,{onItemClick:Ae,mode:"horizontal"===T?"vertical":T,itemIcon:oe,expandIcon:re},Me)}));var Te=g.forwardRef((function(e,t){var n,r=e.eventKey,i=e.children,l=O(r),a=Se(i,l),c=E();return g.useEffect((function(){if(c)return c.registerPath(r,l),function(){c.unregisterPath(r,l)}}),[l]),n=c?a:g.createElement(He,(0,o.A)({ref:t},e),a),g.createElement(I.Provider,{value:l},n)})),De=n(2284);function Le(e){var t=e.className,n=e.style,o=g.useContext(A).prefixCls;return E()?null:g.createElement("li",{role:"separator",className:u()("".concat(o,"-item-divider"),t),style:n})}var Fe=["className","title","eventKey","children"],We=g.forwardRef((function(e,t){var n=e.className,r=e.title,i=(e.eventKey,e.children),l=(0,c.A)(e,Fe),a=g.useContext(A).prefixCls,s="".concat(a,"-item-group");return g.createElement("li",(0,o.A)({ref:t,role:"presentation"},l,{onClick:function(e){return e.stopPropagation()},className:u()(s,n)}),g.createElement("div",{role:"presentation",className:"".concat(s,"-title"),title:"string"==typeof r?r:void 0},r),g.createElement("ul",{role:"group",className:"".concat(s,"-list")},i))}));var Ke=g.forwardRef((function(e,t){var n=e.eventKey,r=Se(e.children,O(n));return E()?r:g.createElement(We,(0,o.A)({ref:t},(0,le.A)(e,["warnKey"])),r)})),_e=["label","children","key","type","extra"];function Ve(e,t,n){var r=t.item,i=t.group,l=t.submenu,a=t.divider;return(e||[]).map((function(e,s){if(e&&"object"===(0,De.A)(e)){var u=e,d=u.label,m=u.children,p=u.key,f=u.type,v=u.extra,h=(0,c.A)(u,_e),b=null!=p?p:"tmp-".concat(s);return m||"group"===f?"group"===f?g.createElement(i,(0,o.A)({key:b},h,{title:d}),Ve(m,t,n)):g.createElement(l,(0,o.A)({key:b},h,{title:d}),Ve(m,t,n)):"divider"===f?g.createElement(a,(0,o.A)({key:b},h)):g.createElement(r,(0,o.A)({key:b},h,{extra:v}),d,(!!v||0===v)&&g.createElement("span",{className:"".concat(n,"-item-extra")},v))}return null})).filter((function(e){return e}))}function Xe(e,t,n,o,r){var l=e,a=(0,i.A)({divider:Le,item:ye,group:Ke,submenu:Te},o);return t&&(l=Ve(t,a,r)),Se(l,n)}var qe=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],Ge=[],Ye=g.forwardRef((function(e,t){var n,s=e,f=s.prefixCls,b=void 0===f?"rc-menu":f,y=s.rootClassName,$=s.style,C=s.className,A=s.tabIndex,x=void 0===A?0:A,E=s.items,I=s.children,O=s.direction,R=s.id,z=s.mode,B=void 0===z?"vertical":z,N=s.inlineCollapsed,j=s.disabled,P=s.disabledOverflow,H=s.subMenuOpenDelay,T=void 0===H?.1:H,D=s.subMenuCloseDelay,L=void 0===D?.1:D,F=s.forceSubMenuRender,W=s.defaultOpenKeys,K=s.openKeys,_=s.activeKey,X=s.defaultActiveFirst,Y=s.selectable,U=void 0===Y||Y,ne=s.multiple,oe=void 0!==ne&&ne,re=s.defaultSelectedKeys,ie=s.selectedKeys,le=s.onSelect,ae=s.onDeselect,ce=s.inlineIndent,se=void 0===ce?24:ce,ue=s.motion,de=s.defaultMotions,pe=s.triggerSubMenuAction,fe=void 0===pe?"hover":pe,ge=s.builtinPlacements,ve=s.itemIcon,he=s.expandIcon,be=s.overflowedIndicator,$e=void 0===be?"...":be,Ce=s.overflowedIndicatorPopupClassName,Ae=s.getPopupContainer,we=s.onClick,xe=s.onOpenChange,Se=s.onKeyDown,Ee=(s.openAnimation,s.openTransitionName,s._internalRenderMenuItem),Ie=s._internalRenderSubMenuItem,Oe=s._internalComponents,Me=(0,c.A)(s,qe),ke=g.useMemo((function(){return[Xe(I,E,Ge,Oe,b),Xe(I,E,Ge,{},b)]}),[I,E,Oe]),Re=(0,a.A)(ke,2),ze=Re[0],Be=Re[1],Ne=g.useState(!1),je=(0,a.A)(Ne,2),Pe=je[0],He=je[1],De=g.useRef(),Le=function(e){var t=(0,m.A)(e,{value:e}),n=(0,a.A)(t,2),o=n[0],r=n[1];return g.useEffect((function(){te+=1;var e="".concat(ee,"-").concat(te);r("rc-menu-uuid-".concat(e))}),[]),o}(R),Fe="rtl"===O;var We=(0,m.A)(W,{value:K,postState:function(e){return e||Ge}}),Ke=(0,a.A)(We,2),_e=Ke[0],Ve=Ke[1],Ye=function(e){function t(){Ve(e),null==xe||xe(e)}arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(0,v.flushSync)(t):t()},Ue=g.useState(_e),Qe=(0,a.A)(Ue,2),Je=Qe[0],Ze=Qe[1],et=g.useRef(!1),tt=g.useMemo((function(){return"inline"!==B&&"vertical"!==B||!N?[B,!1]:["vertical",N]}),[B,N]),nt=(0,a.A)(tt,2),ot=nt[0],rt=nt[1],it="inline"===ot,lt=g.useState(ot),at=(0,a.A)(lt,2),ct=at[0],st=at[1],ut=g.useState(rt),dt=(0,a.A)(ut,2),mt=dt[0],pt=dt[1];g.useEffect((function(){st(ot),pt(rt),et.current&&(it?Ve(Je):Ye(Ge))}),[ot,rt]);var ft=g.useState(0),gt=(0,a.A)(ft,2),vt=gt[0],ht=gt[1],bt=vt>=ze.length-1||"horizontal"!==ct||P;g.useEffect((function(){it&&Ze(_e)}),[_e]),g.useEffect((function(){return et.current=!0,function(){et.current=!1}}),[]);var yt=J(),$t=yt.registerPath,Ct=yt.unregisterPath,At=yt.refreshOverflowKeys,wt=yt.isSubPathKey,xt=yt.getKeyPath,St=yt.getKeys,Et=yt.getSubPathKeys,It=g.useMemo((function(){return{registerPath:$t,unregisterPath:Ct}}),[$t,Ct]),Ot=g.useMemo((function(){return{isSubPathKey:wt}}),[wt]);g.useEffect((function(){At(bt?Ge:ze.slice(vt+1).map((function(e){return e.key})))}),[vt,bt]);var Mt=(0,m.A)(_||X&&(null===(n=ze[0])||void 0===n?void 0:n.key),{value:_}),kt=(0,a.A)(Mt,2),Rt=kt[0],zt=kt[1],Bt=Z((function(e){zt(e)})),Nt=Z((function(){zt(void 0)}));(0,g.useImperativeHandle)(t,(function(){return{list:De.current,focus:function(e){var t,n,o=St(),r=q(o,Le),i=r.elements,l=r.key2element,a=r.element2key,c=V(De.current,i),s=null!=Rt?Rt:c[0]?a.get(c[0]):null===(t=ze.find((function(e){return!e.props.disabled})))||void 0===t?void 0:t.key,u=l.get(s);s&&u&&(null==u||null===(n=u.focus)||void 0===n||n.call(u,e))}}}));var jt=(0,m.A)(re||[],{value:ie,postState:function(e){return Array.isArray(e)?e:null==e?Ge:[e]}}),Pt=(0,a.A)(jt,2),Ht=Pt[0],Tt=Pt[1],Dt=Z((function(e){null==we||we(me(e)),function(e){if(U){var t,n=e.key,o=Ht.includes(n);t=oe?o?Ht.filter((function(e){return e!==n})):[].concat((0,l.A)(Ht),[n]):[n],Tt(t);var r=(0,i.A)((0,i.A)({},e),{},{selectedKeys:t});o?null==ae||ae(r):null==le||le(r)}!oe&&_e.length&&"inline"!==ct&&Ye(Ge)}(e)})),Lt=Z((function(e,t){var n=_e.filter((function(t){return t!==e}));if(t)n.push(e);else if("inline"!==ct){var o=Et(e);n=n.filter((function(e){return!o.has(e)}))}(0,p.A)(_e,n,!0)||Ye(n,!0)})),Ft=G(ct,Rt,Fe,Le,De,St,xt,zt,(function(e,t){var n=null!=t?t:!_e.includes(e);Lt(e,n)}),Se);g.useEffect((function(){He(!0)}),[]);var Wt=g.useMemo((function(){return{_internalRenderMenuItem:Ee,_internalRenderSubMenuItem:Ie}}),[Ee,Ie]),Kt="horizontal"!==ct||P?ze:ze.map((function(e,t){return g.createElement(w,{key:e.key,overflowDisabled:t>vt},e)})),_t=g.createElement(d.A,(0,o.A)({id:R,ref:De,prefixCls:"".concat(b,"-overflow"),component:"ul",itemComponent:ye,className:u()(b,"".concat(b,"-root"),"".concat(b,"-").concat(ct),C,(0,r.A)((0,r.A)({},"".concat(b,"-inline-collapsed"),mt),"".concat(b,"-rtl"),Fe),y),dir:O,style:$,role:"menu",tabIndex:x,data:Kt,renderRawItem:function(e){return e},renderRawRest:function(e){var t=e.length,n=t?ze.slice(-t):null;return g.createElement(Te,{eventKey:Q,title:$e,disabled:bt,internalPopupClose:0===t,popupClassName:Ce},n)},maxCount:"horizontal"!==ct||P?d.A.INVALIDATE:d.A.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){ht(e)},onKeyDown:Ft},Me));return g.createElement(k.Provider,{value:Wt},g.createElement(h.Provider,{value:Le},g.createElement(w,{prefixCls:b,rootClassName:y,mode:ct,openKeys:_e,rtl:Fe,disabled:j,motion:Pe?ue:null,defaultMotions:Pe?de:null,activeKey:Rt,onActive:Bt,onInactive:Nt,selectedKeys:Ht,inlineIndent:se,subMenuOpenDelay:T,subMenuCloseDelay:L,forceSubMenuRender:F,builtinPlacements:ge,triggerSubMenuAction:fe,getPopupContainer:Ae,itemIcon:ve,expandIcon:he,onItemClick:Dt,onOpenChange:Lt},g.createElement(M.Provider,{value:Ot},_t),g.createElement("div",{style:{display:"none"},"aria-hidden":!0},g.createElement(S.Provider,{value:It},Be)))))})),Ue=Ye;Ue.Item=ye,Ue.SubMenu=Te,Ue.ItemGroup=Ke,Ue.Divider=Le;var Qe=Ue},9591:function(e,t,n){n.d(t,{A:function(){return j}});var o=n(8168),r=n(9379),i=n(5544),l=n(3986),a=n(6540),c=n(6942),s=n.n(c),u=n(8462),d=n(981),m=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],p=void 0;function f(e,t){var n=e.prefixCls,i=e.invalidate,c=e.item,d=e.renderItem,f=e.responsive,g=e.responsiveDisabled,v=e.registerSize,h=e.itemKey,b=e.className,y=e.style,$=e.children,C=e.display,A=e.order,w=e.component,x=void 0===w?"div":w,S=(0,l.A)(e,m),E=f&&!C;function I(e){v(h,e)}a.useEffect((function(){return function(){I(null)}}),[]);var O,M=d&&c!==p?d(c,{index:A}):$;i||(O={opacity:E?0:1,height:E?0:p,overflowY:E?"hidden":p,order:f?A:p,pointerEvents:E?"none":p,position:E?"absolute":p});var k={};E&&(k["aria-hidden"]=!0);var R=a.createElement(x,(0,o.A)({className:s()(!i&&n,b),style:(0,r.A)((0,r.A)({},O),y)},k,S,{ref:t}),M);return f&&(R=a.createElement(u.A,{onResize:function(e){I(e.offsetWidth)},disabled:g},R)),R}var g=a.forwardRef(f);g.displayName="Item";var v=g,h=n(6956),b=n(961),y=n(5371);function $(){var e=a.useRef(null);return function(t){e.current||(e.current=[],function(e){if("undefined"==typeof MessageChannel)(0,y.A)(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}((function(){(0,b.unstable_batchedUpdates)((function(){e.current.forEach((function(e){e()})),e.current=null}))}))),e.current.push(t)}}function C(e,t){var n=a.useState(t),o=(0,i.A)(n,2),r=o[0],l=o[1];return[r,(0,h.A)((function(t){e((function(){l(t)}))}))]}var A=a.createContext(null),w=["component"],x=["className"],S=["className"],E=function(e,t){var n=a.useContext(A);if(!n){var r=e.component,i=void 0===r?"div":r,c=(0,l.A)(e,w);return a.createElement(i,(0,o.A)({},c,{ref:t}))}var u=n.className,d=(0,l.A)(n,x),m=e.className,p=(0,l.A)(e,S);return a.createElement(A.Provider,{value:null},a.createElement(v,(0,o.A)({ref:t,className:s()(u,m)},d,p)))},I=a.forwardRef(E);I.displayName="RawItem";var O=I,M=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],k="responsive",R="invalidate";function z(e){return"+ ".concat(e.length," ...")}function B(e,t){var n=e.prefixCls,c=void 0===n?"rc-overflow":n,m=e.data,p=void 0===m?[]:m,f=e.renderItem,g=e.renderRawItem,h=e.itemKey,b=e.itemWidth,y=void 0===b?10:b,w=e.ssr,x=e.style,S=e.className,E=e.maxCount,I=e.renderRest,O=e.renderRawRest,B=e.suffix,N=e.component,j=void 0===N?"div":N,P=e.itemComponent,H=e.onVisibleChange,T=(0,l.A)(e,M),D="full"===w,L=$(),F=C(L,null),W=(0,i.A)(F,2),K=W[0],_=W[1],V=K||0,X=C(L,new Map),q=(0,i.A)(X,2),G=q[0],Y=q[1],U=C(L,0),Q=(0,i.A)(U,2),J=Q[0],Z=Q[1],ee=C(L,0),te=(0,i.A)(ee,2),ne=te[0],oe=te[1],re=C(L,0),ie=(0,i.A)(re,2),le=ie[0],ae=ie[1],ce=(0,a.useState)(null),se=(0,i.A)(ce,2),ue=se[0],de=se[1],me=(0,a.useState)(null),pe=(0,i.A)(me,2),fe=pe[0],ge=pe[1],ve=a.useMemo((function(){return null===fe&&D?Number.MAX_SAFE_INTEGER:fe||0}),[fe,K]),he=(0,a.useState)(!1),be=(0,i.A)(he,2),ye=be[0],$e=be[1],Ce="".concat(c,"-item"),Ae=Math.max(J,ne),we=E===k,xe=p.length&&we,Se=E===R,Ee=xe||"number"==typeof E&&p.length>E,Ie=(0,a.useMemo)((function(){var e=p;return xe?e=null===K&&D?p:p.slice(0,Math.min(p.length,V/y)):"number"==typeof E&&(e=p.slice(0,E)),e}),[p,y,K,E,xe]),Oe=(0,a.useMemo)((function(){return xe?p.slice(ve+1):p.slice(Ie.length)}),[p,Ie,xe,ve]),Me=(0,a.useCallback)((function(e,t){var n;return"function"==typeof h?h(e):null!==(n=h&&(null==e?void 0:e[h]))&&void 0!==n?n:t}),[h]),ke=(0,a.useCallback)(f||function(e){return e},[f]);function Re(e,t,n){(fe!==e||void 0!==t&&t!==ue)&&(ge(e),n||($e(e<p.length-1),null==H||H(e)),void 0!==t&&de(t))}function ze(e,t){Y((function(n){var o=new Map(n);return null===t?o.delete(e):o.set(e,t),o}))}function Be(e){return G.get(Me(Ie[e],e))}(0,d.A)((function(){if(V&&"number"==typeof Ae&&Ie){var e=le,t=Ie.length,n=t-1;if(!t)return void Re(0,null);for(var o=0;o<t;o+=1){var r=Be(o);if(D&&(r=r||0),void 0===r){Re(o-1,void 0,!0);break}if(e+=r,0===n&&e<=V||o===n-1&&e+Be(n)<=V){Re(n,null);break}if(e+Ae>V){Re(o-1,e-r-le+ne);break}}B&&Be(0)+le>V&&de(null)}}),[V,G,ne,le,Me,Ie]);var Ne=ye&&!!Oe.length,je={};null!==ue&&xe&&(je={position:"absolute",left:ue,top:0});var Pe={prefixCls:Ce,responsive:xe,component:P,invalidate:Se},He=g?function(e,t){var n=Me(e,t);return a.createElement(A.Provider,{key:n,value:(0,r.A)((0,r.A)({},Pe),{},{order:t,item:e,itemKey:n,registerSize:ze,display:t<=ve})},g(e,t))}:function(e,t){var n=Me(e,t);return a.createElement(v,(0,o.A)({},Pe,{order:t,key:n,item:e,renderItem:ke,itemKey:n,registerSize:ze,display:t<=ve}))},Te={order:Ne?ve:Number.MAX_SAFE_INTEGER,className:"".concat(Ce,"-rest"),registerSize:function(e,t){oe(t),Z(ne)},display:Ne},De=I||z,Le=O?a.createElement(A.Provider,{value:(0,r.A)((0,r.A)({},Pe),Te)},O(Oe)):a.createElement(v,(0,o.A)({},Pe,Te),"function"==typeof De?De(Oe):De),Fe=a.createElement(j,(0,o.A)({className:s()(!Se&&c,S),style:x,ref:t},T),Ie.map(He),Ee?Le:null,B&&a.createElement(v,(0,o.A)({},Pe,{responsive:we,responsiveDisabled:!xe,order:ve,className:"".concat(Ce,"-suffix"),registerSize:function(e,t){ae(t)},display:!0,style:je}),B));return we?a.createElement(u.A,{onResize:function(e,t){_(t.clientWidth)},disabled:!xe},Fe):Fe}var N=a.forwardRef(B);N.displayName="Overflow",N.Item=O,N.RESPONSIVE=k,N.INVALIDATE=R;var j=N},9957:function(e,t,n){n.d(t,{A:function(){return U}});var o=n(6540),r=n(6942),i=n.n(r),l=n(2279),a=n(4241),c=n(1594);var s=e=>{const{getPrefixCls:t,direction:n}=(0,o.useContext)(l.QO),{prefixCls:r,className:s}=e,u=t("input-group",r),d=t("input"),[m,p,f]=(0,c.Ay)(d),g=i()(u,f,{[`${u}-lg`]:"large"===e.size,[`${u}-sm`]:"small"===e.size,[`${u}-compact`]:e.compact,[`${u}-rtl`]:"rtl"===n},p,s),v=(0,o.useContext)(a.$W),h=(0,o.useMemo)((()=>Object.assign(Object.assign({},v),{isFormItemInput:!1})),[v]);return m(o.createElement("span",{className:g,style:e.style,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onFocus:e.onFocus,onBlur:e.onBlur},o.createElement(a.$W.Provider,{value:h},e.children)))},u=n(8017),d=n(436),m=n(6956),p=n(2065),f=n(8182),g=n(829),v=n(7358),h=n(4277),b=n(6716);const y=e=>{const{componentCls:t,paddingXS:n}=e;return{[t]:{display:"inline-flex",alignItems:"center",flexWrap:"nowrap",columnGap:n,"&-rtl":{direction:"rtl"},[`${t}-input`]:{textAlign:"center",paddingInline:e.paddingXXS},[`&${t}-sm ${t}-input`]:{paddingInline:e.calc(e.paddingXXS).div(2).equal()},[`&${t}-lg ${t}-input`]:{paddingInline:e.paddingXS}}}};var $=(0,v.OF)(["Input","OTP"],(e=>{const t=(0,h.oX)(e,(0,b.C)(e));return[y(t)]}),b.b),C=n(5371),A=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var w=o.forwardRef(((e,t)=>{const{value:n,onChange:r,onActiveChange:i,index:l,mask:a}=e,c=A(e,["value","onChange","onActiveChange","index","mask"]),s=n&&"string"==typeof a?a:n,d=o.useRef(null);o.useImperativeHandle(t,(()=>d.current));const m=()=>{(0,C.A)((()=>{var e;const t=null===(e=d.current)||void 0===e?void 0:e.input;document.activeElement===t&&t&&t.select()}))};return o.createElement(u.A,Object.assign({type:!0===a?"password":"text"},c,{ref:d,value:s,onInput:e=>{r(l,e.target.value)},onFocus:m,onKeyDown:e=>{const{key:t,ctrlKey:n,metaKey:o}=e;"ArrowLeft"===t?i(l-1):"ArrowRight"===t?i(l+1):"z"===t&&(n||o)&&e.preventDefault(),m()},onKeyUp:e=>{"Backspace"!==e.key||n||i(l-1),m()},onMouseDown:m,onMouseUp:m}))})),x=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function S(e){return(e||"").split("")}const E=e=>{const{index:t,prefixCls:n,separator:r}=e,i="function"==typeof r?r(t):r;return i?o.createElement("span",{className:`${n}-separator`},i):null};var I=o.forwardRef(((e,t)=>{const{prefixCls:n,length:r=6,size:c,defaultValue:s,value:u,onChange:v,formatter:h,separator:b,variant:y,disabled:C,status:A,autoFocus:I,mask:O,type:M,onInput:k,inputMode:R}=e,z=x(e,["prefixCls","length","size","defaultValue","value","onChange","formatter","separator","variant","disabled","status","autoFocus","mask","type","onInput","inputMode"]);const{getPrefixCls:B,direction:N}=o.useContext(l.QO),j=B("otp",n),P=(0,p.A)(z,{aria:!0,data:!0,attr:!0}),[H,T,D]=$(j),L=(0,g.A)((e=>null!=c?c:e)),F=o.useContext(a.$W),W=(0,f.v)(F.status,A),K=o.useMemo((()=>Object.assign(Object.assign({},F),{status:W,hasFeedback:!1,feedbackIcon:null})),[F,W]),_=o.useRef(null),V=o.useRef({});o.useImperativeHandle(t,(()=>({focus:()=>{var e;null===(e=V.current[0])||void 0===e||e.focus()},blur:()=>{var e;for(let t=0;t<r;t+=1)null===(e=V.current[t])||void 0===e||e.blur()},nativeElement:_.current})));const X=e=>h?h(e):e,[q,G]=o.useState((()=>S(X(s||""))));o.useEffect((()=>{void 0!==u&&G(S(u))}),[u]);const Y=(0,m.A)((e=>{G(e),k&&k(e),v&&e.length===r&&e.every((e=>e))&&e.some(((e,t)=>q[t]!==e))&&v(e.join(""))})),U=(0,m.A)(((e,t)=>{let n=(0,d.A)(q);for(let r=0;r<e;r+=1)n[r]||(n[r]="");t.length<=1?n[e]=t:n=n.slice(0,e).concat(S(t)),n=n.slice(0,r);for(let r=n.length-1;r>=0&&!n[r];r-=1)n.pop();const o=X(n.map((e=>e||" ")).join(""));return n=S(o).map(((e,t)=>" "!==e||n[t]?e:n[t])),n})),Q=(e,t)=>{var n;const o=U(e,t),i=Math.min(e+t.length,r-1);i!==e&&void 0!==o[e]&&(null===(n=V.current[i])||void 0===n||n.focus()),Y(o)},J=e=>{var t;null===(t=V.current[e])||void 0===t||t.focus()},Z={variant:y,disabled:C,status:W,mask:O,type:M,inputMode:R};return H(o.createElement("div",Object.assign({},P,{ref:_,className:i()(j,{[`${j}-sm`]:"small"===L,[`${j}-lg`]:"large"===L,[`${j}-rtl`]:"rtl"===N},D,T)}),o.createElement(a.$W.Provider,{value:K},Array.from({length:r}).map(((e,t)=>{const n=`otp-${t}`,i=q[t]||"";return o.createElement(o.Fragment,{key:n},o.createElement(w,Object.assign({ref:e=>{V.current[t]=e},index:t,size:L,htmlSize:1,className:`${j}-input`,onChange:Q,value:i,onActiveChange:J,autoFocus:0===t&&I},Z)),t<r-1&&o.createElement(E,{separator:b,index:t,prefixCls:j}))})))))})),O=n(8168),M={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"},k=n(7064),R=function(e,t){return o.createElement(k.A,(0,O.A)({},e,{ref:t,icon:M}))};var z=o.forwardRef(R),B=n(234),N=n(9853),j=n(8719),P=n(8119),H=n(5254),T=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const D=e=>e?o.createElement(B.A,null):o.createElement(z,null),L={click:"onClick",hover:"onMouseOver"};var F=o.forwardRef(((e,t)=>{const{disabled:n,action:r="click",visibilityToggle:a=!0,iconRender:c=D}=e,s=o.useContext(P.A),d=null!=n?n:s,m="object"==typeof a&&void 0!==a.visible,[p,f]=(0,o.useState)((()=>!!m&&a.visible)),g=(0,o.useRef)(null);o.useEffect((()=>{m&&f(a.visible)}),[m,a]);const v=(0,H.A)(g),h=()=>{var e;if(d)return;p&&v();const t=!p;f(t),"object"==typeof a&&(null===(e=a.onVisibleChange)||void 0===e||e.call(a,t))},{className:b,prefixCls:y,inputPrefixCls:$,size:C}=e,A=T(e,["className","prefixCls","inputPrefixCls","size"]),{getPrefixCls:w}=o.useContext(l.QO),x=w("input",$),S=w("input-password",y),E=a&&(e=>{const t=L[r]||"",n=c(p),i={[t]:h,className:`${e}-icon`,key:"passwordIcon",onMouseDown:e=>{e.preventDefault()},onMouseUp:e=>{e.preventDefault()}};return o.cloneElement(o.isValidElement(n)?n:o.createElement("span",null,n),i)})(S),I=i()(S,b,{[`${S}-${C}`]:!!C}),O=Object.assign(Object.assign({},(0,N.A)(A,["suffix","iconRender","visibilityToggle"])),{type:p?"text":"password",className:I,prefixCls:x,suffix:E});return C&&(O.size=C),o.createElement(u.A,Object.assign({ref:(0,j.K4)(t,g)},O))})),W=n(2877),K=n(682),_=n(2941),V=n(6327),X=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var q=o.forwardRef(((e,t)=>{const{prefixCls:n,inputPrefixCls:r,className:a,size:c,suffix:s,enterButton:d=!1,addonAfter:m,loading:p,disabled:f,onSearch:v,onChange:h,onCompositionStart:b,onCompositionEnd:y}=e,$=X(e,["prefixCls","inputPrefixCls","className","size","suffix","enterButton","addonAfter","loading","disabled","onSearch","onChange","onCompositionStart","onCompositionEnd"]),{getPrefixCls:C,direction:A}=o.useContext(l.QO),w=o.useRef(!1),x=C("input-search",n),S=C("input",r),{compactSize:E}=(0,V.RQ)(x,A),I=(0,g.A)((e=>{var t;return null!==(t=null!=c?c:E)&&void 0!==t?t:e})),O=o.useRef(null),M=e=>{var t;document.activeElement===(null===(t=O.current)||void 0===t?void 0:t.input)&&e.preventDefault()},k=e=>{var t,n;v&&v(null===(n=null===(t=O.current)||void 0===t?void 0:t.input)||void 0===n?void 0:n.value,e,{source:"input"})},R="boolean"==typeof d?o.createElement(W.A,null):null,z=`${x}-button`;let B;const N=d||{},P=N.type&&!0===N.type.__ANT_BUTTON;B=P||"button"===N.type?(0,K.Ob)(N,Object.assign({onMouseDown:M,onClick:e=>{var t,n;null===(n=null===(t=null==N?void 0:N.props)||void 0===t?void 0:t.onClick)||void 0===n||n.call(t,e),k(e)},key:"enterButton"},P?{className:z,size:I}:{})):o.createElement(_.Ay,{className:z,type:d?"primary":void 0,size:I,disabled:f,key:"enterButton",onMouseDown:M,onClick:k,loading:p,icon:R},d),m&&(B=[B,(0,K.Ob)(m,{key:"addonAfter"})]);const H=i()(x,{[`${x}-rtl`]:"rtl"===A,[`${x}-${I}`]:!!I,[`${x}-with-button`]:!!d},a),T=Object.assign(Object.assign({},$),{className:H,prefixCls:S,type:"search"});return o.createElement(u.A,Object.assign({ref:(0,j.K4)(O,t),onPressEnter:e=>{w.current||p||k(e)}},T,{size:I,onCompositionStart:e=>{w.current=!0,null==b||b(e)},onCompositionEnd:e=>{w.current=!1,null==y||y(e)},addonAfter:B,suffix:s,onChange:e=>{(null==e?void 0:e.target)&&"click"===e.type&&v&&v(e.target.value,e,{source:"clear"}),null==h||h(e)},disabled:f}))})),G=n(5144);const Y=u.A;Y.Group=s,Y.Search=q,Y.TextArea=G.A,Y.Password=F,Y.OTP=I;var U=Y}}]);
//# sourceMappingURL=ac9637fc46bc1f6d812d954ce915b60ead6677c4-11523dfd34cc8047a009.js.map