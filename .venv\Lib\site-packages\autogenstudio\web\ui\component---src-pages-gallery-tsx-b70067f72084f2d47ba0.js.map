{"version": 3, "file": "component---src-pages-gallery-tsx-b70067f72084f2d47ba0.js", "mappings": ";ms+DASA,MAAMA,GAAY,EAAAC,EAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEC,EAAG,qDAAsDC,IAAK,WACzE,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,WACjC,CAAC,OAAQ,CAAED,EAAG,sDAAuDC,IAAK,WAC1E,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,2BCJlC,MAAMC,GAAQ,EAAAH,EAAA,GAAiB,QAAS,CACtC,CAAC,SAAU,CAAEI,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMJ,IAAK,WAC/C,CAAC,OAAQ,CAAED,EAAG,kDAAmDC,IAAK,WACtE,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,2BCHjC,MAAMK,GAAU,EAAAP,EAAA,GAAiB,UAAW,CAC1C,CACE,OACA,CACEC,EAAG,2HACHC,IAAK,WAGT,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,WAChC,CAAC,OAAQ,CAAED,EAAG,8CAA+CC,IAAK,WAClE,CAAC,OAAQ,CAAED,EAAG,mBAAoBC,IAAK,2BCQlC,MAAMM,EAAgDC,IAUtD,IAVuD,OAC5DC,EAAM,UACNC,EAAS,eACTC,EAAc,SACdC,EAAQ,gBACRC,EAAe,gBACfC,EAAe,gBACfC,EAAe,cACfC,EAAa,UACbC,GAAY,GACbT,EAEC,OAAKC,EA8BHS,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,aAC3CD,EAAAA,cAAA,QAAMC,UAAU,wDACbT,EAAUU,SAGfF,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,iBACbJ,EAAAA,cAAA,UACEK,QAASX,EACTO,UAAU,gKAEVD,EAAAA,cAACM,EAAAA,EAAc,CAACC,YAAa,IAAKN,UAAU,eAMlDD,EAAAA,cAAA,OAAKC,UAAU,qBACbD,EAAAA,cAAA,OAAKC,UAAU,eACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,sBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,UACLR,UAAU,SACVS,KAAMV,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,YACtBI,QAAST,GACV,kBAQPI,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,QAAO,iBACrBF,GAAaC,EAAAA,cAACpB,EAAS,CAACqB,UAAU,gCAInCF,GAAkC,IAArBP,EAAUU,QACvBF,EAAAA,cAAA,OAAKC,UAAU,4EACbD,EAAAA,cAACY,EAAAA,EAAI,CAACX,UAAU,wCAAwC,sBAK5DD,EAAAA,cAAA,OAAKC,UAAU,+CACZT,EAAUqB,KAAKC,GACdd,EAAAA,cAAA,OAAKjB,IAAK+B,EAAQC,GAAId,UAAU,6BAC9BD,EAAAA,cAAA,OACEC,UAAW,+EACTR,aAAc,EAAdA,EAAgBsB,MAAOD,EAAQC,GAAK,YAAc,iBAGrDD,GAAWA,EAAQE,QAAUF,EAAQE,OAAOC,YAC3CjB,EAAAA,cAAA,OACEC,UAAW,8EACTR,aAAc,EAAdA,EAAgBsB,MAAOD,EAAQC,GAC3B,6BACA,sBAENV,QAASA,IAAMV,EAAgBmB,IAG/Bd,EAAAA,cAAA,OAAKC,UAAU,6CACbD,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAAA,QAAMC,UAAU,eAAea,EAAQE,OAAOE,OAE/CJ,EAAQE,OAAOG,KACdnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,kBACbJ,EAAAA,cAAChB,EAAK,CAACiB,UAAU,2CAIvBD,EAAAA,cAAA,OAAKC,UAAU,sFACZa,EAAQE,OAAOG,KACdnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,gBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLW,KAAK,QACLnB,UAAU,uBACVS,KAAMV,EAAAA,cAACpB,EAAS,CAACqB,UAAU,YAC3BI,QAAUgB,IACRA,EAAEC,kBACFxB,EAAcgB,EAAQC,GAAI,KAKlCf,EAAAA,cAACG,EAAAA,EAAO,CACNC,MACuB,IAArBZ,EAAUU,OACN,iCACA,kBAGNF,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLW,KAAK,QACLnB,UAAU,uBACVsB,QAAM,EACNC,SAA+B,IAArBhC,EAAUU,OACpBQ,KAAMV,EAAAA,cAACyB,EAAAA,EAAM,CAACxB,UAAU,yBACxBI,QAAUgB,IACRA,EAAEC,kBACFzB,EAAgBiB,EAAQC,GAAI,OAQtCf,EAAAA,cAAA,OAAKC,UAAU,uDACbD,EAAAA,cAAA,QAAMC,UAAU,yCAAwC,IACpDa,EAAQE,OAAOU,SAASC,SAE5B3B,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAACZ,EAAO,CAACa,UAAU,YACnBD,EAAAA,cAAA,YACG4B,OAAOC,OAAOf,EAAQE,OAAOC,YAAYa,QACxC,CAACC,EAAKC,IAAQD,EAAMC,EAAI9B,QACxB,GACC,IAAI,gBAOZY,EAAQmB,YACPjC,EAAAA,cAAA,OAAKC,UAAU,uDACbD,EAAAA,cAAA,aAAOkC,EAAAA,EAAAA,IAAsBpB,EAAQmB,oBApKnDjC,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAO,cAAcZ,EAAUU,WACtCF,EAAAA,cAAA,UACEK,QAASX,EACTO,UAAU,gKAEVD,EAAAA,cAACmC,EAAAA,EAAa,CAAC5B,YAAa,IAAKN,UAAU,eAKjDD,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,sBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLR,UAAU,iCACVI,QAAST,EACTc,KAAMV,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,gBA0J1B,EAIV,kCChNA,MAAMmC,GAAQ,EAAAvD,EAAA,GAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,UAAWC,IAAK,WAC9B,CAAC,OAAQ,CAAED,EAAG,wCAAyCC,IAAK,WAC5D,CAAC,OAAQ,CAAED,EAAG,qCAAsCC,IAAK,qHCuB3D,MAAMsD,EAMF/C,IAAA,IAAC,KAAEgD,EAAI,OAAEC,EAAM,YAAEC,EAAW,SAAEC,EAAQ,MAAEC,EAAK,YAAEC,GAAarD,EAAA,OAC9DU,EAAAA,cAAA,OACEC,UAAU,mEACVI,QAASA,IAAMkC,EAAOD,EAAMI,IAE5B1C,EAAAA,cAAA,OAAKC,UAAU,wEACbD,EAAAA,cAAA,OAAKC,UAAU,0CACZqC,EAAKM,UAER5C,EAAAA,cAAA,OAAKC,UAAU,cACZ0C,GACC3C,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,SACNK,KAAK,OACLR,UAAU,oIACVS,KAAMV,EAAAA,cAACoC,EAAK,CAACnC,UAAU,gBACvBI,QAAUgB,IACRA,EAAEC,kBACFmB,EAASH,EAAMI,EAAM,IAI3B1C,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,YACNK,KAAK,OACLR,UAAU,oGACVS,KAAMV,EAAAA,cAAC6C,EAAAA,EAAI,CAAC5C,UAAU,gBACtBI,QAAUgB,IACRA,EAAEC,kBACFkB,EAAYF,EAAMI,EAAM,IAG5B1C,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,OACNK,KAAK,OACLR,UAAU,oGACVS,KAAMV,EAAAA,cAAC8C,EAAAA,EAAI,CAAC7C,UAAU,gBACtBI,QAAUgB,IACRA,EAAEC,kBACFiB,EAAOD,EAAMI,EAAM,MAK3B1C,EAAAA,cAAA,OAAKC,UAAU,iBACbD,EAAAA,cAAA,OAAKC,UAAU,8BAA8BqC,EAAKS,OAClD/C,EAAAA,cAAA,OAAKC,UAAU,yDACbD,EAAAA,cAACgD,EAAAA,GAAe,CACdC,QAASX,EAAKY,aAAe,GAC7BC,gBAAgB,EAChBC,cAAe,OAIjB,EAIFC,EAKFC,IAAA,IAAC,MAAEC,EAAK,MAAEnD,KAAUoD,GAASF,EAAA,OAC/BtD,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,oFACZsD,EAAM1C,KAAI,CAACyB,EAAMmB,IAChBzD,EAAAA,cAACqC,EAAaT,OAAA8B,OAAA,CACZ3E,IAAK0E,EACLnB,KAAMA,EACNI,MAAOe,EACPd,YAAaY,EAAMrD,OAAS,GACxBsD,OAIN,EAGFG,EAAU,CACdC,KAAMC,EAAAA,EACNC,MAAOC,EAAAA,EACPC,KAAMC,EAAAA,EACNC,MAAOC,EAAAA,EACPC,YAAaC,EAAAA,GAITC,EAA0D,CAC9DV,KAAM,CAAEW,gBAAiB,0BAA2BC,aAAc,IAClEV,MAAO,CAAE5C,KAAM,YAAagC,YAAa,IACzCgB,MAAO,CAAEA,MAAO,UAAWO,QAAS,IACpCT,KAAM,CACJU,YAAa,GACbxD,KAAM,WACNgC,YAAa,aACbyB,eAAgB,GAChBC,0BAA0B,GAE5BR,YAAa,CAAES,aAAc,IAGlBC,EAIRC,IAA8C,IAA7C,QAAEjE,EAAO,OAAEkE,EAAM,mBAAEC,GAAoBF,EAC3C,IAAKjE,EAAQE,OAAOC,WAClB,OAAOjB,EAAAA,cAAA,OAAKC,UAAU,kBAAiB,uBAEzC,MAAM,EAACiF,EAAiB,EAACC,IAAuBC,EAAAA,EAAAA,UAItC,OACJ,EAACC,EAAU,EAACC,IAAgBF,EAAAA,EAAAA,UAAyB,SACrD,EAACG,EAAiB,EAACC,IAAuBJ,EAAAA,EAAAA,WAAS,IACnD,EAACK,EAAS,EAACC,IAAeN,EAAAA,EAAAA,UAAStE,EAAQE,OAAOE,OAClD,EAACyE,EAAgB,EAACC,IAAsBR,EAAAA,EAAAA,UAC5CtE,EAAQE,OAAOU,SAASwB,cAG1B2C,EAAAA,EAAAA,YAAU,KACRH,EAAY5E,EAAQE,OAAOE,MAC3B0E,EAAmB9E,EAAQE,OAAOU,SAASwB,aAC3CoC,EAAa,QACbH,EAAoB,KAAK,GACxB,CAACrE,EAAQC,KAEZ,MAAM+E,EAAgBA,CACpBC,EACAC,KAIA,MAAMC,EAAiB,IAClBnF,EACHE,OAAQ,IACHF,EAAQE,OACXC,WAAY,IACPH,EAAQE,OAAOC,WAClB,CAAC8E,GAAWC,EAAQlF,EAAQE,OAAOC,WAAW8E,OAIpDf,EAAOiB,GACPhB,GAAmB,EAAK,EAGpBiB,EAAW,CACf3D,OAAQA,CAAC4D,EAAuCzD,KAC9CyC,EAAoB,CAClBgB,YACAJ,SAAU,GAAGV,KACb3C,SACA,EAGJF,YAAaA,CAAC2D,EAAuCzD,KAAmB,IAAD0D,EACrE,MAAML,EAAW,GAAGV,KACdgB,EAA2B,QAAlBD,EAAGD,EAAUpD,aAAK,IAAAqD,OAAA,EAAfA,EAAiBE,QAAQ,QAAS,IAC9CrF,EAAaH,EAAQE,OAAOC,WAAW8E,GAEvCQ,EACJC,KAAKC,IAAGC,MAARF,MAAIG,EAAAA,EAAAA,GACC1F,EACAJ,KAAK+F,IAAO,IAADC,EACV,MAAMC,EAAe,QAAVD,EAAGD,EAAE7D,aAAK,IAAA8D,OAAA,EAAPA,EAASC,MACrB,IAAIC,OAAO,IAAIV,gBAEjB,OAAOS,EAAQE,SAASF,EAAM,IAAM,KAAO,CAAC,IAE7CG,QAAQC,IAAOC,MAAMD,MAAGE,OAAA,CAC3B,KACE,EAENtB,EAAcC,GAAW9E,GAAU,GAAAmG,QAAAT,EAAAA,EAAAA,GAC9B1F,GAAU,CACb,IAAKkF,EAAWpD,MAAO,GAAGsD,KAAaE,QACvC,EAGJ9D,SAAUA,CAAC0D,EAAuCzD,KAEhDoD,EADiB,GAAGT,MACKpE,GACvBA,EAAWgG,QAAO,CAACI,EAAGC,IAAMA,IAAM5E,KACnC,GAoFC6E,EAAW3F,OAAO4F,QAAQ7D,GAAS9C,KAAI4G,IAAA,IAAE1I,EAAK2I,GAAKD,EAAA,MAAM,CAC7D1I,MACAgE,MACE/C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAAC0H,EAAI,CAACzH,UAAU,YACflB,EAAI4I,OAAO,GAAGC,cAAgB7I,EAAI8I,MAAM,GAAG,IAC5C7H,EAAAA,cAAA,QAAMC,UAAU,qCAAoC,IAChDa,EAAQE,OAAOC,WAAW,GAAGlC,MAAuBmB,OAAO,MAInE4H,SACE9H,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,MAAIC,UAAU,yBACXa,EAAQE,OAAOC,WAAW,GAAGlC,MAAuBmB,OAAQ,IACG,IAA/DY,EAAQE,OAAOC,WAAW,GAAGlC,MAAuBmB,OACjDnB,EAAI4I,OAAO,GAAGC,cAAgB7I,EAAI8I,MAAM,GACxC9I,EAAI4I,OAAO,GAAGC,cAAgB7I,EAAI8I,MAAM,GAAK,KAEnD7H,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAMV,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,YACtBI,QAASA,KACPiF,EAAavG,GAxGPgJ,MAChB,MAAMhC,EAAW,GAAGV,KACdpE,EAAaH,EAAQE,OAAOC,WAAW8E,GAC7C,IAAIiC,EACJ,MAAMC,EAAW,OACf5C,EAAUsC,OAAO,GAAGC,cAAgBvC,EAAUwC,MAAM,KAKpDG,EAFE/G,EAAWf,OAAS,EAEP,IACVe,EAAW,GACd8B,MAAOkF,GAIM,CACbrF,SAAU,MACVsF,eAAgB7C,EAChBrE,OAAQsD,EAAee,GACvBtC,MAAOkF,GAIXnC,EAAcC,GAAW9E,IACvB,MAAMkH,EAAa,GAAAf,QAAAT,EAAAA,EAAAA,GAAO1F,GAAU,CAAE+G,IAMtC,OALA7C,EAAoB,CAClBgB,UAAW6B,EACXjC,WACArD,MAAOyF,EAAcjI,OAAS,IAEzBiI,CAAa,GACpB,EAyEQJ,EAAW,GAGZ,OAAOhJ,EAAI4I,OAAO,GAAGC,cAAgB7I,EAAI8I,MAAM,OAGpD7H,EAAAA,cAACqD,EAAazB,OAAA8B,OAAA,CACZH,MAAOzC,EAAQE,OAAOC,WAAW,GAAGlC,MACpCqB,MAAOrB,GACHmH,KAIX,IAED,OACElG,EAAAA,cAAA,OAAKC,UAAU,0BACbD,EAAAA,cAAA,OAAKC,UAAU,2DACbD,EAAAA,cAAA,OACEoI,IAAI,2BACJC,IAAI,iBACJpI,UAAU,wCAEZD,EAAAA,cAAA,OAAKC,UAAU,0DACbD,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,OAAKC,UAAU,2BACZsF,EACCvF,EAAAA,cAACsI,EAAAA,EAAK,CACJC,MAAO9C,EACP+C,SAAWnH,GAAMqE,EAAYrE,EAAEoH,OAAOF,OACtCtI,UAAU,oFAGZD,EAAAA,cAAA,MAAIC,UAAU,qCACXa,EAAQE,OAAOE,MAGnBJ,EAAQE,OAAOG,KACdnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,kBACbJ,EAAAA,cAAChB,EAAK,CAACiB,UAAU,8BAKxBsF,EACCvF,EAAAA,cAAC0I,EAAAA,EAAQ,CACPH,MAAO5C,EACP6C,SAAWnH,GAAMuE,EAAmBvE,EAAEoH,OAAOF,OAC7CtI,UAAU,8DACV0I,KAAM,IAGR3I,EAAAA,cAAA,OAAKC,UAAU,uBACbD,EAAAA,cAAA,KAAGC,UAAU,0CACVa,EAAQE,OAAOU,SAASwB,aAE3BlD,EAAAA,cAAA,OAAKC,UAAU,cACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,gBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLE,KAAMV,EAAAA,cAAC8C,EAAAA,EAAI,CAAC7C,UAAU,YACtBI,QAASA,IAAMmF,GAAoB,GACnC/E,KAAK,OACLR,UAAU,oCAGdD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,oBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLE,KAAMV,EAAAA,cAAC4I,EAAAA,EAAQ,CAAC3I,UAAU,YAC1BI,QA7GGwI,KACrB,MAAMC,EAAUC,KAAKC,UAAUlI,EAAS,KAAM,GACxCmI,EAAW,IAAIC,KAAK,CAACJ,GAAU,CAAErI,KAAM,qBACvCU,EAAMgI,IAAIC,gBAAgBH,GAC1BI,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAOrI,EACZkI,EAAKI,SAAW,GAAG3I,EAAQE,OAAOE,KAC/BwI,cACApD,QAAQ,OAAQ,YACnBgD,SAASK,KAAKC,YAAYP,GAC1BA,EAAKQ,QACLP,SAASK,KAAKG,YAAYT,GAC1BF,IAAIY,gBAAgB5I,EAAI,EAkGNV,KAAK,OACLR,UAAU,sCAMnBsF,GACCvF,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAACQ,EAAAA,GAAM,CAACH,QAASA,IAAMmF,GAAoB,IAAQ,UAGnDxF,EAAAA,cAACQ,EAAAA,GAAM,CAACC,KAAK,UAAUJ,QA3IX2J,KACxB,MAAM/D,EAAiB,IAClBnF,EACHE,OAAQ,IACHF,EAAQE,OACXE,KAAMuE,EACN/D,SAAU,IACLZ,EAAQE,OAAOU,SAClBwB,YAAayC,KAInBX,EAAOiB,GACPhB,GAAmB,GACnBO,GAAoB,EAAM,GA6HqC,UAMzDxF,EAAAA,cAAA,OAAKC,UAAU,cACbD,EAAAA,cAAA,OAAKC,UAAU,iEACbD,EAAAA,cAACZ,EAAO,CAACa,UAAU,2BACnBD,EAAAA,cAAA,QAAMC,UAAU,WACb2B,OAAOC,OAAOf,EAAQE,OAAOC,YAAYa,QACxC,CAACC,EAAKC,IAAQD,EAAMC,EAAI9B,QACxB,GACC,IAAI,eAIXF,EAAAA,cAAA,OAAKC,UAAU,iDAAgD,IAC3Da,EAAQE,OAAOU,SAASC,YAMlC3B,EAAAA,cAACiK,EAAAA,EAAI,CACH1G,MAAOgE,EACPtH,UAAU,eACVmB,KAAK,QACLoH,SAAWzJ,GAAQuG,EAAavG,KAGlCiB,EAAAA,cAACkK,EAAAA,EAAM,CACL9J,MAAM,iBACN+J,UAAU,QACV/I,KAAK,QACLgJ,QAASA,IAAMjF,EAAoB,MACnCkF,OAAQnF,EACRjF,UAAU,2BAETiF,GACClF,EAAAA,cAACsK,EAAAA,EAAe,CACdnE,UAAWjB,EAAiBiB,UAC5BqC,SAjMR+B,IAEKrF,IAELY,EAAcZ,EAAiBa,UAAW9E,GACxCA,EAAWJ,KAAI,CAAC+F,EAAGU,IACjBA,IAAMpC,EAAiBxC,MAAQ6H,EAAmB3D,MAGtDzB,EAAoB,MAAK,EAyLjBiF,QAASA,IAAMjF,EAAoB,MACnCqF,iBAAiB,KAInB,EAIV,sDCndA,MAWaC,EAXeC,MAC1B,IAGE,OADoBC,EAAQ,KAE9B,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,8BAA+BA,GACvCA,CACR,GAG2CF,GCAhCI,EAAwDxL,IAI9D,IAJ+D,KACpE+K,EAAI,SACJU,EAAQ,gBACRnL,GACDN,EACC,MAAM,EAAC+F,EAAU,EAACC,IAAgBF,EAAAA,EAAAA,UAAS,QACrC,EAACjE,EAAI,EAAC6J,IAAU5F,EAAAA,EAAAA,UAAS,KACzB,EAAC6F,EAAY,EAACC,IAAkB9F,EAAAA,EAAAA,UACpC2D,KAAKC,UAAUyB,EAAgB,KAAM,KAEjC,EAACG,EAAM,EAACO,IAAY/F,EAAAA,EAAAA,UAAS,KAC7B,EAACrF,EAAU,EAACqL,IAAgBhG,EAAAA,EAAAA,WAAS,GACrCiG,GAAYC,EAAAA,EAAAA,QAAO,MA0DnBC,EAA2B,CAC/BC,OAAQ,QACRC,gBAAgB,EAChBC,cAAepI,IAA0B,IAAzB,KAAEqI,EAAI,UAAEC,GAAWtI,EACjCuI,YAAW,KACTD,GAAaA,EAAU,KAAK,GAC3B,EAAE,EAEPpD,SA9CwBsD,IACxB,MAAM,OAAEC,EAAM,cAAEC,GAAkBF,EAAKH,KACvC,GAAe,SAAXI,GAAqBC,aAAyBC,KAAM,CACtD,MAAMC,EAAS,IAAIC,WACnBD,EAAOE,OAAU/K,IACf,IAAK,IAADgL,EACF,MAAMpJ,EAAU8F,KAAKuD,MACX,QADgBD,EACxBhL,EAAEoH,cAAM,IAAA4D,OAAA,EAARA,EAAUE,QAIZ3M,EAAgB,CACdoB,OAAQiC,IAEV8H,GACF,CAAE,MAAOyB,GACPrB,EAAS,oBACX,GAEFe,EAAOO,WAAWT,EACpB,KAAsB,UAAXD,GACTZ,EAAS,qBACX,GA2BIuB,GAAWpB,EAAAA,EAAAA,QAAiB,MAE5B/H,EAAQ,CACZ,CACExE,IAAK,MACLgE,MACE/C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAAChB,EAAK,CAACiB,UAAU,YAAY,eAGjC6H,SACE9H,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACsI,EAAAA,EAAK,CACJqE,IAAKD,EACLE,YAAY,uBACZrE,MAAOpH,EACPqH,SAAWnH,GAAM2J,EAAO3J,EAAEoH,OAAOF,SAEnCvI,EAAAA,cAAA,OAAKC,UAAU,WAAU,SAEvBD,EAAAA,cAAA,KACE6M,KAAK,SACLxM,QAAUgB,IACR2J,EACE,uIAEF3J,EAAEyL,gBAAgB,EAEpBtD,KAAK,sIACLf,OAAO,SACPsE,IAAI,aACJ9M,UAAU,eAET,IAAI,eACQ,MAGjBD,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,UACLJ,QA1Gc2M,UACtB5B,GAAa,GACbD,EAAS,IACT,IACE,MAAM8B,QAAiBC,MAAM/L,GACvBgM,QAAcF,EAASG,OAE7BxN,EAAgB,CACdoB,OAAQmM,IAEVpC,GACF,CAAE,MAAOyB,GACPrB,EAAS,4CACX,CAAC,QACCC,GAAa,EACf,GA4FQ5J,UAAWL,GAAOpB,EAClBsN,OAAK,GACN,qBAMP,CACEtO,IAAK,OACLgE,MACE/C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAACsN,EAAAA,EAAU,CAACrN,UAAU,YAAY,gBAGtC6H,SACE9H,EAAAA,cAAA,OAAKC,UAAU,+DACbD,EAAAA,cAACuN,EAAAA,EAAOC,QAAYjC,EAClBvL,EAAAA,cAAA,KAAGC,UAAU,wBACXD,EAAAA,cAACsN,EAAAA,EAAU,CAACrN,UAAU,oCAExBD,EAAAA,cAAA,KAAGC,UAAU,mBAAkB,2CAOvC,CACElB,IAAK,QACLgE,MACE/C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAACyN,EAAAA,EAAI,CAACxN,UAAU,YAAY,eAGhC6H,SACE9H,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAAC0N,EAAAA,EAAY,CACXnF,MAAO0C,EACPzC,SAAU0C,EACVG,UAAWA,EACXsC,SAAS,OACTC,SAAS,KAGb5N,EAAAA,cAACQ,EAAAA,GAAM,CAACC,KAAK,UAAUJ,QA9GLwN,KACxB,IACE,MAAM5K,EAAU8F,KAAKuD,MAAMrB,GAE3BrL,EAAgB,CACdoB,OAAQiC,IAEV8H,GACF,CAAE,MAAOyB,GACPrB,EAAS,sBACX,GAoGyDkC,OAAK,GAAC,kBAQjE,OACErN,EAAAA,cAAC8N,EAAAA,EAAK,CACJ1N,MAAM,qBACNiK,KAAMA,EACNU,SAAUA,EACVgD,OAAQ,KACRC,MAAO,KAEPhO,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAACiK,EAAAA,EAAI,CAACgE,UAAW5I,EAAWmD,SAAUlD,EAAc/B,MAAOA,IAE1DqH,GACC5K,EAAAA,cAACkO,EAAAA,EAAK,CAACC,QAASvD,EAAOnK,KAAK,QAAQ2N,UAAQ,EAACnO,UAAU,UAGrD,EC8EZ,MAhRwCoO,KACtC,MAAM,EAACtO,EAAU,EAACqL,IAAgBhG,EAAAA,EAAAA,WAAS,IACrC,EAAC5F,EAAU,EAAC8O,IAAgBlJ,EAAAA,EAAAA,UAAoB,KAChD,EAAC3F,EAAe,EAAC8O,IAAqBnJ,EAAAA,EAAAA,UAAyB,OAC/D,EAACoJ,EAAkB,EAACC,IAAwBrJ,EAAAA,EAAAA,WAAS,IACrD,EAACsJ,EAAkB,EAACC,IAAwBvJ,EAAAA,EAAAA,WAAS,IACrD,EAACwJ,EAAc,EAACC,IAAoBzJ,EAAAA,EAAAA,WAAS,KACjD,GAAsB,oBAAX0J,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAkB,OAAXF,GAAkBhG,KAAKuD,MAAMyC,EACtC,CACA,OAAO,CAAI,KAGP,KAAEG,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACrBC,EAAYC,GAAiBnB,EAAAA,GAAQoB,cAG5C1J,EAAAA,EAAAA,YAAU,KACc,oBAAXiJ,QACTE,aAAaQ,QAAQ,iBAAkBzG,KAAKC,UAAU4F,GACxD,GACC,CAACA,IAEJ,MAAMa,GAAiBC,EAAAA,EAAAA,cAAY1C,UACjC,GAAKkC,SAAAA,EAAMnO,GAEX,IACEqK,GAAa,GACb,MAAM+B,QAAawC,EAAAA,EAAWC,cAAcV,EAAKnO,IACjDuN,EAAanB,IACR1N,GAAkB0N,EAAKjN,OAAS,GACnCqO,EAAkBpB,EAAK,GAE3B,CAAE,MAAOvC,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CyE,EAAWzE,MAAM,4BACnB,CAAC,QACCQ,GAAa,EACf,IACC,CAAC8D,aAAI,EAAJA,EAAMnO,GAAItB,EAAgB4P,KAE9BxJ,EAAAA,EAAAA,YAAU,KACR4J,GAAgB,GACf,CAACA,KAGJ5J,EAAAA,EAAAA,YAAU,KACR,MACMgK,EADS,IAAIC,gBAAgBhB,OAAOiB,SAASC,QAC1BC,IAAI,aAE7B,GAAIJ,IAAcpQ,EAAgB,CAChC,MAAMyQ,EAAYlJ,SAAS6I,EAAW,IACjC1I,MAAM+I,IACTC,EAAoBD,EAExB,IACC,KAGHrK,EAAAA,EAAAA,YAAU,KACJpG,SAAAA,EAAgBsB,IAClB+N,OAAOsB,QAAQC,UACb,CAAC,EACD,GACA,cAAc5Q,EAAesB,GAAGuP,aAEpC,GACC,CAAC7Q,aAAc,EAAdA,EAAgBsB,KAEpB,MAAMoP,EAAsBnD,UACrBkC,SAAAA,EAAMnO,KAEP2N,EACFZ,EAAAA,EAAMyC,QAAQ,CACZnQ,MAAO,kBACP6C,QAAS,yDACTuN,OAAQ,UACRC,WAAY,UACZC,KAAMA,KACJC,EAAgBd,GAChBlB,GAAqB,EAAM,UAIzBgC,EAAgBd,GACxB,EAGIc,EAAkB3D,UACtB,GAAKkC,SAAAA,EAAMnO,GAAX,CAEAqK,GAAa,GACb,IACE,MAAM+B,QAAawC,EAAAA,EAAWiB,WAAWf,EAAWX,EAAKnO,IACzDwN,EAAkBpB,EACpB,CAAE,MAAOvC,GACPC,QAAQD,MAAM,yBAA0BA,GACxCyE,EAAWzE,MAAM,yBACnB,CAAC,QACCQ,GAAa,EACf,CAXqB,CAWrB,EAmBIyF,EAAsB7D,UAC1B,GAAKkC,SAAAA,EAAMnO,IAAOtB,SAAAA,EAAgBsB,GAElC,IACE,MAAM+P,EAAmB,IACpBC,EACHC,gBAAYC,EACZhP,gBAAYgP,GAERhL,QAAuB0J,EAAAA,EAAW7J,cACtCrG,EAAesB,GACf+P,EACA5B,EAAKnO,IAEPuN,EACE9O,EAAUqB,KAAKqQ,GAAOA,EAAEnQ,KAAOkF,EAAelF,GAAKkF,EAAiBiL,KAEtE3C,EAAkBtI,GAClB0I,GAAqB,GACrBU,EAAW8B,QAAQ,+BACrB,CAAE,MAAOvG,GACPC,QAAQD,MAAM,0BAA2BA,GACzCyE,EAAWzE,MAAM,2BACnB,GAiDF,OAAKsE,SAAAA,EAAMnO,GASTf,EAAAA,cAAA,OAAKC,UAAU,+BACZqP,EAGDtP,EAAAA,cAAC8K,EAAkB,CACjBT,KAAMmE,EACNzD,SAAUA,IAAM0D,GAAqB,GACrC7O,gBAxGsBoN,UAC1B,GAAKkC,SAAAA,EAAMnO,GAAX,CAEAqQ,EAAYC,QAAUnC,EAAKnO,GAC3B,IACE,MAAMuQ,QAAqB3B,EAAAA,EAAW4B,cAAcH,EAAalC,EAAKnO,IACtEuN,EAAa,CAACgD,GAAYlK,QAAAT,EAAAA,EAAAA,GAAKnH,KAC/B+O,EAAkB+C,GAClB7C,GAAqB,GACrBY,EAAW8B,QAAQ,+BACrB,CAAE,MAAOvG,GACPC,QAAQD,MAAM,0BAA2BA,GACzCyE,EAAWzE,MAAM,2BACnB,CAZqB,CAYrB,IA+FE5K,EAAAA,cAAA,OACEC,UAAW,yEACT2O,EAAgB,OAAS,SAG3B5O,EAAAA,cAACX,EAAc,CACbE,OAAQqP,EACRpP,UAAWA,EACXC,eAAgBA,EAChBC,SAAUA,IAAMmP,GAAkBD,GAClCjP,gBAAkBmB,GAAYqP,EAAoBrP,EAAQC,IAC1DnB,gBAAiBA,IAAM6O,GAAqB,GAC5C5O,gBA9EoBmN,UAC1B,GAAKkC,SAAAA,EAAMnO,GAEX,UACQ4O,EAAAA,EAAW6B,cAAc3B,EAAWX,EAAKnO,IAC/CuN,EAAa9O,EAAUyH,QAAQiK,GAAMA,EAAEnQ,KAAO8O,MAC1CpQ,aAAc,EAAdA,EAAgBsB,MAAO8O,GACzBtB,EAAkB,MAEpBc,EAAW8B,QAAQ,+BACrB,CAAE,MAAOvG,GACPC,QAAQD,MAAM,0BAA2BA,GACzCyE,EAAWzE,MAAM,2BACnB,GAkEM9K,cA/DkBkN,UACxB,GAAKkC,SAAAA,EAAMnO,GAEX,IACEqK,GAAa,GACb,MAAMtK,EAAUtB,EAAUiS,MAAMP,GAAMA,EAAEnQ,KAAO8O,IAC/C,GAAK/O,UAAAA,EAASE,OAAOG,IAAK,OAE1B,MAAMuQ,QAAsB/B,EAAAA,EAAWgC,YAAY7Q,EAAQE,OAAOG,WAC5D0P,EAAoB,IACrBa,EACH3Q,GAAI8O,EACJ7O,OAAQ,IACH0Q,EAAc1Q,OACjBU,SAAU,IACLgQ,EAAc1Q,OAAOU,SACxBkQ,YAAY,IAAIC,MAAOC,kBAK7BzC,EAAW8B,QAAQ,8BACrB,CAAE,MAAOvG,GACPC,QAAQD,MAAM,yBAA0BA,GACxCyE,EAAWzE,MAAM,yBACnB,CAAC,QACCQ,GAAa,EACf,GAqCMrL,UAAWA,KAKfC,EAAAA,cAAA,OACEC,UAAW,6CACT2O,EAAgB,QAAU,UAG5B5O,EAAAA,cAAA,OAAKC,UAAU,YAEbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,aAC1CR,GACCO,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC+R,EAAAA,EAAY,CAAC9R,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBACbR,EAAeuB,OAAOE,QAO9BnB,IAAcN,EACbO,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,wBAGrFR,EACFO,EAAAA,cAAC8E,EAAa,CACZhE,QAASrB,EACTuF,OAAQ6L,EACR5L,mBAAoB0J,IAGtB3O,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,4DApE7FD,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,kCA0EnF,EC3PV,MArBoBX,IAAmB,IAAlB,KAAE6N,GAAW7N,EAChC,OACEU,EAAAA,cAACgS,EAAAA,EAAM,CAACC,KAAM9E,EAAK+E,KAAKC,aAAc/R,MAAM,OAAOiJ,KAAM,YACvDrJ,EAAAA,cAAA,QAAMoS,MAAO,CAAEC,OAAQ,QAAUpS,UAAU,YACzCD,EAAAA,cAACqO,EAAc,OAEV,wDCHb,MAAMxL,GAAO,aAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEmL,MAAO,KAAMqE,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAK1T,IAAK,WAC7E,CAAC,OAAQ,CAAED,EAAG,0DAA2DC,IAAK,mECFhF,MAAM8E,GAAQ,aAAiB,QAAS,CACtC,CAAC,OAAQ,CAAE/E,EAAG,4CAA6CC,IAAK,WAChE,CAAC,SAAU,CAAEE,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKJ,IAAK,UAC5C,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,WACjD,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,mECJlD,MAAMkF,GAAS,aAAiB,SAAU,CACxC,CACE,OACA,CACEnF,EAAG,2JACHC,IAAK,oOCXX,MAAM2T,EAAoB,CAACC,EAASC,EAAaC,EAAWC,EAAOC,KAAa,CAC9EC,WAAYL,EACZM,OAAQ,IAAG,QAAKH,EAAMI,cAAcJ,EAAMK,YAAYP,IACtD,CAAC,GAAGG,UAAkB,CACpBK,MAAOP,KAGEQ,EAAeP,IAC1B,MAAM,aACJQ,EACAC,mBAAoBC,EAAQ,SAC5BC,EAAQ,SACRC,EAAQ,SACRC,EAAQ,WACRC,EAAU,WACVC,EACAC,eAAgBC,EAAY,oBAC5BC,EAAmB,wBACnBC,EAAuB,UACvBC,EAAS,iBACTC,EAAgB,uBAChBC,EAAsB,eACtBC,GACEvB,EACJ,MAAO,CACL,CAACQ,GAAe1R,OAAO8B,OAAO9B,OAAO8B,OAAO,CAAC,GAAG,QAAeoP,IAAS,CACtEwB,SAAU,WACVC,QAAS,OACTC,WAAY,SACZC,QAASJ,EACTK,SAAU,aACVX,eACA,CAAC,IAAIT,SAAqB,CACxBqB,UAAW,OAEb,CAAC,GAAGrB,aAAyB,CAC3BsB,KAAM,EACNC,SAAU,GAEZ,CAAC,GAAGvB,UAAsB,CACxBwB,gBAAiBrB,EACjBI,WAAY,GAEd,gBAAiB,CACfU,QAAS,OACTZ,WACAE,cAEF,YAAa,CACXT,MAAOe,GAET,CAAC,IAAIb,kBAA8B,CACjCyB,SAAU,SACVC,QAAS,EACTC,WAAY,cAAczB,KAAYQ,cAAgCR,KAAYQ,2BACpER,KAAYQ,qBAAuCR,KAAYQ,6BAC7DR,KAAYQ,KAE9B,CAAC,IAAIV,yBAAqC,CACxC4B,UAAW,EACXC,aAAc,eACdC,WAAY,EACZC,cAAe,EACfL,QAAS,KAGb,CAAC,GAAG1B,sBAAkC,CACpCkB,WAAY,aACZC,QAASL,EACT,CAAC,GAAGd,UAAsB,CACxBwB,gBAAiBpB,EACjBC,SAAUM,EACVJ,WAAY,GAEd,CAAC,GAAGP,aAAyB,CAC3BiB,QAAS,QACTY,aAAc1B,EACdL,MAAOe,EACPR,SAAUC,GAEZ,CAAC,GAAGN,iBAA6B,CAC/BiB,QAAS,QACTnB,MAAOc,IAGX,CAAC,GAAGZ,YAAwB,CAC1B6B,aAAc,EACdlC,OAAQ,eACRc,aAAc,GAEjB,EAEUuB,EAAexC,IAC1B,MAAM,aACJQ,EAAY,aACZiC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,aACdC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,WACdC,EAAU,iBACVC,EAAgB,aAChBC,EAAY,UACZC,EAAS,gBACTC,EAAe,YACfC,GACEpD,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,YAAaZ,EAAkB+C,EAAgBD,EAAoBD,EAAczC,EAAOQ,GACxF,SAAUZ,EAAkBwD,EAAaD,EAAiBD,EAAWlD,EAAOQ,GAC5E,YAAaZ,EAAkBkD,EAAgBD,EAAoBD,EAAc5C,EAAOQ,GACxF,UAAW1R,OAAO8B,OAAO9B,OAAO8B,OAAO,CAAC,EAAGgP,EAAkBqD,EAAcD,EAAkBD,EAAY/C,EAAOQ,IAAgB,CAC9H,CAAC,GAAGA,uBAAmC,CACrC6C,OAAQ,EACR1B,QAAS,MAIhB,EAEU2B,EAAiBtD,IAC5B,MAAM,aACJQ,EAAY,QACZ+C,EAAO,kBACPC,EAAiB,SACjB7C,EAAQ,aACR8C,EAAY,UACZC,EAAS,eACTC,GACE3D,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,WAAY,CACVoD,kBAAmBjD,GAErB,CAAC,GAAGH,gBAA4B,CAC9BoD,kBAAmBjD,EACnBgB,QAAS,EACTM,SAAU,SACVpB,SAAU4C,EACV1C,YAAY,QAAK0C,GACjBI,gBAAiB,cACjB1D,OAAQ,OACR2D,QAAS,OACTC,OAAQ,UACR,CAAC,GAAGR,WAAkB,CACpBjD,MAAOoD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTlD,MAAOqD,KAIb,eAAgB,CACdrD,MAAOoD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTlD,MAAOqD,KAId,EAUH,OAAe,QAAc,SAAS3D,GAAS,CAACO,EAAaP,GAAQwC,EAAaxC,GAAQsD,EAAetD,MARpEA,IAE5B,CACLmB,wBAAyBnB,EAAMgE,iBAC/BzC,eAAgB,GAAGvB,EAAMiE,kCACzB3C,uBAAwB,GAAGtB,EAAMkE,eAAelE,EAAMmE,mCC3KtDC,EAAgC,SAAUC,EAAG9V,GAC/C,IAAI+V,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOvV,OAAO0V,UAAUC,eAAeC,KAAKL,EAAGE,IAAMhW,EAAEoW,QAAQJ,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCvV,OAAO8V,sBAA2C,KAAIpQ,EAAI,EAAb,IAAgB+P,EAAIzV,OAAO8V,sBAAsBP,GAAI7P,EAAI+P,EAAEnX,OAAQoH,IAClIjG,EAAEoW,QAAQJ,EAAE/P,IAAM,GAAK1F,OAAO0V,UAAUK,qBAAqBH,KAAKL,EAAGE,EAAE/P,MAAK8P,EAAEC,EAAE/P,IAAM6P,EAAEE,EAAE/P,IADuB,CAGvH,OAAO8P,CACT,EAeA,MAAMQ,EAAgB,CACpBzG,QAAS0G,EAAA,EACT/L,KAAMgM,EAAA,EACNlN,MAAOmN,EAAA,EACPC,QAASC,EAAA,GAELC,EAAWC,IACf,MAAM,KACJzX,EAAI,UACJ0X,EAAS,KACT3X,GACE0X,EACEE,EAAWT,EAAcnX,IAAS,KACxC,OAAIC,GACK,QAAeA,EAAmB,gBAAoB,OAAQ,CACnET,UAAW,GAAGmY,UACb1X,IAAO,KAAM,CACdT,UAAW,IAAW,GAAGmY,SAAkB1X,EAAKyX,MAAMlY,eAGtC,gBAAoBoY,EAAU,CAChDpY,UAAW,GAAGmY,UACd,EAEEE,EAAgBH,IACpB,MAAM,WACJI,EAAU,UACVH,EAAS,UACTI,EAAS,YACTC,EAAW,UACXC,GACEP,EACEQ,GAAgC,IAAdH,QAAoCvH,IAAduH,EAAuC,gBAAoBI,EAAA,EAAe,MAAQJ,EAChI,OAAOD,EAA2B,gBAAoB,SAAU3W,OAAO8B,OAAO,CAC5EjD,KAAM,SACNJ,QAASoY,EACTxY,UAAW,GAAGmY,eACdS,SAAU,GACTH,GAAYC,GAAoB,IAAI,EAEnCzK,EAAqB,cAAiB,CAACiK,EAAOxL,KAClD,MAAM,YACFzJ,EACAkV,UAAWU,EAAkB,QAC7B3K,EAAO,OACP4K,EAAM,UACN9Y,EAAS,cACT+Y,EAAa,MACb5G,EAAK,aACL6G,EAAY,aACZC,EAAY,QACZ7Y,EAAO,WACP8Y,EAAU,SACV/K,EAAQ,SACRgL,EAAQ,UACRC,EAAS,UACTb,EAAS,OACTc,EAAM,GACNvY,GACEoX,EACJoB,EAAarC,EAAOiB,EAAO,CAAC,cAAe,YAAa,UAAW,SAAU,YAAa,gBAAiB,QAAS,eAAgB,eAAgB,UAAW,aAAc,WAAY,WAAY,YAAa,YAAa,SAAU,QACpOqB,EAAQC,GAAa,YAAe,GAK3C,MAAMC,EAAc,SAAa,MACjC,sBAA0B/M,GAAK,KAAM,CACnCgN,cAAeD,EAAYE,YAE7B,MAAM,aACJC,EAAY,UACZlF,EACAyE,SAAUU,EACVtB,UAAWuB,EACX9Z,UAAW+Z,EACX5H,MAAO6H,IACL,QAAmB,SACjB7B,EAAYyB,EAAa,QAASf,IACjCoB,EAAYC,EAAQC,GAAa,EAAShC,GAC3CK,EAAcpX,IAClB,IAAIgZ,EACJZ,GAAU,GACe,QAAxBY,EAAKlC,EAAM/N,eAA4B,IAAPiQ,GAAyBA,EAAG7C,KAAKW,EAAO9W,EAAE,EAEvEZ,EAAO,WAAc,SACNwQ,IAAfkH,EAAM1X,KACD0X,EAAM1X,KAGRsY,EAAS,UAAY,QAC3B,CAACZ,EAAM1X,KAAMsY,IAEVR,EAAa,WAAc,MACP,iBAAba,IAAyBA,EAASZ,eACzCa,IAGoB,kBAAbD,EACFA,GAGS,IAAdZ,SAAuBA,KAGlBsB,KACR,CAACT,EAAWb,EAAWY,EAAUU,IAE9BQ,KAAavB,QAAuB9H,IAAb7C,IAAgCA,EACvD2E,EAAW,IAAWqF,EAAW,GAAGA,KAAa3X,IAAQ,CAC7D,CAAC,GAAG2X,wBAAiClV,EACrC,CAAC,GAAGkV,cAAuBkC,EAC3B,CAAC,GAAGlC,cAAuBW,EAC3B,CAAC,GAAGX,SAAgC,QAAdzD,GACrBqF,EAAkB/Z,EAAW+Y,EAAeoB,EAAWD,GACpDI,GAAY,EAAAC,EAAA,GAAUjB,EAAY,CACtCkB,MAAM,EACNtN,MAAM,IAEFwL,EAAkB,WAAc,IACZ,iBAAbS,GAAyBA,EAASZ,UACpCY,EAASZ,UAEda,SAGcpI,IAAduH,EACKA,EAEsB,iBAApBsB,GAAgCA,EAAgBtB,UAClDsB,EAAgBtB,UAElBuB,IACN,CAACvB,EAAWY,EAAUC,EAAWU,IAC9BW,EAAkB,WAAc,KACpC,MAAMC,EAASvB,QAA2CA,EAAWU,EACrE,GAAsB,iBAAXa,EAAqB,CAC9B,MACInC,UAAWnR,GACTsT,EAEN,OADczD,EAAOyD,EAAQ,CAAC,aAEhC,CACA,MAAO,CAAC,CAAC,GACR,CAACvB,EAAUU,IACd,OAAOI,EAAwB,gBAAoB,KAAW,CAC5DU,SAAUpB,EACVqB,WAAY,GAAGzC,WACf0C,cAAc,EACdC,aAAa,EACbC,aAAcC,IAAQ,CACpB/F,UAAW+F,EAAKC,eAElBC,WAAYhC,IACX,CAAC7Z,EAAM8b,KACR,IACEnb,UAAWob,EACXjJ,MAAOkJ,GACLhc,EACJ,OAAoB,gBAAoB,MAAOsC,OAAO8B,OAAO,CAC3D3C,GAAIA,EACJ4L,KAAK,QAAW+M,EAAa0B,GAC7B,aAAc5B,EACdvZ,UAAW,IAAW8S,EAAUsI,GAChCjJ,MAAOxQ,OAAO8B,OAAO9B,OAAO8B,OAAO9B,OAAO8B,OAAO,CAAC,EAAGuW,GAAe7H,GAAQkJ,GAC5ErC,aAAcA,EACdC,aAAcA,EACd7Y,QAASA,EACTwM,KAAM,SACL0N,GAAYD,EAA2B,gBAAoBpC,EAAU,CACtEhV,YAAaA,EACbxC,KAAMyX,EAAMzX,KACZ0X,UAAWA,EACX3X,KAAMA,IACF,KAAmB,gBAAoB,MAAO,CAClDR,UAAW,GAAGmY,aACbjK,EAAuB,gBAAoB,MAAO,CACnDlO,UAAW,GAAGmY,aACbjK,GAAW,KAAMjL,EAA2B,gBAAoB,MAAO,CACxEjD,UAAW,GAAGmY,iBACblV,GAAe,MAAOoW,EAAsB,gBAAoB,MAAO,CACxErZ,UAAW,GAAGmY,YACbkB,GAAU,KAAmB,gBAAoBhB,EAAe,CACjEC,WAAYA,EACZH,UAAWA,EACXI,UAAWG,EACXF,YAAaA,EACbC,UAAWgC,IACV,IACF,IAKL,wEClNA,IAAIa,EAA6B,SAAUC,GACzC,SAASD,IACP,IAAIE,ECPYrE,EAAGsE,EAAGra,EDgBtB,OARA,OAAgBsa,KAAMJ,GCRNnE,EDSGuE,KCTAD,EDSMH,ECTHla,EDSkBua,UCRnCF,GAAI,EAAAG,EAAA,GAAeH,IDQxBD,GCR4B,EAAAK,EAAA,GAA0B1E,GAAG,EAAA2E,EAAA,KAA6BC,QAAQC,UAAUP,EAAGra,GAAK,IAAI,EAAAwa,EAAA,GAAezE,GAAG8E,aAAeR,EAAEhV,MAAM0Q,EAAG/V,KDS1J8a,MAAQ,CACZvR,WAAOqG,EACPnF,KAAM,CACJsQ,eAAgB,KAGbX,CACT,CAEA,OADA,OAAUF,EAAeC,IAClB,OAAaD,EAAe,CAAC,CAClCxc,IAAK,oBACLwJ,MAAO,SAA2BqC,EAAOkB,GACvC6P,KAAKU,SAAS,CACZzR,QACAkB,QAEJ,GACC,CACD/M,IAAK,SACLwJ,MAAO,WACL,MAAM,QACJ4F,EAAO,YACPjL,EAAW,GACXnC,EAAE,SACF+G,GACE6T,KAAKxD,OACH,MACJvN,EAAK,KACLkB,GACE6P,KAAKQ,MACHC,GAAkBtQ,aAAmC,EAASA,EAAKsQ,iBAAmB,KACtFE,OAAkC,IAAZnO,GAA2BvD,GAAS,IAAI0F,WAAanC,EAC3EoO,OAA0C,IAAhBrZ,EAA8BkZ,EAAiBlZ,EAC/E,OAAI0H,EACkB,gBAAoB,EAAO,CAC7C7J,GAAIA,EACJN,KAAM,QACN0N,QAASmO,EACTpZ,YAA0B,gBAAoB,MAAO,CACnDkP,MAAO,CACLuB,SAAU,QACV6I,UAAW,SAEZD,KAGAzU,CACT,IAEJ,CAtDiC,CAsD/B,aACF,QE3DA,MAAM,EAAQ,EACd,EAAMyT,cAAgB,EACtB,8DCGA,MAAM3S,GAAW,aAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAE9J,EAAG,4CAA6CC,IAAK,WAChE,CAAC,WAAY,CAAE0d,OAAQ,mBAAoB1d,IAAK,WAChD,CAAC,OAAQ,CAAE2d,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAK9d,IAAK", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/globe.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/package.js", "webpack://autogentstudio/./src/components/views/gallery/sidebar.tsx", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/trash.js", "webpack://autogentstudio/./src/components/views/gallery/detail.tsx", "webpack://autogentstudio/./src/components/views/gallery/utils.ts", "webpack://autogentstudio/./src/components/views/gallery/create-modal.tsx", "webpack://autogentstudio/./src/components/views/gallery/manager.tsx", "webpack://autogentstudio/./src/pages/gallery.tsx", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/copy.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/users.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/wrench.js", "webpack://autogentstudio/./node_modules/antd/es/alert/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/Alert.js", "webpack://autogentstudio/./node_modules/antd/es/alert/ErrorBoundary.js", "webpack://autogentstudio/./node_modules/@babel/runtime/helpers/esm/callSuper.js", "webpack://autogentstudio/./node_modules/antd/es/alert/index.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/download.js"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCw = createLucideIcon(\"RefreshCw\", [\n  [\"path\", { d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\", key: \"v9h5vc\" }],\n  [\"path\", { d: \"M21 3v5h-5\", key: \"1q7to0\" }],\n  [\"path\", { d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\", key: \"3uifl3\" }],\n  [\"path\", { d: \"M8 16H3v5\", key: \"1cv678\" }]\n]);\n\nexport { RefreshCw as default };\n//# sourceMappingURL=refresh-cw.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Globe = createLucideIcon(\"Globe\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\", key: \"13o1zl\" }],\n  [\"path\", { d: \"M2 12h20\", key: \"9i4pu4\" }]\n]);\n\nexport { Globe as default };\n//# sourceMappingURL=globe.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Package = createLucideIcon(\"Package\", [\n  [\n    \"path\",\n    {\n      d: \"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z\",\n      key: \"1a0edw\"\n    }\n  ],\n  [\"path\", { d: \"M12 22V12\", key: \"d0xqtd\" }],\n  [\"path\", { d: \"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7\", key: \"yx3hmr\" }],\n  [\"path\", { d: \"m7.5 4.27 9 5.15\", key: \"1c824w\" }]\n]);\n\nexport { Package as default };\n//# sourceMappingURL=package.js.map\n", "import React from \"react\";\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\nimport {\n  Plus,\n  Trash2,\n  PanelLeftClose,\n  PanelLeftOpen,\n  Package,\n  RefreshCw,\n  Globe,\n  Info,\n} from \"lucide-react\";\nimport type { Gallery } from \"../../types/datamodel\";\nimport { getRelativeTimeString } from \"../atoms\";\n\ninterface GallerySidebarProps {\n  isOpen: boolean;\n  galleries: Gallery[];\n  currentGallery: Gallery | null;\n  onToggle: () => void;\n  onSelectGallery: (gallery: Gallery) => void;\n  onCreateGallery: () => void;\n  onDeleteGallery: (galleryId: number) => void;\n  onSyncGallery: (galleryId: number) => void;\n  isLoading?: boolean;\n}\n\nexport const GallerySidebar: React.FC<GallerySidebarProps> = ({\n  isOpen,\n  galleries,\n  currentGallery,\n  onToggle,\n  onSelectGallery,\n  onCreateGallery,\n  onDeleteGallery,\n  onSyncGallery,\n  isLoading = false,\n}) => {\n  // Render collapsed state\n  if (!isOpen) {\n    return (\n      <div className=\"h-full border-r border-secondary\">\n        <div className=\"p-2 -ml-2\">\n          <Tooltip title={`Galleries (${galleries.length})`}>\n            <button\n              onClick={onToggle}\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n            >\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\n            </button>\n          </Tooltip>\n        </div>\n\n        <div className=\"mt-4 px-2 -ml-1\">\n          <Tooltip title=\"Create new gallery\">\n            <Button\n              type=\"text\"\n              className=\"w-full p-2 flex justify-center\"\n              onClick={onCreateGallery}\n              icon={<Plus className=\"w-4 h-4\" />}\n            />\n          </Tooltip>\n        </div>\n      </div>\n    );\n  }\n\n  // Render expanded state\n  return (\n    <div className=\"h-full border-r border-secondary\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-primary font-medium\">Galleries</span>\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\n            {galleries.length}\n          </span>\n        </div>\n        <Tooltip title=\"Close Sidebar\">\n          <button\n            onClick={onToggle}\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n          >\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\n          </button>\n        </Tooltip>\n      </div>\n\n      {/* Create Gallery Button */}\n      <div className=\"my-4 flex text-sm\">\n        <div className=\"mr-2 w-full\">\n          <Tooltip title=\"Create new gallery\">\n            <Button\n              type=\"primary\"\n              className=\"w-full\"\n              icon={<Plus className=\"w-4 h-4\" />}\n              onClick={onCreateGallery}\n            >\n              New Gallery\n            </Button>\n          </Tooltip>\n        </div>\n      </div>\n\n      {/* Section Label */}\n      <div className=\"py-2 flex text-sm text-secondary\">\n        <div className=\"flex\">All Galleries</div>\n        {isLoading && <RefreshCw className=\"w-4 h-4 ml-2 animate-spin\" />}\n      </div>\n\n      {/* Galleries List */}\n      {!isLoading && galleries.length === 0 && (\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded\">\n          <Info className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\n          No galleries found\n        </div>\n      )}\n\n      <div className=\"scroll overflow-y-auto h-[calc(100%-170px)]\">\n        {galleries.map((gallery) => (\n          <div key={gallery.id} className=\"relative border-secondary\">\n            <div\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded ${\n                currentGallery?.id === gallery.id ? \"bg-accent\" : \"bg-tertiary\"\n              }`}\n            />\n            {gallery && gallery.config && gallery.config.components && (\n              <div\n                className={`group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary ${\n                  currentGallery?.id === gallery.id\n                    ? \"border-accent bg-secondary\"\n                    : \"border-transparent\"\n                }`}\n                onClick={() => onSelectGallery(gallery)}\n              >\n                {/* Gallery Name and Actions Row */}\n                <div className=\"flex items-center justify-between min-w-0\">\n                  <div className=\"flex items-center gap-2 min-w-0 flex-1\">\n                    <div className=\"truncate flex-1\">\n                      <span className=\"font-medium\">{gallery.config.name}</span>\n                    </div>\n                    {gallery.config.url && (\n                      <Tooltip title=\"Remote Gallery\">\n                        <Globe className=\"w-3 h-3 text-secondary flex-shrink-0\" />\n                      </Tooltip>\n                    )}\n                  </div>\n                  <div className=\"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0\">\n                    {gallery.config.url && (\n                      <Tooltip title=\"Sync gallery\">\n                        <Button\n                          type=\"text\"\n                          size=\"small\"\n                          className=\"p-0 min-w-[24px] h-6\"\n                          icon={<RefreshCw className=\"w-4 h-4\" />}\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            onSyncGallery(gallery.id!);\n                          }}\n                        />\n                      </Tooltip>\n                    )}\n                    <Tooltip\n                      title={\n                        galleries.length === 1\n                          ? \"Cannot delete the last gallery\"\n                          : \"Delete gallery\"\n                      }\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        className=\"p-0 min-w-[24px] h-6\"\n                        danger\n                        disabled={galleries.length === 1}\n                        icon={<Trash2 className=\"w-4 h-4 text-red-500\" />}\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          onDeleteGallery(gallery.id!);\n                        }}\n                      />\n                    </Tooltip>\n                  </div>\n                </div>\n\n                {/* Gallery Metadata */}\n                <div className=\"mt-1 flex items-center gap-2 text-xs text-secondary\">\n                  <span className=\"bg-secondary/20 truncate rounded px-1\">\n                    v{gallery.config.metadata.version}\n                  </span>\n                  <div className=\"flex items-center gap-1\">\n                    <Package className=\"w-3 h-3\" />\n                    <span>\n                      {Object.values(gallery.config.components).reduce(\n                        (sum, arr) => sum + arr.length,\n                        0\n                      )}{\" \"}\n                      components\n                    </span>\n                  </div>\n                </div>\n\n                {/* Updated Timestamp */}\n                {gallery.updated_at && (\n                  <div className=\"mt-1 flex items-center gap-1 text-xs text-secondary\">\n                    <span>{getRelativeTimeString(gallery.updated_at)}</span>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default GallerySidebar;\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Trash = createLucideIcon(\"Trash\", [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\", key: \"4alrt4\" }],\n  [\"path\", { d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\", key: \"v07s0e\" }]\n]);\n\nexport { Trash as default };\n//# sourceMappingURL=trash.js.map\n", "import React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Input } from \"antd\";\nimport {\n  Package,\n  Users,\n  Bot,\n  Globe,\n  Wrench,\n  Brain,\n  Timer,\n  Edit,\n  Copy,\n  Trash,\n  Plus,\n  Download,\n} from \"lucide-react\";\nimport { ComponentEditor } from \"../teambuilder/builder/component-editor/component-editor\";\nimport { TruncatableText } from \"../atoms\";\nimport {\n  Component,\n  ComponentConfig,\n  ComponentTypes,\n  Gallery,\n} from \"../../types/datamodel\";\nimport TextArea from \"antd/es/input/TextArea\";\n\ntype CategoryKey = `${ComponentTypes}s`;\n\ninterface CardActions {\n  onEdit: (component: Component<ComponentConfig>, index: number) => void;\n  onDuplicate: (component: Component<ComponentConfig>, index: number) => void;\n  onDelete: (component: Component<ComponentConfig>, index: number) => void;\n}\n\n// Component Card\nconst ComponentCard: React.FC<\n  CardActions & {\n    item: <PERSON>mponent<ComponentConfig>;\n    index: number;\n    allowDelete: boolean;\n  }\n> = ({ item, onEdit, onDuplicate, onDelete, index, allowDelete }) => (\n  <div\n    className=\"bg-secondary rounded overflow-hidden group h-full cursor-pointer\"\n    onClick={() => onEdit(item, index)}\n  >\n    <div className=\"px-4 py-3 flex items-center justify-between border-b border-tertiary\">\n      <div className=\"text-xs text-secondary truncate flex-1\">\n        {item.provider}\n      </div>\n      <div className=\"flex gap-0\">\n        {allowDelete && (\n          <Button\n            title=\"Delete\"\n            type=\"text\"\n            className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600\"\n            icon={<Trash className=\"w-3.5 h-3.5\" />}\n            onClick={(e) => {\n              e.stopPropagation();\n              onDelete(item, index);\n            }}\n          />\n        )}\n        <Button\n          title=\"Duplicate\"\n          type=\"text\"\n          className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\n          icon={<Copy className=\"w-3.5 h-3.5\" />}\n          onClick={(e) => {\n            e.stopPropagation();\n            onDuplicate(item, index);\n          }}\n        />\n        <Button\n          title=\"Edit\"\n          type=\"text\"\n          className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\n          icon={<Edit className=\"w-3.5 h-3.5\" />}\n          onClick={(e) => {\n            e.stopPropagation();\n            onEdit(item, index);\n          }}\n        />\n      </div>\n    </div>\n    <div className=\"p-4 pb-0 pt-3\">\n      <div className=\"text-base font-medium mb-2\">{item.label}</div>\n      <div className=\"text-sm text-secondary line-clamp-2 mb-3 min-h-[40px]\">\n        <TruncatableText\n          content={item.description || \"\"}\n          showFullscreen={false}\n          textThreshold={70}\n        />\n      </div>\n    </div>\n  </div>\n);\n\n// Component Grid\nconst ComponentGrid: React.FC<\n  {\n    items: Component<ComponentConfig>[];\n    title: string;\n  } & CardActions\n> = ({ items, title, ...actions }) => (\n  <div>\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr\">\n      {items.map((item, idx) => (\n        <ComponentCard\n          key={idx}\n          item={item}\n          index={idx}\n          allowDelete={items.length > 1}\n          {...actions}\n        />\n      ))}\n    </div>\n  </div>\n);\n\nconst iconMap = {\n  team: Users,\n  agent: Bot,\n  tool: Wrench,\n  model: Brain,\n  termination: Timer,\n} as const;\n\n// Add default configurations for each component type\nconst defaultConfigs: Record<ComponentTypes, ComponentConfig> = {\n  team: { selector_prompt: \"Default selector prompt\", participants: [] } as any,\n  agent: { name: \"New Agent\", description: \"\" } as any,\n  model: { model: \"gpt-3.5\", api_key: \"\" } as any,\n  tool: {\n    source_code: \"\",\n    name: \"New Tool\",\n    description: \"A new tool\",\n    global_imports: [],\n    has_cancellation_support: false,\n  },\n  termination: { max_messages: 1 },\n};\n\nexport const GalleryDetail: React.FC<{\n  gallery: Gallery;\n  onSave: (updates: Partial<Gallery>) => void;\n  onDirtyStateChange: (isDirty: boolean) => void;\n}> = ({ gallery, onSave, onDirtyStateChange }) => {\n  if (!gallery.config.components) {\n    return <div className=\"text-secondary\">No components found</div>;\n  }\n  const [editingComponent, setEditingComponent] = useState<{\n    component: Component<ComponentConfig>;\n    category: CategoryKey;\n    index: number;\n  } | null>(null);\n  const [activeTab, setActiveTab] = useState<ComponentTypes>(\"team\");\n  const [isEditingDetails, setIsEditingDetails] = useState(false);\n  const [tempName, setTempName] = useState(gallery.config.name);\n  const [tempDescription, setTempDescription] = useState(\n    gallery.config.metadata.description\n  );\n\n  useEffect(() => {\n    setTempName(gallery.config.name);\n    setTempDescription(gallery.config.metadata.description);\n    setActiveTab(\"team\");\n    setEditingComponent(null);\n  }, [gallery.id]);\n\n  const updateGallery = (\n    category: CategoryKey,\n    updater: (\n      components: Component<ComponentConfig>[]\n    ) => Component<ComponentConfig>[]\n  ) => {\n    const updatedGallery = {\n      ...gallery,\n      config: {\n        ...gallery.config,\n        components: {\n          ...gallery.config.components,\n          [category]: updater(gallery.config.components[category]),\n        },\n      },\n    };\n    onSave(updatedGallery);\n    onDirtyStateChange(true);\n  };\n\n  const handlers = {\n    onEdit: (component: Component<ComponentConfig>, index: number) => {\n      setEditingComponent({\n        component,\n        category: `${activeTab}s` as CategoryKey,\n        index,\n      });\n    },\n\n    onDuplicate: (component: Component<ComponentConfig>, index: number) => {\n      const category = `${activeTab}s` as CategoryKey;\n      const baseLabel = component.label?.replace(/_\\d+$/, \"\");\n      const components = gallery.config.components[category];\n\n      const nextNumber =\n        Math.max(\n          ...components\n            .map((c) => {\n              const match = c.label?.match(\n                new RegExp(`^${baseLabel}_?(\\\\d+)?$`)\n              );\n              return match ? parseInt(match[1] || \"0\") : 0;\n            })\n            .filter((n) => !isNaN(n)),\n          0\n        ) + 1;\n\n      updateGallery(category, (components) => [\n        ...components,\n        { ...component, label: `${baseLabel}_${nextNumber}` },\n      ]);\n    },\n\n    onDelete: (component: Component<ComponentConfig>, index: number) => {\n      const category = `${activeTab}s` as CategoryKey;\n      updateGallery(category, (components) =>\n        components.filter((_, i) => i !== index)\n      );\n    },\n  };\n\n  const handleAdd = () => {\n    const category = `${activeTab}s` as CategoryKey;\n    const components = gallery.config.components[category];\n    let newComponent: Component<ComponentConfig>;\n    const newLabel = `New ${\n      activeTab.charAt(0).toUpperCase() + activeTab.slice(1)\n    }`;\n\n    if (components.length > 0) {\n      // Clone the entire component and just modify the label\n      newComponent = {\n        ...components[0], // This preserves all fields (provider, version, description, etc.)\n        label: newLabel,\n      };\n    } else {\n      // Only for empty categories, use default config\n      newComponent = {\n        provider: \"new\",\n        component_type: activeTab,\n        config: defaultConfigs[activeTab],\n        label: newLabel,\n      };\n    }\n\n    updateGallery(category, (components) => {\n      const newComponents = [...components, newComponent];\n      setEditingComponent({\n        component: newComponent,\n        category,\n        index: newComponents.length - 1,\n      });\n      return newComponents;\n    });\n  };\n\n  const handleComponentUpdate = (\n    updatedComponent: Component<ComponentConfig>\n  ) => {\n    if (!editingComponent) return;\n\n    updateGallery(editingComponent.category, (components) =>\n      components.map((c, i) =>\n        i === editingComponent.index ? updatedComponent : c\n      )\n    );\n    setEditingComponent(null);\n  };\n\n  const handleDetailsSave = () => {\n    const updatedGallery = {\n      ...gallery,\n      config: {\n        ...gallery.config,\n        name: tempName,\n        metadata: {\n          ...gallery.config.metadata,\n          description: tempDescription,\n        },\n      },\n    };\n    onSave(updatedGallery);\n    onDirtyStateChange(true);\n    setIsEditingDetails(false);\n  };\n\n  const handleDownload = () => {\n    const dataStr = JSON.stringify(gallery, null, 2);\n    const dataBlob = new Blob([dataStr], { type: \"application/json\" });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = `${gallery.config.name\n      .toLowerCase()\n      .replace(/\\s+/g, \"_\")}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const tabItems = Object.entries(iconMap).map(([key, Icon]) => ({\n    key,\n    label: (\n      <span className=\"flex items-center gap-2\">\n        <Icon className=\"w-5 h-5\" />\n        {key.charAt(0).toUpperCase() + key.slice(1)}s\n        <span className=\"text-xs font-light text-secondary\">\n          ({gallery.config.components[`${key}s` as CategoryKey].length})\n        </span>\n      </span>\n    ),\n    children: (\n      <div>\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-base font-medium\">\n            {gallery.config.components[`${key}s` as CategoryKey].length}{\" \"}\n            {gallery.config.components[`${key}s` as CategoryKey].length === 1\n              ? key.charAt(0).toUpperCase() + key.slice(1)\n              : key.charAt(0).toUpperCase() + key.slice(1) + \"s\"}\n          </h3>\n          <Button\n            type=\"primary\"\n            icon={<Plus className=\"w-4 h-4\" />}\n            onClick={() => {\n              setActiveTab(key as ComponentTypes);\n              handleAdd();\n            }}\n          >\n            {`Add ${key.charAt(0).toUpperCase() + key.slice(1)}`}\n          </Button>\n        </div>\n        <ComponentGrid\n          items={gallery.config.components[`${key}s` as CategoryKey]}\n          title={key}\n          {...handlers}\n        />\n      </div>\n    ),\n  }));\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4\">\n      <div className=\"relative h-64 rounded bg-secondary overflow-hidden mb-8\">\n        <img\n          src=\"/images/bg/layeredbg.svg\"\n          alt=\"Gallery Banner\"\n          className=\"absolute w-full h-full object-cover\"\n        />\n        <div className=\"relative z-10 p-6 h-full flex flex-col justify-between\">\n          <div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                {isEditingDetails ? (\n                  <Input\n                    value={tempName}\n                    onChange={(e) => setTempName(e.target.value)}\n                    className=\"text-2xl font-medium bg-background/50 backdrop-blur px-2 py-1 rounded w-[400px]\"\n                  />\n                ) : (\n                  <h1 className=\"text-2xl font-medium text-primary\">\n                    {gallery.config.name}\n                  </h1>\n                )}\n                {gallery.config.url && (\n                  <Tooltip title=\"Remote Gallery\">\n                    <Globe className=\"w-5 h-5 text-secondary\" />\n                  </Tooltip>\n                )}\n              </div>\n            </div>\n            {isEditingDetails ? (\n              <TextArea\n                value={tempDescription}\n                onChange={(e) => setTempDescription(e.target.value)}\n                className=\"w-1/2 bg-background/50 backdrop-blur px-2 py-1 rounded mt-2\"\n                rows={2}\n              />\n            ) : (\n              <div className=\"flex flex-col gap-2\">\n                <p className=\"text-secondary w-1/2 mt-2 line-clamp-2\">\n                  {gallery.config.metadata.description}\n                </p>\n                <div className=\"flex gap-0\">\n                  <Tooltip title=\"Edit Gallery\">\n                    <Button\n                      icon={<Edit className=\"w-4 h-4\" />}\n                      onClick={() => setIsEditingDetails(true)}\n                      type=\"text\"\n                      className=\"text-white hover:text-white/80\"\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"Download Gallery\">\n                    <Button\n                      icon={<Download className=\"w-4 h-4\" />}\n                      onClick={handleDownload}\n                      type=\"text\"\n                      className=\"text-white hover:text-white/80\"\n                    />\n                  </Tooltip>\n                </div>\n              </div>\n            )}\n            {isEditingDetails && (\n              <div className=\"flex gap-2 mt-2\">\n                <Button onClick={() => setIsEditingDetails(false)}>\n                  Cancel\n                </Button>\n                <Button type=\"primary\" onClick={handleDetailsSave}>\n                  Save\n                </Button>\n              </div>\n            )}\n          </div>\n          <div className=\"flex gap-2\">\n            <div className=\"bg-tertiary backdrop-blur rounded p-2 flex items-center gap-2\">\n              <Package className=\"w-4 h-4 text-secondary\" />\n              <span className=\"text-sm\">\n                {Object.values(gallery.config.components).reduce(\n                  (sum, arr) => sum + arr.length,\n                  0\n                )}{\" \"}\n                components\n              </span>\n            </div>\n            <div className=\"bg-tertiary backdrop-blur rounded p-2 text-sm\">\n              v{gallery.config.metadata.version}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <Tabs\n        items={tabItems}\n        className=\"gallery-tabs\"\n        size=\"large\"\n        onChange={(key) => setActiveTab(key as ComponentTypes)}\n      />\n\n      <Drawer\n        title=\"Edit Component\"\n        placement=\"right\"\n        size=\"large\"\n        onClose={() => setEditingComponent(null)}\n        open={!!editingComponent}\n        className=\"component-editor-drawer\"\n      >\n        {editingComponent && (\n          <ComponentEditor\n            component={editingComponent.component}\n            onChange={handleComponentUpdate}\n            onClose={() => setEditingComponent(null)}\n            navigationDepth={true}\n          />\n        )}\n      </Drawer>\n    </div>\n  );\n};\n\nexport default GalleryDetail;\n", "import { GalleryConfig } from \"../../types/datamodel\";\n\n// Load and parse the gallery JSON file\nconst loadGalleryFromJson = (): GalleryConfig => {\n  try {\n    // You can adjust the path to your JSON file as needed\n    const galleryJson = require(\"./default_gallery.json\");\n    return galleryJson as GalleryConfig;\n  } catch (error) {\n    console.error(\"Error loading gallery JSON:\", error);\n    throw error;\n  }\n};\n\nexport const defaultGallery: GalleryConfig = loadGalleryFromJson();\n", "import React, { useState, useRef } from \"react\";\nimport { <PERSON><PERSON>, Tabs, Input, Button, Alert, Upload } from \"antd\";\nimport { Globe, Upload as UploadIcon, Code } from \"lucide-react\";\nimport { MonacoEditor } from \"../monaco\";\nimport type { InputRef, UploadFile, UploadProps } from \"antd\";\nimport { defaultGallery } from \"./utils\";\nimport { Gallery, GalleryConfig } from \"../../types/datamodel\";\n\ninterface GalleryCreateModalProps {\n  open: boolean;\n  onCancel: () => void;\n  onCreateGallery: (gallery: Gallery) => void;\n}\n\nexport const GalleryCreateModal: React.FC<GalleryCreateModalProps> = ({\n  open,\n  onCancel,\n  onCreateGallery,\n}) => {\n  const [activeTab, setActiveTab] = useState(\"url\");\n  const [url, setUrl] = useState(\"\");\n  const [jsonContent, setJsonContent] = useState(\n    JSON.stringify(defaultGallery, null, 2)\n  );\n  const [error, setError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const editorRef = useRef(null);\n\n  const handleUrlImport = async () => {\n    setIsLoading(true);\n    setError(\"\");\n    try {\n      const response = await fetch(url);\n      const data = (await response.json()) as GalleryConfig;\n      // TODO: Validate against Gallery schema\n      onCreateGallery({\n        config: data,\n      });\n      onCancel();\n    } catch (err) {\n      setError(\"Failed to fetch or parse gallery from URL\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleFileUpload = (info: { file: UploadFile }) => {\n    const { status, originFileObj } = info.file;\n    if (status === \"done\" && originFileObj instanceof File) {\n      const reader = new FileReader();\n      reader.onload = (e: ProgressEvent<FileReader>) => {\n        try {\n          const content = JSON.parse(\n            e.target?.result as string\n          ) as GalleryConfig;\n\n          // TODO: Validate against Gallery schema\n          onCreateGallery({\n            config: content,\n          });\n          onCancel();\n        } catch (err) {\n          setError(\"Invalid JSON file\");\n        }\n      };\n      reader.readAsText(originFileObj);\n    } else if (status === \"error\") {\n      setError(\"File upload failed\");\n    }\n  };\n\n  const handlePasteImport = () => {\n    try {\n      const content = JSON.parse(jsonContent) as GalleryConfig;\n      // TODO: Validate against Gallery schema\n      onCreateGallery({\n        config: content,\n      });\n      onCancel();\n    } catch (err) {\n      setError(\"Invalid JSON format\");\n    }\n  };\n\n  const uploadProps: UploadProps = {\n    accept: \".json\",\n    showUploadList: false,\n    customRequest: ({ file, onSuccess }) => {\n      setTimeout(() => {\n        onSuccess && onSuccess(\"ok\");\n      }, 0);\n    },\n    onChange: handleFileUpload,\n  };\n\n  const inputRef = useRef<InputRef>(null);\n\n  const items = [\n    {\n      key: \"url\",\n      label: (\n        <span className=\"flex items-center gap-2\">\n          <Globe className=\"w-4 h-4\" /> URL Import\n        </span>\n      ),\n      children: (\n        <div className=\"space-y-4\">\n          <Input\n            ref={inputRef}\n            placeholder=\"Enter gallery URL...\"\n            value={url}\n            onChange={(e) => setUrl(e.target.value)}\n          />\n          <div className=\"text-xs\">\n            Sample\n            <a\n              role=\"button\"\n              onClick={(e) => {\n                setUrl(\n                  \"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json\"\n                );\n                e.preventDefault();\n              }}\n              href=\"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json\"\n              target=\"_blank\"\n              rel=\"noreferrer\"\n              className=\"text-accent\"\n            >\n              {\" \"}\n              gallery.json{\" \"}\n            </a>\n          </div>\n          <Button\n            type=\"primary\"\n            onClick={handleUrlImport}\n            disabled={!url || isLoading}\n            block\n          >\n            Import from URL\n          </Button>\n        </div>\n      ),\n    },\n    {\n      key: \"file\",\n      label: (\n        <span className=\"flex items-center gap-2\">\n          <UploadIcon className=\"w-4 h-4\" /> File Upload\n        </span>\n      ),\n      children: (\n        <div className=\"border-2 border-dashed rounded-lg p-8 text-center space-y-4\">\n          <Upload.Dragger {...uploadProps}>\n            <p className=\"ant-upload-drag-icon\">\n              <UploadIcon className=\"w-8 h-8 mx-auto text-secondary\" />\n            </p>\n            <p className=\"ant-upload-text\">\n              Click or drag JSON file to this area\n            </p>\n          </Upload.Dragger>\n        </div>\n      ),\n    },\n    {\n      key: \"paste\",\n      label: (\n        <span className=\"flex items-center gap-2\">\n          <Code className=\"w-4 h-4\" /> Paste JSON\n        </span>\n      ),\n      children: (\n        <div className=\"space-y-4\">\n          <div className=\"h-64\">\n            <MonacoEditor\n              value={jsonContent}\n              onChange={setJsonContent}\n              editorRef={editorRef}\n              language=\"json\"\n              minimap={false}\n            />\n          </div>\n          <Button type=\"primary\" onClick={handlePasteImport} block>\n            Import JSON\n          </Button>\n        </div>\n      ),\n    },\n  ];\n\n  return (\n    <Modal\n      title=\"Create New Gallery\"\n      open={open}\n      onCancel={onCancel}\n      footer={null}\n      width={800}\n    >\n      <div className=\"mt-4\">\n        <Tabs activeKey={activeTab} onChange={setActiveTab} items={items} />\n\n        {error && (\n          <Alert message={error} type=\"error\" showIcon className=\"mt-4\" />\n        )}\n      </div>\n    </Modal>\n  );\n};\n\nexport default GalleryCreateModal;\n", "import React, { useCallback, useEffect, useState, useContext } from \"react\";\nimport { message, Modal } from \"antd\";\nimport { ChevronRight } from \"lucide-react\";\nimport { appContext } from \"../../../hooks/provider\";\nimport { galleryAPI } from \"./api\";\nimport { GallerySidebar } from \"./sidebar\";\nimport { GalleryDetail } from \"./detail\";\nimport { GalleryCreateModal } from \"./create-modal\";\nimport type { Gallery } from \"../../types/datamodel\";\n\nexport const GalleryManager: React.FC = () => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [galleries, setGalleries] = useState<Gallery[]>([]);\n  const [currentGallery, setCurrentGallery] = useState<Gallery | null>(null);\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\n    if (typeof window !== \"undefined\") {\n      const stored = localStorage.getItem(\"gallerySidebar\");\n      return stored !== null ? JSON.parse(stored) : true;\n    }\n    return true;\n  });\n\n  const { user } = useContext(appContext);\n  const [messageApi, contextHolder] = message.useMessage();\n\n  // Persist sidebar state\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"gallerySidebar\", JSON.stringify(isSidebarOpen));\n    }\n  }, [isSidebarOpen]);\n\n  const fetchGalleries = useCallback(async () => {\n    if (!user?.id) return;\n\n    try {\n      setIsLoading(true);\n      const data = await galleryAPI.listGalleries(user.id);\n      setGalleries(data);\n      if (!currentGallery && data.length > 0) {\n        setCurrentGallery(data[0]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching galleries:\", error);\n      messageApi.error(\"Failed to fetch galleries\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, [user?.id, currentGallery, messageApi]);\n\n  useEffect(() => {\n    fetchGalleries();\n  }, [fetchGalleries]);\n\n  // Handle URL params\n  useEffect(() => {\n    const params = new URLSearchParams(window.location.search);\n    const galleryId = params.get(\"galleryId\");\n\n    if (galleryId && !currentGallery) {\n      const numericId = parseInt(galleryId, 10);\n      if (!isNaN(numericId)) {\n        handleSelectGallery(numericId);\n      }\n    }\n  }, []);\n\n  // Update URL when gallery changes\n  useEffect(() => {\n    if (currentGallery?.id) {\n      window.history.pushState(\n        {},\n        \"\",\n        `?galleryId=${currentGallery.id.toString()}`\n      );\n    }\n  }, [currentGallery?.id]);\n\n  const handleSelectGallery = async (galleryId: number) => {\n    if (!user?.id) return;\n\n    if (hasUnsavedChanges) {\n      Modal.confirm({\n        title: \"Unsaved Changes\",\n        content: \"You have unsaved changes. Do you want to discard them?\",\n        okText: \"Discard\",\n        cancelText: \"Go Back\",\n        onOk: () => {\n          switchToGallery(galleryId);\n          setHasUnsavedChanges(false);\n        },\n      });\n    } else {\n      await switchToGallery(galleryId);\n    }\n  };\n\n  const switchToGallery = async (galleryId: number) => {\n    if (!user?.id) return;\n\n    setIsLoading(true);\n    try {\n      const data = await galleryAPI.getGallery(galleryId, user.id);\n      setCurrentGallery(data);\n    } catch (error) {\n      console.error(\"Error loading gallery:\", error);\n      messageApi.error(\"Failed to load gallery\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCreateGallery = async (galleryData: Gallery) => {\n    if (!user?.id) return;\n\n    galleryData.user_id = user.id;\n    try {\n      const savedGallery = await galleryAPI.createGallery(galleryData, user.id);\n      setGalleries([savedGallery, ...galleries]);\n      setCurrentGallery(savedGallery);\n      setIsCreateModalOpen(false);\n      messageApi.success(\"Gallery created successfully\");\n    } catch (error) {\n      console.error(\"Error creating gallery:\", error);\n      messageApi.error(\"Failed to create gallery\");\n    }\n  };\n\n  const handleUpdateGallery = async (updates: Partial<Gallery>) => {\n    if (!user?.id || !currentGallery?.id) return;\n\n    try {\n      const sanitizedUpdates = {\n        ...updates,\n        created_at: undefined,\n        updated_at: undefined,\n      };\n      const updatedGallery = await galleryAPI.updateGallery(\n        currentGallery.id,\n        sanitizedUpdates,\n        user.id\n      );\n      setGalleries(\n        galleries.map((g) => (g.id === updatedGallery.id ? updatedGallery : g))\n      );\n      setCurrentGallery(updatedGallery);\n      setHasUnsavedChanges(false);\n      messageApi.success(\"Gallery updated successfully\");\n    } catch (error) {\n      console.error(\"Error updating gallery:\", error);\n      messageApi.error(\"Failed to update gallery\");\n    }\n  };\n\n  const handleDeleteGallery = async (galleryId: number) => {\n    if (!user?.id) return;\n\n    try {\n      await galleryAPI.deleteGallery(galleryId, user.id);\n      setGalleries(galleries.filter((g) => g.id !== galleryId));\n      if (currentGallery?.id === galleryId) {\n        setCurrentGallery(null);\n      }\n      messageApi.success(\"Gallery deleted successfully\");\n    } catch (error) {\n      console.error(\"Error deleting gallery:\", error);\n      messageApi.error(\"Failed to delete gallery\");\n    }\n  };\n\n  const handleSyncGallery = async (galleryId: number) => {\n    if (!user?.id) return;\n\n    try {\n      setIsLoading(true);\n      const gallery = galleries.find((g) => g.id === galleryId);\n      if (!gallery?.config.url) return;\n\n      const remoteGallery = await galleryAPI.syncGallery(gallery.config.url);\n      await handleUpdateGallery({\n        ...remoteGallery,\n        id: galleryId,\n        config: {\n          ...remoteGallery.config,\n          metadata: {\n            ...remoteGallery.config.metadata,\n            lastSynced: new Date().toISOString(),\n          },\n        },\n      });\n\n      messageApi.success(\"Gallery synced successfully\");\n    } catch (error) {\n      console.error(\"Error syncing gallery:\", error);\n      messageApi.error(\"Failed to sync gallery\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!user?.id) {\n    return (\n      <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\n        Please log in to view galleries\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative flex h-full w-full\">\n      {contextHolder}\n\n      {/* Create Modal */}\n      <GalleryCreateModal\n        open={isCreateModalOpen}\n        onCancel={() => setIsCreateModalOpen(false)}\n        onCreateGallery={handleCreateGallery}\n      />\n\n      {/* Sidebar */}\n      <div\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\n          isSidebarOpen ? \"w-64\" : \"w-12\"\n        }`}\n      >\n        <GallerySidebar\n          isOpen={isSidebarOpen}\n          galleries={galleries}\n          currentGallery={currentGallery}\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\n          onSelectGallery={(gallery) => handleSelectGallery(gallery.id!)}\n          onCreateGallery={() => setIsCreateModalOpen(true)}\n          onDeleteGallery={handleDeleteGallery}\n          onSyncGallery={handleSyncGallery}\n          isLoading={isLoading}\n        />\n      </div>\n\n      {/* Main Content */}\n      <div\n        className={`flex-1 transition-all -mr-6 duration-200 ${\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\n        }`}\n      >\n        <div className=\"p-4 pt-2\">\n          {/* Breadcrumb */}\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\n            <span className=\"text-primary font-medium\">Galleries</span>\n            {currentGallery && (\n              <>\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\n                <span className=\"text-secondary\">\n                  {currentGallery.config.name}\n                </span>\n              </>\n            )}\n          </div>\n\n          {/* Content Area */}\n          {isLoading && !currentGallery ? (\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\n              Loading galleries...\n            </div>\n          ) : currentGallery ? (\n            <GalleryDetail\n              gallery={currentGallery}\n              onSave={handleUpdateGallery}\n              onDirtyStateChange={setHasUnsavedChanges}\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\n              Select a gallery from the sidebar or create a new one\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GalleryManager;\n", "import * as React from \"react\";\nimport Layout from \"../components/layout\";\nimport { graphql } from \"gatsby\";\nimport GalleryManager from \"../components/views/gallery/manager\";\n\n// markup\nconst GalleryPage = ({ data }: any) => {\n  return (\n    <Layout meta={data.site.siteMetadata} title=\"Home\" link={\"/gallery\"}>\n      <main style={{ height: \"100%\" }} className=\" h-full \">\n        <GalleryManager />\n      </main>\n    </Layout>\n  );\n};\n\nexport const query = graphql`\n  query HomePageQuery {\n    site {\n      siteMetadata {\n        description\n        title\n      }\n    }\n  }\n`;\n\nexport default GalleryPage;\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Copy = createLucideIcon(\"Copy\", [\n  [\"rect\", { width: \"14\", height: \"14\", x: \"8\", y: \"8\", rx: \"2\", ry: \"2\", key: \"17jyea\" }],\n  [\"path\", { d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\", key: \"zix9uf\" }]\n]);\n\nexport { Copy as default };\n//# sourceMappingURL=copy.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Users = createLucideIcon(\"Users\", [\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }],\n  [\"path\", { d: \"M22 21v-2a4 4 0 0 0-3-3.87\", key: \"kshegd\" }],\n  [\"path\", { d: \"M16 3.13a4 4 0 0 1 0 7.75\", key: \"1da9ce\" }]\n]);\n\nexport { Users as default };\n//# sourceMappingURL=users.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Wrench = createLucideIcon(\"Wrench\", [\n  [\n    \"path\",\n    {\n      d: \"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\",\n      key: \"cbrjhi\"\n    }\n  ]\n]);\n\nexport { Wrench as default };\n//# sourceMappingURL=wrench.js.map\n", "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genAlertTypeStyle = (bgColor, borderColor, iconColor, token, alertCls) => ({\n  background: bgColor,\n  border: `${unit(token.lineWidth)} ${token.lineType} ${borderColor}`,\n  [`${alertCls}-icon`]: {\n    color: iconColor\n  }\n});\nexport const genBaseStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow: duration,\n    marginXS,\n    marginSM,\n    fontSize,\n    fontSizeLG,\n    lineHeight,\n    borderRadiusLG: borderRadius,\n    motionEaseInOutCirc,\n    withDescriptionIconSize,\n    colorText,\n    colorTextHeading,\n    withDescriptionPadding,\n    defaultPadding\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      padding: defaultPadding,\n      wordWrap: 'break-word',\n      borderRadius,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-content`]: {\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginXS,\n        lineHeight: 0\n      },\n      '&-description': {\n        display: 'none',\n        fontSize,\n        lineHeight\n      },\n      '&-message': {\n        color: colorTextHeading\n      },\n      [`&${componentCls}-motion-leave`]: {\n        overflow: 'hidden',\n        opacity: 1,\n        transition: `max-height ${duration} ${motionEaseInOutCirc}, opacity ${duration} ${motionEaseInOutCirc},\n        padding-top ${duration} ${motionEaseInOutCirc}, padding-bottom ${duration} ${motionEaseInOutCirc},\n        margin-bottom ${duration} ${motionEaseInOutCirc}`\n      },\n      [`&${componentCls}-motion-leave-active`]: {\n        maxHeight: 0,\n        marginBottom: '0 !important',\n        paddingTop: 0,\n        paddingBottom: 0,\n        opacity: 0\n      }\n    }),\n    [`${componentCls}-with-description`]: {\n      alignItems: 'flex-start',\n      padding: withDescriptionPadding,\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginSM,\n        fontSize: withDescriptionIconSize,\n        lineHeight: 0\n      },\n      [`${componentCls}-message`]: {\n        display: 'block',\n        marginBottom: marginXS,\n        color: colorTextHeading,\n        fontSize: fontSizeLG\n      },\n      [`${componentCls}-description`]: {\n        display: 'block',\n        color: colorText\n      }\n    },\n    [`${componentCls}-banner`]: {\n      marginBottom: 0,\n      border: '0 !important',\n      borderRadius: 0\n    }\n  };\n};\nexport const genTypeStyle = token => {\n  const {\n    componentCls,\n    colorSuccess,\n    colorSuccessBorder,\n    colorSuccessBg,\n    colorWarning,\n    colorWarningBorder,\n    colorWarningBg,\n    colorError,\n    colorErrorBorder,\n    colorErrorBg,\n    colorInfo,\n    colorInfoBorder,\n    colorInfoBg\n  } = token;\n  return {\n    [componentCls]: {\n      '&-success': genAlertTypeStyle(colorSuccessBg, colorSuccessBorder, colorSuccess, token, componentCls),\n      '&-info': genAlertTypeStyle(colorInfoBg, colorInfoBorder, colorInfo, token, componentCls),\n      '&-warning': genAlertTypeStyle(colorWarningBg, colorWarningBorder, colorWarning, token, componentCls),\n      '&-error': Object.assign(Object.assign({}, genAlertTypeStyle(colorErrorBg, colorErrorBorder, colorError, token, componentCls)), {\n        [`${componentCls}-description > pre`]: {\n          margin: 0,\n          padding: 0\n        }\n      })\n    }\n  };\n};\nexport const genActionStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    motionDurationMid,\n    marginXS,\n    fontSizeIcon,\n    colorIcon,\n    colorIconHover\n  } = token;\n  return {\n    [componentCls]: {\n      '&-action': {\n        marginInlineStart: marginXS\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: marginXS,\n        padding: 0,\n        overflow: 'hidden',\n        fontSize: fontSizeIcon,\n        lineHeight: unit(fontSizeIcon),\n        backgroundColor: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        [`${iconCls}-close`]: {\n          color: colorIcon,\n          transition: `color ${motionDurationMid}`,\n          '&:hover': {\n            color: colorIconHover\n          }\n        }\n      },\n      '&-close-text': {\n        color: colorIcon,\n        transition: `color ${motionDurationMid}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const paddingHorizontal = 12; // Fixed value here.\n  return {\n    withDescriptionIconSize: token.fontSizeHeading3,\n    defaultPadding: `${token.paddingContentVerticalSM}px ${paddingHorizontal}px`,\n    withDescriptionPadding: `${token.paddingMD}px ${token.paddingContentHorizontalLG}px`\n  };\n};\nexport default genStyleHooks('Alert', token => [genBaseStyle(token), genTypeStyle(token), genActionStyle(token)], prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport InfoCircleFilled from \"@ant-design/icons/es/icons/InfoCircleFilled\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst iconMapFilled = {\n  success: CheckCircleFilled,\n  info: InfoCircleFilled,\n  error: CloseCircleFilled,\n  warning: ExclamationCircleFilled\n};\nconst IconNode = props => {\n  const {\n    icon,\n    prefixCls,\n    type\n  } = props;\n  const iconType = iconMapFilled[type] || null;\n  if (icon) {\n    return replaceElement(icon, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-icon`\n    }, icon), () => ({\n      className: classNames(`${prefixCls}-icon`, icon.props.className)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(iconType, {\n    className: `${prefixCls}-icon`\n  });\n};\nconst CloseIconNode = props => {\n  const {\n    isClosable,\n    prefixCls,\n    closeIcon,\n    handleClose,\n    ariaProps\n  } = props;\n  const mergedCloseIcon = closeIcon === true || closeIcon === undefined ? /*#__PURE__*/React.createElement(CloseOutlined, null) : closeIcon;\n  return isClosable ? (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\",\n    onClick: handleClose,\n    className: `${prefixCls}-close-icon`,\n    tabIndex: 0\n  }, ariaProps), mergedCloseIcon)) : null;\n};\nconst Alert = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      description,\n      prefixCls: customizePrefixCls,\n      message,\n      banner,\n      className,\n      rootClassName,\n      style,\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      afterClose,\n      showIcon,\n      closable,\n      closeText,\n      closeIcon,\n      action,\n      id\n    } = props,\n    otherProps = __rest(props, [\"description\", \"prefixCls\", \"message\", \"banner\", \"className\", \"rootClassName\", \"style\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"afterClose\", \"showIcon\", \"closable\", \"closeText\", \"closeIcon\", \"action\", \"id\"]);\n  const [closed, setClosed] = React.useState(false);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Alert');\n    warning.deprecated(!closeText, 'closeText', 'closable.closeIcon');\n  }\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const {\n    getPrefixCls,\n    direction,\n    closable: contextClosable,\n    closeIcon: contextCloseIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('alert');\n  const prefixCls = getPrefixCls('alert', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const handleClose = e => {\n    var _a;\n    setClosed(true);\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props, e);\n  };\n  const type = React.useMemo(() => {\n    if (props.type !== undefined) {\n      return props.type;\n    }\n    // banner mode defaults to 'warning'\n    return banner ? 'warning' : 'info';\n  }, [props.type, banner]);\n  // closeable when closeText or closeIcon is assigned\n  const isClosable = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) return true;\n    if (closeText) {\n      return true;\n    }\n    if (typeof closable === 'boolean') {\n      return closable;\n    }\n    // should be true when closeIcon is 0 or ''\n    if (closeIcon !== false && closeIcon !== null && closeIcon !== undefined) {\n      return true;\n    }\n    return !!contextClosable;\n  }, [closeText, closeIcon, closable, contextClosable]);\n  // banner mode defaults to Icon\n  const isShowIcon = banner && showIcon === undefined ? true : showIcon;\n  const alertCls = classNames(prefixCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-description`]: !!description,\n    [`${prefixCls}-no-icon`]: !isShowIcon,\n    [`${prefixCls}-banner`]: !!banner,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, cssVarCls, hashId);\n  const restProps = pickAttrs(otherProps, {\n    aria: true,\n    data: true\n  });\n  const mergedCloseIcon = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) {\n      return closable.closeIcon;\n    }\n    if (closeText) {\n      return closeText;\n    }\n    if (closeIcon !== undefined) {\n      return closeIcon;\n    }\n    if (typeof contextClosable === 'object' && contextClosable.closeIcon) {\n      return contextClosable.closeIcon;\n    }\n    return contextCloseIcon;\n  }, [closeIcon, closable, closeText, contextCloseIcon]);\n  const mergedAriaProps = React.useMemo(() => {\n    const merged = closable !== null && closable !== void 0 ? closable : contextClosable;\n    if (typeof merged === 'object') {\n      const {\n          closeIcon: _\n        } = merged,\n        ariaProps = __rest(merged, [\"closeIcon\"]);\n      return ariaProps;\n    }\n    return {};\n  }, [closable, contextClosable]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !closed,\n    motionName: `${prefixCls}-motion`,\n    motionAppear: false,\n    motionEnter: false,\n    onLeaveStart: node => ({\n      maxHeight: node.offsetHeight\n    }),\n    onLeaveEnd: afterClose\n  }, (_ref, setRef) => {\n    let {\n      className: motionClassName,\n      style: motionStyle\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n      id: id,\n      ref: composeRef(internalRef, setRef),\n      \"data-show\": !closed,\n      className: classNames(alertCls, motionClassName),\n      style: Object.assign(Object.assign(Object.assign({}, contextStyle), style), motionStyle),\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      role: \"alert\"\n    }, restProps), isShowIcon ? (/*#__PURE__*/React.createElement(IconNode, {\n      description: description,\n      icon: props.icon,\n      prefixCls: prefixCls,\n      type: type\n    })) : null, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-content`\n    }, message ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-message`\n    }, message) : null, description ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-description`\n    }, description) : null), action ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-action`\n    }, action) : null, /*#__PURE__*/React.createElement(CloseIconNode, {\n      isClosable: isClosable,\n      prefixCls: prefixCls,\n      closeIcon: mergedCloseIcon,\n      handleClose: handleClose,\n      ariaProps: mergedAriaProps\n    }));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Alert.displayName = 'Alert';\n}\nexport default Alert;", "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };", "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Download = createLucideIcon(\"Download\", [\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }],\n  [\"polyline\", { points: \"7 10 12 15 17 10\", key: \"2ggqvy\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"15\", y2: \"3\", key: \"1vk2je\" }]\n]);\n\nexport { Download as default };\n//# sourceMappingURL=download.js.map\n"], "names": ["RefreshCw", "createLucideIcon", "d", "key", "Globe", "cx", "cy", "r", "Package", "GallerySidebar", "_ref", "isOpen", "galleries", "currentGallery", "onToggle", "onSelectGallery", "onCreateGallery", "onDeleteGallery", "onSyncGallery", "isLoading", "React", "className", "length", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "<PERSON><PERSON>", "type", "icon", "Plus", "Info", "map", "gallery", "id", "config", "components", "name", "url", "size", "e", "stopPropagation", "danger", "disabled", "Trash2", "metadata", "version", "Object", "values", "reduce", "sum", "arr", "updated_at", "getRelativeTimeString", "PanelLeftOpen", "Trash", "ComponentCard", "item", "onEdit", "onDuplicate", "onDelete", "index", "allowDelete", "provider", "Copy", "Edit", "label", "TruncatableText", "content", "description", "showFullscreen", "textT<PERSON><PERSON>old", "ComponentGrid", "_ref2", "items", "actions", "idx", "assign", "iconMap", "team", "Users", "agent", "Bot", "tool", "<PERSON><PERSON>", "model", "Brain", "termination", "Timer", "defaultConfigs", "selector_prompt", "participants", "api_key", "source_code", "global_imports", "has_cancellation_support", "max_messages", "GalleryDetail", "_ref3", "onSave", "onDirtyStateChange", "editingComponent", "setEditingComponent", "useState", "activeTab", "setActiveTab", "isEditingDetails", "setIsEditingDetails", "tempName", "setTempName", "tempDescription", "setTempDescription", "useEffect", "updateGallery", "category", "updater", "updatedGallery", "handlers", "component", "_component$label", "baseLabel", "replace", "nextNumber", "Math", "max", "apply", "_toConsumableArray", "c", "_c$label", "match", "RegExp", "parseInt", "filter", "n", "isNaN", "concat", "_", "i", "tabItems", "entries", "_ref4", "Icon", "char<PERSON>t", "toUpperCase", "slice", "children", "handleAdd", "newComponent", "new<PERSON>abel", "component_type", "newComponents", "src", "alt", "Input", "value", "onChange", "target", "TextArea", "rows", "Download", "handleDownload", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "toLowerCase", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDetailsSave", "Tabs", "Drawer", "placement", "onClose", "open", "ComponentEditor", "updatedComponent", "navigationDepth", "defaultGallery", "loadGalleryFromJson", "require", "error", "console", "GalleryCreateModal", "onCancel", "setUrl", "json<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setError", "setIsLoading", "editor<PERSON><PERSON>", "useRef", "uploadProps", "accept", "showUploadList", "customRequest", "file", "onSuccess", "setTimeout", "info", "status", "originFileObj", "File", "reader", "FileReader", "onload", "_e$target", "parse", "result", "err", "readAsText", "inputRef", "ref", "placeholder", "role", "preventDefault", "rel", "async", "response", "fetch", "data", "json", "block", "UploadIcon", "Upload", "<PERSON><PERSON>", "Code", "MonacoEditor", "language", "minimap", "handlePasteImport", "Modal", "footer", "width", "active<PERSON><PERSON>", "<PERSON><PERSON>", "message", "showIcon", "GalleryManager", "setGalleries", "setCurrentGallery", "isCreateModalOpen", "setIsCreateModalOpen", "hasUnsavedChanges", "setHasUnsavedChanges", "isSidebarOpen", "setIsSidebarOpen", "window", "stored", "localStorage", "getItem", "user", "useContext", "appContext", "messageApi", "contextHolder", "useMessage", "setItem", "fetchGalleries", "useCallback", "galleryAPI", "listGalleries", "galleryId", "URLSearchParams", "location", "search", "get", "numericId", "handleSelectGallery", "history", "pushState", "toString", "confirm", "okText", "cancelText", "onOk", "switchToGallery", "getGallery", "handleUpdateGallery", "sanitizedUpdates", "updates", "created_at", "undefined", "g", "success", "galleryData", "user_id", "savedGallery", "createGallery", "deleteGallery", "find", "remoteGallery", "syncGallery", "lastSynced", "Date", "toISOString", "ChevronRight", "Layout", "meta", "site", "siteMetadata", "style", "height", "x", "y", "rx", "ry", "genAlertTypeStyle", "bgColor", "borderColor", "iconColor", "token", "alertCls", "background", "border", "lineWidth", "lineType", "color", "genBaseStyle", "componentCls", "motionDurationSlow", "duration", "marginXS", "marginSM", "fontSize", "fontSizeLG", "lineHeight", "borderRadiusLG", "borderRadius", "motionEaseInOutCirc", "withDescriptionIconSize", "colorText", "colorTextHeading", "withDescriptionPadding", "defaultPadding", "position", "display", "alignItems", "padding", "wordWrap", "direction", "flex", "min<PERSON><PERSON><PERSON>", "marginInlineEnd", "overflow", "opacity", "transition", "maxHeight", "marginBottom", "paddingTop", "paddingBottom", "genTypeStyle", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "margin", "genActionStyle", "iconCls", "motionDurationMid", "fontSizeIcon", "colorIcon", "colorIconHover", "marginInlineStart", "backgroundColor", "outline", "cursor", "fontSizeHeading3", "paddingContentVerticalSM", "paddingMD", "paddingContentHorizontalLG", "__rest", "s", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "iconMapFilled", "CheckCircleFilled", "InfoCircleFilled", "CloseCircleFilled", "warning", "ExclamationCircleFilled", "IconNode", "props", "prefixCls", "iconType", "CloseIconNode", "isClosable", "closeIcon", "handleClose", "ariaProps", "mergedCloseIcon", "CloseOutlined", "tabIndex", "customizePrefixCls", "banner", "rootClassName", "onMouseEnter", "onMouseLeave", "afterClose", "closable", "closeText", "action", "otherProps", "closed", "setClosed", "internalRef", "nativeElement", "current", "getPrefixCls", "contextClosable", "contextCloseIcon", "contextClassName", "contextStyle", "wrapCSSVar", "hashId", "cssVarCls", "_a", "isShowIcon", "restProps", "pickAttrs", "aria", "mergedAriaProps", "merged", "visible", "motionName", "motionAppear", "motionEnter", "onLeaveStart", "node", "offsetHeight", "onLeaveEnd", "setRef", "motionClassName", "motionStyle", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "o", "this", "arguments", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "state", "componentStack", "setState", "errorMessage", "errorDescription", "overflowX", "points", "x1", "x2", "y1", "y2"], "sourceRoot": ""}