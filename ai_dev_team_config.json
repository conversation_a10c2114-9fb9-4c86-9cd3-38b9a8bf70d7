{"provider": "autogen_agentchat.teams.SelectorGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A comprehensive team of 8 specialized agents for building Python AI applications with FastAPI, RAG, vector analysis, SMS integration, and Supabase backend - including architecture design, development, testing, and deployment specialists.", "label": "AI_Application_Development_Team", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Lead architect specializing in AI application design and system architecture", "label": "AI_Architect", "config": {"name": "ai_architect", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create architecture diagrams and system designs", "label": "ArchitectureTool", "config": {"source_code": "async def create_architecture_diagram(components: List[str], connections: List[Dict[str, str]], output_format: str = 'mermaid') -> str:\n    \"\"\"Create system architecture diagrams\n    \n    Args:\n        components: List of system components\n        connections: List of connections between components\n        output_format: Output format (mermaid, plantuml)\n    \n    Returns:\n        str: Architecture diagram in specified format\n    \"\"\"\n    if output_format == 'mermaid':\n        diagram = 'graph TD\\n'\n        for component in components:\n            diagram += f'    {component.replace(\" \", \"_\")}[\"{component}\"]\\n'\n        for conn in connections:\n            source = conn['from'].replace(' ', '_')\n            target = conn['to'].replace(' ', '_')\n            label = conn.get('label', '')\n            diagram += f'    {source} --> {target}'\n            if label:\n                diagram += f' : {label}'\n            diagram += '\\n'\n        return diagram\n    return 'Architecture diagram created'", "name": "create_architecture_diagram", "description": "Create system architecture diagrams for AI applications", "global_imports": [{"module": "typing", "imports": ["List", "Dict"]}], "has_cancellation_support": false}}]}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "AI Architecture Specialist - designs scalable AI application architectures", "system_message": "You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:\n1. Design overall system architecture for AI applications\n2. Plan integration patterns for FastAPI, RAG, vector databases, and external services\n3. Define data flow and component interactions\n4. Ensure scalability, security, and performance considerations\n5. Create technical specifications and architecture diagrams\n6. Guide technology stack decisions\n7. Plan deployment and infrastructure requirements\nFocus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "FastAPI and backend development specialist", "label": "FastAPI_Developer", "config": {"name": "fastapi_developer", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench for FastAPI development tools", "label": "StaticWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Generate FastAPI code templates", "label": "FastAPIGenerator", "config": {"source_code": "async def generate_fastapi_template(endpoints: List[Dict[str, str]], auth_type: str = 'jwt') -> str:\n    \"\"\"Generate FastAPI application template\n    \n    Args:\n        endpoints: List of API endpoints with methods and descriptions\n        auth_type: Authentication type (jwt, oauth2, api_key)\n    \n    Returns:\n        str: FastAPI application template code\n    \"\"\"\n    template = '''from fastapi import FastAPI, Depends, HTTPException, status\nfrom fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>\nfrom fastapi.middleware.cors import CORSMiddleware\nfrom pydantic import BaseModel\nimport uvicorn\n\napp = FastAPI(title=\"AI Application API\", version=\"1.0.0\")\n\n# CORS middleware\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\"*\"],\n    allow_credentials=True,\n    allow_methods=[\"*\"],\n    allow_headers=[\"*\"],\n)\n\n# Security\nsecurity = HTTPBearer()\n\n'''\n    \n    for endpoint in endpoints:\n        method = endpoint.get('method', 'GET').lower()\n        path = endpoint.get('path', '/')\n        description = endpoint.get('description', '')\n        \n        template += f'''\n@app.{method}(\"{path}\")\nasync def {path.replace('/', '_').replace('-', '_').strip('_')}():\n    \"\"\"{description}\"\"\"\n    return {{\"message\": \"Endpoint implementation needed\"}}\n'''\n    \n    template += '''\\nif __name__ == \"__main__\":\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000)'''\n    \n    return template", "name": "generate_fastapi_template", "description": "Generate FastAPI application templates with endpoints", "global_imports": [{"module": "typing", "imports": ["List", "Dict"]}], "has_cancellation_support": false}}]}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "FastAPI Backend Development Specialist", "system_message": "You are a FastAPI Backend Development Specialist. Your expertise includes:\n1. Building high-performance FastAPI applications\n2. Implementing RESTful APIs with proper documentation\n3. Database integration with Supabase and PostgreSQL\n4. Authentication and authorization (JWT, OAuth2)\n5. Middleware implementation and request/response handling\n6. API versioning and rate limiting\n7. WebSocket implementation for real-time features\n8. Integration with external services and APIs\n9. Performance optimization and caching strategies\nFocus on clean, maintainable code following FastAPI best practices.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}], "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o"}}, "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 50, "include_agent_event": false}}]}}, "selector_prompt": "You are coordinating an AI application development team by selecting the team member to speak/act next. The following specialized roles are available:\\n    {roles}\\n\\n    **Role Descriptions:**\\n    - ai_architect: Designs system architecture, plans integrations, creates technical specifications\\n    - fastapi_developer: Builds FastAPI backend, implements APIs, handles authentication and middleware\\n\\n    **Selection Strategy:**\\n    1. **Planning Phase**: Start with ai_architect for system design\\n    2. **Development Phase**: Rotate between specialists based on current task\\n\\n    **Guidelines:**\\n    - Select ai_architect for high-level design decisions and architecture planning\\n    - Choose technical specialists when working on their domain-specific components\\n\\n    Based on the conversation context and current development needs, select the most appropriate specialist.\\n\\n    {history}\\n\\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.", "allow_repeated_speaker": true, "max_selector_attempts": 3, "emit_team_events": false, "model_client_streaming": false}}