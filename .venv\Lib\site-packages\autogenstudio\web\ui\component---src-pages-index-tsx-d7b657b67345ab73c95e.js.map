{"version": 3, "file": "component---src-pages-index-tsx-d7b657b67345ab73c95e.js", "mappings": "0TAYO,MAAMA,EAA8CC,IAMpD,IANqD,QAC1DC,EAAO,OACPC,EAAM,SACNC,EAAQ,OACRC,EAAM,MACNC,GACDL,EACC,MAAOM,GAAQC,EAAAA,EAAKC,WACd,EAACC,EAAQ,EAACC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAYC,GAAiBC,EAAAA,GAAQC,cAG5CC,EAAAA,EAAAA,YAAU,KACJZ,EACFE,EAAKW,eAAe,CAClBC,MAAMjB,aAAO,EAAPA,EAASiB,OAAQ,GACvBC,SAASlB,aAAO,EAAPA,EAASkB,eAAWC,IAG/Bd,EAAKe,aACP,GACC,CAACf,EAAML,EAASG,IAEnB,MAuBMkB,GAAcb,GAA4B,IAAjBJ,EAAMkB,OAErC,OACEC,EAAAA,cAACC,EAAAA,EAAK,CACJC,MAAOzB,EAAU,eAAiB,iBAClC0B,KAAMvB,EACND,SAAUA,EACVyB,OAAQ,KACRC,UAAU,eACVC,aAAW,GAEVjB,EACDW,EAAAA,cAACjB,EAAAA,EAAI,CACHD,KAAMA,EACNY,KAAK,eACLa,OAAO,WACPC,SAvC6CC,UACjD,UACQ/B,EAAO,IACRgC,EACHC,GAAIlC,aAAO,EAAPA,EAASkC,KAEfvB,EAAWwB,QACT,WAAWnC,EAAU,UAAY,yBAErC,CAAE,MAAOoC,GACHA,aAAiBC,OACnB1B,EAAWyB,MAAMA,EAAMvB,QAE3B,GA2BIyB,eAvBJC,IAEA5B,EAAWyB,MAAM,oCACjBI,QAAQJ,MAAM,0BAA2BG,EAAU,EAqB/CE,aAAa,OAEblB,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CACRC,MAAM,eACN1B,KAAK,OACL2B,MAAO,CACL,CAAEC,UAAU,EAAMhC,QAAS,+BAC3B,CAAEiC,IAAK,IAAKjC,QAAS,+CAGvBU,EAAAA,cAACwB,EAAAA,EAAK,OAGRxB,EAAAA,cAAA,OAAKK,UAAU,sBACbL,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CACRd,UAAU,SACVe,MAAM,OACN1B,KAAK,UACL2B,MAAO,CAAC,CAAEC,UAAU,EAAMhC,QAAS,0BAEnCU,EAAAA,cAACyB,EAAAA,EAAM,CACLC,YAAY,gBACZzC,QAASA,EACT0C,SAAU1C,GAAWa,EACrB8B,YAAU,EACVC,iBAAiB,WACjBC,aAAcA,CAACC,EAAOC,KAAM,IAAAC,EAAA,OACZ,QAAdA,EAACD,aAAM,EAANA,EAAQZ,aAAK,IAAAa,EAAAA,EAAI,IACfC,cACAC,SAASJ,EAAMG,cAAc,EAElCE,QAASvD,EAAMwD,KAAKC,IAAI,CACtBC,MAAOD,EAAK3B,GACZS,MAAO,GAAGkB,EAAKE,UAAUpB,UAAUkB,EAAKE,UAAUC,sBAEpDC,gBAAiBzD,EAAUe,EAAAA,cAAC2C,EAAAA,EAAI,CAACC,KAAK,UAAa,SAKzD5C,EAAAA,cAAA,OAAKK,UAAU,wBACbL,EAAAA,cAAC6C,EAAAA,KAAI,CAACC,GAAG,UAAS,mBAGnBhD,GACCE,EAAAA,cAAA,OAAKK,UAAU,mFACbL,EAAAA,cAAC+C,EAAAA,EAAiB,CAAC1C,UAAU,YAC7BL,EAAAA,cAAA,YAAM,gDAIVA,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CAACd,UAAU,yBACnBL,EAAAA,cAAA,OAAKK,UAAU,cACbL,EAAAA,cAACgD,EAAAA,GAAM,CAACC,QAAStE,GAAU,UAC3BqB,EAAAA,cAACgD,EAAAA,GAAM,CAACE,KAAK,UAAUC,SAAS,SAASxB,SAAU7B,GAChDrB,EAAU,SAAW,aAKxB,EAIZ,I,oKCiBA,MAhJ2BD,IAIK,IAJJ,MAC1BK,EAAK,UACLuE,EAAS,eACTC,GACwB7E,EACxB,MAAM,EAAC8E,EAAe,EAACC,IAAqBpE,EAAAA,EAAAA,aACtC,EAACqE,EAAe,EAACC,IAAqBtE,EAAAA,EAAAA,WAC1C,KACE,GAAsB,oBAAXuE,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAOF,EAASG,SAASH,QAAU/D,CACrC,MAGE,EAACmE,EAAO,EAACC,IAAa7E,EAAAA,EAAAA,UAAiB,IAGvC8E,EAAgBpF,EAAMqF,QAAQ5B,IAAU,IAAD6B,EAAAC,EAC3C,OACsB,QAApBD,EAAA7B,EAAKE,UAAUpB,aAAK,IAAA+C,OAAA,EAApBA,EAAsBjC,cAAcC,SAAS4B,EAAO7B,kBAC1B,QADwCkC,EAClE9B,EAAKE,UAAU6B,mBAAW,IAAAD,OAAA,EAA1BA,EAA4BlC,cAAcC,SAAS4B,EAAO7B,eAAc,KAK5E1C,EAAAA,EAAAA,YAAU,KACJgE,GAAkB3E,EAAMyF,MAAMhC,GAASA,EAAK3B,KAAO6C,IACrDD,EAAkBC,GACT3E,EAAMkB,OAAS,GACxBwD,EAAkB1E,EAAM,GAAG8B,GAC7B,GACC,CAAC9B,EAAO2E,IAEX,MAqCM1D,GAAcsD,GAA8B,IAAjBvE,EAAMkB,OAkCjCwE,EAAY,CAChBC,MAjC6B,CAC7B,CACEtB,KAAM,QACN9B,MACEpB,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKK,UAAU,+BAA8B,iBAC7CL,EAAAA,cAACwB,EAAAA,EAAK,CACJiD,OAAQzE,EAAAA,cAAC0E,EAAAA,EAAU,CAACrE,UAAU,YAC9BqB,YAAY,eACZiD,SAAWC,GAAMZ,EAAUY,EAAEC,OAAOtC,UAI1CuC,IAAK,aAEP,CACE5B,KAAM,YACP6B,QAAAC,EAAAA,EAAAA,GACEf,EAAc5B,KAAKC,IAAI,IAAA2C,EAAA,MAAM,CAC9B7D,MACEpB,EAAAA,cAAA,WACEA,EAAAA,cAAA,YAAMkF,EAAAA,EAAAA,IAAa5C,EAAKE,UAAUpB,OAAS,GAAI,KAC/CpB,EAAAA,cAAA,OAAKK,UAAU,0BACZiC,EAAKE,UAAUC,iBAItBqC,KAAKxC,SAAQ,QAAJ2C,EAAJ3C,EAAM3B,UAAE,IAAAsE,OAAJ,EAAJA,EAAUE,aAAc,GAC7BC,KAAMpF,EAAAA,cAACqF,EAAAA,EAAG,CAAChF,UAAU,YACtB,MAKD4C,QA1D4CxC,UAC5C,MAAM6E,EAAYxB,SAASc,EAAEE,KACRjG,EAAM0G,MAAMjD,GAASA,EAAK3B,KAAO2E,IAQtD/B,EAAkB+B,GALhBrE,QAAQJ,MAAM,2BAA4ByE,EAKhB,GAmDxBE,EAAe3G,EAAM0G,MAAMjD,GAASA,EAAK3B,KAAO2C,IAEtD,OACEtD,EAAAA,cAAA,OAAKK,UAAU,oBACbL,EAAAA,cAACyF,EAAAA,EAASzC,OAAM,CACd0C,KAAMnB,EACNrB,KAAK,UACL7C,UAAU,SACVsF,UAAU,cACVP,KAAMpF,EAAAA,cAAC4F,EAAAA,EAAW,CAACvF,UAAU,YAC7B4C,QAtFqBxC,UACzB,IAAK6C,EAAgB,OAEC,oBAAXI,QACTE,aAAaiC,QAAQ,iBAAkBvC,EAAe6B,YAGxD,MAAMK,EAAe3G,EAAM0G,MAAMjD,GAASA,EAAK3B,KAAO2C,IACjDkC,UAGC,IAAIM,SAASC,GAAYC,WAAWD,EAAS,OACnD1C,EAAeC,EAAgBkC,EAAahD,UAAUpB,OAAS,IAAG,EA2E9DO,UAAW2B,GAAkBF,GAE7BpD,EAAAA,cAAA,OAAKK,UAAU,GAAG4F,MAAO,CAAEC,MAAO,UAChClG,EAAAA,cAACmG,EAAAA,EAAI,CAAC9F,UAAU,+BAA+B,iBAInDL,EAAAA,cAAA,OACEK,UAAU,yBACVH,MAAOsF,aAAY,EAAZA,EAAchD,UAAUpB,QAE9B8D,EAAAA,EAAAA,KAAaM,aAAY,EAAZA,EAAchD,UAAUpB,QAAS,GAAI,KAGpDtB,GACCE,EAAAA,cAAA,OAAKK,UAAU,0DACbL,EAAAA,cAACoG,EAAAA,EAAQ,CAAC/F,UAAU,YACpBL,EAAAA,cAAA,YAAM,iCAGN,EC5HH,MAAMqG,EAAkC7H,IAWxC,IAXyC,OAC9CI,EAAM,SACN0H,EAAQ,eACRC,EAAc,SACdC,EAAQ,gBACRC,EAAe,cACfC,EAAa,gBACbC,EAAe,UACfvD,GAAY,EAAK,eACjBC,EAAc,MACdxE,GACDL,EACC,OAAKI,EAiCHoB,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,kFACbL,EAAAA,cAAA,OAAKK,UAAU,2BACbL,EAAAA,cAAA,QAAMK,UAAU,4BAA2B,YAC3CL,EAAAA,cAAA,QAAMK,UAAU,wDACbiG,EAASvG,SAGdC,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,iBACbF,EAAAA,cAAA,UACEiD,QAASuD,EACTnG,UAAU,gKAEVL,EAAAA,cAAC6G,EAAAA,EAAc,CAACC,YAAa,IAAKzG,UAAU,eAKlDL,EAAAA,cAAA,OAAKK,UAAU,uBACbL,EAAAA,cAAA,OAAKK,UAAU,qBACZzB,GACCoB,EAAAA,cAAC+G,EAAkB,CACjBlI,MAAOA,EACPuE,UAAWA,EACXC,eAAgBA,MAMxBrD,EAAAA,cAAA,OAAKK,UAAU,oCACbL,EAAAA,cAACgH,EAAAA,EAAO,CAAC3G,UAAU,gCACnBL,EAAAA,cAAA,OAAKK,UAAU,wBAAuB,UAC5B,IACRL,EAAAA,cAAA,QAAMK,UAAU,mCACb,IAAI,IACHiG,EAASvG,OAAO,IAAE,KACd,KAGTqD,GACCpD,EAAAA,cAACiH,EAAAA,EAAU,CAAC5G,UAAU,6CAMxB+C,GAAiC,IAApBkD,EAASvG,QACtBC,EAAAA,cAAA,OAAKK,UAAU,6EACbL,EAAAA,cAACoG,EAAAA,EAAQ,CAAC/F,UAAU,wCAAwC,4BAKhEL,EAAAA,cAAA,OAAKK,UAAU,mDACZiG,EAASjE,KAAK6E,GACblH,EAAAA,cAAA,OAAK8E,IAAKoC,EAAEvG,GAAIN,UAAU,YACxBL,EAAAA,cAAA,OACEK,UAAW,0GAERkG,aAAc,EAAdA,EAAgB5F,MAAOuG,EAAEvG,GAAK,YAAc,gBAG9C,KAEHX,EAAAA,cAAA,OACEK,UAAW,8GACTkG,aAAc,EAAdA,EAAgB5F,MAAOuG,EAAEvG,GAAK,6BAA+B,IAE/DsC,QAASA,IAAMwD,EAAgBS,IAE/BlH,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,oBAAoB6G,EAAExH,MACrCM,EAAAA,cAAA,QAAMK,UAAU,oCACb8G,EAAAA,EAAAA,IAAsBD,EAAEE,YAAc,MAG3CpH,EAAAA,cAAA,OAAKK,UAAU,iFACbL,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,gBACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACLN,KAAK,QACLvC,UAAU,uBACV+E,KAAMpF,EAAAA,cAACqH,EAAAA,EAAI,CAAChH,UAAU,YACtB4C,QAAU2B,IACRA,EAAE0C,kBACFZ,EAAcQ,EAAE,KAItBlH,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,kBACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACLN,KAAK,QACLvC,UAAU,uBACVkH,QAAM,EACNnC,KAAMpF,EAAAA,cAACwH,EAAAA,EAAM,CAACnH,UAAU,yBACxB4C,QAAU2B,IACRA,EAAE0C,kBACEJ,EAAEvG,IAAIgG,EAAgBO,EAAEvG,GAAG,YAlI/CX,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,cACbL,EAAAA,cAAC4G,EAAAA,EAAO,CACN1G,MAAMF,EAAAA,cAAA,YAAM,WACD,IACTA,EAAAA,cAAA,QAAMK,UAAU,oBAAmB,IAAEiG,EAASvG,OAAO,KAAS,MAGhEC,EAAAA,cAAA,UACEiD,QAASuD,EACTnG,UAAU,gKAEVL,EAAAA,cAACyH,EAAAA,EAAa,CAACX,YAAa,IAAKzG,UAAU,eAIjDL,EAAAA,cAAA,OAAKK,UAAU,mBACbL,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,sBACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACL7C,UAAU,iCACV4C,QAASA,IAAMyD,IACftB,KAAMpF,EAAAA,cAACmG,EAAAA,EAAI,CAAC9F,UAAU,gBAqH1B,E,kCCrKH,MAAMqH,GAAkBC,EAAAA,EAAAA,IAAqB,CAACC,EAAKC,KAAG,CAE3DC,UAAW,GACXC,gBAAiB,KACjB3E,WAAW,EACXvC,MAAO,KAGPmH,eAAgBvH,UACd,IACEmH,EAAI,CAAExE,WAAW,EAAMvC,MAAO,OAC9B,MAAMiH,QAAkBG,EAAAA,EAAWC,cAAcC,GAEjDP,EAAI,CACFE,YAEAC,gBAAiBF,IAAME,iBAAmBD,EAAU,IAAM,KAC1D1E,WAAW,GAEf,CAAE,MAAOvC,GACP+G,EAAI,CACF/G,MACEA,aAAiBC,MAAQD,EAAMvB,QAAU,4BAC3C8D,WAAW,GAEf,GAGFgF,cAAgBC,IACdT,EAAI,CAAEG,gBAAiBM,GAAU,EAGnCC,mBAAoBA,IACXT,IAAME,oBCtCJQ,EAA2BA,KACtC,MAAM,EAAC1J,EAAM,EAAC2J,IAAYrJ,EAAAA,EAAAA,UAAiB,KACrC,EAACiE,EAAU,EAACqF,IAAgBtJ,EAAAA,EAAAA,WAAS,IACrC,EAACuJ,EAAa,EAACC,IAAmBxJ,EAAAA,EAAAA,WAAS,IAC3C,EAACyJ,EAAe,EAACC,IAAqB1J,EAAAA,EAAAA,aACtC,EAAC2J,EAAc,EAACC,IAAoB5J,EAAAA,EAAAA,WAAS,KACjD,GAAsB,oBAAXuE,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAkB,OAAXF,GAAkBqF,KAAKC,MAAMtF,EACtC,CACA,OAAO,CAAI,KAENvE,EAAYC,GAAiBC,EAAAA,GAAQC,cAEtC,KAAE2J,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACtB,QAAE3K,EAAO,WAAE4K,EAAU,SAAE/C,EAAQ,YAAEgD,IAAgBC,EAAAA,EAAAA,MACjD,EAACC,EAAc,EAACC,IAAoBtK,EAAAA,EAAAA,WAAS,IAC7C,EAACuK,EAAkB,EAACC,IAAwBxK,EAAAA,EAAAA,UAChD,MAGIyK,EAAelC,KAmBrBlI,EAAAA,EAAAA,YAAU,KACc,oBAAXkE,QACTE,aAAaiC,QAAQ,iBAAkBmD,KAAKa,UAAUf,GACxD,GACC,CAACA,IAEJ,MAAMgB,GAAgBC,EAAAA,EAAAA,cAAYtJ,UAChC,GAAKyI,SAAAA,EAAMvI,GAEX,IACE8H,GAAa,GACb,MAAMuB,QAAaC,EAAAA,EAAWC,aAAahB,EAAKvI,IAChD2I,EAAYU,GAGZ,MACMG,EADS,IAAIC,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,cACxBpJ,GAAWuL,EAAKjK,OAAS,IAAMoK,GAClCd,EAAWW,EAAK,GAEpB,CAAE,MAAOnJ,GACPI,QAAQJ,MAAM,2BAA4BA,GAC1CzB,EAAWyB,MAAM,yBACnB,CAAC,QACC4H,GAAa,EACf,IACC,CAACS,aAAI,EAAJA,EAAMvI,GAAI2I,EAAa7K,EAAS4K,KAGpC7J,EAAAA,EAAAA,YAAU,KACR,MACM2K,EADS,IAAIC,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,aAEzBsC,IAAc1L,GAChB6L,EAAoB,CAAE3J,GAAImD,SAASqG,IACrC,GACC,KAGH3K,EAAAA,EAAAA,YAAU,KACR,MAAM+K,EAAuBA,MACZ,IAAIH,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,cAEXpJ,GAChB4K,EAAW,KACb,EAIF,OADA3F,OAAO8G,iBAAiB,WAAYD,GAC7B,IAAM7G,OAAO+G,oBAAoB,WAAYF,EAAqB,GACxE,CAAC9L,IAEJ,MAoEM6L,EAAsB7J,UAC1B,GAAKyI,SAAAA,EAAMvI,IAAO+J,EAAgB/J,GAElC,IACE8H,GAAa,GACb,MAAMuB,QAAaC,EAAAA,EAAWU,WAAWD,EAAgB/J,GAAIuI,EAAKvI,IAClE,IAAKqJ,EAQH,OAPA5K,EAAWyB,MAAM,qBACjB6C,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAInH,OAAO2G,SAASS,eAC7CxE,EAASvG,OAAS,EACpBsJ,EAAW/C,EAAS,IAEpB+C,EAAW,OAIfA,EAAWW,GACXtG,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAI,cAAcH,EAAgB/J,KACjE,CAAE,MAAOE,GACPI,QAAQJ,MAAM,yBAA0BA,GACxCzB,EAAWyB,MAAM,wBACnB,CAAC,QACC4H,GAAa,EACf,IAGFjJ,EAAAA,EAAAA,YAAU,KACRsK,GAAe,GACd,CAACA,IAGJ,MAAMiB,GAAahB,EAAAA,EAAAA,cAAYtJ,UAE7B,GAAKyI,SAAAA,EAAMvI,GAEX,IACE8H,GAAa,GACb,MAAMuC,QAAkBC,EAAAA,GAAQC,UAAUhC,EAAKvI,IAC/C,GAAIqK,EAAUjL,OAAS,EACrByI,EAASwC,OACJ,CACL/J,QAAQkK,IAAI,+CACNvB,EAAa5B,eAAekB,EAAKvI,IACvC,MAAMyK,EAAiBxB,EAAatB,qBAE9B+C,EAAaD,aAAc,EAAdA,EAAgBE,OAAOC,WAAW1M,MAAM,GAG3D,GAFAoC,QAAQkK,IAAI,uCAAwCE,GAEhDA,EAAY,CACd,MAAMG,EAAiB,CACrBhJ,UAAW6I,GAEPI,QAAoBR,EAAAA,GAAQS,WAAWF,EAAUtC,EAAKvI,IAC5DM,QAAQkK,IAAI,wBAAyBK,GAErChD,EAAS,CAACiD,GACZ,CACF,CACF,CAAE,MAAO5K,GACPI,QAAQJ,MAAM,wBAAyBA,GACvCzB,EAAWyB,MAAM,sBACnB,CAAC,QACC4H,GAAa,EACf,IACC,CAACS,aAAI,EAAJA,EAAMvI,GAAIvB,IAOd,OAJAI,EAAAA,EAAAA,YAAU,KACRuL,GAAY,GACX,CAACA,IAGF/K,EAAAA,cAAA,OAAKK,UAAU,+BACZhB,EACDW,EAAAA,cAAA,OACEK,UAAW,yEACTyI,EAAgB,OAAS,SAG3B9I,EAAAA,cAACqG,EAAO,CACNzH,OAAQkK,EACRjK,MAAOA,EACPwE,eA1GiB5C,MAAOkL,EAAgBC,KAC9C,GAAK1C,SAAAA,EAAMvI,GACX,IACE,MAAMkL,EAAc,GAAGD,EAASE,UAC9B,EACA,UACK,IAAIC,MAAOC,2BACZC,QAAgBhC,EAAAA,EAAWiC,cAC/B,CACExM,KAAMmM,EACNlM,QAASgM,GAEXzC,EAAKvI,IAGP2I,EAAY,CAAC2C,GAAOlH,QAAAC,EAAAA,EAAAA,GAAKsB,KACzB+C,EAAW4C,GACX7M,EAAWwB,QAAQ,mBACrB,CAAE,MAAOC,GACPzB,EAAWyB,MAAM,yBACnB,GAuFMyF,SAAUA,EACVC,eAAgB9H,EAChB+H,SAAUA,IAAMuC,GAAkBD,GAClCrC,gBAAiB6D,EACjB5D,cAAgBjI,IACdoK,EAAkBpK,GAClBkK,GAAgB,EAAK,EAEvBhC,gBApIoBlG,UAC1B,GAAKyI,SAAAA,EAAMvI,GAEX,UACyBsJ,EAAAA,EAAWkC,cAAchC,EAAWjB,EAAKvI,IAChE2I,EAAYhD,EAASpC,QAAQgD,GAAMA,EAAEvG,KAAOwJ,MACxC1L,aAAO,EAAPA,EAASkC,MAAOwJ,GAAiC,IAApB7D,EAASvG,SACxCsJ,EAAW/C,EAAS,IAAM,MAC1B5C,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAInH,OAAO2G,SAASS,WAEnD1L,EAAWwB,QAAQ,kBACrB,CAAE,MAAOC,GACPI,QAAQJ,MAAM,0BAA2BA,GACzCzB,EAAWyB,MAAM,yBACnB,GAuHMuC,UAAWA,KAIfpD,EAAAA,cAAA,OACEK,UAAW,uCACTyI,EAAgB,QAAU,UAG3BrK,GAAW6H,EAASvG,OAAS,EAC5BC,EAAAA,cAAA,OAAKK,UAAU,mBAEbL,EAAAA,cAAA,OAAKK,UAAW,WAAUmJ,EAAgB,QAAU,WAClDxJ,EAAAA,cAACoM,EAAAA,EAAQ,CACP3N,QAASA,EACT+K,cAAeA,EACf6C,eAtPaC,KACzB,GAAIhG,EAASvG,OAAS,EAAG,CAEvB,MAAMwM,EAAejG,EAASf,MAAM2B,GAAMA,EAAEvG,MAAOlC,aAAO,EAAPA,EAASkC,MAC5DgJ,EAAqB4C,GAAgB9N,EACvC,MAEEkL,EAAqBlL,GAEvBgL,GAAiB,EAAK,EA8OV+C,gBAAiBlC,EACjBmC,kBAAmBnG,KAKtBkD,GACCxJ,EAAAA,cAAA,OAAKK,UAAU,kDACbL,EAAAA,cAACoM,EAAAA,EAAQ,CACP3N,QAASiL,EACTF,eAAe,EACfkD,iBAAiB,EACjBC,cAvPUC,KACxBnD,GAAiB,GACjBE,EAAqB,KAAK,EAsPZ6C,gBAAiB7C,EACjB8C,kBAAmBnG,MAM3BtG,EAAAA,cAAA,OAAKK,UAAU,0DAAyD,sEAM5EL,EAAAA,cAACzB,EAAa,CACZM,MAAOA,EACPJ,QAASmK,EACThK,OAAQ8J,EACRhK,OA/MoB+B,UACxB,GAAKyI,SAAAA,EAAMvI,GAEX,IACE,GAAIkM,EAAYlM,GAAI,CAClB,MAAMmM,QAAgB7C,EAAAA,EAAW8C,cAC/BF,EAAYlM,GACZkM,EACA3D,EAAKvI,IAEP2I,EAAYhD,EAASjE,KAAK6E,GAAOA,EAAEvG,KAAOmM,EAAQnM,GAAKmM,EAAU5F,MAC7DzI,aAAO,EAAPA,EAASkC,MAAOmM,EAAQnM,IAC1B0I,EAAWyD,EAEf,KAAO,CACL,MAAMb,QAAgBhC,EAAAA,EAAWiC,cAAcW,EAAa3D,EAAKvI,IACjE2I,EAAY,CAAC2C,GAAOlH,QAAAC,EAAAA,EAAAA,GAAKsB,KACzB+C,EAAW4C,EACb,CACAtD,GAAgB,GAChBE,OAAkBjJ,EACpB,CAAE,MAAOiB,GACPzB,EAAWyB,MAAM,wBACjBI,QAAQJ,MAAMA,EAChB,GAwLIlC,SAAUA,KACRgK,GAAgB,GAChBE,OAAkBjJ,EAAU,IAG5B,EClSV,MArBkBpB,IAAmB,IAAlB,KAAEwL,GAAWxL,EAC9B,OACEwB,EAAAA,cAACgN,EAAAA,EAAM,CAACC,KAAMjD,EAAKkD,KAAKC,aAAcjN,MAAM,OAAOkN,KAAM,KACvDpN,EAAAA,cAAA,QAAMiG,MAAO,CAAEoH,OAAQ,QAAUhN,UAAU,YACzCL,EAAAA,cAACuI,EAAc,OAEV,C", "sources": ["webpack://autogentstudio/./src/components/views/playground/editor.tsx", "webpack://autogentstudio/./src/components/views/playground/newsession.tsx", "webpack://autogentstudio/./src/components/views/playground/sidebar.tsx", "webpack://autogentstudio/./src/components/views/gallery/store.tsx", "webpack://autogentstudio/./src/components/views/playground/manager.tsx", "webpack://autogentstudio/./src/pages/index.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { Modal, Form, message, Input, Button, Select, Spin } from \"antd\";\nimport { TriangleAlertIcon } from \"lucide-react\";\nimport type { FormProps } from \"antd\";\nimport { SessionEditorProps } from \"./types\";\nimport { Link } from \"gatsby\";\n\ntype FieldType = {\n  name: string;\n  team_id?: number;\n};\n\nexport const SessionEditor: React.FC<SessionEditorProps> = ({\n  session,\n  onSave,\n  onCancel,\n  isOpen,\n  teams,\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [messageApi, contextHolder] = message.useMessage();\n\n  // Set form values when modal opens or session changes\n  useEffect(() => {\n    if (isOpen) {\n      form.setFieldsValue({\n        name: session?.name || \"\",\n        team_id: session?.team_id || undefined,\n      });\n    } else {\n      form.resetFields();\n    }\n  }, [form, session, isOpen]);\n\n  const onFinish: FormProps<FieldType>[\"onFinish\"] = async (values) => {\n    try {\n      await onSave({\n        ...values,\n        id: session?.id,\n      });\n      messageApi.success(\n        `Session ${session ? \"updated\" : \"created\"} successfully`\n      );\n    } catch (error) {\n      if (error instanceof Error) {\n        messageApi.error(error.message);\n      }\n    }\n  };\n\n  const onFinishFailed: FormProps<FieldType>[\"onFinishFailed\"] = (\n    errorInfo\n  ) => {\n    messageApi.error(\"Please check the form for errors\");\n    console.error(\"Form validation failed:\", errorInfo);\n  };\n\n  const hasNoTeams = !loading && teams.length === 0;\n\n  return (\n    <Modal\n      title={session ? \"Edit Session\" : \"Create Session\"}\n      open={isOpen}\n      onCancel={onCancel}\n      footer={null}\n      className=\"text-primary\"\n      forceRender\n    >\n      {contextHolder}\n      <Form\n        form={form}\n        name=\"session-form\"\n        layout=\"vertical\"\n        onFinish={onFinish}\n        onFinishFailed={onFinishFailed}\n        autoComplete=\"off\"\n      >\n        <Form.Item<FieldType>\n          label=\"Session Name\"\n          name=\"name\"\n          rules={[\n            { required: true, message: \"Please enter a session name\" },\n            { max: 100, message: \"Session name cannot exceed 100 characters\" },\n          ]}\n        >\n          <Input />\n        </Form.Item>\n\n        <div className=\"space-y-2   w-full\">\n          <Form.Item<FieldType>\n            className=\"w-full\"\n            label=\"Team\"\n            name=\"team_id\"\n            rules={[{ required: true, message: \"Please select a team\" }]}\n          >\n            <Select\n              placeholder=\"Select a team\"\n              loading={loading}\n              disabled={loading || hasNoTeams}\n              showSearch\n              optionFilterProp=\"children\"\n              filterOption={(input, option) =>\n                (option?.label ?? \"\")\n                  .toLowerCase()\n                  .includes(input.toLowerCase())\n              }\n              options={teams.map((team) => ({\n                value: team.id,\n                label: `${team.component.label} (${team.component.component_type})`,\n              }))}\n              notFoundContent={loading ? <Spin size=\"small\" /> : null}\n            />\n          </Form.Item>\n        </div>\n\n        <div className=\"text-sm text-accent \">\n          <Link to=\"/build\">view all teams</Link>\n        </div>\n\n        {hasNoTeams && (\n          <div className=\"flex border p-1 rounded -mt-2 mb-4 items-center gap-1.5 text-sm text-yellow-600\">\n            <TriangleAlertIcon className=\"h-4 w-4\" />\n            <span>No teams found. Please create a team first.</span>\n          </div>\n        )}\n\n        <Form.Item className=\"flex justify-end mb-0\">\n          <div className=\"flex gap-2\">\n            <Button onClick={onCancel}>Cancel</Button>\n            <Button type=\"primary\" htmlType=\"submit\" disabled={hasNoTeams}>\n              {session ? \"Update\" : \"Create\"}\n            </Button>\n          </div>\n        </Form.Item>\n      </Form>\n    </Modal>\n  );\n};\n\nexport default SessionEditor;\n", "import React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON>, Dropdown, MenuProps, message, Select, Space } from \"antd\";\nimport { Plus, InfoIcon, Bot, TextSearch, ChevronDown } from \"lucide-react\";\nimport { Team } from \"../../types/datamodel\";\nimport { truncateText } from \"../../utils/utils\";\nimport Input from \"antd/es/input/Input\";\n\ninterface NewSessionControlsProps {\n  teams: Team[];\n  isLoading: boolean;\n  onStartSession: (teamId: number, teamName: string) => void;\n}\n\nconst NewSessionControls = ({\n  teams,\n  isLoading,\n  onStartSession,\n}: NewSessionControlsProps) => {\n  const [selectedTeamId, setSelectedTeamId] = useState<number | undefined>();\n  const [lastUsedTeamId, setLastUsedTeamId] = useState<number | undefined>(\n    () => {\n      if (typeof window !== \"undefined\") {\n        const stored = localStorage.getItem(\"lastUsedTeamId\");\n        return stored ? parseInt(stored) : undefined;\n      }\n    }\n  );\n  const [search, setSearch] = useState<string>(\"\");\n\n  // Filter teams based on search\n  const filteredTeams = teams.filter((team) => {\n    return (\n      team.component.label?.toLowerCase().includes(search.toLowerCase()) ||\n      team.component.description?.toLowerCase().includes(search.toLowerCase())\n    );\n  });\n\n  // Auto-select last used team on load\n  useEffect(() => {\n    if (lastUsedTeamId && teams.some((team) => team.id === lastUsedTeamId)) {\n      setSelectedTeamId(lastUsedTeamId);\n    } else if (teams.length > 0) {\n      setSelectedTeamId(teams[0].id);\n    }\n  }, [teams, lastUsedTeamId]);\n\n  const handleStartSession = async () => {\n    if (!selectedTeamId) return;\n\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"lastUsedTeamId\", selectedTeamId.toString());\n    }\n\n    const selectedTeam = teams.find((team) => team.id === selectedTeamId);\n    if (!selectedTeam) return;\n\n    // Give UI time to update before starting session\n    await new Promise((resolve) => setTimeout(resolve, 100));\n    onStartSession(selectedTeamId, selectedTeam.component.label || \"\");\n  };\n\n  const handleMenuClick: MenuProps[\"onClick\"] = async (e) => {\n    const newTeamId = parseInt(e.key);\n    const selectedTeam = teams.find((team) => team.id === newTeamId);\n\n    if (!selectedTeam) {\n      console.error(\"Selected team not found:\", newTeamId);\n      return;\n    }\n\n    // Update state first\n    setSelectedTeamId(newTeamId);\n\n    // // Save to localStorage\n    // if (typeof window !== \"undefined\") {\n    //   localStorage.setItem(\"lastUsedTeamId\", e.key);\n    // }\n\n    // // Delay the session start to allow UI to update\n    // await new Promise((resolve) => setTimeout(resolve, 100));\n    // onStartSession(newTeamId, selectedTeam.component.label || \"\");\n  };\n\n  const hasNoTeams = !isLoading && teams.length === 0;\n\n  const items: MenuProps[\"items\"] = [\n    {\n      type: \"group\",\n      label: (\n        <div>\n          <div className=\"text-xs text-secondary mb-1\">Select a team</div>\n          <Input\n            prefix={<TextSearch className=\"w-4 h-4\" />}\n            placeholder=\"Search teams\"\n            onChange={(e) => setSearch(e.target.value)}\n          />\n        </div>\n      ),\n      key: \"from-team\",\n    },\n    {\n      type: \"divider\",\n    },\n    ...filteredTeams.map((team) => ({\n      label: (\n        <div>\n          <div>{truncateText(team.component.label || \"\", 20)}</div>\n          <div className=\"text-xs text-secondary\">\n            {team.component.component_type}\n          </div>\n        </div>\n      ),\n      key: team?.id?.toString() || \"\",\n      icon: <Bot className=\"w-4 h-4\" />,\n    })),\n  ];\n\n  const menuProps = {\n    items,\n    onClick: handleMenuClick,\n  };\n\n  const selectedTeam = teams.find((team) => team.id === selectedTeamId);\n\n  return (\n    <div className=\"space-y-2 w-full\">\n      <Dropdown.Button\n        menu={menuProps}\n        type=\"primary\"\n        className=\"w-full\"\n        placement=\"bottomRight\"\n        icon={<ChevronDown className=\"w-4 h-4\" />}\n        onClick={handleStartSession}\n        disabled={!selectedTeamId || isLoading}\n      >\n        <div className=\"\" style={{ width: \"183px\" }}>\n          <Plus className=\"w-4 h-4 inline-block -mt-1\" /> New Session\n        </div>\n      </Dropdown.Button>\n\n      <div\n        className=\"text-xs text-secondary\"\n        title={selectedTeam?.component.label}\n      >\n        {truncateText(selectedTeam?.component.label || \"\", 30)}\n      </div>\n\n      {hasNoTeams && (\n        <div className=\"flex items-center gap-1.5 text-xs text-yellow-600 mt-1\">\n          <InfoIcon className=\"h-3 w-3\" />\n          <span>Create a team to get started</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NewSessionControls;\n", "import React from \"react\";\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\nimport {\n  Plus,\n  Edit,\n  Trash2,\n  PanelLeftClose,\n  PanelLeftOpen,\n  InfoIcon,\n  RefreshCcw,\n  History,\n} from \"lucide-react\";\nimport type { Session, Team } from \"../../types/datamodel\";\nimport { getRelativeTimeString } from \"../atoms\";\nimport NewSessionControls from \"./newsession\";\n\ninterface SidebarProps {\n  isOpen: boolean;\n  sessions: Session[];\n  currentSession: Session | null;\n  onToggle: () => void;\n  onSelectSession: (session: Session) => void;\n  onEditSession: (session?: Session) => void;\n  onDeleteSession: (sessionId: number) => void;\n  isLoading?: boolean;\n  onStartSession: (teamId: number, teamName: string) => void;\n  teams: Team[];\n}\n\nexport const Sidebar: React.FC<SidebarProps> = ({\n  isOpen,\n  sessions,\n  currentSession,\n  onToggle,\n  onSelectSession,\n  onEditSession,\n  onDeleteSession,\n  isLoading = false,\n  onStartSession,\n  teams,\n}) => {\n  if (!isOpen) {\n    return (\n      <div className=\"h-full  border-r border-secondary\">\n        <div className=\"p-2 -ml-2 \">\n          <Tooltip\n            title=<span>\n              Sessions{\" \"}\n              <span className=\"text-accent mx-1\"> {sessions.length} </span>{\" \"}\n            </span>\n          >\n            <button\n              onClick={onToggle}\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n            >\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\n            </button>\n          </Tooltip>\n        </div>\n        <div className=\"mt-4 px-2 -ml-1\">\n          <Tooltip title=\"Create new session\">\n            <Button\n              type=\"text\"\n              className=\"w-full p-2 flex justify-center\"\n              onClick={() => onEditSession()}\n              icon={<Plus className=\"w-4 h-4\" />}\n            />\n          </Tooltip>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full border-r border-secondary \">\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-primary font-medium\">Sessions</span>\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\n            {sessions.length}\n          </span>\n        </div>\n        <Tooltip title=\"Close Sidebar\">\n          <button\n            onClick={onToggle}\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n          >\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\n          </button>\n        </Tooltip>\n      </div>\n\n      <div className=\"my-4 flex text-sm  \">\n        <div className=\" mr-2 w-full pr-2\">\n          {isOpen && (\n            <NewSessionControls\n              teams={teams}\n              isLoading={isLoading}\n              onStartSession={onStartSession}\n            />\n          )}\n        </div>\n      </div>\n\n      <div className=\"py-2 flex text-sm text-secondary\">\n        <History className=\"w-4 h-4 inline-block mr-1.5\" />\n        <div className=\"inline-block -mt-0.5\">\n          Recents{\" \"}\n          <span className=\"text-accent text-xs mx-1 mt-0.5\">\n            {\" \"}\n            ({sessions.length}){\" \"}\n          </span>{\" \"}\n        </div>\n\n        {isLoading && (\n          <RefreshCcw className=\"w-4 h-4 inline-block ml-2 animate-spin\" />\n        )}\n      </div>\n\n      {/* no sessions found */}\n\n      {!isLoading && sessions.length === 0 && (\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded \">\n          <InfoIcon className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\n          No recent sessions found\n        </div>\n      )}\n\n      <div className=\"overflow-y-auto   scroll   h-[calc(100%-181px)]\">\n        {sessions.map((s) => (\n          <div key={s.id} className=\"relative\">\n            <div\n              className={`bg-accent absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80  rounded ${\n                 currentSession?.id === s.id ? \"bg-accent\" : \"bg-tertiary\"\n               }`}\n            >\n              {\" \"}\n            </div>\n            <div\n              className={`group ml-1 flex items-center justify-between rounded-l p-2 py-1 text-sm cursor-pointer hover:bg-tertiary ${\n                currentSession?.id === s.id ? \"border-accent bg-secondary\" : \"\"\n              }`}\n              onClick={() => onSelectSession(s)}\n            >\n              <div className=\"flex flex-col min-w-0 flex-1 mr-2\">\n                <div className=\"truncate text-sm\">{s.name}</div>\n                <span className=\"truncate text-xs text-secondary\">\n                  {getRelativeTimeString(s.updated_at || \"\")}\n                </span>\n              </div>\n              <div className=\"py-3 flex gap-1 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n                <Tooltip title=\"Edit session\">\n                  <Button\n                    type=\"text\"\n                    size=\"small\"\n                    className=\"p-1 min-w-[24px] h-6\"\n                    icon={<Edit className=\"w-4 h-4\" />}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      onEditSession(s);\n                    }}\n                  />\n                </Tooltip>\n                <Tooltip title=\"Delete session\">\n                  <Button\n                    type=\"text\"\n                    size=\"small\"\n                    className=\"p-1 min-w-[24px] h-6\"\n                    danger\n                    icon={<Trash2 className=\"w-4 h-4 text-red-500\" />}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      if (s.id) onDeleteSession(s.id);\n                    }}\n                  />\n                </Tooltip>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n", "import { create } from \"zustand\";\nimport { Gallery } from \"../../types/datamodel\";\nimport { galleryAPI } from \"./api\";\n\ninterface GalleryState {\n  // State\n  galleries: Gallery[];\n  selectedGallery: Gallery | null;\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  fetchGalleries: (userId: string) => Promise<void>;\n  selectGallery: (gallery: Gallery) => void;\n  getSelectedGallery: () => Gallery | null;\n}\n\nexport const useGalleryStore = create<GalleryState>((set, get) => ({\n  // Initial state\n  galleries: [],\n  selectedGallery: null,\n  isLoading: false,\n  error: null,\n\n  // Actions\n  fetchGalleries: async (userId: string) => {\n    try {\n      set({ isLoading: true, error: null });\n      const galleries = await galleryAPI.listGalleries(userId);\n\n      set({\n        galleries,\n        // Automatically select first gallery if none selected\n        selectedGallery: get().selectedGallery || galleries[0] || null,\n        isLoading: false,\n      });\n    } catch (error) {\n      set({\n        error:\n          error instanceof Error ? error.message : \"Failed to fetch galleries\",\n        isLoading: false,\n      });\n    }\n  },\n\n  selectGallery: (gallery: Gallery) => {\n    set({ selectedGallery: gallery });\n  },\n\n  getSelectedGallery: () => {\n    return get().selectedGallery;\n  },\n}));\n", "import React, { useCallback, useEffect, useState, useContext } from \"react\";\nimport { Button, message } from \"antd\";\nimport { useConfigStore } from \"../../../hooks/store\";\nimport { appContext } from \"../../../hooks/provider\";\nimport { sessionAPI } from \"./api\";\nimport { SessionEditor } from \"./editor\";\nimport type { Session, Team } from \"../../types/datamodel\";\nimport ChatView from \"./chat/chat\";\nimport { Sidebar } from \"./sidebar\";\nimport { teamAPI } from \"../teambuilder/api\";\nimport { useGalleryStore } from \"../gallery/store\";\n\nexport const SessionManager: React.FC = () => {\n  const [teams, setTeams] = useState<Team[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isEditorOpen, setIsEditorOpen] = useState(false);\n  const [editingSession, setEditingSession] = useState<Session | undefined>();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\n    if (typeof window !== \"undefined\") {\n      const stored = localStorage.getItem(\"sessionSidebar\");\n      return stored !== null ? JSON.parse(stored) : true;\n    }\n    return true; // Default value during SSR\n  });\n  const [messageApi, contextHolder] = message.useMessage();\n\n  const { user } = useContext(appContext);\n  const { session, setSession, sessions, setSessions } = useConfigStore();\n  const [isCompareMode, setIsCompareMode] = useState(false);\n  const [comparisonSession, setComparisonSession] = useState<Session | null>(\n    null\n  );\n\n  const galleryStore = useGalleryStore();\n\n  const handleCompareClick = () => {\n    if (sessions.length > 1) {\n      // Find the first session that isn't the current one\n      const otherSession = sessions.find((s) => s.id !== session?.id);\n      setComparisonSession(otherSession || session);\n    } else {\n      // If only one session, show it in both panels\n      setComparisonSession(session);\n    }\n    setIsCompareMode(true);\n  };\n\n  const handleExitCompare = () => {\n    setIsCompareMode(false);\n    setComparisonSession(null);\n  };\n\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"sessionSidebar\", JSON.stringify(isSidebarOpen));\n    }\n  }, [isSidebarOpen]);\n\n  const fetchSessions = useCallback(async () => {\n    if (!user?.id) return;\n\n    try {\n      setIsLoading(true);\n      const data = await sessionAPI.listSessions(user.id);\n      setSessions(data);\n\n      // Only set first session if there's no sessionId in URL\n      const params = new URLSearchParams(window.location.search);\n      const sessionId = params.get(\"sessionId\");\n      if (!session && data.length > 0 && !sessionId) {\n        setSession(data[0]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching sessions:\", error);\n      messageApi.error(\"Error loading sessions\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, [user?.id, setSessions, session, setSession]);\n\n  // Handle initial URL params\n  useEffect(() => {\n    const params = new URLSearchParams(window.location.search);\n    const sessionId = params.get(\"sessionId\");\n\n    if (sessionId && !session) {\n      handleSelectSession({ id: parseInt(sessionId) } as Session);\n    }\n  }, []);\n\n  // Handle browser back/forward\n  useEffect(() => {\n    const handleLocationChange = () => {\n      const params = new URLSearchParams(window.location.search);\n      const sessionId = params.get(\"sessionId\");\n\n      if (!sessionId && session) {\n        setSession(null);\n      }\n    };\n\n    window.addEventListener(\"popstate\", handleLocationChange);\n    return () => window.removeEventListener(\"popstate\", handleLocationChange);\n  }, [session]);\n\n  const handleSaveSession = async (sessionData: Partial<Session>) => {\n    if (!user?.id) return;\n\n    try {\n      if (sessionData.id) {\n        const updated = await sessionAPI.updateSession(\n          sessionData.id,\n          sessionData,\n          user.id\n        );\n        setSessions(sessions.map((s) => (s.id === updated.id ? updated : s)));\n        if (session?.id === updated.id) {\n          setSession(updated);\n        }\n      } else {\n        const created = await sessionAPI.createSession(sessionData, user.id);\n        setSessions([created, ...sessions]);\n        setSession(created);\n      }\n      setIsEditorOpen(false);\n      setEditingSession(undefined);\n    } catch (error) {\n      messageApi.error(\"Error saving session\");\n      console.error(error);\n    }\n  };\n\n  const handleDeleteSession = async (sessionId: number) => {\n    if (!user?.id) return;\n\n    try {\n      const response = await sessionAPI.deleteSession(sessionId, user.id);\n      setSessions(sessions.filter((s) => s.id !== sessionId));\n      if (session?.id === sessionId || sessions.length === 0) {\n        setSession(sessions[0] || null);\n        window.history.pushState({}, \"\", window.location.pathname); // Clear URL params\n      }\n      messageApi.success(\"Session deleted\");\n    } catch (error) {\n      console.error(\"Error deleting session:\", error);\n      messageApi.error(\"Error deleting session\");\n    }\n  };\n\n  const handleQuickStart = async (teamId: number, teamName: string) => {\n    if (!user?.id) return;\n    try {\n      const defaultName = `${teamName.substring(\n        0,\n        20\n      )} - ${new Date().toLocaleString()} Session`;\n      const created = await sessionAPI.createSession(\n        {\n          name: defaultName,\n          team_id: teamId,\n        },\n        user.id\n      );\n\n      setSessions([created, ...sessions]);\n      setSession(created);\n      messageApi.success(\"Session created!\");\n    } catch (error) {\n      messageApi.error(\"Error creating session\");\n    }\n  };\n\n  // Modify the existing session selection handler\n  const handleSelectSession = async (selectedSession: Session) => {\n    if (!user?.id || !selectedSession.id) return;\n\n    try {\n      setIsLoading(true);\n      const data = await sessionAPI.getSession(selectedSession.id, user.id);\n      if (!data) {\n        messageApi.error(\"Session not found\");\n        window.history.pushState({}, \"\", window.location.pathname);\n        if (sessions.length > 0) {\n          setSession(sessions[0]);\n        } else {\n          setSession(null);\n        }\n        return;\n      }\n      setSession(data);\n      window.history.pushState({}, \"\", `?sessionId=${selectedSession.id}`);\n    } catch (error) {\n      console.error(\"Error loading session:\", error);\n      messageApi.error(\"Error loading session\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchSessions();\n  }, [fetchSessions]);\n\n  // Add teams fetching\n  const fetchTeams = useCallback(async () => {\n    // console.log(\"Fetching teams\", user);\n    if (!user?.id) return;\n\n    try {\n      setIsLoading(true);\n      const teamsData = await teamAPI.listTeams(user.id);\n      if (teamsData.length > 0) {\n        setTeams(teamsData);\n      } else {\n        console.log(\"No teams found, creating default team\");\n        await galleryStore.fetchGalleries(user.id);\n        const defaultGallery = galleryStore.getSelectedGallery();\n\n        const sampleTeam = defaultGallery?.config.components.teams[0];\n        console.log(\"Default Gallery .. manager fetching \", sampleTeam);\n        // // If no teams, create a default team\n        if (sampleTeam) {\n          const teamData: Team = {\n            component: sampleTeam,\n          };\n          const defaultTeam = await teamAPI.createTeam(teamData, user.id);\n          console.log(\"Default team created:\", teamData);\n\n          setTeams([defaultTeam]);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching teams:\", error);\n      messageApi.error(\"Error loading teams\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, [user?.id, messageApi]);\n\n  // Fetch teams on mount\n  useEffect(() => {\n    fetchTeams();\n  }, [fetchTeams]);\n\n  return (\n    <div className=\"relative flex h-full w-full\">\n      {contextHolder}\n      <div\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\n          isSidebarOpen ? \"w-64\" : \"w-12\"\n        }`}\n      >\n        <Sidebar\n          isOpen={isSidebarOpen}\n          teams={teams}\n          onStartSession={handleQuickStart}\n          sessions={sessions}\n          currentSession={session}\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\n          onSelectSession={handleSelectSession}\n          onEditSession={(session) => {\n            setEditingSession(session);\n            setIsEditorOpen(true);\n          }}\n          onDeleteSession={handleDeleteSession}\n          isLoading={isLoading}\n        />\n      </div>\n\n      <div\n        className={`flex-1 transition-all duration-200 ${\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\n        }`}\n      >\n        {session && sessions.length > 0 ? (\n          <div className=\"pl-4 flex gap-4\">\n            {/* Primary ChatView */}\n            <div className={`flex-1 ${isCompareMode ? \"w-1/2\" : \"w-full\"}`}>\n              <ChatView\n                session={session}\n                isCompareMode={isCompareMode}\n                onCompareClick={handleCompareClick}\n                onSessionChange={handleSelectSession}\n                availableSessions={sessions}\n              />\n            </div>\n\n            {/* Comparison ChatView */}\n            {isCompareMode && (\n              <div className=\"flex-1 w-1/2 border-l border-secondary/20 pl-4\">\n                <ChatView\n                  session={comparisonSession}\n                  isCompareMode={true}\n                  isSecondaryView={true}\n                  onExitCompare={handleExitCompare}\n                  onSessionChange={setComparisonSession}\n                  availableSessions={sessions}\n                />\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"flex items-center justify-center h-full text-secondary\">\n            No session selected. Create or select a session from the sidebar.\n          </div>\n        )}\n      </div>\n\n      <SessionEditor\n        teams={teams}\n        session={editingSession}\n        isOpen={isEditorOpen}\n        onSave={handleSaveSession}\n        onCancel={() => {\n          setIsEditorOpen(false);\n          setEditingSession(undefined);\n        }}\n      />\n    </div>\n  );\n};\n", "import * as React from \"react\";\nimport Layout from \"../components/layout\";\nimport { graphql } from \"gatsby\";\nimport ChatView from \"../components/views/playground/chat/chat\";\nimport { SessionManager } from \"../components/views/playground/manager\";\n\n// markup\nconst IndexPage = ({ data }: any) => {\n  return (\n    <Layout meta={data.site.siteMetadata} title=\"Home\" link={\"/\"}>\n      <main style={{ height: \"100%\" }} className=\" h-full \">\n        <SessionManager />\n      </main>\n    </Layout>\n  );\n};\n\nexport const query = graphql`\n  query HomePageQuery {\n    site {\n      siteMetadata {\n        description\n        title\n      }\n    }\n  }\n`;\n\nexport default IndexPage;\n"], "names": ["SessionEditor", "_ref", "session", "onSave", "onCancel", "isOpen", "teams", "form", "Form", "useForm", "loading", "setLoading", "useState", "messageApi", "contextHolder", "message", "useMessage", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "team_id", "undefined", "resetFields", "hasNoTeams", "length", "React", "Modal", "title", "open", "footer", "className", "forceRender", "layout", "onFinish", "async", "values", "id", "success", "error", "Error", "onFinishFailed", "errorInfo", "console", "autoComplete", "<PERSON><PERSON>", "label", "rules", "required", "max", "Input", "Select", "placeholder", "disabled", "showSearch", "optionFilterProp", "filterOption", "input", "option", "_option$label", "toLowerCase", "includes", "options", "map", "team", "value", "component", "component_type", "notFoundContent", "Spin", "size", "Link", "to", "TriangleAlertIcon", "<PERSON><PERSON>", "onClick", "type", "htmlType", "isLoading", "onStartSession", "selectedTeamId", "setSelectedTeamId", "lastUsedTeamId", "setLastUsedTeamId", "window", "stored", "localStorage", "getItem", "parseInt", "search", "setSearch", "filteredTeams", "filter", "_team$component$label", "_team$component$descr", "description", "some", "menuProps", "items", "prefix", "TextSearch", "onChange", "e", "target", "key", "concat", "_toConsumableArray", "_team$id", "truncateText", "toString", "icon", "Bot", "newTeamId", "find", "selectedTeam", "Dropdown", "menu", "placement", "ChevronDown", "setItem", "Promise", "resolve", "setTimeout", "style", "width", "Plus", "InfoIcon", "Sidebar", "sessions", "currentSession", "onToggle", "onSelectSession", "onEditSession", "onDeleteSession", "<PERSON><PERSON><PERSON>", "PanelLeftClose", "strokeWidth", "NewSessionControls", "History", "RefreshCcw", "s", "getRelativeTimeString", "updated_at", "Edit", "stopPropagation", "danger", "Trash2", "PanelLeftOpen", "useGalleryStore", "create", "set", "get", "galleries", "selectedGallery", "fetchGalleries", "galleryAPI", "listGalleries", "userId", "selectGallery", "gallery", "getSelectedGallery", "Session<PERSON>anager", "setTeams", "setIsLoading", "isEditorOpen", "setIsEditorOpen", "editingSession", "setEditingSession", "isSidebarOpen", "setIsSidebarOpen", "JSON", "parse", "user", "useContext", "appContext", "setSession", "setSessions", "useConfigStore", "isCompareMode", "setIsCompareMode", "comparisonSession", "setComparisonSession", "galleryStore", "stringify", "fetchSessions", "useCallback", "data", "sessionAPI", "listSessions", "sessionId", "URLSearchParams", "location", "handleSelectSession", "handleLocationChange", "addEventListener", "removeEventListener", "selectedSession", "getSession", "history", "pushState", "pathname", "fetchTeams", "teamsData", "teamAPI", "listTeams", "log", "defaultGallery", "sampleTeam", "config", "components", "teamData", "defaultTeam", "createTeam", "teamId", "teamName", "defaultName", "substring", "Date", "toLocaleString", "created", "createSession", "deleteSession", "ChatView", "onCompareClick", "handleCompareClick", "otherSession", "onSessionChange", "availableSessions", "isSecondaryView", "onExitCompare", "handleExitCompare", "sessionData", "updated", "updateSession", "Layout", "meta", "site", "siteMetadata", "link", "height"], "sourceRoot": ""}