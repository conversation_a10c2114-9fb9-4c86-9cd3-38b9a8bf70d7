{"version": 3, "file": "component---src-pages-labs-tsx-c05b19297a0756c4ba40.js", "mappings": ";oJASA,MAAMA,GAAgB,aAAiB,gBAAiB,CACtD,CACE,OACA,CACEC,EAAG,2EACHC,IAAK,WAGT,CAAC,OAAQ,CAAED,EAAG,UAAWC,IAAK,WAC9B,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,mECTnC,MAAMC,GAAa,aAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEF,EAAG,qDAAsDC,IAAK,WACzE,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,WAC/B,CAAC,OAAQ,CAAED,EAAG,sDAAuDC,IAAK,WAC1E,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,4KCO5B,MAAME,EAA0CC,IAOhD,IAPiD,OACtDC,EAAM,KACNC,EAAI,WACJC,EAAU,SACVC,EAAQ,YACRC,EAAW,UACXC,GAAY,GACbN,EAEC,OAAKC,EAkBHM,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BAEbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,SAK7CD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,iBACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACK,EAAAA,EAAc,CAACC,YAAa,IAAKL,UAAU,eAMjDF,GACCC,EAAAA,cAAA,OAAKC,UAAU,OACbD,EAAAA,cAACT,EAAAA,EAAU,CAACU,UAAU,wCAKxBF,GAA6B,IAAhBJ,EAAKY,QAClBP,EAAAA,cAAA,OAAKC,UAAU,iFACbD,EAAAA,cAACQ,EAAAA,EAAQ,CAACP,UAAU,wCAAwC,+CAMhED,EAAAA,cAAA,OAAKC,UAAU,4CACZN,EAAKc,KAAKC,GACTV,EAAAA,cAAA,OAAKV,IAAKoB,EAAIC,GAAIV,UAAU,YAC1BD,EAAAA,cAAA,OACEC,UAAW,+FAERL,aAAU,EAAVA,EAAYe,MAAOD,EAAIC,GAAK,YAAc,iBAG/CX,EAAAA,cAAA,OACEC,UAAW,8EACTL,aAAU,EAAVA,EAAYe,MAAOD,EAAIC,GACnB,6BACA,sBAENP,QAASA,IAAMN,EAAYY,IAG3BV,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,QAAMC,UAAU,oBAAoBS,EAAIP,cAvElDH,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,iBACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACY,EAAAA,EAAa,CAACN,YAAa,IAAKL,UAAU,eAsE/C,gBClFV,MArB+BY,IAE3Bb,EAAAA,cAAA,OAAKC,UAAU,IACbD,EAAAA,cAAA,MAAIC,UAAU,4BAA2B,0DAIzCD,EAAAA,cAACc,EAAAA,EAAK,CACJb,UAAU,OACVc,QAAQ,gBACRC,YACEhB,EAAAA,cAAA,MAAIC,UAAU,iCACZD,EAAAA,cAAA,UAAI,6BAGRiB,KAAK,UCZN,MAGMC,EAAwCzB,IAAc,IAAb,IAAEiB,GAAKjB,EAE3D,MACO,iBADCiB,EAAIC,GAEDX,EAAAA,cAACa,EAAY,MAIlBb,EAAAA,cAAA,OAAKC,UAAU,kBAAiB,wBACTD,EAAAA,cAAA,cAASU,EAAIP,OAAe,wBAGzD,EC+DF,MAhFqCgB,KACnC,MAAM,EAACpB,EAAU,EAACqB,IAAgBC,EAAAA,EAAAA,WAAS,IACrC,EAAC1B,EAAK,EAAC2B,IAAWD,EAAAA,EAAAA,UAAgB,KAClC,EAACzB,EAAW,EAAC2B,IAAiBF,EAAAA,EAAAA,UAAqB,OACnD,EAACG,EAAc,EAACC,IAAoBJ,EAAAA,EAAAA,WAAS,KACjD,GAAsB,oBAAXK,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,eACpC,OAAkB,OAAXF,GAAkBG,KAAKC,MAAMJ,EACtC,CACA,OAAO,CAAI,IAiBb,OAbAK,EAAAA,EAAAA,YAAU,KACc,oBAAXN,QACTE,aAAaK,QAAQ,cAAeH,KAAKI,UAAUV,GACrD,GACC,CAACA,KAGJQ,EAAAA,EAAAA,YAAU,MACHpC,GAAcD,EAAKY,OAAS,GAC/BgB,EAAc5B,EAAK,GACrB,GACC,CAACA,EAAMC,IAGRI,EAAAA,cAAA,OAAKC,UAAU,kCAEbD,EAAAA,cAAA,OACEC,UAAW,0EACTuB,EAAgB,OAAS,SAG3BxB,EAAAA,cAACR,EAAW,CACVE,OAAQ8B,EACR7B,KAAMA,EACNC,WAAYA,EACZC,SAAUA,IAAM4B,GAAkBD,GAClC1B,YAAayB,EACbxB,UAAWA,KAKfC,EAAAA,cAAA,OACEC,UAAW,wDACTuB,EAAgB,QAAU,UAG5BxB,EAAAA,cAAA,OAAKC,UAAU,YAEbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,QAC1CL,GACCI,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACmC,EAAAA,EAAY,CAAClC,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBAAkBL,EAAWO,SAInDH,EAAAA,cAAA,OAAKC,UAAU,kEACbD,EAAAA,cAACZ,EAAAA,EAAa,CAACa,UAAU,oDAAqD,IAAI,sGAKnFL,EACCI,EAAAA,cAACkB,EAAU,CAACR,IAAKd,IAEjBI,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,kDAMzF,ECtDV,MArBiBR,IAAmB,IAAlB,KAAE2C,GAAW3C,EAC7B,OACEO,EAAAA,cAACqC,EAAAA,EAAM,CAACC,KAAMF,EAAKG,KAAKC,aAAcrC,MAAM,OAAOsC,KAAM,SACvDzC,EAAAA,cAAA,QAAM0C,MAAO,CAAEC,OAAQ,QAAU1C,UAAU,YACzCD,EAAAA,cAACmB,EAAW,OAEP,wDCJb,MAAMyB,GAAO,aAAiB,OAAQ,CACpC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMzD,IAAK,WAC/C,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,WAChC,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,oOCTlC,MAAM0D,EAAoB,CAACC,EAASC,EAAaC,EAAWC,EAAOC,KAAa,CAC9EC,WAAYL,EACZM,OAAQ,IAAG,QAAKH,EAAMI,cAAcJ,EAAMK,YAAYP,IACtD,CAAC,GAAGG,UAAkB,CACpBK,MAAOP,KAGEQ,EAAeP,IAC1B,MAAM,aACJQ,EACAC,mBAAoBC,EAAQ,SAC5BC,EAAQ,SACRC,EAAQ,SACRC,EAAQ,WACRC,EAAU,WACVC,EACAC,eAAgBC,EAAY,oBAC5BC,EAAmB,wBACnBC,EAAuB,UACvBC,EAAS,iBACTC,EAAgB,uBAChBC,EAAsB,eACtBC,GACEvB,EACJ,MAAO,CACL,CAACQ,GAAegB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAezB,IAAS,CACtE0B,SAAU,WACVC,QAAS,OACTC,WAAY,SACZC,QAASN,EACTO,SAAU,aACVb,eACA,CAAC,IAAIT,SAAqB,CACxBuB,UAAW,OAEb,CAAC,GAAGvB,aAAyB,CAC3BwB,KAAM,EACNC,SAAU,GAEZ,CAAC,GAAGzB,UAAsB,CACxB0B,gBAAiBvB,EACjBI,WAAY,GAEd,gBAAiB,CACfY,QAAS,OACTd,WACAE,cAEF,YAAa,CACXT,MAAOe,GAET,CAAC,IAAIb,kBAA8B,CACjC2B,SAAU,SACVC,QAAS,EACTC,WAAY,cAAc3B,KAAYQ,cAAgCR,KAAYQ,2BACpER,KAAYQ,qBAAuCR,KAAYQ,6BAC7DR,KAAYQ,KAE9B,CAAC,IAAIV,yBAAqC,CACxC8B,UAAW,EACXC,aAAc,eACdC,WAAY,EACZC,cAAe,EACfL,QAAS,KAGb,CAAC,GAAG5B,sBAAkC,CACpCoB,WAAY,aACZC,QAASP,EACT,CAAC,GAAGd,UAAsB,CACxB0B,gBAAiBtB,EACjBC,SAAUM,EACVJ,WAAY,GAEd,CAAC,GAAGP,aAAyB,CAC3BmB,QAAS,QACTY,aAAc5B,EACdL,MAAOe,EACPR,SAAUC,GAEZ,CAAC,GAAGN,iBAA6B,CAC/BmB,QAAS,QACTrB,MAAOc,IAGX,CAAC,GAAGZ,YAAwB,CAC1B+B,aAAc,EACdpC,OAAQ,eACRc,aAAc,GAEjB,EAEUyB,EAAe1C,IAC1B,MAAM,aACJQ,EAAY,aACZmC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,aACdC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,WACdC,EAAU,iBACVC,EAAgB,aAChBC,EAAY,UACZC,EAAS,gBACTC,EAAe,YACfC,GACEtD,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,YAAaZ,EAAkBiD,EAAgBD,EAAoBD,EAAc3C,EAAOQ,GACxF,SAAUZ,EAAkB0D,EAAaD,EAAiBD,EAAWpD,EAAOQ,GAC5E,YAAaZ,EAAkBoD,EAAgBD,EAAoBD,EAAc9C,EAAOQ,GACxF,UAAWgB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG7B,EAAkBuD,EAAcD,EAAkBD,EAAYjD,EAAOQ,IAAgB,CAC9H,CAAC,GAAGA,uBAAmC,CACrC+C,OAAQ,EACR1B,QAAS,MAIhB,EAEU2B,EAAiBxD,IAC5B,MAAM,aACJQ,EAAY,QACZiD,EAAO,kBACPC,EAAiB,SACjB/C,EAAQ,aACRgD,EAAY,UACZC,EAAS,eACTC,GACE7D,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,WAAY,CACVsD,kBAAmBnD,GAErB,CAAC,GAAGH,gBAA4B,CAC9BsD,kBAAmBnD,EACnBkB,QAAS,EACTM,SAAU,SACVtB,SAAU8C,EACV5C,YAAY,QAAK4C,GACjBI,gBAAiB,cACjB5D,OAAQ,OACR6D,QAAS,OACTC,OAAQ,UACR,CAAC,GAAGR,WAAkB,CACpBnD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,KAIb,eAAgB,CACdvD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,KAId,EAUH,OAAe,QAAc,SAAS7D,GAAS,CAACO,EAAaP,GAAQ0C,EAAa1C,GAAQwD,EAAexD,MARpEA,IAE5B,CACLmB,wBAAyBnB,EAAMkE,iBAC/B3C,eAAgB,GAAGvB,EAAMmE,kCACzB7C,uBAAwB,GAAGtB,EAAMoE,eAAepE,EAAMqE,mCC3KtDC,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAO/C,OAAOmD,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,mBAAjC/C,OAAOuD,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIlD,OAAOuD,sBAAsBR,GAAIS,EAAIN,EAAEvH,OAAQ6H,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKxD,OAAOmD,UAAUM,qBAAqBJ,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAeA,MAAMS,EAAgB,CACpBC,QAASC,EAAA,EACTC,KAAMC,EAAA,EACNC,MAAOC,EAAA,EACPC,QAASC,EAAA,GAELC,EAAWC,IACf,MAAM,KACJC,EAAI,UACJC,EAAS,KACTjI,GACE+H,EACEG,EAAWb,EAAcrH,IAAS,KACxC,OAAIgI,GACK,QAAeA,EAAmB,gBAAoB,OAAQ,CACnEhJ,UAAW,GAAGiJ,UACbD,IAAO,KAAM,CACdhJ,UAAW,IAAW,GAAGiJ,SAAkBD,EAAKD,MAAM/I,eAGtC,gBAAoBkJ,EAAU,CAChDlJ,UAAW,GAAGiJ,UACd,EAEEE,EAAgBJ,IACpB,MAAM,WACJK,EAAU,UACVH,EAAS,UACTI,EAAS,YACTC,EAAW,UACXC,GACER,EACES,GAAgC,IAAdH,QAAoCI,IAAdJ,EAAuC,gBAAoBK,EAAA,EAAe,MAAQL,EAChI,OAAOD,EAA2B,gBAAoB,SAAUzE,OAAOC,OAAO,CAC5E5D,KAAM,SACNb,QAASmJ,EACTtJ,UAAW,GAAGiJ,eACdU,SAAU,GACTJ,GAAYC,GAAoB,IAAI,EAEnC3I,EAAqB,cAAiB,CAACkI,EAAOa,KAClD,MAAM,YACF7I,EACAkI,UAAWY,EAAkB,QAC7B/I,EAAO,OACPgJ,EAAM,UACN9J,EAAS,cACT+J,EAAa,MACbtH,EAAK,aACLuH,EAAY,aACZC,EAAY,QACZ9J,EAAO,WACP+J,EAAU,SACVC,EAAQ,SACRC,EAAQ,UACRC,EAAS,UACThB,EAAS,OACTiB,EAAM,GACN5J,GACEqI,EACJwB,EAAa9C,EAAOsB,EAAO,CAAC,cAAe,YAAa,UAAW,SAAU,YAAa,gBAAiB,QAAS,eAAgB,eAAgB,UAAW,aAAc,WAAY,WAAY,YAAa,YAAa,SAAU,QACpOyB,EAAQC,GAAa,YAAe,GAK3C,MAAMC,EAAc,SAAa,MACjC,sBAA0Bd,GAAK,KAAM,CACnCe,cAAeD,EAAYE,YAE7B,MAAM,aACJC,EAAY,UACZ3F,EACAkF,SAAUU,EACVzB,UAAW0B,EACX/K,UAAWgL,EACXvI,MAAOwI,IACL,QAAmB,SACjBhC,EAAY4B,EAAa,QAAShB,IACjCqB,EAAYC,EAAQC,GAAa,EAASnC,GAC3CK,EAAc3B,IAClB,IAAI0D,EACJZ,GAAU,GACe,QAAxBY,EAAKtC,EAAMuC,eAA4B,IAAPD,GAAyBA,EAAGrD,KAAKe,EAAOpB,EAAE,EAEvE3G,EAAO,WAAc,SACNyI,IAAfV,EAAM/H,KACD+H,EAAM/H,KAGR8I,EAAS,UAAY,QAC3B,CAACf,EAAM/H,KAAM8I,IAEVV,EAAa,WAAc,MACP,iBAAbgB,IAAyBA,EAASf,eACzCgB,IAGoB,kBAAbD,EACFA,GAGS,IAAdf,SAAuBA,KAGlByB,KACR,CAACT,EAAWhB,EAAWe,EAAUU,IAE9BS,KAAazB,QAAuBL,IAAbU,IAAgCA,EACvD/G,EAAW,IAAW6F,EAAW,GAAGA,KAAajI,IAAQ,CAC7D,CAAC,GAAGiI,wBAAiClI,EACrC,CAAC,GAAGkI,cAAuBsC,EAC3B,CAAC,GAAGtC,cAAuBa,EAC3B,CAAC,GAAGb,SAAgC,QAAd/D,GACrB8F,EAAkBhL,EAAW+J,EAAeqB,EAAWD,GACpDK,GAAY,EAAAC,EAAA,GAAUlB,EAAY,CACtCmB,MAAM,EACNvJ,MAAM,IAEFqH,EAAkB,WAAc,IACZ,iBAAbY,GAAyBA,EAASf,UACpCe,EAASf,UAEdgB,SAGcZ,IAAdJ,EACKA,EAEsB,iBAApByB,GAAgCA,EAAgBzB,UAClDyB,EAAgBzB,UAElB0B,IACN,CAAC1B,EAAWe,EAAUC,EAAWU,IAC9BY,EAAkB,WAAc,KACpC,MAAMC,EAASxB,QAA2CA,EAAWU,EACrE,GAAsB,iBAAXc,EAAqB,CAC9B,MACIvC,UAAWwC,GACTD,EAEN,OADcnE,EAAOmE,EAAQ,CAAC,aAEhC,CACA,MAAO,CAAC,CAAC,GACR,CAACxB,EAAUU,IACd,OAAOI,EAAwB,gBAAoB,KAAW,CAC5DY,SAAUtB,EACVuB,WAAY,GAAG9C,WACf+C,cAAc,EACdC,aAAa,EACbC,aAAcC,IAAQ,CACpB1G,UAAW0G,EAAKC,eAElBC,WAAYnC,IACX,CAAC1K,EAAM8M,KACR,IACEtM,UAAWuM,EACX9J,MAAO+J,GACLhN,EACJ,OAAoB,gBAAoB,MAAOmF,OAAOC,OAAO,CAC3DlE,GAAIA,EACJkJ,KAAK,QAAWc,EAAa4B,GAC7B,aAAc9B,EACdxK,UAAW,IAAWoD,EAAUmJ,GAChC9J,MAAOkC,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGqG,GAAexI,GAAQ+J,GAC5ExC,aAAcA,EACdC,aAAcA,EACd9J,QAASA,EACTsM,KAAM,SACLjB,GAAYD,EAA2B,gBAAoBzC,EAAU,CACtE/H,YAAaA,EACbiI,KAAMD,EAAMC,KACZC,UAAWA,EACXjI,KAAMA,IACF,KAAmB,gBAAoB,MAAO,CAClDhB,UAAW,GAAGiJ,aACbnI,EAAuB,gBAAoB,MAAO,CACnDd,UAAW,GAAGiJ,aACbnI,GAAW,KAAMC,EAA2B,gBAAoB,MAAO,CACxEf,UAAW,GAAGiJ,iBACblI,GAAe,MAAOuJ,EAAsB,gBAAoB,MAAO,CACxEtK,UAAW,GAAGiJ,YACbqB,GAAU,KAAmB,gBAAoBnB,EAAe,CACjEC,WAAYA,EACZH,UAAWA,EACXI,UAAWG,EACXF,YAAaA,EACbC,UAAWoC,IACV,IACF,IAKL,wEClNA,IAAIe,EAA6B,SAAUC,GACzC,SAASD,IACP,IAAIE,ECPYhF,EAAGiF,EAAGlF,EDgBtB,OARA,OAAgBmF,KAAMJ,GCRN9E,EDSGkF,KCTAD,EDSMH,ECTH/E,EDSkBoF,UCRnCF,GAAI,EAAAG,EAAA,GAAeH,IDQxBD,GCR4B,EAAAK,EAAA,GAA0BrF,GAAG,EAAAsF,EAAA,KAA6BC,QAAQC,UAAUP,EAAGlF,GAAK,IAAI,EAAAqF,EAAA,GAAepF,GAAGyF,aAAeR,EAAES,MAAM1F,EAAGD,KDS1J4F,MAAQ,CACZ7E,WAAOe,EACPjB,KAAM,CACJgF,eAAgB,KAGbZ,CACT,CAEA,OADA,OAAUF,EAAeC,IAClB,OAAaD,EAAe,CAAC,CAClCrN,IAAK,oBACLoO,MAAO,SAA2B/E,EAAOF,GACvCsE,KAAKY,SAAS,CACZhF,QACAF,QAEJ,GACC,CACDnJ,IAAK,SACLoO,MAAO,WACL,MAAM,QACJ3M,EAAO,YACPC,EAAW,GACXL,EAAE,SACFiN,GACEb,KAAK/D,OACH,MACJL,EAAK,KACLF,GACEsE,KAAKS,MACHC,GAAkBhF,aAAmC,EAASA,EAAKgF,iBAAmB,KACtFI,OAAkC,IAAZ9M,GAA2B4H,GAAS,IAAImF,WAAa/M,EAC3EgN,OAA0C,IAAhB/M,EAA8ByM,EAAiBzM,EAC/E,OAAI2H,EACkB,gBAAoB,EAAO,CAC7ChI,GAAIA,EACJM,KAAM,QACNF,QAAS8M,EACT7M,YAA0B,gBAAoB,MAAO,CACnD0B,MAAO,CACLuB,SAAU,QACV+J,UAAW,SAEZD,KAGAH,CACT,IAEJ,CAtDiC,CAsD/B,aACF,QE3DA,MAAM,EAAQ,EACd,EAAMjB,cAAgB,EACtB,8DCGA,MAAMxK,GAAe,aAAiB,eAAgB,CACpD,CAAC,OAAQ,CAAE9C,EAAG,gBAAiBC,IAAK", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-ccw.js", "webpack://autogentstudio/./src/components/views/labs/sidebar.tsx", "webpack://autogentstudio/./src/components/views/labs/labs/component.tsx", "webpack://autogentstudio/./src/components/views/labs/labs/guides.tsx", "webpack://autogentstudio/./src/components/views/labs/manager.tsx", "webpack://autogentstudio/./src/pages/labs.tsx", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/info.js", "webpack://autogentstudio/./node_modules/antd/es/alert/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/Alert.js", "webpack://autogentstudio/./node_modules/antd/es/alert/ErrorBoundary.js", "webpack://autogentstudio/./node_modules/@babel/runtime/helpers/esm/callSuper.js", "webpack://autogentstudio/./node_modules/antd/es/alert/index.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/chevron-right.js"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst TriangleAlert = createLucideIcon(\"TriangleAlert\", [\n  [\n    \"path\",\n    {\n      d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n      key: \"wmoenq\"\n    }\n  ],\n  [\"path\", { d: \"M12 9v4\", key: \"juzpu7\" }],\n  [\"path\", { d: \"M12 17h.01\", key: \"p32p05\" }]\n]);\n\nexport { TriangleAlert as default };\n//# sourceMappingURL=triangle-alert.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCcw = createLucideIcon(\"RefreshCcw\", [\n  [\"path\", { d: \"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\", key: \"14sxne\" }],\n  [\"path\", { d: \"M3 3v5h5\", key: \"1xhq8a\" }],\n  [\"path\", { d: \"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16\", key: \"1hlbsb\" }],\n  [\"path\", { d: \"M16 16h5v5\", key: \"ccwih5\" }]\n]);\n\nexport { RefreshCcw as default };\n//# sourceMappingURL=refresh-ccw.js.map\n", "import React from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"antd\";\nimport {\n  PanelLeftClose,\n  PanelLeftOpen,\n  Book,\n  InfoIcon,\n  RefreshCcw,\n} from \"lucide-react\";\nimport type { Lab } from \"./types\";\n\ninterface LabsSidebarProps {\n  isOpen: boolean;\n  labs: Lab[];\n  currentLab: Lab | null;\n  onToggle: () => void;\n  onSelectLab: (guide: Lab) => void;\n  isLoading?: boolean;\n}\n\nexport const LabsSidebar: React.FC<LabsSidebarProps> = ({\n  isOpen,\n  labs,\n  currentLab,\n  onToggle,\n  onSelectLab,\n  isLoading = false,\n}) => {\n  // Render collapsed state\n  if (!isOpen) {\n    return (\n      <div className=\"h-full border-r border-secondary\">\n        <div className=\"p-2 -ml-2\">\n          <Tooltip title=\"Documentation\">\n            <button\n              onClick={onToggle}\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n            >\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\n            </button>\n          </Tooltip>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full border-r border-secondary\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\n        <div className=\"flex items-center gap-2\">\n          {/* <Book className=\"w-4 h-4\" /> */}\n          <span className=\"text-primary font-medium\">Labs</span>\n          {/* <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\n            {guides.length}\n          </span> */}\n        </div>\n        <Tooltip title=\"Close Sidebar\">\n          <button\n            onClick={onToggle}\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\n          >\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\n          </button>\n        </Tooltip>\n      </div>\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"p-4\">\n          <RefreshCcw className=\"w-4 h-4 inline-block animate-spin\" />\n        </div>\n      )}\n\n      {/* Empty State */}\n      {!isLoading && labs.length === 0 && (\n        <div className=\"p-2 mt-2 mr-2 text-center text-secondary text-sm border border-dashed rounded\">\n          <InfoIcon className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\n          No labs available. Please check back later.\n        </div>\n      )}\n\n      {/* Guides List */}\n      <div className=\"overflow-y-auto h-[calc(100%-64px)] mt-4\">\n        {labs.map((lab) => (\n          <div key={lab.id} className=\"relative\">\n            <div\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80 rounded ${\n                 currentLab?.id === lab.id ? \"bg-accent\" : \"bg-tertiary\"\n               }`}\n            />\n            <div\n              className={`group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary ${\n                currentLab?.id === lab.id\n                  ? \"border-accent bg-secondary\"\n                  : \"border-transparent\"\n              }`}\n              onClick={() => onSelectLab(lab)}\n            >\n              {/* Guide Title */}\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm truncate\">{lab.title}</span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { Alert } from \"antd\";\nimport { copyToClipboard } from \"./guides\";\nimport { Download } from \"lucide-react\";\n\nconst ComponentLab: React.FC = () => {\n  return (\n    <div className=\"\">\n      <h1 className=\"tdext-2xl font-bold mb-6\">\n        Using AutoGen Studio Teams in Python Code and REST API\n      </h1>\n\n      <Alert\n        className=\"mb-6\"\n        message=\"Prerequisites\"\n        description={\n          <ul className=\"list-disc pl-4 mt-2 space-y-1\">\n            <li>AutoGen Studio installed</li>\n          </ul>\n        }\n        type=\"info\"\n      />\n    </div>\n  );\n};\n\nexport default ComponentLab;\n", "import React from \"react\";\nimport { Lab } from \"../types\";\nimport ComponentLab from \"./component\";\n\ninterface LabContentProps {\n  lab: Lab;\n}\n\nexport const copyToClipboard = (text: string) => {\n  navigator.clipboard.writeText(text);\n};\nexport const LabContent: React.FC<LabContentProps> = ({ lab }) => {\n  // Render different content based on guide type and id\n  switch (lab.id) {\n    case \"python-setup\":\n      return <ComponentLab />;\n\n    default:\n      return (\n        <div className=\"text-secondary\">\n          A Lab with the title <strong>{lab.title}</strong> is work in progress!\n        </div>\n      );\n  }\n};\n\nexport default LabContent;\n", "import React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON>ronR<PERSON>, TriangleAlert } from \"lucide-react\";\nimport { LabsSidebar } from \"./sidebar\";\nimport { Lab, defaultLabs } from \"./types\";\nimport { LabContent } from \"./labs/guides\";\n\nexport const LabsManager: React.FC = () => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [labs, setLabs] = useState<Lab[]>([]);\n  const [currentLab, setcurrentLab] = useState<Lab | null>(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\n    if (typeof window !== \"undefined\") {\n      const stored = localStorage.getItem(\"labsSidebar\");\n      return stored !== null ? JSON.parse(stored) : true;\n    }\n    return true;\n  });\n\n  // Persist sidebar state\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"labsSidebar\", JSON.stringify(isSidebarOpen));\n    }\n  }, [isSidebarOpen]);\n\n  // Set first guide as current if none selected\n  useEffect(() => {\n    if (!currentLab && labs.length > 0) {\n      setcurrentLab(labs[0]);\n    }\n  }, [labs, currentLab]);\n\n  return (\n    <div className=\"relative    flex h-full w-full\">\n      {/* Sidebar */}\n      <div\n        className={`absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out ${\n          isSidebarOpen ? \"w-64\" : \"w-12\"\n        }`}\n      >\n        <LabsSidebar\n          isOpen={isSidebarOpen}\n          labs={labs}\n          currentLab={currentLab}\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\n          onSelectLab={setcurrentLab}\n          isLoading={isLoading}\n        />\n      </div>\n\n      {/* Main Content */}\n      <div\n        className={`flex-1 transition-all max-w-5xl  -mr-6 duration-200 ${\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\n        }`}\n      >\n        <div className=\"p-4 pt-2\">\n          {/* Breadcrumb */}\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\n            <span className=\"text-primary font-medium\">Labs</span>\n            {currentLab && (\n              <>\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\n                <span className=\"text-secondary\">{currentLab.title}</span>\n              </>\n            )}\n          </div>\n          <div className=\"rounded border border-secondary border-dashed p-2 text-sm mb-4\">\n            <TriangleAlert className=\"w-4 h-4 inline-block mr-2 -mt-1 text-secondary \" />{\" \"}\n            Labs is designed to host experimental features for building and\n            debugging multiagent applications.\n          </div>\n          {/* Content Area */}\n          {currentLab ? (\n            <LabContent lab={currentLab} />\n          ) : (\n            <div className=\"flex items-center justify-center h-[calc(100vh-190px)] text-secondary\">\n              Select a lab from the sidebar to get started\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LabsManager;\n", "import * as React from \"react\";\nimport Layout from \"../components/layout\";\nimport { graphql } from \"gatsby\";\nimport DeployManager from \"../components/views/deploy/manager\";\nimport LabsManager from \"../components/views/labs/manager\";\n\n// markup\nconst LabsPage = ({ data }: any) => {\n  return (\n    <Layout meta={data.site.siteMetadata} title=\"Home\" link={\"/labs\"}>\n      <main style={{ height: \"100%\" }} className=\" h-full \">\n        <LabsManager />\n      </main>\n    </Layout>\n  );\n};\n\nexport const query = graphql`\n  query HomePageQuery {\n    site {\n      siteMetadata {\n        description\n        title\n      }\n    }\n  }\n`;\n\nexport default LabsPage;\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Info = createLucideIcon(\"Info\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 16v-4\", key: \"1dtifu\" }],\n  [\"path\", { d: \"M12 8h.01\", key: \"e9boi3\" }]\n]);\n\nexport { Info as default };\n//# sourceMappingURL=info.js.map\n", "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genAlertTypeStyle = (bgColor, borderColor, iconColor, token, alertCls) => ({\n  background: bgColor,\n  border: `${unit(token.lineWidth)} ${token.lineType} ${borderColor}`,\n  [`${alertCls}-icon`]: {\n    color: iconColor\n  }\n});\nexport const genBaseStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow: duration,\n    marginXS,\n    marginSM,\n    fontSize,\n    fontSizeLG,\n    lineHeight,\n    borderRadiusLG: borderRadius,\n    motionEaseInOutCirc,\n    withDescriptionIconSize,\n    colorText,\n    colorTextHeading,\n    withDescriptionPadding,\n    defaultPadding\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      padding: defaultPadding,\n      wordWrap: 'break-word',\n      borderRadius,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-content`]: {\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginXS,\n        lineHeight: 0\n      },\n      '&-description': {\n        display: 'none',\n        fontSize,\n        lineHeight\n      },\n      '&-message': {\n        color: colorTextHeading\n      },\n      [`&${componentCls}-motion-leave`]: {\n        overflow: 'hidden',\n        opacity: 1,\n        transition: `max-height ${duration} ${motionEaseInOutCirc}, opacity ${duration} ${motionEaseInOutCirc},\n        padding-top ${duration} ${motionEaseInOutCirc}, padding-bottom ${duration} ${motionEaseInOutCirc},\n        margin-bottom ${duration} ${motionEaseInOutCirc}`\n      },\n      [`&${componentCls}-motion-leave-active`]: {\n        maxHeight: 0,\n        marginBottom: '0 !important',\n        paddingTop: 0,\n        paddingBottom: 0,\n        opacity: 0\n      }\n    }),\n    [`${componentCls}-with-description`]: {\n      alignItems: 'flex-start',\n      padding: withDescriptionPadding,\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginSM,\n        fontSize: withDescriptionIconSize,\n        lineHeight: 0\n      },\n      [`${componentCls}-message`]: {\n        display: 'block',\n        marginBottom: marginXS,\n        color: colorTextHeading,\n        fontSize: fontSizeLG\n      },\n      [`${componentCls}-description`]: {\n        display: 'block',\n        color: colorText\n      }\n    },\n    [`${componentCls}-banner`]: {\n      marginBottom: 0,\n      border: '0 !important',\n      borderRadius: 0\n    }\n  };\n};\nexport const genTypeStyle = token => {\n  const {\n    componentCls,\n    colorSuccess,\n    colorSuccessBorder,\n    colorSuccessBg,\n    colorWarning,\n    colorWarningBorder,\n    colorWarningBg,\n    colorError,\n    colorErrorBorder,\n    colorErrorBg,\n    colorInfo,\n    colorInfoBorder,\n    colorInfoBg\n  } = token;\n  return {\n    [componentCls]: {\n      '&-success': genAlertTypeStyle(colorSuccessBg, colorSuccessBorder, colorSuccess, token, componentCls),\n      '&-info': genAlertTypeStyle(colorInfoBg, colorInfoBorder, colorInfo, token, componentCls),\n      '&-warning': genAlertTypeStyle(colorWarningBg, colorWarningBorder, colorWarning, token, componentCls),\n      '&-error': Object.assign(Object.assign({}, genAlertTypeStyle(colorErrorBg, colorErrorBorder, colorError, token, componentCls)), {\n        [`${componentCls}-description > pre`]: {\n          margin: 0,\n          padding: 0\n        }\n      })\n    }\n  };\n};\nexport const genActionStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    motionDurationMid,\n    marginXS,\n    fontSizeIcon,\n    colorIcon,\n    colorIconHover\n  } = token;\n  return {\n    [componentCls]: {\n      '&-action': {\n        marginInlineStart: marginXS\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: marginXS,\n        padding: 0,\n        overflow: 'hidden',\n        fontSize: fontSizeIcon,\n        lineHeight: unit(fontSizeIcon),\n        backgroundColor: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        [`${iconCls}-close`]: {\n          color: colorIcon,\n          transition: `color ${motionDurationMid}`,\n          '&:hover': {\n            color: colorIconHover\n          }\n        }\n      },\n      '&-close-text': {\n        color: colorIcon,\n        transition: `color ${motionDurationMid}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const paddingHorizontal = 12; // Fixed value here.\n  return {\n    withDescriptionIconSize: token.fontSizeHeading3,\n    defaultPadding: `${token.paddingContentVerticalSM}px ${paddingHorizontal}px`,\n    withDescriptionPadding: `${token.paddingMD}px ${token.paddingContentHorizontalLG}px`\n  };\n};\nexport default genStyleHooks('Alert', token => [genBaseStyle(token), genTypeStyle(token), genActionStyle(token)], prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport InfoCircleFilled from \"@ant-design/icons/es/icons/InfoCircleFilled\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst iconMapFilled = {\n  success: CheckCircleFilled,\n  info: InfoCircleFilled,\n  error: CloseCircleFilled,\n  warning: ExclamationCircleFilled\n};\nconst IconNode = props => {\n  const {\n    icon,\n    prefixCls,\n    type\n  } = props;\n  const iconType = iconMapFilled[type] || null;\n  if (icon) {\n    return replaceElement(icon, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-icon`\n    }, icon), () => ({\n      className: classNames(`${prefixCls}-icon`, icon.props.className)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(iconType, {\n    className: `${prefixCls}-icon`\n  });\n};\nconst CloseIconNode = props => {\n  const {\n    isClosable,\n    prefixCls,\n    closeIcon,\n    handleClose,\n    ariaProps\n  } = props;\n  const mergedCloseIcon = closeIcon === true || closeIcon === undefined ? /*#__PURE__*/React.createElement(CloseOutlined, null) : closeIcon;\n  return isClosable ? (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\",\n    onClick: handleClose,\n    className: `${prefixCls}-close-icon`,\n    tabIndex: 0\n  }, ariaProps), mergedCloseIcon)) : null;\n};\nconst Alert = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      description,\n      prefixCls: customizePrefixCls,\n      message,\n      banner,\n      className,\n      rootClassName,\n      style,\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      afterClose,\n      showIcon,\n      closable,\n      closeText,\n      closeIcon,\n      action,\n      id\n    } = props,\n    otherProps = __rest(props, [\"description\", \"prefixCls\", \"message\", \"banner\", \"className\", \"rootClassName\", \"style\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"afterClose\", \"showIcon\", \"closable\", \"closeText\", \"closeIcon\", \"action\", \"id\"]);\n  const [closed, setClosed] = React.useState(false);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Alert');\n    warning.deprecated(!closeText, 'closeText', 'closable.closeIcon');\n  }\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const {\n    getPrefixCls,\n    direction,\n    closable: contextClosable,\n    closeIcon: contextCloseIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('alert');\n  const prefixCls = getPrefixCls('alert', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const handleClose = e => {\n    var _a;\n    setClosed(true);\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props, e);\n  };\n  const type = React.useMemo(() => {\n    if (props.type !== undefined) {\n      return props.type;\n    }\n    // banner mode defaults to 'warning'\n    return banner ? 'warning' : 'info';\n  }, [props.type, banner]);\n  // closeable when closeText or closeIcon is assigned\n  const isClosable = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) return true;\n    if (closeText) {\n      return true;\n    }\n    if (typeof closable === 'boolean') {\n      return closable;\n    }\n    // should be true when closeIcon is 0 or ''\n    if (closeIcon !== false && closeIcon !== null && closeIcon !== undefined) {\n      return true;\n    }\n    return !!contextClosable;\n  }, [closeText, closeIcon, closable, contextClosable]);\n  // banner mode defaults to Icon\n  const isShowIcon = banner && showIcon === undefined ? true : showIcon;\n  const alertCls = classNames(prefixCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-description`]: !!description,\n    [`${prefixCls}-no-icon`]: !isShowIcon,\n    [`${prefixCls}-banner`]: !!banner,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, cssVarCls, hashId);\n  const restProps = pickAttrs(otherProps, {\n    aria: true,\n    data: true\n  });\n  const mergedCloseIcon = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) {\n      return closable.closeIcon;\n    }\n    if (closeText) {\n      return closeText;\n    }\n    if (closeIcon !== undefined) {\n      return closeIcon;\n    }\n    if (typeof contextClosable === 'object' && contextClosable.closeIcon) {\n      return contextClosable.closeIcon;\n    }\n    return contextCloseIcon;\n  }, [closeIcon, closable, closeText, contextCloseIcon]);\n  const mergedAriaProps = React.useMemo(() => {\n    const merged = closable !== null && closable !== void 0 ? closable : contextClosable;\n    if (typeof merged === 'object') {\n      const {\n          closeIcon: _\n        } = merged,\n        ariaProps = __rest(merged, [\"closeIcon\"]);\n      return ariaProps;\n    }\n    return {};\n  }, [closable, contextClosable]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !closed,\n    motionName: `${prefixCls}-motion`,\n    motionAppear: false,\n    motionEnter: false,\n    onLeaveStart: node => ({\n      maxHeight: node.offsetHeight\n    }),\n    onLeaveEnd: afterClose\n  }, (_ref, setRef) => {\n    let {\n      className: motionClassName,\n      style: motionStyle\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n      id: id,\n      ref: composeRef(internalRef, setRef),\n      \"data-show\": !closed,\n      className: classNames(alertCls, motionClassName),\n      style: Object.assign(Object.assign(Object.assign({}, contextStyle), style), motionStyle),\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      role: \"alert\"\n    }, restProps), isShowIcon ? (/*#__PURE__*/React.createElement(IconNode, {\n      description: description,\n      icon: props.icon,\n      prefixCls: prefixCls,\n      type: type\n    })) : null, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-content`\n    }, message ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-message`\n    }, message) : null, description ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-description`\n    }, description) : null), action ? /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-action`\n    }, action) : null, /*#__PURE__*/React.createElement(CloseIconNode, {\n      isClosable: isClosable,\n      prefixCls: prefixCls,\n      closeIcon: mergedCloseIcon,\n      handleClose: handleClose,\n      ariaProps: mergedAriaProps\n    }));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Alert.displayName = 'Alert';\n}\nexport default Alert;", "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };", "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronRight = createLucideIcon(\"ChevronRight\", [\n  [\"path\", { d: \"m9 18 6-6-6-6\", key: \"mthhwq\" }]\n]);\n\nexport { ChevronRight as default };\n//# sourceMappingURL=chevron-right.js.map\n"], "names": ["Triangle<PERSON><PERSON><PERSON>", "d", "key", "RefreshCcw", "LabsSidebar", "_ref", "isOpen", "labs", "currentLab", "onToggle", "onSelectLab", "isLoading", "React", "className", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "length", "InfoIcon", "map", "lab", "id", "PanelLeftOpen", "ComponentLab", "<PERSON><PERSON>", "message", "description", "type", "Lab<PERSON><PERSON>nt", "LabsManager", "setIsLoading", "useState", "setLabs", "setcurrentLab", "isSidebarOpen", "setIsSidebarOpen", "window", "stored", "localStorage", "getItem", "JSON", "parse", "useEffect", "setItem", "stringify", "ChevronRight", "data", "Layout", "meta", "site", "siteMetadata", "link", "style", "height", "Info", "cx", "cy", "r", "genAlertTypeStyle", "bgColor", "borderColor", "iconColor", "token", "alertCls", "background", "border", "lineWidth", "lineType", "color", "genBaseStyle", "componentCls", "motionDurationSlow", "duration", "marginXS", "marginSM", "fontSize", "fontSizeLG", "lineHeight", "borderRadiusLG", "borderRadius", "motionEaseInOutCirc", "withDescriptionIconSize", "colorText", "colorTextHeading", "withDescriptionPadding", "defaultPadding", "Object", "assign", "position", "display", "alignItems", "padding", "wordWrap", "direction", "flex", "min<PERSON><PERSON><PERSON>", "marginInlineEnd", "overflow", "opacity", "transition", "maxHeight", "marginBottom", "paddingTop", "paddingBottom", "genTypeStyle", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "margin", "genActionStyle", "iconCls", "motionDurationMid", "fontSizeIcon", "colorIcon", "colorIconHover", "marginInlineStart", "backgroundColor", "outline", "cursor", "fontSizeHeading3", "paddingContentVerticalSM", "paddingMD", "paddingContentHorizontalLG", "__rest", "s", "e", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "iconMapFilled", "success", "CheckCircleFilled", "info", "InfoCircleFilled", "error", "CloseCircleFilled", "warning", "ExclamationCircleFilled", "IconNode", "props", "icon", "prefixCls", "iconType", "CloseIconNode", "isClosable", "closeIcon", "handleClose", "ariaProps", "mergedCloseIcon", "undefined", "CloseOutlined", "tabIndex", "ref", "customizePrefixCls", "banner", "rootClassName", "onMouseEnter", "onMouseLeave", "afterClose", "showIcon", "closable", "closeText", "action", "otherProps", "closed", "setClosed", "internalRef", "nativeElement", "current", "getPrefixCls", "contextClosable", "contextCloseIcon", "contextClassName", "contextStyle", "wrapCSSVar", "hashId", "cssVarCls", "_a", "onClose", "isShowIcon", "restProps", "pickAttrs", "aria", "mergedAriaProps", "merged", "_", "visible", "motionName", "motionAppear", "motionEnter", "onLeaveStart", "node", "offsetHeight", "onLeaveEnd", "setRef", "motionClassName", "motionStyle", "role", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "o", "this", "arguments", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "state", "componentStack", "value", "setState", "children", "errorMessage", "toString", "errorDescription", "overflowX"], "sourceRoot": ""}