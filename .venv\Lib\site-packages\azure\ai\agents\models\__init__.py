# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=wrong-import-position

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._patch import *  # pylint: disable=unused-wildcard-import


from ._models import (  # type: ignore
    AISearchIndexResource,
    Agent,
    AgentErrorDetail,
    AgentThread,
    AgentThreadCreationO<PERSON>s,
    Agent<PERSON>1Error,
    AgentsNamedToolChoice,
    AgentsResponseFormat,
    AzureAISearchToolDefinition,
    AzureAISearchToolResource,
    AzureFunctionBinding,
    AzureFunctionDefinition,
    AzureFunctionStorageQueue,
    AzureFunctionToolDefinition,
    BingCustomSearchConfiguration,
    BingCustomSearchToolDefinition,
    BingCustomSearchToolParameters,
    BingGroundingSearchConfiguration,
    BingGroundingSearchToolParameters,
    BingGroundingToolDefinition,
    CodeInterpreterToolDefinition,
    CodeInterpreterToolResource,
    ConnectedAgentDetails,
    ConnectedAgentToolDefinition,
    FabricDataAgentToolParameters,
    FileInfo,
    FileListResponse,
    FileSearchRankingOptions,
    FileSearchToolCallContent,
    FileSearchToolDefinition,
    FileSearchToolDefinitionDetails,
    FileSearchToolResource,
    FunctionDefinition,
    FunctionName,
    FunctionToolDefinition,
    IncompleteRunDetails,
    MessageAttachment,
    MessageContent,
    MessageDelta,
    MessageDeltaChunk,
    MessageDeltaContent,
    MessageDeltaImageFileContent,
    MessageDeltaImageFileContentObject,
    MessageDeltaTextAnnotation,
    MessageDeltaTextContent,
    MessageDeltaTextContentObject,
    MessageDeltaTextFileCitationAnnotation,
    MessageDeltaTextFileCitationAnnotationObject,
    MessageDeltaTextFilePathAnnotation,
    MessageDeltaTextFilePathAnnotationObject,
    MessageDeltaTextUrlCitationAnnotation,
    MessageDeltaTextUrlCitationDetails,
    MessageImageFileContent,
    MessageImageFileDetails,
    MessageImageFileParam,
    MessageImageUrlParam,
    MessageIncompleteDetails,
    MessageInputContentBlock,
    MessageInputImageFileBlock,
    MessageInputImageUrlBlock,
    MessageInputTextBlock,
    MessageTextAnnotation,
    MessageTextContent,
    MessageTextDetails,
    MessageTextFileCitationAnnotation,
    MessageTextFileCitationDetails,
    MessageTextFilePathAnnotation,
    MessageTextFilePathDetails,
    MessageTextUrlCitationAnnotation,
    MessageTextUrlCitationDetails,
    MicrosoftFabricToolDefinition,
    OpenApiAnonymousAuthDetails,
    OpenApiAuthDetails,
    OpenApiConnectionAuthDetails,
    OpenApiConnectionSecurityScheme,
    OpenApiFunctionDefinition,
    OpenApiManagedAuthDetails,
    OpenApiManagedSecurityScheme,
    OpenApiToolDefinition,
    RequiredAction,
    RequiredFunctionToolCall,
    RequiredFunctionToolCallDetails,
    RequiredToolCall,
    ResponseFormatJsonSchema,
    ResponseFormatJsonSchemaType,
    RunCompletionUsage,
    RunError,
    RunStep,
    RunStepAzureAISearchToolCall,
    RunStepBingCustomSearchToolCall,
    RunStepBingGroundingToolCall,
    RunStepCodeInterpreterImageOutput,
    RunStepCodeInterpreterImageReference,
    RunStepCodeInterpreterLogOutput,
    RunStepCodeInterpreterToolCall,
    RunStepCodeInterpreterToolCallDetails,
    RunStepCodeInterpreterToolCallOutput,
    RunStepCompletionUsage,
    RunStepDelta,
    RunStepDeltaChunk,
    RunStepDeltaCodeInterpreterDetailItemObject,
    RunStepDeltaCodeInterpreterImageOutput,
    RunStepDeltaCodeInterpreterImageOutputObject,
    RunStepDeltaCodeInterpreterLogOutput,
    RunStepDeltaCodeInterpreterOutput,
    RunStepDeltaCodeInterpreterToolCall,
    RunStepDeltaDetail,
    RunStepDeltaFileSearchToolCall,
    RunStepDeltaFunction,
    RunStepDeltaFunctionToolCall,
    RunStepDeltaMessageCreation,
    RunStepDeltaMessageCreationObject,
    RunStepDeltaToolCall,
    RunStepDeltaToolCallObject,
    RunStepDetails,
    RunStepError,
    RunStepFileSearchToolCall,
    RunStepFileSearchToolCallResult,
    RunStepFileSearchToolCallResults,
    RunStepFunctionToolCall,
    RunStepFunctionToolCallDetails,
    RunStepMessageCreationDetails,
    RunStepMessageCreationReference,
    RunStepMicrosoftFabricToolCall,
    RunStepOpenAPIToolCall,
    RunStepSharepointToolCall,
    RunStepToolCall,
    RunStepToolCallDetails,
    SharepointGroundingToolParameters,
    SharepointToolDefinition,
    SubmitToolOutputsAction,
    SubmitToolOutputsDetails,
    ThreadMessage,
    ThreadMessageOptions,
    ThreadRun,
    ToolConnection,
    ToolDefinition,
    ToolOutput,
    ToolResources,
    TruncationObject,
    VectorStore,
    VectorStoreAutoChunkingStrategyRequest,
    VectorStoreAutoChunkingStrategyResponse,
    VectorStoreChunkingStrategyRequest,
    VectorStoreChunkingStrategyResponse,
    VectorStoreConfiguration,
    VectorStoreConfigurations,
    VectorStoreDataSource,
    VectorStoreExpirationPolicy,
    VectorStoreFile,
    VectorStoreFileBatch,
    VectorStoreFileCount,
    VectorStoreFileError,
    VectorStoreStaticChunkingStrategyOptions,
    VectorStoreStaticChunkingStrategyRequest,
    VectorStoreStaticChunkingStrategyResponse,
)

from ._enums import (  # type: ignore
    AgentStreamEvent,
    AgentsNamedToolChoiceType,
    AgentsResponseFormatMode,
    AgentsToolChoiceOptionMode,
    AzureAISearchQueryType,
    DoneEvent,
    ErrorEvent,
    FilePurpose,
    FileState,
    ImageDetailLevel,
    IncompleteDetailsReason,
    ListSortOrder,
    MessageBlockType,
    MessageIncompleteDetailsReason,
    MessageRole,
    MessageStatus,
    MessageStreamEvent,
    OpenApiAuthType,
    ResponseFormat,
    RunAdditionalFieldList,
    RunStatus,
    RunStepErrorCode,
    RunStepStatus,
    RunStepStreamEvent,
    RunStepType,
    RunStreamEvent,
    ThreadStreamEvent,
    TruncationStrategy,
    VectorStoreChunkingStrategyRequestType,
    VectorStoreChunkingStrategyResponseType,
    VectorStoreDataSourceAssetType,
    VectorStoreExpirationPolicyAnchor,
    VectorStoreFileBatchStatus,
    VectorStoreFileErrorCode,
    VectorStoreFileStatus,
    VectorStoreFileStatusFilter,
    VectorStoreStatus,
)
from ._patch import __all__ as _patch_all
from ._patch import *
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "AISearchIndexResource",
    "Agent",
    "AgentErrorDetail",
    "AgentThread",
    "AgentThreadCreationOptions",
    "AgentV1Error",
    "AgentsNamedToolChoice",
    "AgentsResponseFormat",
    "AzureAISearchToolDefinition",
    "AzureAISearchToolResource",
    "AzureFunctionBinding",
    "AzureFunctionDefinition",
    "AzureFunctionStorageQueue",
    "AzureFunctionToolDefinition",
    "BingCustomSearchConfiguration",
    "BingCustomSearchToolDefinition",
    "BingCustomSearchToolParameters",
    "BingGroundingSearchConfiguration",
    "BingGroundingSearchToolParameters",
    "BingGroundingToolDefinition",
    "CodeInterpreterToolDefinition",
    "CodeInterpreterToolResource",
    "ConnectedAgentDetails",
    "ConnectedAgentToolDefinition",
    "FabricDataAgentToolParameters",
    "FileInfo",
    "FileListResponse",
    "FileSearchRankingOptions",
    "FileSearchToolCallContent",
    "FileSearchToolDefinition",
    "FileSearchToolDefinitionDetails",
    "FileSearchToolResource",
    "FunctionDefinition",
    "FunctionName",
    "FunctionToolDefinition",
    "IncompleteRunDetails",
    "MessageAttachment",
    "MessageContent",
    "MessageDelta",
    "MessageDeltaChunk",
    "MessageDeltaContent",
    "MessageDeltaImageFileContent",
    "MessageDeltaImageFileContentObject",
    "MessageDeltaTextAnnotation",
    "MessageDeltaTextContent",
    "MessageDeltaTextContentObject",
    "MessageDeltaTextFileCitationAnnotation",
    "MessageDeltaTextFileCitationAnnotationObject",
    "MessageDeltaTextFilePathAnnotation",
    "MessageDeltaTextFilePathAnnotationObject",
    "MessageDeltaTextUrlCitationAnnotation",
    "MessageDeltaTextUrlCitationDetails",
    "MessageImageFileContent",
    "MessageImageFileDetails",
    "MessageImageFileParam",
    "MessageImageUrlParam",
    "MessageIncompleteDetails",
    "MessageInputContentBlock",
    "MessageInputImageFileBlock",
    "MessageInputImageUrlBlock",
    "MessageInputTextBlock",
    "MessageTextAnnotation",
    "MessageTextContent",
    "MessageTextDetails",
    "MessageTextFileCitationAnnotation",
    "MessageTextFileCitationDetails",
    "MessageTextFilePathAnnotation",
    "MessageTextFilePathDetails",
    "MessageTextUrlCitationAnnotation",
    "MessageTextUrlCitationDetails",
    "MicrosoftFabricToolDefinition",
    "OpenApiAnonymousAuthDetails",
    "OpenApiAuthDetails",
    "OpenApiConnectionAuthDetails",
    "OpenApiConnectionSecurityScheme",
    "OpenApiFunctionDefinition",
    "OpenApiManagedAuthDetails",
    "OpenApiManagedSecurityScheme",
    "OpenApiToolDefinition",
    "RequiredAction",
    "RequiredFunctionToolCall",
    "RequiredFunctionToolCallDetails",
    "RequiredToolCall",
    "ResponseFormatJsonSchema",
    "ResponseFormatJsonSchemaType",
    "RunCompletionUsage",
    "RunError",
    "RunStep",
    "RunStepAzureAISearchToolCall",
    "RunStepBingCustomSearchToolCall",
    "RunStepBingGroundingToolCall",
    "RunStepCodeInterpreterImageOutput",
    "RunStepCodeInterpreterImageReference",
    "RunStepCodeInterpreterLogOutput",
    "RunStepCodeInterpreterToolCall",
    "RunStepCodeInterpreterToolCallDetails",
    "RunStepCodeInterpreterToolCallOutput",
    "RunStepCompletionUsage",
    "RunStepDelta",
    "RunStepDeltaChunk",
    "RunStepDeltaCodeInterpreterDetailItemObject",
    "RunStepDeltaCodeInterpreterImageOutput",
    "RunStepDeltaCodeInterpreterImageOutputObject",
    "RunStepDeltaCodeInterpreterLogOutput",
    "RunStepDeltaCodeInterpreterOutput",
    "RunStepDeltaCodeInterpreterToolCall",
    "RunStepDeltaDetail",
    "RunStepDeltaFileSearchToolCall",
    "RunStepDeltaFunction",
    "RunStepDeltaFunctionToolCall",
    "RunStepDeltaMessageCreation",
    "RunStepDeltaMessageCreationObject",
    "RunStepDeltaToolCall",
    "RunStepDeltaToolCallObject",
    "RunStepDetails",
    "RunStepError",
    "RunStepFileSearchToolCall",
    "RunStepFileSearchToolCallResult",
    "RunStepFileSearchToolCallResults",
    "RunStepFunctionToolCall",
    "RunStepFunctionToolCallDetails",
    "RunStepMessageCreationDetails",
    "RunStepMessageCreationReference",
    "RunStepMicrosoftFabricToolCall",
    "RunStepOpenAPIToolCall",
    "RunStepSharepointToolCall",
    "RunStepToolCall",
    "RunStepToolCallDetails",
    "SharepointGroundingToolParameters",
    "SharepointToolDefinition",
    "SubmitToolOutputsAction",
    "SubmitToolOutputsDetails",
    "ThreadMessage",
    "ThreadMessageOptions",
    "ThreadRun",
    "ToolConnection",
    "ToolDefinition",
    "ToolOutput",
    "ToolResources",
    "TruncationObject",
    "VectorStore",
    "VectorStoreAutoChunkingStrategyRequest",
    "VectorStoreAutoChunkingStrategyResponse",
    "VectorStoreChunkingStrategyRequest",
    "VectorStoreChunkingStrategyResponse",
    "VectorStoreConfiguration",
    "VectorStoreConfigurations",
    "VectorStoreDataSource",
    "VectorStoreExpirationPolicy",
    "VectorStoreFile",
    "VectorStoreFileBatch",
    "VectorStoreFileCount",
    "VectorStoreFileError",
    "VectorStoreStaticChunkingStrategyOptions",
    "VectorStoreStaticChunkingStrategyRequest",
    "VectorStoreStaticChunkingStrategyResponse",
    "AgentStreamEvent",
    "AgentsNamedToolChoiceType",
    "AgentsResponseFormatMode",
    "AgentsToolChoiceOptionMode",
    "AzureAISearchQueryType",
    "DoneEvent",
    "ErrorEvent",
    "FilePurpose",
    "FileState",
    "ImageDetailLevel",
    "IncompleteDetailsReason",
    "ListSortOrder",
    "MessageBlockType",
    "MessageIncompleteDetailsReason",
    "MessageRole",
    "MessageStatus",
    "MessageStreamEvent",
    "OpenApiAuthType",
    "ResponseFormat",
    "RunAdditionalFieldList",
    "RunStatus",
    "RunStepErrorCode",
    "RunStepStatus",
    "RunStepStreamEvent",
    "RunStepType",
    "RunStreamEvent",
    "ThreadStreamEvent",
    "TruncationStrategy",
    "VectorStoreChunkingStrategyRequestType",
    "VectorStoreChunkingStrategyResponseType",
    "VectorStoreDataSourceAssetType",
    "VectorStoreExpirationPolicyAnchor",
    "VectorStoreFileBatchStatus",
    "VectorStoreFileErrorCode",
    "VectorStoreFileStatus",
    "VectorStoreFileStatusFilter",
    "VectorStoreStatus",
]
__all__.extend([p for p in _patch_all if p not in __all__])  # pyright: ignore
_patch_sdk()
