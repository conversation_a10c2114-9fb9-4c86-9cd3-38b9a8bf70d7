/*! For license information please see d8f268ac9f2b87d121a872ac85fce383f250aa9f-fc7486aa69dd43bf2d43.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[514],{41:function(e,t,n){"use strict";let r=n(4570),o=n(4809).uniqueId;e.exports={run:function(e){("greedy"===e.graph().acyclicer?r(e,function(e){return t=>e.edge(t).weight}(e)):function(e){let t=[],n={},r={};function o(i){Object.hasOwn(r,i)||(r[i]=!0,n[i]=!0,e.outEdges(i).forEach((e=>{Object.hasOwn(n,e.w)?t.push(e):o(e.w)})),delete n[i])}return e.nodes().forEach(o),t}(e)).forEach((t=>{let n=e.edge(t);e.removeEdge(t),n.forwardName=t.name,n.reversed=!0,e.setEdge(t.w,t.v,n,o("rev"))}))},undo:function(e){e.edges().forEach((t=>{let n=e.edge(t);if(n.reversed){e.removeEdge(t);let r=n.forwardName;delete n.reversed,delete n.forwardName,e.setEdge(t.w,t.v,n,r)}}))}}},85:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},208:function(e,t,n){"use strict";n.d(t,{A:function(){return Ft}});var r=n(436),o=n(6540),i=n(9036),a=n(955),s=n(2941),c=n(180),l=n(2744);function u({title:e,titleId:t,...n},r){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}var d=o.forwardRef(u);function h({title:e,titleId:t,...n},r){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"}))}var f=o.forwardRef(h);function m({title:e,titleId:t,...n},r){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}var p=o.forwardRef(m),g=n(1240),y=n(2279),v=n(867),x=n(4642),w=n(8811),b=n(6029),E=n(7852),_=n(7541),k=n(7850),N=n(3567),S=n(6942),M=n.n(S),O=n(2370),C=n(934),A=n(2187),$=n(275),I=n(5905),z=n(4277),T=n(7358);var P=e=>{const{componentCls:t,notificationMarginEdge:n,animationMaxHeight:r}=e,o=`${t}-notice`,i=new A.Mo("antNotificationFadeIn",{"0%":{transform:"translate3d(100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}}),a=new A.Mo("antNotificationTopFadeIn",{"0%":{top:-r,opacity:0},"100%":{top:0,opacity:1}}),s=new A.Mo("antNotificationBottomFadeIn",{"0%":{bottom:e.calc(r).mul(-1).equal(),opacity:0},"100%":{bottom:0,opacity:1}}),c=new A.Mo("antNotificationLeftFadeIn",{"0%":{transform:"translate3d(-100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}});return{[t]:{[`&${t}-top, &${t}-bottom`]:{marginInline:0,[o]:{marginInline:"auto auto"}},[`&${t}-top`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:a}},[`&${t}-bottom`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:s}},[`&${t}-topRight, &${t}-bottomRight`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:i}},[`&${t}-topLeft, &${t}-bottomLeft`]:{marginRight:{value:0,_skip_check_:!0},marginLeft:{value:n,_skip_check_:!0},[o]:{marginInlineEnd:"auto",marginInlineStart:0},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:c}}}}};const j=["top","topLeft","topRight","bottom","bottomLeft","bottomRight"],L={topLeft:"left",topRight:"right",bottomLeft:"left",bottomRight:"right",top:"left",bottom:"left"},R=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={overflow:"hidden",[`& > ${e.componentCls}-notice`]:{opacity:0,transition:`opacity ${e.motionDurationMid}`}};return Object.assign({[`&:not(:nth-last-child(-n+${e.notificationStackLayer}))`]:{opacity:0,overflow:"hidden",color:"transparent",pointerEvents:"none"}},t)},D=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={background:e.colorBgBlur,backdropFilter:"blur(10px)","-webkit-backdrop-filter":"blur(10px)"};return Object.assign({},t)};var H=e=>{const{componentCls:t}=e;return Object.assign({[`${t}-stack`]:{[`& > ${t}-notice-wrapper`]:Object.assign({transition:`transform ${e.motionDurationSlow}, backdrop-filter 0s`,willChange:"transform, opacity",position:"absolute"},R(e))},[`${t}-stack:not(${t}-stack-expanded)`]:{[`& > ${t}-notice-wrapper`]:Object.assign({},D(e))},[`${t}-stack${t}-stack-expanded`]:{[`& > ${t}-notice-wrapper`]:{"&:not(:nth-last-child(-n + 1))":{opacity:1,overflow:"unset",color:"inherit",pointerEvents:"auto",[`& > ${e.componentCls}-notice`]:{opacity:1}},"&:after":{content:'""',position:"absolute",height:e.margin,width:"100%",insetInline:0,bottom:e.calc(e.margin).mul(-1).equal(),background:"transparent",pointerEvents:"auto"}}}},j.map((t=>((e,t)=>{const{componentCls:n}=e;return{[`${n}-${t}`]:{[`&${n}-stack > ${n}-notice-wrapper`]:{[t.startsWith("top")?"top":"bottom"]:0,[L[t]]:{value:0,_skip_check_:!0}}}}})(e,t))).reduce(((e,t)=>Object.assign(Object.assign({},e),t)),{}))};const B=e=>{const{iconCls:t,componentCls:n,boxShadow:r,fontSizeLG:o,notificationMarginBottom:i,borderRadiusLG:a,colorSuccess:s,colorInfo:c,colorWarning:l,colorError:u,colorTextHeading:d,notificationBg:h,notificationPadding:f,notificationMarginEdge:m,notificationProgressBg:p,notificationProgressHeight:g,fontSize:y,lineHeight:v,width:x,notificationIconSize:w,colorText:b}=e,E=`${n}-notice`;return{position:"relative",marginBottom:i,marginInlineStart:"auto",background:h,borderRadius:a,boxShadow:r,[E]:{padding:f,width:x,maxWidth:`calc(100vw - ${(0,A.zA)(e.calc(m).mul(2).equal())})`,overflow:"hidden",lineHeight:v,wordWrap:"break-word"},[`${E}-message`]:{marginBottom:e.marginXS,color:d,fontSize:o,lineHeight:e.lineHeightLG},[`${E}-description`]:{fontSize:y,color:b},[`${E}-closable ${E}-message`]:{paddingInlineEnd:e.paddingLG},[`${E}-with-icon ${E}-message`]:{marginBottom:e.marginXS,marginInlineStart:e.calc(e.marginSM).add(w).equal(),fontSize:o},[`${E}-with-icon ${E}-description`]:{marginInlineStart:e.calc(e.marginSM).add(w).equal(),fontSize:y},[`${E}-icon`]:{position:"absolute",fontSize:w,lineHeight:1,[`&-success${t}`]:{color:s},[`&-info${t}`]:{color:c},[`&-warning${t}`]:{color:l},[`&-error${t}`]:{color:u}},[`${E}-close`]:Object.assign({position:"absolute",top:e.notificationPaddingVertical,insetInlineEnd:e.notificationPaddingHorizontal,color:e.colorIcon,outline:"none",width:e.notificationCloseButtonSize,height:e.notificationCloseButtonSize,borderRadius:e.borderRadiusSM,transition:`background-color ${e.motionDurationMid}, color ${e.motionDurationMid}`,display:"flex",alignItems:"center",justifyContent:"center","&:hover":{color:e.colorIconHover,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},(0,I.K8)(e)),[`${E}-progress`]:{position:"absolute",display:"block",appearance:"none",WebkitAppearance:"none",inlineSize:`calc(100% - ${(0,A.zA)(a)} * 2)`,left:{_skip_check_:!0,value:a},right:{_skip_check_:!0,value:a},bottom:0,blockSize:g,border:0,"&, &::-webkit-progress-bar":{borderRadius:a,backgroundColor:"rgba(0, 0, 0, 0.04)"},"&::-moz-progress-bar":{background:p},"&::-webkit-progress-value":{borderRadius:a,background:p}},[`${E}-actions`]:{float:"right",marginTop:e.marginSM}}},F=e=>{const{componentCls:t,notificationMarginBottom:n,notificationMarginEdge:r,motionDurationMid:o,motionEaseInOut:i}=e,a=`${t}-notice`,s=new A.Mo("antNotificationFadeOut",{"0%":{maxHeight:e.animationMaxHeight,marginBottom:n},"100%":{maxHeight:0,marginBottom:0,paddingTop:0,paddingBottom:0,opacity:0}});return[{[t]:Object.assign(Object.assign({},(0,I.dF)(e)),{position:"fixed",zIndex:e.zIndexPopup,marginRight:{value:r,_skip_check_:!0},[`${t}-hook-holder`]:{position:"relative"},[`${t}-fade-appear-prepare`]:{opacity:"0 !important"},[`${t}-fade-enter, ${t}-fade-appear`]:{animationDuration:e.motionDurationMid,animationTimingFunction:i,animationFillMode:"both",opacity:0,animationPlayState:"paused"},[`${t}-fade-leave`]:{animationTimingFunction:i,animationFillMode:"both",animationDuration:o,animationPlayState:"paused"},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationPlayState:"running"},[`${t}-fade-leave${t}-fade-leave-active`]:{animationName:s,animationPlayState:"running"},"&-rtl":{direction:"rtl",[`${a}-actions`]:{float:"left"}}})},{[t]:{[`${a}-wrapper`]:Object.assign({},B(e))}}]},V=e=>({zIndexPopup:e.zIndexPopupBase+$.jH+50,width:384}),Y=e=>{const t=e.paddingMD,n=e.paddingLG;return(0,z.oX)(e,{notificationBg:e.colorBgElevated,notificationPaddingVertical:t,notificationPaddingHorizontal:n,notificationIconSize:e.calc(e.fontSizeLG).mul(e.lineHeightLG).equal(),notificationCloseButtonSize:e.calc(e.controlHeightLG).mul(.55).equal(),notificationMarginBottom:e.margin,notificationPadding:`${(0,A.zA)(e.paddingMD)} ${(0,A.zA)(e.paddingContentHorizontalLG)}`,notificationMarginEdge:e.marginLG,animationMaxHeight:150,notificationStackLayer:3,notificationProgressHeight:2,notificationProgressBg:`linear-gradient(90deg, ${e.colorPrimaryBorderHover}, ${e.colorPrimary})`})};var X=(0,T.OF)("Notification",(e=>{const t=Y(e);return[F(t),P(t),H(t)]}),V),G=(0,T.bf)(["Notification","PurePanel"],(e=>{const t=`${e.componentCls}-notice`,n=Y(e);return{[`${t}-pure-panel`]:Object.assign(Object.assign({},B(n)),{width:n.width,maxWidth:`calc(100vw - ${(0,A.zA)(e.calc(n.notificationMarginEdge).mul(2).equal())})`,margin:0})}}),V),q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};k.A,w.A,b.A,_.A,N.A;function W(e,t){return null===t||!1===t?null:t||o.createElement(E.A,{className:`${e}-close-icon`})}const U={success:w.A,info:k.A,error:b.A,warning:_.A},Z=e=>{const{prefixCls:t,icon:n,type:r,message:i,description:a,actions:s,role:c="alert"}=e;let l=null;return n?l=o.createElement("span",{className:`${t}-icon`},n):r&&(l=o.createElement(U[r]||null,{className:M()(`${t}-icon`,`${t}-icon-${r}`)})),o.createElement("div",{className:M()({[`${t}-with-icon`]:l}),role:c},l,o.createElement("div",{className:`${t}-message`},i),o.createElement("div",{className:`${t}-description`},a),s&&o.createElement("div",{className:`${t}-actions`},s))};var K=e=>{const{prefixCls:t,className:n,icon:r,type:i,message:a,description:s,btn:c,actions:l,closable:u=!0,closeIcon:d,className:h}=e,f=q(e,["prefixCls","className","icon","type","message","description","btn","actions","closable","closeIcon","className"]),{getPrefixCls:m}=o.useContext(y.QO),p=null!=l?l:c;const g=t||m("notification"),v=`${g}-notice`,x=(0,C.A)(g),[w,b,E]=X(g,x);return w(o.createElement("div",{className:M()(`${v}-pure-panel`,b,n,E,x)},o.createElement(G,{prefixCls:g}),o.createElement(O.$T,Object.assign({},f,{prefixCls:g,eventKey:"pure",duration:null,closable:u,className:M()({notificationClassName:h}),closeIcon:W(g,d),content:o.createElement(Z,{prefixCls:v,icon:r,type:i,message:a,description:s,actions:p})}))))},J=n(8877),Q=n(1320);var ee=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const te=e=>{let{children:t,prefixCls:n}=e;const r=(0,C.A)(n),[i,a,s]=X(n,r);return i(o.createElement(O.ph,{classNames:{list:M()(a,s,r)}},t))},ne=(e,t)=>{let{prefixCls:n,key:r}=t;return o.createElement(te,{prefixCls:n,key:r},e)},re=o.forwardRef(((e,t)=>{const{top:n,bottom:r,prefixCls:i,getContainer:a,maxCount:s,rtl:c,onAllRemoved:l,stack:u,duration:d,pauseOnHover:h=!0,showProgress:f}=e,{getPrefixCls:m,getPopupContainer:p,notification:g,direction:v}=(0,o.useContext)(y.QO),[,x]=(0,Q.Ay)(),w=i||m("notification"),[b,E]=(0,O.hN)({prefixCls:w,style:e=>function(e,t,n){let r;switch(e){case"top":r={left:"50%",transform:"translateX(-50%)",right:"auto",top:t,bottom:"auto"};break;case"topLeft":r={left:0,top:t,bottom:"auto"};break;case"topRight":r={right:0,top:t,bottom:"auto"};break;case"bottom":r={left:"50%",transform:"translateX(-50%)",right:"auto",top:"auto",bottom:n};break;case"bottomLeft":r={left:0,top:"auto",bottom:n};break;default:r={right:0,top:"auto",bottom:n}}return r}(e,null!=n?n:24,null!=r?r:24),className:()=>M()({[`${w}-rtl`]:null!=c?c:"rtl"===v}),motion:()=>function(e){return{motionName:`${e}-fade`}}(w),closable:!0,closeIcon:W(w),duration:null!=d?d:4.5,getContainer:()=>(null==a?void 0:a())||(null==p?void 0:p())||document.body,maxCount:s,pauseOnHover:h,showProgress:f,onAllRemoved:l,renderNotifications:ne,stack:!1!==u&&{threshold:"object"==typeof u?null==u?void 0:u.threshold:void 0,offset:8,gap:x.margin}});return o.useImperativeHandle(t,(()=>Object.assign(Object.assign({},b),{prefixCls:w,notification:g}))),E}));function oe(e){const t=o.useRef(null),n=((0,J.rJ)("Notification"),o.useMemo((()=>{const n=n=>{var r;if(!t.current)return;const{open:i,prefixCls:a,notification:s}=t.current,c=`${a}-notice`,{message:l,description:u,icon:d,type:h,btn:f,actions:m,className:p,style:g,role:y="alert",closeIcon:v,closable:x}=n,w=ee(n,["message","description","icon","type","btn","actions","className","style","role","closeIcon","closable"]);const b=null!=m?m:f,E=W(c,function(e,t,n){return void 0!==e?e:void 0!==(null==t?void 0:t.closeIcon)?t.closeIcon:null==n?void 0:n.closeIcon}(v,e,s));return i(Object.assign(Object.assign({placement:null!==(r=null==e?void 0:e.placement)&&void 0!==r?r:"topRight"},w),{content:o.createElement(Z,{prefixCls:c,icon:d,type:h,message:l,description:u,actions:b,role:y}),className:M()(h&&`${c}-${h}`,p,null==s?void 0:s.className),style:Object.assign(Object.assign({},null==s?void 0:s.style),g),closeIcon:E,closable:null!=x?x:!!E}))},r={open:n,destroy:e=>{var n,r;void 0!==e?null===(n=t.current)||void 0===n||n.close(e):null===(r=t.current)||void 0===r||r.destroy()}};return["success","info","warning","error"].forEach((e=>{r[e]=t=>n(Object.assign(Object.assign({},t),{type:e}))})),r}),[]));return[n,o.createElement(re,Object.assign({key:"notification-holder"},e,{ref:t}))]}let ie=null,ae=e=>e(),se=[],ce={};function le(){const{getContainer:e,rtl:t,maxCount:n,top:r,bottom:o,showProgress:i,pauseOnHover:a}=ce,s=(null==e?void 0:e())||document.body;return{getContainer:()=>s,rtl:t,maxCount:n,top:r,bottom:o,showProgress:i,pauseOnHover:a}}const ue=o.forwardRef(((e,t)=>{const{notificationConfig:n,sync:r}=e,{getPrefixCls:i}=(0,o.useContext)(y.QO),a=ce.prefixCls||i("notification"),s=(0,o.useContext)(g.B),[c,l]=oe(Object.assign(Object.assign(Object.assign({},n),{prefixCls:a}),s.notification));return o.useEffect(r,[]),o.useImperativeHandle(t,(()=>{const e=Object.assign({},c);return Object.keys(e).forEach((t=>{e[t]=function(){return r(),c[t].apply(c,arguments)}})),{instance:e,sync:r}})),l})),de=o.forwardRef(((e,t)=>{const[n,r]=o.useState(le),i=()=>{r(le)};o.useEffect(i,[]);const a=(0,v.cr)(),s=a.getRootPrefixCls(),c=a.getIconPrefixCls(),l=a.getTheme(),u=o.createElement(ue,{ref:t,sync:i,notificationConfig:n});return o.createElement(v.Ay,{prefixCls:s,iconPrefixCls:c,theme:l},a.holderRender?a.holderRender(u):u)}));function he(){if(!ie){const e=document.createDocumentFragment(),t={fragment:e};return ie=t,void ae((()=>{(0,x.K)()(o.createElement(de,{ref:e=>{const{instance:n,sync:r}=e||{};Promise.resolve().then((()=>{!t.instance&&n&&(t.instance=n,t.sync=r,he())}))}}),e)}))}ie.instance&&(se.forEach((e=>{switch(e.type){case"open":ae((()=>{ie.instance.open(Object.assign(Object.assign({},ce),e.config))}));break;case"destroy":ae((()=>{null==ie||ie.instance.destroy(e.key)}))}})),se=[])}function fe(e){(0,v.cr)();se.push({type:"open",config:e}),he()}const me={open:fe,destroy:e=>{se.push({type:"destroy",key:e}),he()},config:function(e){ce=Object.assign(Object.assign({},ce),e),ae((()=>{var e;null===(e=null==ie?void 0:ie.sync)||void 0===e||e.call(ie)}))},useNotification:function(e){return oe(e)},_InternalPanelDoNotUseOrYouWillBeFired:K};["success","info","warning","error"].forEach((e=>{me[e]=t=>fe(Object.assign(Object.assign({},t),{type:e}))}));var pe=me,ge=n(8355),ye=n(1788);const ve=(0,ye.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),xe=(0,ye.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var we=n(8697),be=n(4796);const Ee=5242880,_e=["text/plain","image/jpeg","image/png","image/gif","image/svg+xml"],ke=1500;function Ne(e){let{onSubmit:t,loading:n,error:l,disabled:u=!1}=e;const h=o.useRef(null),[m,g]=o.useState(n),[y,v]=o.useState("What is the capital of France?"),[x,w]=o.useState([]),[b,E]=o.useState(!1),[_,k]=pe.useNotification(),N=u||n;o.useEffect((()=>{if(h.current){h.current.style.height="auto";const e=h.current.scrollHeight;h.current.style.height=`${e}px`}}),[y]),o.useEffect((()=>{!m||n||l||S(),g(n)}),[n,l,m]),o.useEffect((()=>{const e=e=>{var t;if(!N&&null!==(t=e.clipboardData)&&void 0!==t&&t.items){let t=!1;for(let n=0;n<e.clipboardData.items.length;n++){const a=e.clipboardData.items[n];if(0===a.type.indexOf("image/")){t=!0;const n=a.getAsFile();if(n&&n.size<=Ee){e.preventDefault();const t=`pasted-image-${(new Date).getTime()}.png`,o=new File([n],t,{type:n.type}),a={uid:`paste-${Date.now()}`,name:t,status:"done",size:o.size,type:o.type,originFileObj:o};w((e=>[].concat((0,r.A)(e),[a]))),i.Ay.success("Image pasted successfully")}else n&&n.size>Ee&&i.Ay.error("Pasted image is too large. Maximum size is 5MB.")}"text/plain"!==a.type||t||a.getAsString((t=>{if(t.length>ke){setTimeout((()=>{if(h.current){const e=h.current.value,n=h.current.selectionStart||0,r=h.current.selectionEnd||0,o=e.substring(0,n-t.length)+e.substring(r);h.current.value=o,v(o)}}),0),e.preventDefault();const n=new Blob([t],{type:"text/plain"}),i=new File([n],`pasted-text-${(new Date).getTime()}.txt`,{type:"text/plain"}),a={uid:`paste-${Date.now()}`,name:i.name,status:"done",size:i.size,type:i.type,originFileObj:i};w((e=>[].concat((0,r.A)(e),[a]))),_.info({message:o.createElement("span",{className:"text-sm"},"Large Text Converted to File"),description:o.createElement("span",{className:"text-sm text-secondary"},"Your pasted text has been attached as a file."),duration:3})}}))}}};return document.addEventListener("paste",e),()=>{document.removeEventListener("paste",e)}}),[N,_]);const S=()=>{h.current&&(h.current.value="",h.current.style.height="64px",v(""),w([]))},M=()=>{var e;if((null!==(e=h.current)&&void 0!==e&&e.value||x.length>0)&&!N){var n;const e=(null===(n=h.current)||void 0===n?void 0:n.value)||"",r=x.filter((e=>e.originFileObj)).map((e=>e.originFileObj));t(e,r)}},O={name:"file",multiple:!0,fileList:x,beforeUpload:e=>{if(e.size>Ee)return i.Ay.error(`${e.name} is too large. Maximum size is 5MB.`),ge.A.LIST_IGNORE;if(!_e.includes(e.type))return _.warning({message:o.createElement("span",{className:"text-sm"},"Unsupported File Type"),description:o.createElement("span",{className:"text-sm text-secondary"},"Please upload only text (.txt) or images (.jpg, .png, .gif, .svg) files."),showProgress:!0,duration:8.5}),ge.A.LIST_IGNORE;const t={uid:e.uid,name:e.name,status:"done",size:e.size,type:e.type,originFileObj:e};return w((e=>[].concat((0,r.A)(e),[t]))),!1},onRemove:e=>{w(x.filter((t=>t.uid!==e.uid)))},showUploadList:!1,customRequest:e=>{let{onSuccess:t}=e;t&&t("ok")}};return o.createElement("div",{className:"mt-2 w-full"},k,x.length>0&&o.createElement("div",{className:"-mb-2 mx-1 bg-tertiary rounded-t border-b-0 p-2 flex bodrder flex-wrap gap-2"},x.map((e=>o.createElement("div",{key:e.uid,className:"flex items-center gap-1 bg-secondary rounded px-2 py-1 text-xs"},(e=>(e.type||"").startsWith("image/")?o.createElement(ve,{className:"w-4 h-4"}):o.createElement(xe,{className:"w-4 h-4"}))(e),o.createElement("span",{className:"truncate max-w-[150px]"},(0,c.EJ)(e.name,20)),o.createElement(s.Ay,{type:"text",size:"small",className:"p-0 ml-1 flex items-center justify-center",onClick:()=>w((t=>t.filter((t=>t.uid!==e.uid)))),icon:o.createElement(we.A,{className:"w-3 h-3"})}))))),o.createElement("div",{className:`mt-2 rounded shadow-sm flex mb-1 transition-all duration-200 ${b?"ring-2 ring-blue-400":""} ${N?"opacity-50":""}`,onDragOver:e=>{e.preventDefault(),e.stopPropagation(),N||E(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),E(!1)},onDrop:e=>{if(e.preventDefault(),e.stopPropagation(),E(!1),N)return;Array.from(e.dataTransfer.files).forEach((e=>{if(e.size>Ee)return void i.Ay.error(`${e.name} is too large. Maximum size is 5MB.`);if(!_e.includes(e.type))return void _.warning({message:o.createElement("span",{className:"text-sm"},"Unsupported File Type"),description:o.createElement("span",{className:"text-sm text-secondary"},"Please upload only text (.txt) or images (.jpg, .png, .gif, .svg) files."),showProgress:!0,duration:8.5});const t={uid:`file-${Date.now()}-${e.name}`,name:e.name,status:"done",size:e.size,type:e.type,originFileObj:e};w((e=>[].concat((0,r.A)(e),[t])))}))}},o.createElement("form",{className:"flex-1 relative",onSubmit:e=>{e.preventDefault(),M()}},b&&o.createElement("div",{className:"absolute inset-0 bg-blue-100 bg-opacity-60 flex items-center justify-center rounded z-10 pointer-events-none"},o.createElement("div",{className:"text-accent tex-xs   items-center"},o.createElement(be.A,{className:"h-4 w-4 mr-2  inline-block"}),o.createElement("span",{className:"text-xs  inline-block"},"Drop files here"))),o.createElement("textarea",{id:"queryInput",name:"queryInput",ref:h,value:y,onChange:e=>{v(e.target.value)},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),M())},className:`flex items-center w-full resize-none text-gray-600 rounded ${b?"border-2 border-blue-500 bg-blue-50":"border border-accent bg-white"} p-2 pl-5 pr-16 ${N?"cursor-not-allowed":""}`,style:{maxHeight:"120px",overflowY:"auto",minHeight:"50px",transition:"all 0.2s ease-in-out"},placeholder:b?"Drop files here...":"Type your message here...",disabled:N}),o.createElement("div",{className:"absolute right-3 bottom-2 flex gap-2"},o.createElement("div",{className:"   "+(u||N?"  opacity-50 pointer-events-none ":"")}," ",o.createElement(ge.A,Object.assign({className:"zero-padding-upload  "},O),o.createElement(a.A,{title:o.createElement("span",{className:"text-sm"},"Upload File"," ",o.createElement("span",{className:"text-secondary text-xs"},"(max 5mb)")),placement:"top"},o.createElement(s.Ay,{type:"text",disabled:N,className:" "},o.createElement(be.A,{strokeWidth:2,size:26,className:"p-1 inline-block w-8 text-accent"}))))),o.createElement("button",{type:"button",onClick:M,disabled:N||""===y.trim()&&0===x.length,className:"bg-accent transition duration-300 rounded flex justify-center items-center w-11 h-9 "+(N||""===y.trim()&&0===x.length?"cursor-not-allowed opacity-50":"hover:brightness-75")},n?o.createElement(d,{className:"text-white animate-spin rounded-full h-6 w-6"}):o.createElement(f,{className:"h-6 w-6 text-white"}))))),l&&!l.status&&o.createElement("div",{className:"p-2 border rounded mt-4 text-orange-500 text-sm"},o.createElement(p,{className:"h-5 text-orange-500 inline-block mr-2"}),l.message))}var Se=n(2197),Me=n(9850),Oe=n(2640);const Ce=(0,ye.A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Ae=(0,ye.A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var $e=n(4471),Ie=n(418);const ze=(0,ye.A)("CircleStop",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{x:"9",y:"9",width:"6",height:"6",rx:"1",key:"1ssd4o"}]]);var Te=n(2102),Pe=n(5107);const je=(0,ye.A)("PanelRightOpen",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m10 15-3-3 3-3",key:"1pgupc"}]]),Le=(0,ye.A)("PanelRightClose",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m8 9 3 3-3 3",key:"12hl5m"}]]);var Re=n(1133),De=n(8057),He=n.n(De),Be=(n(8280),n(2275));const Fe=(0,ye.A)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),Ve=(0,ye.A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var Ye=function(e){let{data:t,isConnectable:n}=e;const r=(0,o.useCallback)((()=>{"end"!==t.type&&console.log(`${t.type} ${t.label} clicked`)}),[t.type,t.label]),i=(()=>{const e=t.isActive?"ring-2 ring-accent/50 ":"  ";return"end"===t.type?{wrapper:`relative min-w-[180px] shadow rounded-lg overflow-hidden  ${e}`,border:"complete"===t.status?"var(--accent)":"error"===t.status?"rgb(239 68 68)":"var(--secondary)"}:{wrapper:`min-w-[180px] rounded-lg shadow overflow-hidden ${e}`,border:void 0}})();return o.createElement("div",{className:i.wrapper,onClick:r,style:i.border?{borderColor:i.border}:void 0},o.createElement(Re.h7,{type:"target",position:Be.yX.Top,style:{background:"#555"},isConnectable:n,id:"target"}),o.createElement("div",{className:"flex agent-draghandle items-center gap-2 px-3 py-2 bg-secondary border-b border-border"},(()=>{switch(t.type){case"user":return o.createElement(Fe,{className:"text-primary",size:20});case"agent":return o.createElement(Oe.A,{className:"text-primary",size:20});case"end":return o.createElement(Ve,{className:"text-primary",size:20})}})(),o.createElement("span",{className:"text-sm font-medium text-primary truncate"},t.label)),o.createElement("div",{className:"bg-tertiary px-3 py-2"},"end"===t.type?o.createElement(o.Fragment,null,o.createElement("div",{className:"flex items-center justify-center gap-2"},(()=>{if("end"!==t.type||!t.status)return null;switch(t.status){case"complete":return o.createElement($e.A,{className:"text-accent",size:24});case"error":return o.createElement(Ie.A,{className:"text-red-500",size:24});case"stopped":return o.createElement(ze,{className:"text-red-500",size:24});default:return null}})(),o.createElement("span",{className:"text-primary text-sm font-medium"},t.status&&t.status.charAt(0).toUpperCase()+t.status.slice(1))),t.reason&&o.createElement("div",{className:"mt-1 text-xs text-secondary max-w-[200px] text-center"},t.reason.length>100?`${t.reason.substring(0,97)}...`:t.reason)):o.createElement(o.Fragment,null,t.agentType&&o.createElement("div",{className:"text-sm text-secondary"},t.agentType),t.description&&o.createElement("div",{className:"text-xs text-secondary mt-1 truncate max-w-[200px]"},t.description))),"end"!==t.type&&o.createElement(Re.h7,{type:"source",position:Be.yX.Bottom,id:"source",style:{background:"#555"},isConnectable:n}))};var Xe=n(5625),Ge=n(8603),qe=n(468);const We=(0,ye.A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var Ue=n(6043),Ze=n(9492),Ke=n(942),Je=n(3324);const Qe=(0,ye.A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),et=(0,ye.A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),tt=(0,ye.A)("MessageSquareOff",[["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10",key:"pwpm4a"}]]);var nt=n(4718);const rt=e=>{let{isFullscreen:t,onToggleFullscreen:n,onResetView:r}=e;const{agentFlow:i,setAgentFlowSettings:c}=(0,Xe.J)(),l=e=>()=>{c({[e]:!i[e]})},u=[{key:"grid",label:"Show Grid",icon:o.createElement(qe.A,{size:16}),onClick:l("showGrid")},{key:"tokens",label:"Show Tokens",icon:o.createElement(We,{size:16}),onClick:l("showTokens")},{key:"miniMap",label:"Mini Map",icon:o.createElement(Ue.A,{size:16}),onClick:l("showMiniMap")},{type:"divider"},{key:"reset",label:"Reset View",icon:o.createElement(Ze.A,{size:16}),onClick:r,disabled:!r}];return o.createElement("div",{className:"absolute top-2 right-2 bg-secondary bg-opacity-70 hover:bg-secondary rounded backdrop-blur-sm z-50"},o.createElement("div",{className:"p-1 flex items-center gap-1"},o.createElement(a.A,{title:t?"Exit Fullscreen":"Enter Fullscreen"},o.createElement(s.Ay,{type:"text",icon:t?o.createElement(Ke.A,{size:18}):o.createElement(Je.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:n})),o.createElement(a.A,{title:`Switch to ${"TB"===i.direction?"Horizontal":"Vertical"} Layout`},o.createElement(s.Ay,{type:"text",icon:"TB"===i.direction?o.createElement(Qe,{size:18}):o.createElement(et,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:()=>c({direction:"TB"===i.direction?"LR":"TB"})})),o.createElement(a.A,{title:i.showLabels?"Hide Labels":"Show Labels"},o.createElement(s.Ay,{type:"text",icon:i.showLabels?o.createElement(Ae,{size:18}):o.createElement(tt,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:l("showLabels")})),o.createElement(Ge.A,{menu:{items:u},trigger:["click"],getPopupContainer:e=>e.parentNode,overlayStyle:{zIndex:1e3}},o.createElement(s.Ay,{type:"text",icon:o.createElement(nt.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",title:"More Options"}))))};var ot=n(9957),it=n(8458);const at=(0,ye.A)("DraftingCompass",[["path",{d:"m12.99 6.74 1.93 3.44",key:"iwagvd"}],["path",{d:"M19.136 12a10 10 0 0 1-14.271 0",key:"ppmlo4"}],["path",{d:"m21 21-2.16-3.84",key:"vylbct"}],["path",{d:"m3 21 8.02-14.26",key:"1ssaw4"}],["circle",{cx:"12",cy:"5",r:"2",key:"f1ur92"}]]),st=(0,ye.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ct=(0,ye.A)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]);var lt=n(2206),ut=n(6813);const dt=e=>e>=1e3?`${(e/1e3).toFixed(1)}k`:e,ht=e=>{var t,n;let{event:r,onClose:i}=e;return o.createElement("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center transition-opacity duration-300",onClick:i},o.createElement("div",{className:"relative bg-primary w-full h-full md:w-4/5 md:h-4/5 md:rounded-lg p-8 overflow-auto",style:{opacity:.95},onClick:e=>e.stopPropagation()},o.createElement(a.A,{title:"Close"},o.createElement("button",{onClick:i,className:"absolute top-4 right-4 p-2 rounded-full bg-tertiary hover:bg-secondary text-primary transition-colors"},o.createElement(we.A,{size:24}))),o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"flex items-center gap-2 mb-4"},o.createElement(ut.A,{size:20,className:"text-accent"}),o.createElement("h3",{className:"text-lg font-medium"},"LLM Call Details"),o.createElement("h4",{className:"text-sm text-secondary"},r.agent_id.split("/")[0]," • ",r.response.model," •"," ",dt(r.response.usage.total_tokens)," tokens")),o.createElement("div",{className:"space-y-2"},o.createElement("h4",{className:"text-sm font-medium"},"Messages"),r.messages.map(((e,t)=>o.createElement("div",{key:t,className:"p-4 bg-tertiary rounded-lg"},o.createElement("div",{className:"flex justify-between mb-2"},o.createElement("span",{className:"text-xs font-medium uppercase text-secondary"},e.name&&`${e.name}`||e.role," ")),Array.isArray(e.content)?e.content.map(((e,t)=>o.createElement("div",{key:t,className:"text-sm text-secondary"},o.createElement(lt.PA,{content:e,showFullscreen:!1})))):o.createElement(lt.PA,{content:e.content,showFullscreen:!1}))))),r.response.choices&&o.createElement("div",{className:"space-y-2"},o.createElement("h4",{className:"text-sm font-medium"},"Response"),o.createElement("div",{className:"p-4 bg-tertiary rounded-lg"},o.createElement(lt.PA,{content:null===(t=r.response)||void 0===t||null===(n=t.choices[0])||void 0===n?void 0:n.message.content,textThreshold:1e3}))),o.createElement("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4"},o.createElement("div",{className:"p-3 bg-tertiary rounded-lg"},o.createElement("div",{className:"text-xs text-secondary mb-1"},"Model"),o.createElement("div",{className:"font-medium"},r.response.model)),o.createElement("div",{className:"p-3 bg-tertiary rounded-lg"},o.createElement("div",{className:"text-xs text-secondary mb-1"},"Prompt Tokens"),o.createElement("div",{className:"font-medium"},r.response.usage.prompt_tokens)),o.createElement("div",{className:"p-3 bg-tertiary rounded-lg"},o.createElement("div",{className:"text-xs text-secondary mb-1"},"Completion Tokens"),o.createElement("div",{className:"font-medium"},r.response.usage.completion_tokens)),o.createElement("div",{className:"p-3 bg-tertiary rounded-lg"},o.createElement("div",{className:"text-xs text-secondary mb-1"},"Total Tokens"),o.createElement("div",{className:"font-medium"},r.response.usage.total_tokens))))))};var ft=e=>{var t;let{content:n}=e;const{0:r,1:i}=(0,o.useState)(!1),s=(0,o.useMemo)((()=>{try{return JSON.parse(n)}catch(e){return console.error("Failed to parse LLM log content:",e),null}}),[n]);if(!s)return o.createElement("div",{className:"flex items-center gap-2 text-red-500 p-2 bg-red-500/10 rounded"},o.createElement(ut.A,{size:16}),o.createElement("span",null,"Invalid log format"));const{messages:c,response:l,agent_id:u}=s,d=(null===(t=c[0])||void 0===t||t.name,l.usage.total_tokens),h=u?`${u.split("/")[0]}`:"";return o.createElement(o.Fragment,null,o.createElement("div",{className:"flex items-center gap-2 py-2   bg-secondary/20 rounded-lg text-sm text-secondary hover:text-primary transition-colors group"},o.createElement(ut.A,{size:14,className:"text-accent"}),o.createElement("span",{className:"flex-1"},h?`${h}`:""," • ",l.model," •"," ",dt(d)," tokens"),o.createElement(a.A,{title:"View details"},o.createElement("button",{onClick:()=>i(!0),className:"p-1 mr-1 hover:bg-secondary rounded-md transition-colors"},o.createElement(Je.A,{size:14,className:"group-hover:text-accent"})))),r&&o.createElement(ht,{event:s,onClose:()=>i(!1)}))};const mt=e=>e.url?e.url:e.data?`data:image/png;base64,${e.data}`:"/api/placeholder/400/320",pt=e=>{let{content:t,thumbnail:n=!1}=e;return o.createElement("div",{className:"space-y-2"},t.map(((e,t)=>"string"==typeof e?o.createElement(lt.PA,{key:t,content:e,className:"break-all"}):o.createElement(lt.wx,{key:t,src:mt(e),alt:e.alt||"Image",className:" h-auto rounded border border-secondary "+(n?"w-24 h-24 ":" w-full ")}))))},gt=e=>{let{content:t}=e;return o.createElement("div",{className:"space-y-2"},t.map((e=>o.createElement("div",{key:e.id,className:"relative pl-3 border border-secondary rounded p-2"},o.createElement("div",{className:"absolute top-0 -left-0.5 w-1 bg-secondary h-full rounded"}),o.createElement("div",{className:"font-medium"},o.createElement(at,{className:"w-4 h-4 text-accent inline-block mr-1.5 -mt-0.5"})," ","Calling ",e.name," tool with arguments"),o.createElement(lt.PA,{content:JSON.stringify(e.arguments,null,2),isJson:!0,className:"text-sm mt-1 bg-secondary p-2 rounded"})))))},yt=e=>{let{content:t}=e;return o.createElement("div",{className:"space-y-2"},t.map((e=>o.createElement("div",{key:e.call_id,className:"rounded p-2 pl-3 relative border border-secondary"},o.createElement("div",{className:"absolute top-0 -left-0.5 w-1 bg-secondary h-full rounded"}),o.createElement("div",{className:"font-medium"},o.createElement(at,{className:"w-4 text-accent h-4 inline-block mr-1.5 -mt-0.5"})," ","Tool Result"),o.createElement(lt.PA,{content:e.content,className:"text-sm mt-1 bg-secondary p-2 border border-secondary rounded scroll overflow-x-scroll"})))))},vt={isToolCallContent(e){return!!Array.isArray(e)&&e.every((e=>"object"==typeof e&&null!==e&&"id"in e&&"arguments"in e&&"name"in e))},isNestedMessageContent(e){return!!Array.isArray(e)&&e.every((e=>"object"==typeof e&&null!==e&&"source"in e&&"content"in e&&"type"in e))},isMultiModalContent(e){return!!Array.isArray(e)&&e.every((e=>"string"==typeof e||"object"==typeof e&&null!==e&&("url"in e||"data"in e)))},isFunctionExecutionResult(e){return!!Array.isArray(e)&&e.every((e=>"object"==typeof e&&null!==e&&"call_id"in e&&"content"in e))},isUser(e){return"user"===e},isMessageArray(e){return Array.isArray(e)}},xt=e=>{let{content:t}=e;return o.createElement("div",{className:"space-y-4"},t.map(((e,t)=>o.createElement("div",{key:t,className:""+(t>0?"bordper border-secondary rounded   bg-secondary/30":"")},"string"==typeof e.content?o.createElement(lt.PA,{content:e.content,className:"break-all "+(0===t?"text-base":"text-sm")}):vt.isMultiModalContent(e.content)?o.createElement(pt,{content:e.content,thumbnail:!0}):o.createElement("pre",{className:"text-xs whitespace-pre-wrap overflow-x-auto"},JSON.stringify(e.content,null,2))))))},wt=e=>{let{message:t,isLast:n=!1,className:r=""}=e;if(!t)return null;if(vt.isMessageArray(t))return t.length>0?o.createElement(wt,{message:t[0],isLast:n,className:r}):null;const i=vt.isUser(t.source),a=t.content,s="llm_call_event"===t.source;return o.createElement("div",{className:`relative group ${n?"":"mb-2"} ${r} ${s?"border-accent":""}`},o.createElement("div",{className:`\n        flex items-start gap-2 p-2 rounded\n        ${i?"bg-secondary":"bg-tertiary"}\n        border border-secondary\n        transition-all duration-200\n      `},o.createElement("div",{className:`\n          p-1.5 rounded bg-light \n          ${i?"text-accent":"text-primary"}\n        `},i?o.createElement(st,{size:14}):"llm_call_event"==t.source?o.createElement(ct,{size:14}):o.createElement(Oe.A,{size:14})),o.createElement("div",{className:"flex-1 min-w-0"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-sm font-semibold text-primary"},t.source)),o.createElement("div",{className:"text-sm text-secondary"},vt.isToolCallContent(a)?o.createElement(gt,{content:a}):vt.isMultiModalContent(a)?o.createElement(pt,{content:a,thumbnail:!0}):vt.isNestedMessageContent(a)?o.createElement(xt,{content:a}):vt.isFunctionExecutionResult(a)?o.createElement(yt,{content:a}):"llm_call_event"===t.source?o.createElement(ft,{content:String(a)}):o.createElement(lt.PA,{content:String(a),className:"break-all"})),t.models_usage&&o.createElement("div",{className:"text-xs text-secondary mt-1"},"Tokens:"," ",(t.models_usage.prompt_tokens||0)+(t.models_usage.completion_tokens||0)))))},{Search:bt}=ot.A,Et=e=>{var t,n;let{open:r,onClose:i,edge:a}=e;const{0:s,1:c}=(0,o.useState)(""),l=(0,o.useMemo)((()=>{var e;return null!=a&&null!==(e=a.data)&&void 0!==e&&e.messages?a.data.messages.reduce(((e,t)=>{var n,r;return e+((null===(n=t.models_usage)||void 0===n?void 0:n.prompt_tokens)||0)+((null===(r=t.models_usage)||void 0===r?void 0:r.completion_tokens)||0)}),0):0}),[null==a||null===(t=a.data)||void 0===t?void 0:t.messages]),u=(0,o.useMemo)((()=>{var e;return null!=a&&null!==(e=a.data)&&void 0!==e&&e.messages?s?a.data.messages.filter((e=>"string"==typeof e.content&&e.content.toLowerCase().includes(s.toLowerCase()))):a.data.messages:[]}),[null==a||null===(n=a.data)||void 0===n?void 0:n.messages,s]);return a?o.createElement(it.A,{title:o.createElement("div",{className:"space-y-2"},o.createElement("div",{className:"font-medium"},a.source," → ",a.target),o.createElement("div",{className:"text-sm text-secondary flex justify-between"},a.data&&o.createElement("span",null,a.data.messages.length," message",""+(a.data.messages.length>1?"s":"")),o.createElement("span",null,l.toLocaleString()," tokens")),a.data&&a.data.messages.length>0&&o.createElement("div",{className:"text-xs py-2 font-normal"}," ","The above represents the number of times the ",`${a.target}`," ","node sent a message"," ",o.createElement("span",{className:"font-semibold underline text-accent"},"after")," ","the ",`${a.source}`," node."," ")),open:r,onCancel:i,width:800,footer:null},o.createElement("div",{className:"max-h-[70vh] overflow-y-auto space-y-4 scroll pr-2"},o.createElement(bt,{placeholder:"Search message content...",value:s,onChange:e=>c(e.target.value),allowClear:!0,className:"sticky top-0 z-10"}),o.createElement("div",{className:"space-y-4 "},u.map(((e,t)=>o.createElement(wt,{key:t,message:e,isLast:t===u.length-1}))),0===u.length&&o.createElement("div",{className:"text-center text-secondary py-8"},"No messages found")))):null},_t={default:{width:170,height:100},end:{width:170,height:80},task:{width:170,height:100}},kt=function(e,t,n,r,o){void 0===r&&(r=!1);const i="active"===(null==o?void 0:o.status)||"awaiting_input"===(null==o?void 0:o.status);return"user"===t?{id:e,type:"agentNode",position:{x:0,y:0},data:{type:"user",label:"User",agentType:"user",description:"Human user",isActive:r,status:"",reason:"",draggable:!i}}:"end"===t?{id:e,type:"agentNode",position:{x:0,y:0},data:{type:"end",label:"End",status:null==o?void 0:o.status,reason:(null==o?void 0:o.error_message)||"",agentType:"",description:"",isActive:!1,draggable:!1}}:{id:e,type:"agentNode",position:{x:0,y:0},data:{type:"agent",label:e,agentType:(null==n?void 0:n.label)||"",description:(null==n?void 0:n.description)||"",isActive:r,status:"",reason:"",draggable:!i}}},Nt={agentNode:Ye},St={custom:e=>{var t;let{id:n,sourceX:r,sourceY:i,targetX:a,targetY:s,source:c,target:l,data:u,style:d={},markerEnd:h}=e;const f=c===l,m=d.strokeWidth||1,p=(null===(t=u.messages)||void 0===t?void 0:t.length)||0,g=f?Math.max(m,2):Math.min(Math.max(p,1),5)*m;let y="",v=0,x=0;if("secondary"===u.routingType||f){const e=140;y=`\n      M ${r},${i}\n      L ${r},${i+10}\n      L ${r+e},${i+10}\n      L ${r+e},${s-10}\n      L ${a},${s-10}\n      L ${a},${s}\n    `,v=r+e,x=(i+s)/2}else[y,v,x]=(0,Be.oN)({sourceX:r,sourceY:i,targetX:a,targetY:s});const w=((e,t)=>{if(!u.routingType||f)return{x:e,y:t};u.routingType;const n="secondary"===u.routingType?-25:25,o=a-r,c=s-i;return{x:Math.abs(o)>Math.abs(c)?e:e+n,y:t+("secondary"===u.routingType?-20:20)}})(v,x);return o.createElement(o.Fragment,null,o.createElement("path",{id:n,className:"react-flow__edge-path",d:y,style:{...d,strokeWidth:g,stroke:"secondary"===u.routingType?"#0891b2":d.stroke},markerEnd:h}),(null==u?void 0:u.label)&&o.createElement(Re.rV,null,o.createElement("div",{style:{position:"absolute",transform:`translate(-50%, -50%) translate(${w.x}px,${w.y}px)`,pointerEvents:"all",transition:"all 0.2s ease-in-out"},onClick:u.onClick},o.createElement("div",{className:"px-2 py-1 rounded bg-secondary hover:bg-tertiary text-primary   cursor-pointer transform hover:scale-110 transition-all flex items-center gap-1",style:{whiteSpace:"nowrap"}},p>0&&o.createElement("span",{className:"text-xs text-secondary"},"(",p,")"),o.createElement("span",{className:"text-sm"},u.label)))))}},Mt=e=>{let{teamConfig:t,run:n}=e;const{fitView:r}=(0,Re.VH)(),{0:i,1:a}=(0,o.useState)([]),{0:s,1:c}=(0,o.useState)([]),{0:l,1:u}=(0,o.useState)(!1),{0:d,1:h}=(0,o.useState)(!1),{agentFlow:f}=(0,Xe.J)(),{0:m,1:p}=(0,o.useState)(!1),{0:g,1:y}=(0,o.useState)(null),v=(0,o.useCallback)((e=>{var t;null!==(t=e.data)&&void 0!==t&&t.messages&&(y(e),p(!0))}),[]),x=(0,o.useCallback)((e=>{a((t=>(0,Re._0)(e,t)))}),[]),w=(0,o.useRef)(null);(0,o.useEffect)((()=>{if(l){const e=setTimeout((()=>{r({padding:.2,duration:200}),u(!1)}),100);return()=>clearTimeout(e)}}),[l,r]);const b=(0,o.useCallback)((e=>{if(!n.task)return{nodes:[],edges:[]};const r=new Map,o=new Map,i=new Map;if(e.length>0){var a,s,c;const o=t.config.participants.find((t=>{var n;return t.config.name===(null===(n=e[0])||void 0===n?void 0:n.source)}));r.set(null===(a=e[0])||void 0===a?void 0:a.source,kt(null===(s=e[0])||void 0===s?void 0:s.source,"user"===(null===(c=e[0])||void 0===c?void 0:c.source)?"user":"agent",o,!1,n))}for(let f=0;f<e.length-1;f++){const i=e[f],a=e[f+1],s=`${null==i?void 0:i.source}->${null==a?void 0:a.source}`;if(o.has(s)){var l,u;const e=o.get(s);e.count++,e.totalTokens+=((null==i||null===(l=i.models_usage)||void 0===l?void 0:l.prompt_tokens)||0)+((null==i||null===(u=i.models_usage)||void 0===u?void 0:u.completion_tokens)||0),e.messages.push(i)}else{var d,h;o.set(s,{source:null==i?void 0:i.source,target:null==a?void 0:a.source,count:1,totalTokens:((null==i||null===(d=i.models_usage)||void 0===d?void 0:d.prompt_tokens)||0)+((null==i||null===(h=i.models_usage)||void 0===h?void 0:h.completion_tokens)||0),messages:[i]})}if(!r.has(null==a?void 0:a.source)){const e=t.config.participants.find((e=>e.config.name===a.source));r.set(a.source,kt(a.source,"user"===a.source?"user":"agent",e,!1,n))}}o.forEach(((e,t)=>{const[n,r]=t.split("->"),a=`${r}->${n}`,s=o.get(a);if(s&&!i.has(t)){const t=[n,r].sort().join("->");i.set(t,{forward:e,reverse:s})}}));const m=[],p=new Set,g=e=>f.showLabels&&e.totalTokens>0?`${e.count>1?`${e.count}x`:""} ${f.showTokens?`(${e.totalTokens} tokens)`:""}`.trim():"";if(o.forEach(((e,t)=>{if(p.has(t))return;const[r,a]=t.split("->"),s=[r,a].sort().join("->");if(i.get(s)){const e=`${r}->${a}`,t=`${a}->${r}`,n=`${r}-${a}-forward`,i=`${a}-${r}-reverse`,s=(e,t,n,r)=>({id:n,source:e.source,target:e.target,type:"custom",data:{label:g(e),messages:f.showMessages?e.messages:[],routingType:t?"secondary":"primary",bidirectionalPair:r},style:{stroke:"#2563eb",strokeWidth:1}});m.push(s(o.get(e),!1,n,i),s(o.get(t),!0,i,n)),p.add(e),p.add(t)}else m.push({id:`${e.source}-${e.target}-${t}`,source:e.source,target:e.target,type:"custom",data:{label:g(e),messages:f.showMessages?e.messages:[]},animated:"active"===n.status&&t===Array.from(o.keys()).pop(),style:{stroke:"#2563eb",strokeWidth:1}})})),n&&e.length>0){const t=e[e.length-1];if(["complete","error","stopped"].includes(n.status)){r.set("end",kt("end","end",void 0,!1,n));const e={complete:"#2563eb",stopped:"red",error:"red",active:"#2563eb",awaiting_input:"#2563eb",timeout:"red",created:"#2563eb"}[n.status];m.push({id:`${null==t?void 0:t.source}-end`,source:null==t?void 0:t.source,target:"end",type:"custom",data:{label:f.showLabels?"ended":"",messages:[]},style:{stroke:e,strokeWidth:1,opacity:1,zIndex:100}})}}return{nodes:Array.from(r.values()),edges:m}}),[t.config.participants,n,f]),E=(0,o.useCallback)((()=>{h(!d),u(!0)}),[d]);(0,o.useEffect)((()=>{if(!d)return;const e=e=>{"Escape"===e.key&&E()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[d,E]),(0,o.useEffect)((()=>{const{nodes:e,edges:t}=b(n.messages.map((e=>e.config))),{nodes:o,edges:i}=((e,t,n)=>{const r=(new(He().graphlib.Graph)).setDefaultEdgeLabel((()=>({})));r.setGraph({rankdir:n,nodesep:110,ranksep:100,ranker:"network-simplex",marginx:30,marginy:30}),e.forEach((e=>{const t="end"===e.data.type?_t.end:_t.default;r.setNode(e.id,{...e,...t})}));const o=new Map;t.forEach((e=>{e.source,e.target,e.target,e.source;const t=[e.source,e.target].sort().join("-");o.has(t)||o.set(t,[]),o.get(t).push({source:e.source,target:e.target})})),o.forEach(((e,t)=>{if(2===e.length){const[t,n]=e;r.setEdge(t.source,t.target,{weight:2,minlen:1}),r.setEdge(n.source,n.target,{weight:1,minlen:1})}else{const t=e[0];r.setEdge(t.source,t.target,{weight:1,minlen:1})}})),He().layout(r);const i=e.map((e=>{const{x:t,y:n}=r.node(e.id),o="end"===e.data.type?_t.end:_t.default;return{...e,position:{x:t-o.width/2,y:n-o.height/2}}})),a=new Map(i.map((e=>[e.id,{x:e.position.x+_t.default.width/2,y:e.position.y+_t.default.height/2}])));return{nodes:i,edges:t.map((e=>{const t=a.get(e.source),n=a.get(e.target);return{...e,sourceX:t.x,sourceY:t.y,targetX:n.x,targetY:n.y}}))}})(e,t,f.direction);a(o),c(i),n.messages.length>0&&setTimeout((()=>{r({padding:.2,duration:200})}),50)}),[n.messages,b,f.direction,n.status,r]);const _={nodes:i,edges:s.map((e=>({...e,data:{...e.data,onClick:()=>v(e)}}))),nodeTypes:Nt,edgeTypes:St,defaultViewport:{x:0,y:0,zoom:1},minZoom:.5,maxZoom:2,onNodesChange:x,proOptions:{hideAttribution:!0}},k=(0,o.useMemo)((()=>({isFullscreen:d,onToggleFullscreen:E,onResetView:()=>r({padding:.2,duration:200})})),[d,E,r]);return o.createElement("div",{ref:w,className:`transition-all duration-200 ${d?"fixed inset-4 z-[50] shadow":"w-full h-full min-h-[300px]"} bg-tertiary rounded-lg`},d&&o.createElement("div",{className:"fixed inset-0 -z-10 bg-background/80 backdrop-blur-sm",onClick:E}),o.createElement(Re.Gc,_,f.showGrid&&o.createElement(Re.VS,null),f.showMiniMap&&o.createElement(Re.of,null),o.createElement("div",{className:"absolute top-0 right-0 z-10"},o.createElement(rt,k))),o.createElement(Et,{open:m,onClose:()=>{p(!1),y(null)},edge:g}))};function Ot(e){return o.createElement(Re.Ln,null,o.createElement(Mt,e))}const Ct=(0,ye.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),At=(0,ye.A)("SendHorizontal",[["path",{d:"M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z",key:"117uat"}],["path",{d:"M6 12h16",key:"s4cdu5"}]]),$t={DURATION_MS:18e4,DURATION_SEC:180,WEBSOCKET_CODE:4e3,DEFAULT_MESSAGE:"Input timeout after 3 minutes",WARNING_THRESHOLD_SEC:30};var It=e=>{let{prompt:t,onSubmit:n,disabled:r=!1,onTimeout:i}=e;const[a,s]=o.useState(""),[c,l]=o.useState(!1),[u,d]=o.useState($t.DURATION_SEC),[h,f]=o.useState(!1),m=o.useRef(null),p=(0,o.useRef)();(0,o.useEffect)((()=>(r||(p.current=setInterval((()=>{d((e=>e<=1?(clearInterval(p.current),null==i||i(),0):e-1))}),1e3)),()=>{p.current&&clearInterval(p.current)})),[r,i]),o.useEffect((()=>{m.current&&!r&&m.current.focus()}),[r]);const g=async()=>{if(a.trim()&&!r&&!c){l(!0);try{await n(a.trim()),s(""),f(!1)}finally{l(!1)}}};return o.createElement("div",{className:"p-4 bg-accent/10 border border-accent/20 rounded-lg mt-3 "+(r?"opacity-50":"")},o.createElement("div",{className:"flex justify-between items-center mb-2"},o.createElement("div",{className:"text-sm font-medium text-primary flex items-center gap-2"},t,!h&&!r&&o.createElement("span",{className:"relative flex h-3 w-3"},o.createElement("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-accent opacity-75"}),o.createElement("span",{className:"relative inline-flex rounded-full h-3 w-3 bg-accent"}))),!r&&o.createElement("div",{className:"flex items-center gap-2 text-sm"},o.createElement(Ct,{size:14,className:"text-accent"}),o.createElement("span",{className:u<$t.WARNING_THRESHOLD_SEC?"text-red-500 font-bold animate-pulse":"text-accent"},(y=u,`${Math.floor(y/60)}:${(y%60).toString().padStart(2,"0")}`)))),o.createElement("div",{className:"flex gap-2"},o.createElement("input",{ref:m,type:"text",value:a,onChange:e=>{s(e.target.value),h||f(!0)},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),g())},disabled:r||c,className:"text-primary flex-1 px-3 py-2 rounded bg-tertiary border border-secondary focus:border-accent focus:ring-1 focus:ring-accent outline-none disabled:opacity-50",placeholder:r?"Input timeout - please restart the conversation":"Type your response..."}),o.createElement("button",{onClick:g,disabled:r||c||!a.trim(),className:"px-4 py-2 rounded bg-accent text-white hover:bg-accent/90 disabled:opacity-50 disabled:hover:bg-accent flex items-center gap-2 transition-all "+(h||r||a.trim()?"":"animate-pulse")},c?o.createElement(Ce,{size:16,className:"animate-spin"}):o.createElement(At,{size:16}),o.createElement("span",null,"Submit"))));var y},zt=n(1325);const Tt=e=>{let{content:t,source:n}=e;const{0:r,1:i}=(0,o.useState)(!0);return(0,o.useEffect)((()=>{const e=setInterval((()=>{i((e=>!e))}),530);return()=>clearInterval(e)}),[]),o.createElement("div",{className:"flex items-start gap-2 p-2 rounded bg-tertiary border border-secondary transition-all duration-200 mb-6"},o.createElement("div",{className:"p-1.5 rounded bg-light text-primary"},o.createElement(Oe.A,{size:14})),o.createElement("div",{className:"flex-1 min-w-0"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-sm font-semibold text-primary"},n)),o.createElement("div",{className:"text-sm text-secondary break-all"},t,r&&o.createElement("span",{className:"inline-block w-2 h-4 ml-1 bg-accent/70 animate-pulse"}))))},Pt=e=>e.filter((e=>"llm_call_event"!==e.config.source));var jt=e=>{var t,n,r,i,s;let{run:c,onInputResponse:l,onCancel:u,teamConfig:d,isFirstRun:h=!1,streamingContent:f}=e;const{0:m,1:p}=(0,o.useState)(!0),g=(0,o.useRef)(null),y="active"===c.status||"awaiting_input"===c.status,{uiSettings:v}=(0,zt.C)(),{0:x,1:w}=(0,o.useState)(null===(t=v.show_agent_flow_by_default)||void 0===t||t),b=(0,o.useMemo)((()=>v.show_llm_call_events?c.messages:c.messages.filter((e=>"llm_call_event"!==e.config.source))),[c.messages,v.show_llm_call_events]);console.log("Run task",c.task),(0,o.useEffect)((()=>{setTimeout((()=>{g.current&&g.current.scrollTo({top:g.current.scrollHeight,behavior:"smooth"})}),450)}),[c.messages,f]);const E=e=>{switch(e){case"active":return o.createElement("div",{className:"inline-block mr-1"},o.createElement(Ce,{size:20,className:"inline-block mr-1 text-accent animate-spin"}),o.createElement("span",{className:"inline-block mr-2 ml-1 "},"Processing"),o.createElement(lt.XC,{size:8}));case"awaiting_input":return o.createElement("div",{className:"text-sm mb-2"},o.createElement(Ae,{size:20,className:"inline-block mr-2 text-accent"}),o.createElement("span",{className:"inline-block mr-2"},"Waiting for your input "),o.createElement(lt.XC,{size:8}));case"complete":return o.createElement("div",{className:"text-sm mb-2"},o.createElement($e.A,{size:20,className:"inline-block mr-2 text-accent"}),"Task completed");case"error":return o.createElement("div",{className:"text-sm mb-2"},o.createElement(Ie.A,{size:20,className:"inline-block mr-2 text-red-500"}),c.error_message||"An error occurred");case"stopped":return o.createElement("div",{className:"text-sm mb-2"},o.createElement(ze,{size:20,className:"inline-block mr-2 text-red-500"}),"Task was stopped");default:return null}},_=null===(n=c.team_result)||void 0===n||null===(r=n.task_result)||void 0===r?void 0:r.messages.slice(-1)[0],k=b.filter((e=>"llm_call_event"!==e.config.source)).slice(-1)[0];return o.createElement("div",{className:"space-y-6  mr-2 "},o.createElement("div",{className:(h?"mb-2":"mt-4")+" mb-4 pb-2 pt-2 border-b border-dashed border-secondary"},o.createElement("div",{className:"text-xs text-secondary"},o.createElement(a.A,{title:o.createElement("div",{className:"text-xs"},o.createElement("div",null,"ID: ",c.id),o.createElement("div",null,"Created: ",new Date(c.created_at).toLocaleString()),o.createElement("div",null,"Status: ",c.status))},o.createElement("span",{className:"cursor-help"},"Run ...",c.id," | ",(0,lt.vq)((null==c?void 0:c.created_at)||"")," ")),!h&&o.createElement(o.Fragment,null," ","|"," ",o.createElement(Ie.A,{className:"w-4 h-4 -mt-1 inline-block mr-1 ml-1"}),"Note: Each run does not share data with previous runs in the same session yet."))),o.createElement("div",{className:"flex flex-col items-end w-full"},o.createElement("div",{className:"w-full"},o.createElement(wt,{message:c.task,isLast:!1}))),o.createElement("div",{className:"flex flex-col items-start"},o.createElement("div",{className:"flex items-center gap-2 mb-1"},o.createElement("div",{className:"p-1.5 rounded bg-secondary text-primary"},o.createElement(Oe.A,{size:20})),o.createElement("span",{className:"text-sm font-medium text-primary"},"Agent Team")),o.createElement("div",{className:"   w-full"},o.createElement("div",{className:"p-4 bg-secondary border border-secondary rounded"},o.createElement("div",{className:"flex justify-between items-start mb-2"},o.createElement("div",{className:"text-primary"},E(c.status)),y&&u&&o.createElement("button",{onClick:u,className:"px-4 text-sm py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors flex items-center gap-2"},o.createElement(ze,{size:16}),"Cancel Run")),"awaiting_input"!==c.status&&"active"!==c.status&&o.createElement("div",{className:"text-sm break-all"},o.createElement("div",{className:"text-xs bg-tertiary mb-1 text-secondary border-secondary -mt-2 bdorder rounded p-2"},"Stop reason: ",null===(i=c.team_result)||void 0===i||null===(s=i.task_result)||void 0===s?void 0:s.stop_reason),k?o.createElement(wt,{message:k.config,isLast:!0}):o.createElement(o.Fragment,null,_&&o.createElement(wt,{message:_})))),o.createElement("div",{className:""},b.length>0&&o.createElement("div",{className:"mt-2 pl-4 border-secondary rounded-b border-l-2 border-secondary/30"},o.createElement("div",{className:"flex pt-2"},o.createElement("div",{className:"flex-1"},o.createElement("button",{onClick:()=>p(!m),className:"flex items-center gap-1 text-sm text-secondary hover:text-primary transition-colors"},o.createElement(Ae,{size:16})," Agent steps [",o.createElement("span",{className:"text-accent text-xs"},m?o.createElement("span",null,o.createElement(Te.A,{size:16,className:"inline-block mr-1"}),"Hide"):o.createElement("span",null," ",o.createElement(Pe.A,{size:16,className:"inline-block mr-1"})," ","Show more"))," ","]")),o.createElement("div",{className:"text-sm text-secondary"},(e=>e.reduce(((e,t)=>{var n;return null!==(n=t.config)&&void 0!==n&&n.models_usage?e+(t.config.models_usage.prompt_tokens||0)+(t.config.models_usage.completion_tokens||0):e}),0))(b)," tokens |"," ",b.length," messages")),m&&o.createElement("div",{className:"flex relative flex-row gap-4"},!x&&o.createElement("div",{className:"z-50 absolute right-2 top-2 bg-tertiary rounded p-2 hover:opacity-100 opacity-80"},o.createElement(a.A,{title:"Show message flow graph"},o.createElement("button",{onClick:()=>w(!0),className:" p-1 rounded-md bg-tertiary  hover:bg-secondary  transition-colors"},o.createElement(je,{strokeWidth:1.5,size:22})))),o.createElement("div",{ref:g,className:"flex-1 mt-2 overflow-y-auto max-h-[400px] scroll-smooth scroll pb-2 relative"},o.createElement("div",{id:"scroll-gradient",className:"scroll-gradient h-8"}," ",o.createElement("span",{className:"  inline-block h-6"})," "),b.map(((e,t)=>o.createElement("div",{key:"message_id"+t+c.id,className:"  mr-2"},o.createElement(wt,{message:e.config,isLast:t===b.length-1})))),f&&f.runId===c.id&&o.createElement("div",{className:"mr-2 mb-10"},o.createElement(Tt,{content:f.content,source:f.source})),"awaiting_input"===c.status&&l&&o.createElement("div",{className:"mt-4 mr-2"},o.createElement(It,{prompt:"Type your response...",onSubmit:l})),o.createElement("div",{className:"text-primary mt-2"},o.createElement("div",{className:"w-4 h-4 inline-block  border-secondary rounded-bl-lg border-l-2 border-b-2"})," ",o.createElement("div",{className:"inline-block "},E(c.status)))),x&&o.createElement("div",{className:"bg-tertiary flex-1 rounded mt-2 relative"},o.createElement("div",{className:"z-10 absolute left-2 top-2 p-2 hover:opacity-100 opacity-80"},o.createElement(a.A,{title:"Hide message flow"},o.createElement("button",{onClick:()=>w(!1),className:" p-1 rounded-md bg-tertiary hover:bg-secondary transition-colors"},o.createElement(Le,{strokeWidth:1.5,size:22})))),d&&o.createElement(Ot,{teamConfig:d,run:{...c,messages:Pt(b)}}))))))))},Lt=n(7677);const Rt=(0,ye.A)("SquareSplitHorizontal",[["path",{d:"M8 19H5c-1 0-2-1-2-2V7c0-1 1-2 2-2h3",key:"lubmu8"}],["path",{d:"M16 5h3c1 0 2 1 2 2v10c0 1-1 2-2 2h-3",key:"1ag34g"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);var Dt=n(5061),Ht=n(7015);var Bt=e=>{let{session:t,availableSessions:n,onSessionChange:i,className:s=""}=e;const[c,l]=o.useState(""),u=n.filter((e=>e.name.toLowerCase().includes(c.toLowerCase()))),d=[{type:"group",key:"search-sessions",label:o.createElement("div",null,o.createElement("div",{className:"text-xs text-secondary mb-1"},"Search sessions"),o.createElement(ot.A,{prefix:o.createElement(Ht.A,{className:"w-4 h-4"}),placeholder:"Search sessions",onChange:e=>l(e.target.value),onClick:e=>e.stopPropagation()}))},{type:"divider"}].concat((0,r.A)(u.map((e=>({key:(e.id||"").toString(),label:o.createElement("div",{className:"py-1"},o.createElement("div",{className:"font-medium"},e.name),o.createElement("div",{className:"text-xs text-secondary"},(0,lt.vq)(e.updated_at||"")))})))));return o.createElement(Ge.A,{menu:{items:d,onClick:e=>{let{key:t}=e;const r=n.find((e=>e.id===Number(t)));r&&i(r)}},trigger:["click"]},o.createElement("div",{className:`cursor-pointer flex items-center gap-2 min-w-0 ${s}`},o.createElement(a.A,{title:null==t?void 0:t.name},o.createElement("span",{className:"text-primary font-medium truncate overflow-hidden"},(null==t?void 0:t.name)||"Select Session")),o.createElement(Pe.A,{className:"w-4 h-4 text-secondary flex-shrink-0"})))};n(8351).A;function Ft(e){let{session:t,isCompareMode:n=!1,isSecondaryView:u=!1,onCompareClick:d,onExitCompare:h,onSessionChange:f,availableSessions:m=[],showCompareButton:p=!0}=e;const g=(0,c.Tt)(),[y,v]=o.useState(!1),[x,w]=o.useState({status:!0,message:"All good"}),[b,E]=o.useState([]),[_,k]=o.useState(null),[N,S]=i.Ay.useMessage(),M=o.useRef(null),[O,C]=o.useState(null),{user:A}=o.useContext(l.v),[$,I]=o.useState(null),[z,T]=o.useState(null),P=o.useRef(null),j=o.useRef(null),L=(e,t,n)=>({created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),config:e,session_id:n,run_id:t,user_id:(null==A?void 0:A.id)||void 0});o.useEffect((()=>{null!=t&&t.id?((async()=>{if(null!=t&&t.id&&null!=A&&A.id)try{const e=await Me.j.getSessionRuns(t.id,A.id);E(e.runs)}catch(x){console.error("Error loading session runs:",x),N.error("Failed to load chat history")}})(),k(null)):(E([]),k(null))}),[null==t?void 0:t.id]),o.useEffect((()=>{null!=t&&t.team_id&&null!=A&&A.id&&Se.CG.getTeam(t.team_id,A.id).then((e=>{T(e.component)})).catch((e=>{console.error("Error loading team config:",e),T(null)}))}),[t]),o.useEffect((()=>{setTimeout((()=>{M.current&&b.length>0&&M.current.scrollTo({top:M.current.scrollHeight,behavior:"auto"})}),450)}),[b.length,null==_?void 0:_.messages]),o.useEffect((()=>()=>{P.current&&clearTimeout(P.current),null==$||$.close()}),[$]);const R=e=>{console.error("Error:",e),i.Ay.error("Error during request processing"),k((t=>{if(!t)return null;const n={...t,status:"error",error_message:e instanceof Error?e.message:"Unknown error occurred"};return E((e=>[].concat((0,r.A)(e),[n]))),null})),w({status:!1,message:e instanceof Error?e.message:"Unknown error occurred"})},D=(e,n,o)=>{if(!t||!t.id)throw new Error("Invalid session configuration");(null==$?void 0:$.readyState)===WebSocket.OPEN&&$.close();const i=H(g),a="https:"===window.location.protocol?"wss:":"ws:",s=localStorage.getItem("auth_token"),c=new WebSocket(`${a}//${i}/api/ws/runs/${e}?token=${s}`);return k({id:e,created_at:(new Date).toISOString(),status:"active",task:L({content:n,source:"user"},e,t.id||0).config,team_result:null,messages:[],error_message:void 0}),c.onopen=()=>{c.send(JSON.stringify({type:"start",task:n,files:o,team_config:z}))},c.onmessage=e=>{try{(e=>{k((n=>{if(!n||null==t||!t.id)return null;switch(e.type){case"error":P.current&&(clearTimeout(P.current),P.current=null),$&&($.close(),I(null),j.current=null),console.log("Error: ",e.error);const o={...n,status:"error",error_message:e.error||"An error occurred"};return E((e=>[].concat((0,r.A)(e),[o]))),null;case"message_chunk":if(!e.data)return n;try{const t=e.data;C((e=>({runId:n.id,content:((null==e?void 0:e.content)||"")+(t.content||""),source:t.source||"assistant"})))}catch(x){console.error("Error parsing message chunk:",x)}return n;case"message":if(C(null),!e.data)return n;const i=L(e.data,n.id,t.id);return{...n,messages:[].concat((0,r.A)(n.messages),[i])};case"input_request":return P.current&&clearTimeout(P.current),P.current=setTimeout((()=>{const e=j.current;console.log("Input timeout",e),(null==e?void 0:e.readyState)===WebSocket.OPEN&&(e.send(JSON.stringify({type:"stop",reason:$t.DEFAULT_MESSAGE,code:$t.WEBSOCKET_CODE})),k((e=>e?{...e,status:"stopped",error_message:$t.DEFAULT_MESSAGE}:null)))}),$t.DURATION_MS),{...n,status:"awaiting_input"};case"result":case"completion":const a="complete"===e.status?"complete":"error"===e.status?"error":"stopped",s=e=>e&&"task_result"in e&&"usage"in e&&"duration"in e,c={...n,status:a,team_result:e.data&&s(e.data)?e.data:null};return"complete"===a?(P.current&&(clearTimeout(P.current),P.current=null),$&&($.close(),I(null),j.current=null),E((e=>[].concat((0,r.A)(e),[c]))),null):c;default:return n}}))})(JSON.parse(e.data))}catch(x){console.error("WebSocket message parsing error:",x)}},c.onclose=()=>{j.current=null,I(null)},c.onerror=e=>{R(e)},c},H=e=>{try{let t=e.replace(/(^\w+:|^)\/\//,"");return t=t.startsWith("localhost")?t.replace("/api",""):"/api"===t?window.location.host:t.replace("/api","").replace(/\/$/,""),t}catch(x){throw console.error("Error processing server URL:",x),new Error("Invalid server URL configuration")}};return o.createElement("div",{className:"text-primary h-[calc(100vh-165px)] bg-primary relative rounded flex-1 scroll"},S,o.createElement("div",{className:"flex pt-2 items-center justify-between text-sm h-10"},o.createElement("div",{className:"flex items-center gap-2 min-w-0 overflow-hidden flex-1 pr-4"},n?o.createElement(Bt,{session:t,availableSessions:m,onSessionChange:f||(()=>{}),className:"w-full"}):o.createElement(o.Fragment,null,o.createElement("span",{className:"text-primary font-medium whitespace-nowrap flex-shrink-0"},"Sessions"),t&&o.createElement(o.Fragment,null,o.createElement(Lt.A,{className:"w-4 h-4 text-secondary flex-shrink-0"}),o.createElement(a.A,{title:t.name},o.createElement("span",{className:"text-secondary truncate overflow-hidden"},t.name))))),o.createElement("div",{className:"flex items-center gap-2 flex-shrink-0 whitespace-nowrap"},!n&&!u&&p&&o.createElement(s.Ay,{type:"text",onClick:d,icon:o.createElement(Rt,{className:"w-4 h-4"})},"Compare"),n&&u&&o.createElement(s.Ay,{type:"text",onClick:h,icon:o.createElement(we.A,{className:"w-4 h-4"})},"Exit Compare"))),o.createElement("div",{className:"flex flex-col h-full"},o.createElement("div",{ref:M,className:"flex-1 overflow-y-auto scroll mt-2 min-h-0 relative"},o.createElement("div",{id:"scroll-gradient",className:"scroll-gradient h-8 top-0"}," ",o.createElement("span",{className:"  inline-block h-6"})," "),o.createElement(o.Fragment,null,z&&o.createElement(o.Fragment,null,b.map(((e,t)=>o.createElement(jt,{teamConfig:z,key:e.id+"-review-"+t,run:e,isFirstRun:0===t}))),_&&o.createElement(jt,{run:_,teamConfig:z,onInputResponse:async e=>{if(j.current&&_){if(j.current.readyState!==WebSocket.OPEN)return console.error("Socket not in OPEN state:",j.current.readyState),void R(new Error("WebSocket connection not available"));P.current&&(clearTimeout(P.current),P.current=null);try{j.current.send(JSON.stringify({type:"input_response",response:e})),k((e=>e?{...e,status:"active"}:null))}catch(x){R(x)}}},onCancel:async()=>{if(j.current&&_){P.current&&(clearTimeout(P.current),P.current=null);try{j.current.send(JSON.stringify({type:"stop",reason:"Cancelled by user"})),k((e=>e?{...e,status:"stopped"}:null))}catch(x){R(x)}}},isFirstRun:0===b.length,streamingContent:O}),!_&&0===b.length&&o.createElement("div",{className:"flex items-center justify-center h-[80%]"},o.createElement("div",{className:"text-center"},o.createElement(Dt.A,{strokeWidth:1,className:"w-64 h-64 mb-4 inline-block"}),o.createElement("div",{className:"  font-medium mb-2"},"Start a new task"),o.createElement("div",{className:"text-secondary text-sm"},"Enter a task to get started")))),!z&&o.createElement("div",{className:"flex items-center justify-center h-[80%]"},o.createElement("div",{className:"text-center  "},o.createElement(Dt.A,{strokeWidth:1,className:"w-64 h-64 mb-4 inline-block"}),o.createElement("div",{className:"  font-medium mb-2"},"No team configuration found for this session (may have been deleted)."," "),o.createElement("div",{className:"text-secondary text-sm"},"Add a team to the session to get started."))))),t&&z&&o.createElement("div",{className:"flex-shrink-0"},o.createElement(Ne,{onSubmit:async function(e,n){if(void 0===n&&(n=[]),w(null),v(!0),$&&($.close(),I(null),j.current=null),P.current&&(clearTimeout(P.current),P.current=null),null!=t&&t.id&&z)try{const r=await(async e=>await Me.j.createRun(e,(null==A?void 0:A.id)||""))(t.id),o=await(0,c.f7)(n);k({id:r,created_at:(new Date).toISOString(),status:"created",messages:[],task:{content:e,source:"user"},team_result:null,error_message:void 0});const i=D(r,e,o);I(i),j.current=i}catch(x){R(x)}finally{v(!1)}else v(!1)},loading:y,error:x,disabled:"awaiting_input"===(null==_?void 0:_.status)||"active"===(null==_?void 0:_.status)}))))}},258:function(e,t,n){"use strict";let r=n(4809);e.exports={run:function(e){e.graph().dummyChains=[],e.edges().forEach((t=>function(e,t){let n,o,i,a=t.v,s=e.node(a).rank,c=t.w,l=e.node(c).rank,u=t.name,d=e.edge(t),h=d.labelRank;if(l===s+1)return;for(e.removeEdge(t),i=0,++s;s<l;++i,++s)d.points=[],o={width:0,height:0,edgeLabel:d,edgeObj:t,rank:s},n=r.addDummyNode(e,"edge",o,"_d"),s===h&&(o.width=d.width,o.height=d.height,o.dummy="edge-label",o.labelpos=d.labelpos),e.setEdge(a,n,{weight:d.weight},u),0===i&&e.graph().dummyChains.push(n),a=n;e.setEdge(a,c,{weight:d.weight},u)}(e,t)))},undo:function(e){e.graph().dummyChains.forEach((t=>{let n,r=e.node(t),o=r.edgeLabel;for(e.setEdge(r.edgeObj,o);r.dummy;)n=e.successors(t)[0],e.removeNode(t),o.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(o.x=r.x,o.y=r.y,o.width=r.width,o.height=r.height),t=n,r=e.node(t)}))}}},334:function(e,t,n){let r=n(4809),o=n(6743).Graph;e.exports={debugOrdering:function(e){let t=r.buildLayerMatrix(e),n=new o({compound:!0,multigraph:!0}).setGraph({});return e.nodes().forEach((t=>{n.setNode(t,{label:t}),n.setParent(t,"layer"+e.node(t).rank)})),e.edges().forEach((e=>n.setEdge(e.v,e.w,{},e.name))),t.forEach(((e,t)=>{let r="layer"+t;n.setNode(r,{rank:"same"}),e.reduce(((e,t)=>(n.setEdge(e,t,{style:"invis"}),t)))})),n}}},418:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},468:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},477:function(e){e.exports=function(e,t,n){let r,o={};n.forEach((n=>{let i,a,s=e.parent(n);for(;s;){if(i=e.parent(s),i?(a=o[i],o[i]=s):(a=r,r=s),a&&a!==s)return void t.setEdge(a,s);s=i}}))}},860:function(e,t,n){"use strict";var r=n(9294).longestPath,o=n(3776),i=n(3061);e.exports=function(e){switch(e.graph().ranker){case"network-simplex":default:s(e);break;case"tight-tree":!function(e){r(e),o(e)}(e);break;case"longest-path":a(e)}};var a=r;function s(e){i(e)}},942:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1236:function(e,t,n){let r=n(4809);function o(e,t,n,o,i,a){let s={width:0,height:0,rank:a,borderType:t},c=i[t][a-1],l=r.addDummyNode(e,"border",s,n);i[t][a]=l,e.setParent(l,o),c&&e.setEdge(c,l,{weight:1})}e.exports=function(e){e.children().forEach((function t(n){let r=e.children(n),i=e.node(n);if(r.length&&r.forEach(t),Object.hasOwn(i,"minRank")){i.borderLeft=[],i.borderRight=[];for(let t=i.minRank,r=i.maxRank+1;t<r;++t)o(e,"borderLeft","_bl",n,i,t),o(e,"borderRight","_br",n,i,t)}}))}},1325:function(e,t,n){"use strict";n.d(t,{C:function(){return s}});var r=n(1511),o=n(7134),i=n(1806);const a={show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1},s=(0,r.v)()((0,o.Zr)(((e,t)=>({serverSettings:null,isLoading:!1,error:null,uiSettings:a,initializeSettings:async n=>{if(!t().isLoading)try{e({isLoading:!0,error:null});const t=await i.Y.getSettings(n),r=(e=>e&&e.config&&e.config.ui?e.config.ui:a)(t);e({serverSettings:t,uiSettings:r,isLoading:!1})}catch(r){console.error("Failed to load settings:",r),e({error:"Failed to load settings",isLoading:!1,uiSettings:a})}},updateUISettings:n=>{const{uiSettings:r}=t(),o={...r,...n};e({uiSettings:o})},resetUISettings:async()=>(e({uiSettings:a}),Promise.resolve())})),{name:"ags-app-settings-0",partialize:e=>({uiSettings:e.uiSettings})}))},1806:function(e,t,n){"use strict";n.d(t,{Y:function(){return i}});var r=n(7387);let o=function(e){function t(){return e.apply(this,arguments)||this}(0,r.A)(t,e);var n=t.prototype;return n.getSettings=async function(e){const t=await fetch(`${this.getBaseUrl()}/settings/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch settings");return n.data},n.updateSettings=async function(e,t){const n={...e,user_id:e.user_id||t};console.log("settingsData",n);const r=await fetch(`${this.getBaseUrl()}/settings/`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(n)}),o=await r.json();if(!o.status)throw new Error(o.message||"Failed to update settings");return o.data},t}(n(3838).y);const i=new o},2162:function(e,t,n){"use strict";var r=n(6540),o=n(9888);var i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,s=r.useRef,c=r.useEffect,l=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=s(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=l((function(){function e(e){if(!c){if(c=!0,a=e,e=r(e),void 0!==o&&h.hasValue){var t=h.value;if(o(t,e))return s=t}return s=e}if(t=s,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,s=n)}var a,s,c=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]}),[t,n,r,o]);var f=a(e,d[0],d[1]);return c((function(){h.hasValue=!0,h.value=f}),[f]),u(f),f}},2275:function(e,t,n){"use strict";n.d(t,{Do:function(){return rr},WZ:function(){return Qn},TG:function(){return or},ny:function(){return er},yX:function(){return ir},xN:function(){return Jo},Qc:function(){return tr},I$:function(){return jo},aQ:function(){return Fo},di:function(){return Vo},kO:function(){return Ko},ET:function(){return ai},rN:function(){return so},bi:function(){return No},_s:function(){return Yr},aE:function(){return vr},Hm:function(){return wo},KE:function(){return Tr},tn:function(){return Jn},xc:function(){return Zn},us:function(){return Vr},Pr:function(){return yr},e_:function(){return Qr},Fp:function(){return no},Mi:function(){return Ar},HF:function(){return sr},Eo:function(){return Gr},b5:function(){return po},Tq:function(){return xr},qX:function(){return oo},q1:function(){return Kr},YV:function(){return gr},oj:function(){return qr},aZ:function(){return fr},aW:function(){return xo},uD:function(){return Br},Jo:function(){return hr},U$:function(){return mr},X6:function(){return $r},oN:function(){return fo},ah:function(){return co},R4:function(){return Rr},r8:function(){return Oo},ZO:function(){return Kn},bK:function(){return nr},b$:function(){return cr},uj:function(){return io},v5:function(){return Ur},Ue:function(){return Dr},Er:function(){return Zr},oB:function(){return lr},kf:function(){return zr},mW:function(){return Ir},QE:function(){return Fr},kM:function(){return Or},No:function(){return Ao},Ff:function(){return jr},zj:function(){return Lr},s_:function(){return Pr},vS:function(){return ko},qn:function(){return Io},uL:function(){return Co}});var r={value:()=>{}};function o(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw new Error("illegal type: "+e);r[e]=[]}return new i(r)}function i(e){this._=e}function a(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}function s(e,t,n){for(var o=0,i=e.length;o<i;++o)if(e[o].name===t){e[o]=r,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}i.prototype=o.prototype={constructor:i,on:function(e,t){var n,r,o=this._,i=(r=o,(e+"").trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:t}}))),c=-1,l=i.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++c<l;)if(n=(e=i[c]).type)o[n]=s(o[n],e.name,t);else if(null==t)for(n in o)o[n]=s(o[n],e.name,null);return this}for(;++c<l;)if((n=(e=i[c]).type)&&(n=a(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new i(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=new Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(r=this._[e]).length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};var c=o;function l(){}function u(e){return null==e?l:function(){return this.querySelector(e)}}function d(){return[]}function h(e){return null==e?d:function(){return this.querySelectorAll(e)}}function f(e){return function(){return null==(t=e.apply(this,arguments))?[]:Array.isArray(t)?t:Array.from(t);var t}}function m(e){return function(){return this.matches(e)}}function p(e){return function(t){return t.matches(e)}}var g=Array.prototype.find;function y(){return this.firstElementChild}var v=Array.prototype.filter;function x(){return Array.from(this.children)}function w(e){return new Array(e.length)}function b(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function E(e,t,n,r,o,i){for(var a,s=0,c=t.length,l=i.length;s<l;++s)(a=t[s])?(a.__data__=i[s],r[s]=a):n[s]=new b(e,i[s]);for(;s<c;++s)(a=t[s])&&(o[s]=a)}function _(e,t,n,r,o,i,a){var s,c,l,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(c=t[s])&&(f[s]=l=a.call(c,c.__data__,s,t)+"",u.has(l)?o[s]=c:u.set(l,c));for(s=0;s<h;++s)l=a.call(e,i[s],s,i)+"",(c=u.get(l))?(r[s]=c,c.__data__=i[s],u.delete(l)):n[s]=new b(e,i[s]);for(s=0;s<d;++s)(c=t[s])&&u.get(f[s])===c&&(o[s]=c)}function k(e){return e.__data__}function N(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function S(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}b.prototype={constructor:b,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var M="http://www.w3.org/1999/xhtml",O={svg:"http://www.w3.org/2000/svg",xhtml:M,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function C(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),O.hasOwnProperty(t)?{space:O[t],local:e}:e}function A(e){return function(){this.removeAttribute(e)}}function $(e){return function(){this.removeAttributeNS(e.space,e.local)}}function I(e,t){return function(){this.setAttribute(e,t)}}function z(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function T(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function P(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function j(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function L(e){return function(){this.style.removeProperty(e)}}function R(e,t,n){return function(){this.style.setProperty(e,t,n)}}function D(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function H(e,t){return e.style.getPropertyValue(t)||j(e).getComputedStyle(e,null).getPropertyValue(t)}function B(e){return function(){delete this[e]}}function F(e,t){return function(){this[e]=t}}function V(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function Y(e){return e.trim().split(/^|\s+/)}function X(e){return e.classList||new G(e)}function G(e){this._node=e,this._names=Y(e.getAttribute("class")||"")}function q(e,t){for(var n=X(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function W(e,t){for(var n=X(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function U(e){return function(){q(this,e)}}function Z(e){return function(){W(this,e)}}function K(e,t){return function(){(t.apply(this,arguments)?q:W)(this,e)}}function J(){this.textContent=""}function Q(e){return function(){this.textContent=e}}function ee(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function te(){this.innerHTML=""}function ne(e){return function(){this.innerHTML=e}}function re(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function oe(){this.nextSibling&&this.parentNode.appendChild(this)}function ie(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ae(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===M&&t.documentElement.namespaceURI===M?t.createElement(e):t.createElementNS(n,e)}}function se(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function ce(e){var t=C(e);return(t.local?se:ae)(t)}function le(){return null}function ue(){var e=this.parentNode;e&&e.removeChild(this)}function de(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function he(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function fe(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)n=t[r],e.type&&n.type!==e.type||n.name!==e.name?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function me(e,t,n){return function(){var r,o=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(o)for(var a=0,s=o.length;a<s;++a)if((r=o[a]).type===e.type&&r.name===e.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),void(r.value=t);this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}function pe(e,t,n){var r=j(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function ge(e,t){return function(){return pe(this,e,t)}}function ye(e,t){return function(){return pe(this,e,t.apply(this,arguments))}}G.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var ve=[null];function xe(e,t){this._groups=e,this._parents=t}function we(){return new xe([[document.documentElement]],ve)}xe.prototype=we.prototype={constructor:xe,select:function(e){"function"!=typeof e&&(e=u(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i,a,s=t[o],c=s.length,l=r[o]=new Array(c),d=0;d<c;++d)(i=s[d])&&(a=e.call(i,i.__data__,d,s))&&("__data__"in i&&(a.__data__=i.__data__),l[d]=a);return new xe(r,this._parents)},selectAll:function(e){e="function"==typeof e?f(e):h(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var a,s=t[i],c=s.length,l=0;l<c;++l)(a=s[l])&&(r.push(e.call(a,a.__data__,l,s)),o.push(a));return new xe(r,o)},selectChild:function(e){return this.select(null==e?y:function(e){return function(){return g.call(this.children,e)}}("function"==typeof e?e:p(e)))},selectChildren:function(e){return this.selectAll(null==e?x:function(e){return function(){return v.call(this.children,e)}}("function"==typeof e?e:p(e)))},filter:function(e){"function"!=typeof e&&(e=m(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i,a=t[o],s=a.length,c=r[o]=[],l=0;l<s;++l)(i=a[l])&&e.call(i,i.__data__,l,a)&&c.push(i);return new xe(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,k);var n,r=t?_:E,o=this._parents,i=this._groups;"function"!=typeof e&&(n=e,e=function(){return n});for(var a=i.length,s=new Array(a),c=new Array(a),l=new Array(a),u=0;u<a;++u){var d=o[u],h=i[u],f=h.length,m=N(e.call(d,d&&d.__data__,u,o)),p=m.length,g=c[u]=new Array(p),y=s[u]=new Array(p);r(d,h,g,y,l[u]=new Array(f),m,t);for(var v,x,w=0,b=0;w<p;++w)if(v=g[w]){for(w>=b&&(b=w+1);!(x=y[b])&&++b<p;);v._next=x||null}}return(s=new xe(s,o))._enter=c,s._exit=l,s},enter:function(){return new xe(this._enter||this._groups.map(w),this._parents)},exit:function(){return new xe(this._exit||this._groups.map(w),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),s=new Array(o),c=0;c<a;++c)for(var l,u=n[c],d=r[c],h=u.length,f=s[c]=new Array(h),m=0;m<h;++m)(l=u[m]||d[m])&&(f[m]=l);for(;c<o;++c)s[c]=n[c];return new xe(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=S);for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var a,s=n[i],c=s.length,l=o[i]=new Array(c),u=0;u<c;++u)(a=s[u])&&(l[u]=a);l.sort(t)}return new xe(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,s=i.length;a<s;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=C(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?$:A:"function"==typeof t?n.local?P:T:n.local?z:I)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?L:"function"==typeof t?D:R)(e,t,null==n?"":n)):H(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?B:"function"==typeof t?V:F)(e,t)):this.node()[e]},classed:function(e,t){var n=Y(e+"");if(arguments.length<2){for(var r=X(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?K:t?U:Z)(n,t))},text:function(e){return arguments.length?this.each(null==e?J:("function"==typeof e?ee:Q)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?te:("function"==typeof e?re:ne)(e)):this.node().innerHTML},raise:function(){return this.each(oe)},lower:function(){return this.each(ie)},append:function(e){var t="function"==typeof e?e:ce(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:ce(e),r=null==t?le:"function"==typeof t?t:u(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(ue)},clone:function(e){return this.select(e?he:de)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=function(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?me:fe,r=0;r<a;++r)this.each(s(i[r],t,n));return this}var s=this.node().__on;if(s)for(var c,l=0,u=s.length;l<u;++l)for(r=0,c=s[l];r<a;++r)if((o=i[r]).type===c.type&&o.name===c.name)return c.value},dispatch:function(e,t){return this.each(("function"==typeof t?ye:ge)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};var be=we;function Ee(e){return"string"==typeof e?new xe([[document.querySelector(e)]],[document.documentElement]):new xe([[e]],ve)}function _e(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const ke={passive:!1},Ne={capture:!0,passive:!1};function Se(e){e.stopImmediatePropagation()}function Me(e){e.preventDefault(),e.stopImmediatePropagation()}function Oe(e){var t=e.document.documentElement,n=Ee(e).on("dragstart.drag",Me,Ne);"onselectstart"in t?n.on("selectstart.drag",Me,Ne):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Ce(e,t){var n=e.document.documentElement,r=Ee(e).on("dragstart.drag",null);t&&(r.on("click.drag",Me,Ne),setTimeout((function(){r.on("click.drag",null)}),0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var Ae=e=>()=>e;function $e(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:s,dx:c,dy:l,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:u}})}function Ie(e){return!e.ctrlKey&&!e.button}function ze(){return this.parentNode}function Te(e,t){return null==t?{x:e.x,y:e.y}:t}function Pe(){return navigator.maxTouchPoints||"ontouchstart"in this}function je(){var e,t,n,r,o=Ie,i=ze,a=Te,s=Pe,l={},u=c("start","drag","end"),d=0,h=0;function f(e){e.on("mousedown.drag",m).filter(s).on("touchstart.drag",y).on("touchmove.drag",v,ke).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function m(a,s){if(!r&&o.call(this,a,s)){var c=w(this,i.call(this,a,s),a,s,"mouse");c&&(Ee(a.view).on("mousemove.drag",p,Ne).on("mouseup.drag",g,Ne),Oe(a.view),Se(a),n=!1,e=a.clientX,t=a.clientY,c("start",a))}}function p(r){if(Me(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>h}l.mouse("drag",r)}function g(e){Ee(e.view).on("mousemove.drag mouseup.drag",null),Ce(e.view,n),Me(e),l.mouse("end",e)}function y(e,t){if(o.call(this,e,t)){var n,r,a=e.changedTouches,s=i.call(this,e,t),c=a.length;for(n=0;n<c;++n)(r=w(this,s,e,t,a[n].identifier,a[n]))&&(Se(e),r("start",e,a[n]))}}function v(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=l[r[t].identifier])&&(Me(e),n("drag",e,r[t]))}function x(e){var t,n,o=e.changedTouches,i=o.length;for(r&&clearTimeout(r),r=setTimeout((function(){r=null}),500),t=0;t<i;++t)(n=l[o[t].identifier])&&(Se(e),n("end",e,o[t]))}function w(e,t,n,r,o,i){var s,c,h,m=u.copy(),p=_e(i||n,t);if(null!=(h=a.call(e,new $e("beforestart",{sourceEvent:n,target:f,identifier:o,active:d,x:p[0],y:p[1],dx:0,dy:0,dispatch:m}),r)))return s=h.x-p[0]||0,c=h.y-p[1]||0,function n(i,a,u){var g,y=p;switch(i){case"start":l[o]=n,g=d++;break;case"end":delete l[o],--d;case"drag":p=_e(u||a,t),g=d}m.call(i,e,new $e(i,{sourceEvent:a,subject:h,target:f,identifier:o,active:g,x:p[0]+s,y:p[1]+c,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:m}),r)}}return f.filter=function(e){return arguments.length?(o="function"==typeof e?e:Ae(!!e),f):o},f.container=function(e){return arguments.length?(i="function"==typeof e?e:Ae(e),f):i},f.subject=function(e){return arguments.length?(a="function"==typeof e?e:Ae(e),f):a},f.touchable=function(e){return arguments.length?(s="function"==typeof e?e:Ae(!!e),f):s},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,f):Math.sqrt(h)},f}$e.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function Le(e){return((e=Math.exp(e))+1/e)/2}var Re,De,He=function e(t,n,r){function o(e,o){var i,a,s=e[0],c=e[1],l=e[2],u=o[0],d=o[1],h=o[2],f=u-s,m=d-c,p=f*f+m*m;if(p<1e-12)a=Math.log(h/l)/t,i=function(e){return[s+e*f,c+e*m,l*Math.exp(t*e*a)]};else{var g=Math.sqrt(p),y=(h*h-l*l+r*p)/(2*l*n*g),v=(h*h-l*l-r*p)/(2*h*n*g),x=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-x)/t,i=function(e){var r,o=e*a,i=Le(x),u=l/(n*g)*(i*(r=t*o+x,((r=Math.exp(2*r))-1)/(r+1))-function(e){return((e=Math.exp(e))-1/e)/2}(x));return[s+u*f,c+u*m,l*i/Le(t*o+x)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4),Be=0,Fe=0,Ve=0,Ye=0,Xe=0,Ge=0,qe="object"==typeof performance&&performance.now?performance:Date,We="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Ue(){return Xe||(We(Ze),Xe=qe.now()+Ge)}function Ze(){Xe=0}function Ke(){this._call=this._time=this._next=null}function Je(e,t,n){var r=new Ke;return r.restart(e,t,n),r}function Qe(){Xe=(Ye=qe.now())+Ge,Be=Fe=0;try{!function(){Ue(),++Be;for(var e,t=Re;t;)(e=Xe-t._time)>=0&&t._call.call(void 0,e),t=t._next;--Be}()}finally{Be=0,function(){var e,t,n=Re,r=1/0;for(;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:Re=t);De=e,tt(r)}(),Xe=0}}function et(){var e=qe.now(),t=e-Ye;t>1e3&&(Ge-=t,Ye=e)}function tt(e){Be||(Fe&&(Fe=clearTimeout(Fe)),e-Xe>24?(e<1/0&&(Fe=setTimeout(Qe,e-qe.now()-Ge)),Ve&&(Ve=clearInterval(Ve))):(Ve||(Ye=qe.now(),Ve=setInterval(et,1e3)),Be=1,We(Qe)))}function nt(e,t,n){var r=new Ke;return t=null==t?0:+t,r.restart((n=>{r.stop(),e(n+t)}),t,n),r}Ke.prototype=Je.prototype={constructor:Ke,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?Ue():+n)+(null==t?0:+t),this._next||De===this||(De?De._next=this:Re=this,De=this),this._call=e,this._time=n,tt()},stop:function(){this._call&&(this._call=null,this._time=1/0,tt())}};var rt=c("start","end","cancel","interrupt"),ot=[],it=2,at=5,st=6;function ct(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var r,o=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var l,u,d,h;if(1!==n.state)return c();for(l in o)if((h=o[l]).name===n.name){if(3===h.state)return nt(a);4===h.state?(h.state=st,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete o[l]):+l<t&&(h.state=st,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete o[l])}if(nt((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=it,n.on.call("start",e,e.__data__,n.index,n.group),n.state===it){for(n.state=3,r=new Array(d=n.tween.length),l=0,u=-1;l<d;++l)(h=n.tween[l].value.call(e,e.__data__,n.index,n.group))&&(r[++u]=h);r.length=u+1}}function s(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(c),n.state=at,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);n.state===at&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){for(var r in n.state=st,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=Je(i,0,n.time)}(e,n,{name:t,index:r,group:o,on:rt,tween:ot,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function lt(e,t){var n=dt(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function ut(e,t){var n=dt(e,t);if(n.state>3)throw new Error("too late; already running");return n}function dt(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function ht(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i)(n=i[o]).name===t?(r=n.state>it&&n.state<at,n.state=st,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]):a=!1;a&&delete e.__transition}}function ft(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var mt,pt=180/Math.PI,gt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function yt(e,t,n,r,o,i){var a,s,c;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*pt,skewX:Math.atan(c)*pt,scaleX:a,scaleY:s}}function vt(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],c=[];return i=e(i),a=e(a),function(e,r,o,i,a,s){if(e!==o||r!==i){var c=a.push("translate(",null,t,null,n);s.push({i:c-4,x:ft(e,o)},{i:c-2,x:ft(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,c),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(o(n)+"rotate(",null,r)-2,x:ft(e,t)})):t&&n.push(o(n)+"rotate("+t+r)}(i.rotate,a.rotate,s,c),function(e,t,n,i){e!==t?i.push({i:n.push(o(n)+"skewX(",null,r)-2,x:ft(e,t)}):t&&n.push(o(n)+"skewX("+t+r)}(i.skewX,a.skewX,s,c),function(e,t,n,r,i,a){if(e!==n||t!==r){var s=i.push(o(i)+"scale(",null,",",null,")");a.push({i:s-4,x:ft(e,n)},{i:s-2,x:ft(t,r)})}else 1===n&&1===r||i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,c),i=a=null,function(e){for(var t,n=-1,r=c.length;++n<r;)s[(t=c[n]).i]=t.x(e);return s.join("")}}}var xt=vt((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?gt:yt(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),wt=vt((function(e){return null==e?gt:(mt||(mt=document.createElementNS("http://www.w3.org/2000/svg","g")),mt.setAttribute("transform",e),(e=mt.transform.baseVal.consolidate())?yt((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):gt)}),", ",")",")");function bt(e,t){var n,r;return function(){var o=ut(this,e),i=o.tween;if(i!==n)for(var a=0,s=(r=n=i).length;a<s;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}o.tween=r}}function Et(e,t,n){var r,o;if("function"!=typeof n)throw new Error;return function(){var i=ut(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var s={name:t,value:n},c=0,l=o.length;c<l;++c)if(o[c].name===t){o[c]=s;break}c===l&&o.push(s)}i.tween=o}}function _t(e,t,n){var r=e._id;return e.each((function(){var e=ut(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return dt(e,r).value[t]}}function kt(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Nt(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function St(){}var Mt=.7,Ot=1/Mt,Ct="\\s*([+-]?\\d+)\\s*",At="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",$t="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",It=/^#([0-9a-f]{3,8})$/,zt=new RegExp(`^rgb\\(${Ct},${Ct},${Ct}\\)$`),Tt=new RegExp(`^rgb\\(${$t},${$t},${$t}\\)$`),Pt=new RegExp(`^rgba\\(${Ct},${Ct},${Ct},${At}\\)$`),jt=new RegExp(`^rgba\\(${$t},${$t},${$t},${At}\\)$`),Lt=new RegExp(`^hsl\\(${At},${$t},${$t}\\)$`),Rt=new RegExp(`^hsla\\(${At},${$t},${$t},${At}\\)$`),Dt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Ht(){return this.rgb().formatHex()}function Bt(){return this.rgb().formatRgb()}function Ft(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=It.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?Vt(t):3===n?new Gt(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?Yt(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?Yt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=zt.exec(e))?new Gt(t[1],t[2],t[3],1):(t=Tt.exec(e))?new Gt(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Pt.exec(e))?Yt(t[1],t[2],t[3],t[4]):(t=jt.exec(e))?Yt(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Lt.exec(e))?Jt(t[1],t[2]/100,t[3]/100,1):(t=Rt.exec(e))?Jt(t[1],t[2]/100,t[3]/100,t[4]):Dt.hasOwnProperty(e)?Vt(Dt[e]):"transparent"===e?new Gt(NaN,NaN,NaN,0):null}function Vt(e){return new Gt(e>>16&255,e>>8&255,255&e,1)}function Yt(e,t,n,r){return r<=0&&(e=t=n=NaN),new Gt(e,t,n,r)}function Xt(e,t,n,r){return 1===arguments.length?((o=e)instanceof St||(o=Ft(o)),o?new Gt((o=o.rgb()).r,o.g,o.b,o.opacity):new Gt):new Gt(e,t,n,null==r?1:r);var o}function Gt(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function qt(){return`#${Kt(this.r)}${Kt(this.g)}${Kt(this.b)}`}function Wt(){const e=Ut(this.opacity);return`${1===e?"rgb(":"rgba("}${Zt(this.r)}, ${Zt(this.g)}, ${Zt(this.b)}${1===e?")":`, ${e})`}`}function Ut(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Zt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Kt(e){return((e=Zt(e))<16?"0":"")+e.toString(16)}function Jt(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new en(e,t,n,r)}function Qt(e){if(e instanceof en)return new en(e.h,e.s,e.l,e.opacity);if(e instanceof St||(e=Ft(e)),!e)return new en;if(e instanceof en)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,s=i-o,c=(i+o)/2;return s?(a=t===i?(n-r)/s+6*(n<r):n===i?(r-t)/s+2:(t-n)/s+4,s/=c<.5?i+o:2-i-o,a*=60):s=c>0&&c<1?0:a,new en(a,s,c,e.opacity)}function en(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function tn(e){return(e=(e||0)%360)<0?e+360:e}function nn(e){return Math.max(0,Math.min(1,e||0))}function rn(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}function on(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}kt(St,Ft,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ht,formatHex:Ht,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Qt(this).formatHsl()},formatRgb:Bt,toString:Bt}),kt(Gt,Xt,Nt(St,{brighter(e){return e=null==e?Ot:Math.pow(Ot,e),new Gt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?Mt:Math.pow(Mt,e),new Gt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Gt(Zt(this.r),Zt(this.g),Zt(this.b),Ut(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:qt,formatHex:qt,formatHex8:function(){return`#${Kt(this.r)}${Kt(this.g)}${Kt(this.b)}${Kt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Wt,toString:Wt})),kt(en,(function(e,t,n,r){return 1===arguments.length?Qt(e):new en(e,t,n,null==r?1:r)}),Nt(St,{brighter(e){return e=null==e?Ot:Math.pow(Ot,e),new en(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?Mt:Math.pow(Mt,e),new en(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new Gt(rn(e>=240?e-240:e+120,o,r),rn(e,o,r),rn(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new en(tn(this.h),nn(this.s),nn(this.l),Ut(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ut(this.opacity);return`${1===e?"hsl(":"hsla("}${tn(this.h)}, ${100*nn(this.s)}%, ${100*nn(this.l)}%${1===e?")":`, ${e})`}`}}));var an=e=>()=>e;function sn(e,t){return function(n){return e+n*t}}function cn(e){return 1==(e=+e)?ln:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}(t,n,e):an(isNaN(t)?n:t)}}function ln(e,t){var n=t-e;return n?sn(e,n):an(isNaN(e)?t:e)}var un=function e(t){var n=cn(t);function r(e,t){var r=n((e=Xt(e)).r,(t=Xt(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=ln(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return r.gamma=e,r}(1);function dn(e){return function(t){var n,r,o=t.length,i=new Array(o),a=new Array(o),s=new Array(o);for(n=0;n<o;++n)r=Xt(t[n]),i[n]=r.r||0,a[n]=r.g||0,s[n]=r.b||0;return i=e(i),a=e(a),s=e(s),r.opacity=1,function(e){return r.r=i(e),r.g=a(e),r.b=s(e),r+""}}}dn((function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],i=e[r+1],a=r>0?e[r-1]:2*o-i,s=r<t-1?e[r+2]:2*i-o;return on((n-r/t)*t,a,o,i,s)}})),dn((function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],i=e[r%t],a=e[(r+1)%t],s=e[(r+2)%t];return on((n-r/t)*t,o,i,a,s)}}));var hn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,fn=new RegExp(hn.source,"g");function mn(e,t){var n,r,o,i=hn.lastIndex=fn.lastIndex=0,a=-1,s=[],c=[];for(e+="",t+="";(n=hn.exec(e))&&(r=fn.exec(t));)(o=r.index)>i&&(o=t.slice(i,o),s[a]?s[a]+=o:s[++a]=o),(n=n[0])===(r=r[0])?s[a]?s[a]+=r:s[++a]=r:(s[++a]=null,c.push({i:a,x:ft(n,r)})),i=fn.lastIndex;return i<t.length&&(o=t.slice(i),s[a]?s[a]+=o:s[++a]=o),s.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var n,r=0;r<t;++r)s[(n=c[r]).i]=n.x(e);return s.join("")})}function pn(e,t){var n;return("number"==typeof t?ft:t instanceof Ft?un:(n=Ft(t))?(t=n,un):mn)(e,t)}function gn(e){return function(){this.removeAttribute(e)}}function yn(e){return function(){this.removeAttributeNS(e.space,e.local)}}function vn(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}}function xn(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}function wn(e,t,n){var r,o,i;return function(){var a,s,c=n(this);if(null!=c)return(a=this.getAttribute(e))===(s=c+"")?null:a===r&&s===o?i:(o=s,i=t(r=a,c));this.removeAttribute(e)}}function bn(e,t,n){var r,o,i;return function(){var a,s,c=n(this);if(null!=c)return(a=this.getAttributeNS(e.space,e.local))===(s=c+"")?null:a===r&&s===o?i:(o=s,i=t(r=a,c));this.removeAttributeNS(e.space,e.local)}}function En(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}(e,o)),n}return o._value=t,o}function _n(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}(e,o)),n}return o._value=t,o}function kn(e,t){return function(){lt(this,e).delay=+t.apply(this,arguments)}}function Nn(e,t){return t=+t,function(){lt(this,e).delay=t}}function Sn(e,t){return function(){ut(this,e).duration=+t.apply(this,arguments)}}function Mn(e,t){return t=+t,function(){ut(this,e).duration=t}}var On=be.prototype.constructor;function Cn(e){return function(){this.style.removeProperty(e)}}var An=0;function $n(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function In(){return++An}var zn=be.prototype;$n.prototype=function(e){return be().transition(e)}.prototype={constructor:$n,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=u(e));for(var r=this._groups,o=r.length,i=new Array(o),a=0;a<o;++a)for(var s,c,l=r[a],d=l.length,h=i[a]=new Array(d),f=0;f<d;++f)(s=l[f])&&(c=e.call(s,s.__data__,f,l))&&("__data__"in s&&(c.__data__=s.__data__),h[f]=c,ct(h[f],t,n,f,h,dt(s,n)));return new $n(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=h(e));for(var r=this._groups,o=r.length,i=[],a=[],s=0;s<o;++s)for(var c,l=r[s],u=l.length,d=0;d<u;++d)if(c=l[d]){for(var f,m=e.call(c,c.__data__,d,l),p=dt(c,n),g=0,y=m.length;g<y;++g)(f=m[g])&&ct(f,t,n,g,m,p);i.push(m),a.push(c)}return new $n(i,a,t,n)},selectChild:zn.selectChild,selectChildren:zn.selectChildren,filter:function(e){"function"!=typeof e&&(e=m(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i,a=t[o],s=a.length,c=r[o]=[],l=0;l<s;++l)(i=a[l])&&e.call(i,i.__data__,l,a)&&c.push(i);return new $n(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=new Array(r),s=0;s<i;++s)for(var c,l=t[s],u=n[s],d=l.length,h=a[s]=new Array(d),f=0;f<d;++f)(c=l[f]||u[f])&&(h[f]=c);for(;s<r;++s)a[s]=t[s];return new $n(a,this._parents,this._name,this._id)},selection:function(){return new On(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=In(),r=this._groups,o=r.length,i=0;i<o;++i)for(var a,s=r[i],c=s.length,l=0;l<c;++l)if(a=s[l]){var u=dt(a,t);ct(a,e,n,l,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new $n(r,this._parents,e,n)},call:zn.call,nodes:zn.nodes,node:zn.node,size:zn.size,empty:zn.empty,each:zn.each,on:function(e,t){var n=this._id;return arguments.length<2?dt(this.node(),n).on.on(e):this.each(function(e,t,n){var r,o,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?lt:ut;return function(){var a=i(this,e),s=a.on;s!==r&&(o=(r=s).copy()).on(t,n),a.on=o}}(n,e,t))},attr:function(e,t){var n=C(e),r="transform"===n?wt:pn;return this.attrTween(e,"function"==typeof t?(n.local?bn:wn)(n,r,_t(this,"attr."+e,t)):null==t?(n.local?yn:gn)(n):(n.local?xn:vn)(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var r=C(e);return this.tween(n,(r.local?En:_n)(r,t))},style:function(e,t,n){var r="transform"==(e+="")?xt:pn;return null==t?this.styleTween(e,function(e,t){var n,r,o;return function(){var i=H(this,e),a=(this.style.removeProperty(e),H(this,e));return i===a?null:i===n&&a===r?o:o=t(n=i,r=a)}}(e,r)).on("end.style."+e,Cn(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var r,o,i;return function(){var a=H(this,e),s=n(this),c=s+"";return null==s&&(this.style.removeProperty(e),c=s=H(this,e)),a===c?null:a===r&&c===o?i:(o=c,i=t(r=a,s))}}(e,r,_t(this,"style."+e,t))).each(function(e,t){var n,r,o,i,a="style."+t,s="end."+a;return function(){var c=ut(this,e),l=c.on,u=null==c.value[a]?i||(i=Cn(t)):void 0;l===n&&o===u||(r=(n=l).copy()).on(s,o=u),c.on=r}}(this._id,e)):this.styleTween(e,function(e,t,n){var r,o,i=n+"";return function(){var a=H(this,e);return a===i?null:a===r?o:o=t(r=a,n)}}(e,r,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw new Error;return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}(e,i,n)),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(_t(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){return function(t){this.textContent=e.call(this,t)}}(r)),t}return r._value=e,r}(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=dt(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?bt:Et)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?kn:Nn)(t,e)):dt(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Sn:Mn)(t,e)):dt(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw new Error;return function(){ut(this,e).ease=t}}(t,e)):dt(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;ut(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise((function(i,a){var s={value:a},c={value:function(){0==--o&&i()}};n.each((function(){var n=ut(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),n.on=t})),0===o&&i()}))},[Symbol.iterator]:zn[Symbol.iterator]};var Tn={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function Pn(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}be.prototype.interrupt=function(e){return this.each((function(){ht(this,e)}))},be.prototype.transition=function(e){var t,n;e instanceof $n?(t=e._id,e=e._name):(t=In(),(n=Tn).time=Ue(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,s=r[i],c=s.length,l=0;l<c;++l)(a=s[l])&&ct(a,e,t,l,s,n||Pn(a,t));return new $n(r,this._parents,e,t)};var jn=e=>()=>e;function Ln(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function Rn(e,t,n){this.k=e,this.x=t,this.y=n}Rn.prototype={constructor:Rn,scale:function(e){return 1===e?this:new Rn(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new Rn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Dn=new Rn(1,0,0);function Hn(e){for(;!e.__zoom;)if(!(e=e.parentNode))return Dn;return e.__zoom}function Bn(e){e.stopImmediatePropagation()}function Fn(e){e.preventDefault(),e.stopImmediatePropagation()}function Vn(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function Yn(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function Xn(){return this.__zoom||Dn}function Gn(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function qn(){return navigator.maxTouchPoints||"ontouchstart"in this}function Wn(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function Un(){var e,t,n,r=Vn,o=Yn,i=Wn,a=Gn,s=qn,l=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],d=250,h=He,f=c("start","zoom","end"),m=0,p=10;function g(e){e.property("__zoom",Xn).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",N).filter(s).on("touchstart.zoom",S).on("touchmove.zoom",M).on("touchend.zoom touchcancel.zoom",O).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(l[0],Math.min(l[1],t)))===e.k?e:new Rn(t,e.x,e.y)}function v(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new Rn(e.k,r,o)}function x(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function w(e,t,n,r){e.on("start.zoom",(function(){b(this,arguments).event(r).start()})).on("interrupt.zoom end.zoom",(function(){b(this,arguments).event(r).end()})).tween("zoom",(function(){var e=this,i=arguments,a=b(e,i).event(r),s=o.apply(e,i),c=null==n?x(s):"function"==typeof n?n.apply(e,i):n,l=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,d="function"==typeof t?t.apply(e,i):t,f=h(u.invert(c).concat(l/u.k),d.invert(c).concat(l/d.k));return function(e){if(1===e)e=d;else{var t=f(e),n=l/t[2];e=new Rn(n,c[0]-t[0]*n,c[1]-t[1]*n)}a.zoom(null,e)}}))}function b(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=o.apply(e,t),this.taps=0}function _(e,...t){if(r.apply(this,arguments)){var n=b(this,t).event(e),o=this.__zoom,s=Math.max(l[0],Math.min(l[1],o.k*Math.pow(2,a.apply(this,arguments)))),c=_e(e);if(n.wheel)n.mouse[0][0]===c[0]&&n.mouse[0][1]===c[1]||(n.mouse[1]=o.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(o.k===s)return;n.mouse=[c,o.invert(c)],ht(this),n.start()}Fn(e),n.wheel=setTimeout((function(){n.wheel=null,n.end()}),150),n.zoom("mouse",i(v(y(o,s),n.mouse[0],n.mouse[1]),n.extent,u))}}function k(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=b(this,t,!0).event(e),s=Ee(e.view).on("mousemove.zoom",(function(e){if(Fn(e),!a.moved){var t=e.clientX-l,n=e.clientY-d;a.moved=t*t+n*n>m}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=_e(e,o),a.mouse[1]),a.extent,u))}),!0).on("mouseup.zoom",(function(e){s.on("mousemove.zoom mouseup.zoom",null),Ce(e.view,a.moved),Fn(e),a.event(e).end()}),!0),c=_e(e,o),l=e.clientX,d=e.clientY;Oe(e.view),Bn(e),a.mouse=[c,this.__zoom.invert(c)],ht(this),a.start()}}function N(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,a=_e(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(a),c=n.k*(e.shiftKey?.5:2),l=i(v(y(n,c),a,s),o.apply(this,t),u);Fn(e),d>0?Ee(this).transition().duration(d).call(w,l,a,e):Ee(this).call(g.transform,l,a,e)}}function S(n,...o){if(r.apply(this,arguments)){var i,a,s,c,l=n.touches,u=l.length,d=b(this,o,n.changedTouches.length===u).event(n);for(Bn(n),a=0;a<u;++a)c=[c=_e(s=l[a],this),this.__zoom.invert(c),s.identifier],d.touch0?d.touch1||d.touch0[2]===c[2]||(d.touch1=c,d.taps=0):(d.touch0=c,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=c[0],e=setTimeout((function(){e=null}),500)),ht(this),d.start())}}function M(e,...t){if(this.__zooming){var n,r,o,a,s=b(this,t).event(e),c=e.changedTouches,l=c.length;for(Fn(e),n=0;n<l;++n)o=_e(r=c[n],this),s.touch0&&s.touch0[2]===r.identifier?s.touch0[0]=o:s.touch1&&s.touch1[2]===r.identifier&&(s.touch1[0]=o);if(r=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],m=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,g=(g=m[0]-h[0])*g+(g=m[1]-h[1])*g;r=y(r,Math.sqrt(p/g)),o=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+m[0])/2,(h[1]+m[1])/2]}else{if(!s.touch0)return;o=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(v(r,o,a),s.extent,u))}}function O(e,...r){if(this.__zooming){var o,i,a=b(this,r).event(e),s=e.changedTouches,c=s.length;for(Bn(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),500),o=0;o<c;++o)i=s[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=_e(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var l=Ee(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return g.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",Xn),e!==o?w(e,t,n,r):o.interrupt().each((function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},g.scaleBy=function(e,t,n,r){g.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,r)},g.scaleTo=function(e,t,n,r){g.transform(e,(function(){var e=o.apply(this,arguments),r=this.__zoom,a=null==n?x(e):"function"==typeof n?n.apply(this,arguments):n,s=r.invert(a),c="function"==typeof t?t.apply(this,arguments):t;return i(v(y(r,c),a,s),e,u)}),n,r)},g.translateBy=function(e,t,n,r){g.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),o.apply(this,arguments),u)}),null,r)},g.translateTo=function(e,t,n,r,a){g.transform(e,(function(){var e=o.apply(this,arguments),a=this.__zoom,s=null==r?x(e):"function"==typeof r?r.apply(this,arguments):r;return i(Dn.translate(s[0],s[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),r,a)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=Ee(this.that).datum();f.call(e,this.that,new Ln(e,{sourceEvent:this.sourceEvent,target:g,type:e,transform:this.that.__zoom,dispatch:f}),t)}},g.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:jn(+e),g):a},g.filter=function(e){return arguments.length?(r="function"==typeof e?e:jn(!!e),g):r},g.touchable=function(e){return arguments.length?(s="function"==typeof e?e:jn(!!e),g):s},g.extent=function(e){return arguments.length?(o="function"==typeof e?e:jn([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),g):o},g.scaleExtent=function(e){return arguments.length?(l[0]=+e[0],l[1]=+e[1],g):[l[0],l[1]]},g.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],g):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},g.constrain=function(e){return arguments.length?(i=e,g):i},g.duration=function(e){return arguments.length?(d=+e,g):d},g.interpolate=function(e){return arguments.length?(h=e,g):h},g.on=function(){var e=f.on.apply(f,arguments);return e===f?g:e},g.clickDistance=function(e){return arguments.length?(m=(e=+e)*e,g):Math.sqrt(m)},g.tapDistance=function(e){return arguments.length?(p=+e,g):p},g}Hn.prototype=Rn.prototype;const Zn={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:r})=>`Couldn't create edge for ${e} handle id: "${"source"===e?n:r}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},Kn=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Jn=["Enter"," ","Escape"];var Qn,er,tr;!function(e){e.Strict="strict",e.Loose="loose"}(Qn||(Qn={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(er||(er={})),function(e){e.Partial="partial",e.Full="full"}(tr||(tr={}));const nr={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var rr,or,ir;!function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(rr||(rr={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(or||(or={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(ir||(ir={}));const ar={[ir.Left]:ir.Right,[ir.Right]:ir.Left,[ir.Top]:ir.Bottom,[ir.Bottom]:ir.Top};function sr(e){return null===e?null:e?"valid":"invalid"}const cr=e=>"id"in e&&"source"in e&&"target"in e,lr=e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e),ur=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),dr=(e,t=[0,0])=>{const{width:n,height:r}=Br(e),o=e.origin??t,i=n*o[0],a=r*o[1];return{x:e.position.x-i,y:e.position.y-a}},hr=(e,t={nodeOrigin:[0,0],nodeLookup:void 0})=>{if(0===e.length)return{x:0,y:0,width:0,height:0};const n=e.reduce(((e,n)=>{const r="string"==typeof n;let o=t.nodeLookup||r?void 0:n;t.nodeLookup&&(o=r?t.nodeLookup.get(n):ur(n)?n:t.nodeLookup.get(n.id));const i=o?Cr(o,t.nodeOrigin):{x:0,y:0,x2:0,y2:0};return Nr(e,i)}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Mr(n)},fr=(e,t={})=>{if(0===e.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach((e=>{if(void 0===t.filter||t.filter(e)){const t=Cr(e);n=Nr(n,t)}})),Mr(n)},mr=(e,t,[n,r,o]=[0,0,1],i=!1,a=!1)=>{const s={...jr(t,[n,r,o]),width:t.width/o,height:t.height/o},c=[];for(const l of e.values()){const{measured:e,selectable:t=!0,hidden:n=!1}=l;if(a&&!t||n)continue;const r=e.width??l.width??l.initialWidth??null,o=e.height??l.height??l.initialHeight??null,u=$r(s,Or(l)),d=(r??0)*(o??0),h=i&&u>0;(!l.internals.handleBounds||h||u>=d||l.dragging)&&c.push(l)}return c},pr=(e,t)=>{const n=new Set;return e.forEach((e=>{n.add(e.id)})),t.filter((e=>n.has(e.source)||n.has(e.target)))};function gr(e,t){const n=new Map,r=t?.nodes?new Set(t.nodes.map((e=>e.id))):null;return e.forEach((e=>{!(e.measured.width&&e.measured.height&&(t?.includeHiddenNodes||!e.hidden))||r&&!r.has(e.id)||n.set(e.id,e)})),n}async function yr({nodes:e,width:t,height:n,panZoom:r,minZoom:o,maxZoom:i},a){if(0===e.size)return Promise.resolve(!1);const s=fr(e),c=Rr(s,t,n,a?.minZoom??o,a?.maxZoom??i,a?.padding??.1);return await r.setViewport(c,{duration:a?.duration}),Promise.resolve(!0)}function vr({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:r=[0,0],nodeExtent:o,onError:i}){const a=n.get(e),s=a.parentId?n.get(a.parentId):void 0,{x:c,y:l}=s?s.internals.positionAbsolute:{x:0,y:0},u=a.origin??r;let d=o;if("parent"!==a.extent||a.expandParent)s&&Hr(a.extent)&&(d=[[a.extent[0][0]+c,a.extent[0][1]+l],[a.extent[1][0]+c,a.extent[1][1]+l]]);else if(s){const e=s.measured.width,t=s.measured.height;e&&t&&(d=[[c,l],[c+e,l+t]])}else i?.("005",Zn.error005());const h=Hr(d)?br(t,d,a.measured):t;return void 0!==a.measured.width&&void 0!==a.measured.height||i?.("015",Zn.error015()),{position:{x:h.x-c+(a.measured.width??0)*u[0],y:h.y-l+(a.measured.height??0)*u[1]},positionAbsolute:h}}async function xr({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:r,onBeforeDelete:o}){const i=new Set(e.map((e=>e.id))),a=[];for(const d of n){if(!1===d.deletable)continue;const e=i.has(d.id),t=!e&&d.parentId&&a.find((e=>e.id===d.parentId));(e||t)&&a.push(d)}const s=new Set(t.map((e=>e.id))),c=r.filter((e=>!1!==e.deletable)),l=pr(a,c);for(const d of c){s.has(d.id)&&!l.find((e=>e.id===d.id))&&l.push(d)}if(!o)return{edges:l,nodes:a};const u=await o({nodes:a,edges:l});return"boolean"==typeof u?u?{edges:l,nodes:a}:{edges:[],nodes:[]}:u}const wr=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),br=(e={x:0,y:0},t,n)=>({x:wr(e.x,t[0][0],t[1][0]-(n?.width??0)),y:wr(e.y,t[0][1],t[1][1]-(n?.height??0))});function Er(e,t,n){const{width:r,height:o}=Br(n),{x:i,y:a}=n.internals.positionAbsolute;return br(e,[[i,a],[i+r,a+o]],t)}const _r=(e,t,n)=>e<t?wr(Math.abs(e-t),1,t)/t:e>n?-wr(Math.abs(e-n),1,t)/t:0,kr=(e,t,n=15,r=40)=>[_r(e.x,r,t.width-r)*n,_r(e.y,r,t.height-r)*n],Nr=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Sr=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),Mr=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),Or=(e,t=[0,0])=>{const{x:n,y:r}=ur(e)?e.internals.positionAbsolute:dr(e,t);return{x:n,y:r,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},Cr=(e,t=[0,0])=>{const{x:n,y:r}=ur(e)?e.internals.positionAbsolute:dr(e,t);return{x:n,y:r,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:r+(e.measured?.height??e.height??e.initialHeight??0)}},Ar=(e,t)=>Mr(Nr(Sr(e),Sr(t))),$r=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),r=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*r)},Ir=e=>zr(e.width)&&zr(e.height)&&zr(e.x)&&zr(e.y),zr=e=>!isNaN(e)&&isFinite(e),Tr=(e,t)=>{0},Pr=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),jr=({x:e,y:t},[n,r,o],i=!1,a=[1,1])=>{const s={x:(e-n)/o,y:(t-r)/o};return i?Pr(s,a):s},Lr=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),Rr=(e,t,n,r,o,i)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),c=Math.min(a,s),l=wr(c,r,o);return{x:t/2-(e.x+e.width/2)*l,y:n/2-(e.y+e.height/2)*l,zoom:l}},Dr=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function Hr(e){return void 0!==e&&"parent"!==e}function Br(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function Fr(e){return void 0!==(e.measured?.width??e.width??e.initialWidth)&&void 0!==(e.measured?.height??e.height??e.initialHeight)}function Vr(e,t={width:0,height:0},n,r,o){const i={...e},a=r.get(n);if(a){const e=a.origin||o;i.x+=a.internals.positionAbsolute.x-(t.width??0)*e[0],i.y+=a.internals.positionAbsolute.y-(t.height??0)*e[1]}return i}function Yr(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function Xr(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:r,containerBounds:o}){const{x:i,y:a}=Kr(e),s=jr({x:i-(o?.left??0),y:a-(o?.top??0)},r),{x:c,y:l}=n?Pr(s,t):s;return{xSnapped:c,ySnapped:l,...s}}const Gr=e=>({width:e.offsetWidth,height:e.offsetHeight}),qr=e=>e?.getRootNode?.()||window?.document,Wr=["INPUT","SELECT","TEXTAREA"];function Ur(e){const t=e.composedPath?.()?.[0]||e.target;if(1!==t?.nodeType)return!1;return Wr.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey")}const Zr=e=>"clientX"in e,Kr=(e,t)=>{const n=Zr(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},Jr=(e,t,n,r,o)=>{const i=t.querySelectorAll(`.${e}`);return i&&i.length?Array.from(i).map((t=>{const i=t.getBoundingClientRect();return{id:t.getAttribute("data-handleid"),type:e,nodeId:o,position:t.getAttribute("data-handlepos"),x:(i.left-n.left)/r,y:(i.top-n.top)/r,...Gr(t)}})):null};function Qr({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:a,targetControlY:s}){const c=.125*e+.375*o+.375*a+.125*n,l=.125*t+.375*i+.375*s+.125*r;return[c,l,Math.abs(c-e),Math.abs(l-t)]}function eo(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function to({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case ir.Left:return[t-eo(t-r,i),n];case ir.Right:return[t+eo(r-t,i),n];case ir.Top:return[t,n-eo(n-o,i)];case ir.Bottom:return[t,n+eo(o-n,i)]}}function no({sourceX:e,sourceY:t,sourcePosition:n=ir.Bottom,targetX:r,targetY:o,targetPosition:i=ir.Top,curvature:a=.25}){const[s,c]=to({pos:n,x1:e,y1:t,x2:r,y2:o,c:a}),[l,u]=to({pos:i,x1:r,y1:o,x2:e,y2:t,c:a}),[d,h,f,m]=Qr({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:s,sourceControlY:c,targetControlX:l,targetControlY:u});return[`M${e},${t} C${s},${c} ${l},${u} ${r},${o}`,d,h,f,m]}function ro({sourceX:e,sourceY:t,targetX:n,targetY:r}){const o=Math.abs(n-e)/2,i=n<e?n+o:n-o,a=Math.abs(r-t)/2;return[i,r<t?r+a:r-a,o,a]}function oo({sourceNode:e,targetNode:t,selected:n=!1,zIndex:r=0,elevateOnSelect:o=!1}){if(!o)return r;const i=n||t.selected||e.selected,a=Math.max(e.internals.z||0,t.internals.z||0,1e3);return r+(i?a:0)}function io({sourceNode:e,targetNode:t,width:n,height:r,transform:o}){const i=Nr(Cr(e),Cr(t));i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1);const a={x:-o[0]/o[2],y:-o[1]/o[2],width:n/o[2],height:r/o[2]};return $r(a,Mr(i))>0}const ao=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`xy-edge__${e}${t||""}-${n}${r||""}`,so=(e,t)=>{if(!e.source||!e.target)return Tr("006",Zn.error006()),t;let n;return n=cr(e)?{...e}:{...e,id:ao(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:(null===n.sourceHandle&&delete n.sourceHandle,null===n.targetHandle&&delete n.targetHandle,t.concat(n))};function co({sourceX:e,sourceY:t,targetX:n,targetY:r}){const[o,i,a,s]=ro({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,a,s]}const lo={[ir.Left]:{x:-1,y:0},[ir.Right]:{x:1,y:0},[ir.Top]:{x:0,y:-1},[ir.Bottom]:{x:0,y:1}},uo=({source:e,sourcePosition:t=ir.Bottom,target:n})=>t===ir.Left||t===ir.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},ho=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function fo({sourceX:e,sourceY:t,sourcePosition:n=ir.Bottom,targetX:r,targetY:o,targetPosition:i=ir.Top,borderRadius:a=5,centerX:s,centerY:c,offset:l=20}){const[u,d,h,f,m]=function({source:e,sourcePosition:t=ir.Bottom,target:n,targetPosition:r=ir.Top,center:o,offset:i}){const a=lo[t],s=lo[r],c={x:e.x+a.x*i,y:e.y+a.y*i},l={x:n.x+s.x*i,y:n.y+s.y*i},u=uo({source:c,sourcePosition:t,target:l}),d=0!==u.x?"x":"y",h=u[d];let f,m,p=[];const g={x:0,y:0},y={x:0,y:0},[v,x,w,b]=ro({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]==-1){f=o.x??v,m=o.y??x;const e=[{x:f,y:c.y},{x:f,y:l.y}],t=[{x:c.x,y:m},{x:l.x,y:m}];p=a[d]===h?"x"===d?e:t:"x"===d?t:e}else{const o=[{x:c.x,y:l.y}],u=[{x:l.x,y:c.y}];if(p="x"===d?a.x===h?u:o:a.y===h?o:u,t===r){const t=Math.abs(e[d]-n[d]);if(t<=i){const r=Math.min(i-1,i-t);a[d]===h?g[d]=(c[d]>e[d]?-1:1)*r:y[d]=(l[d]>n[d]?-1:1)*r}}if(t!==r){const e="x"===d?"y":"x",t=a[d]===s[e],n=c[e]>l[e],r=c[e]<l[e];(1===a[d]&&(!t&&n||t&&r)||1!==a[d]&&(!t&&r||t&&n))&&(p="x"===d?o:u)}const v={x:c.x+g.x,y:c.y+g.y},x={x:l.x+y.x,y:l.y+y.y};Math.max(Math.abs(v.x-p[0].x),Math.abs(x.x-p[0].x))>=Math.max(Math.abs(v.y-p[0].y),Math.abs(x.y-p[0].y))?(f=(v.x+x.x)/2,m=p[0].y):(f=p[0].x,m=(v.y+x.y)/2)}return[[e,{x:c.x+g.x,y:c.y+g.y},...p,{x:l.x+y.x,y:l.y+y.y},n],f,m,w,b]}({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:s,y:c},offset:l}),p=u.reduce(((e,t,n)=>{let r="";return r=n>0&&n<u.length-1?function(e,t,n,r){const o=Math.min(ho(e,t)/2,ho(t,n)/2,r),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+o*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+o*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+o*(e.y<n.y?-1:1)}Q ${i},${a} ${i+o*s},${a}`}(u[n-1],t,u[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=r}),"");return[p,d,h,f,m]}function mo(e){return e&&!(!e.internals.handleBounds&&!e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function po(e){const{sourceNode:t,targetNode:n}=e;if(!mo(t)||!mo(n))return null;const r=t.internals.handleBounds||go(t.handles),o=n.internals.handleBounds||go(n.handles),i=vo(r?.source??[],e.sourceHandle),a=vo(e.connectionMode===Qn.Strict?o?.target??[]:(o?.target??[]).concat(o?.source??[]),e.targetHandle);if(!i||!a)return e.onError?.("008",Zn.error008(i?"target":"source",{id:e.id,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle})),null;const s=i?.position||ir.Bottom,c=a?.position||ir.Top,l=yo(t,i,s),u=yo(n,a,c);return{sourceX:l.x,sourceY:l.y,targetX:u.x,targetY:u.y,sourcePosition:s,targetPosition:c}}function go(e){if(!e)return null;const t=[],n=[];for(const r of e)r.width=r.width??1,r.height=r.height??1,"source"===r.type?t.push(r):"target"===r.type&&n.push(r);return{source:t,target:n}}function yo(e,t,n=ir.Left,r=!1){const o=(t?.x??0)+e.internals.positionAbsolute.x,i=(t?.y??0)+e.internals.positionAbsolute.y,{width:a,height:s}=t??Br(e);if(r)return{x:o+a/2,y:i+s/2};switch(t?.position??n){case ir.Top:return{x:o+a/2,y:i};case ir.Right:return{x:o+a,y:i+s/2};case ir.Bottom:return{x:o+a/2,y:i+s};case ir.Left:return{x:o,y:i+s/2}}}function vo(e,t){return e&&(t?e.find((e=>e.id===t)):e[0])||null}function xo(e,t){if(!e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`}function wo(e,{id:t,defaultColor:n,defaultMarkerStart:r,defaultMarkerEnd:o}){const i=new Set;return e.reduce(((e,a)=>([a.markerStart||r,a.markerEnd||o].forEach((r=>{if(r&&"object"==typeof r){const o=xo(r,t);i.has(o)||(e.push({id:o,color:r.color||n,...r}),i.add(o))}})),e)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))}const bo={nodeOrigin:[0,0],nodeExtent:Kn,elevateNodesOnSelect:!0,defaults:{}},Eo={...bo,checkEquality:!0};function _o(e,t){const n={...e};for(const r in t)void 0!==t[r]&&(n[r]=t[r]);return n}function ko(e,t,n){const r=_o(bo,n);for(const o of e.values())if(o.parentId)So(o,e,t,r);else{const e=dr(o,r.nodeOrigin),t=Hr(o.extent)?o.extent:r.nodeExtent,n=br(e,t,Br(o));o.internals.positionAbsolute=n}}function No(e,t,n,r){const o=_o(Eo,r),i=new Map(t),a=o?.elevateNodesOnSelect?1e3:0;t.clear(),n.clear();for(const s of e){let e=i.get(s.id);if(o.checkEquality&&s===e?.internals.userNode)t.set(s.id,e);else{const n=dr(s,o.nodeOrigin),r=Hr(s.extent)?s.extent:o.nodeExtent,i=br(n,r,Br(s));e={...o.defaults,...s,measured:{width:s.measured?.width,height:s.measured?.height},internals:{positionAbsolute:i,handleBounds:s.measured?e?.internals.handleBounds:void 0,z:Mo(s,a),userNode:s}},t.set(s.id,e)}s.parentId&&So(e,t,n,r)}}function So(e,t,n,r){const{elevateNodesOnSelect:o,nodeOrigin:i,nodeExtent:a}=_o(bo,r),s=e.parentId,c=t.get(s);if(!c)return void console.warn(`Parent node ${s} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(e,t){if(!e.parentId)return;const n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}(e,n);const l=o?1e3:0,{x:u,y:d,z:h}=function(e,t,n,r,o){const{x:i,y:a}=t.internals.positionAbsolute,s=Br(e),c=dr(e,n),l=Hr(e.extent)?br(c,e.extent,s):c;let u=br({x:i+l.x,y:a+l.y},r,s);"parent"===e.extent&&(u=Er(u,s,t));const d=Mo(e,o),h=t.internals.z??0;return{x:u.x,y:u.y,z:h>d?h:d}}(e,c,i,a,l),{positionAbsolute:f}=e.internals,m=u!==f.x||d!==f.y;(m||h!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:m?{x:u,y:d}:f,z:h}})}function Mo(e,t){return(zr(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function Oo(e,t,n,r=[0,0]){const o=[],i=new Map;for(const a of e){const e=t.get(a.parentId);if(!e)continue;const n=i.get(a.parentId)?.expandedRect??Or(e),r=Ar(n,a.rect);i.set(a.parentId,{expandedRect:r,parent:e})}return i.size>0&&i.forEach((({expandedRect:t,parent:i},a)=>{const s=i.internals.positionAbsolute,c=Br(i),l=i.origin??r,u=t.x<s.x?Math.round(Math.abs(s.x-t.x)):0,d=t.y<s.y?Math.round(Math.abs(s.y-t.y)):0,h=Math.max(c.width,Math.round(t.width)),f=Math.max(c.height,Math.round(t.height)),m=(h-c.width)*l[0],p=(f-c.height)*l[1];(u>0||d>0||m||p)&&(o.push({id:a,type:"position",position:{x:i.position.x-u+m,y:i.position.y-d+p}}),n.get(a)?.forEach((t=>{e.some((e=>e.id===t.id))||o.push({id:t.id,type:"position",position:{x:t.position.x+u,y:t.position.y+d}})}))),(c.width<t.width||c.height<t.height||u||d)&&o.push({id:a,type:"dimensions",setAttributes:!0,dimensions:{width:h+(u?l[0]*u-m:0),height:f+(d?l[1]*d-p:0)}})})),o}function Co(e,t,n,r,o,i){const a=r?.querySelector(".xyflow__viewport");let s=!1;if(!a)return{changes:[],updatedInternals:s};const c=[],l=window.getComputedStyle(a),{m22:u}=new window.DOMMatrixReadOnly(l.transform),d=[];for(const h of e.values()){const e=t.get(h.id);if(!e)continue;if(e.hidden){t.set(e.id,{...e,internals:{...e.internals,handleBounds:void 0}}),s=!0;continue}const r=Gr(h.nodeElement),a=e.measured.width!==r.width||e.measured.height!==r.height;if(!(!r.width||!r.height||!a&&e.internals.handleBounds&&!h.force)){const l=h.nodeElement.getBoundingClientRect(),f=Hr(e.extent)?e.extent:i;let{positionAbsolute:m}=e.internals;e.parentId&&"parent"===e.extent?m=Er(m,r,t.get(e.parentId)):f&&(m=br(m,f,r));const p={...e,measured:r,internals:{...e.internals,positionAbsolute:m,handleBounds:{source:Jr("source",h.nodeElement,l,u,e.id),target:Jr("target",h.nodeElement,l,u,e.id)}}};t.set(e.id,p),e.parentId&&So(p,t,n,{nodeOrigin:o}),s=!0,a&&(c.push({id:e.id,type:"dimensions",dimensions:r}),e.expandParent&&e.parentId&&d.push({id:e.id,parentId:e.parentId,rect:Or(p,o)}))}}if(d.length>0){const e=Oo(d,t,n,o);c.push(...e)}return{changes:c,updatedInternals:s}}async function Ao({delta:e,panZoom:t,transform:n,translateExtent:r,width:o,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);const a=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[o,i]],r),s=!!a&&(a.x!==n[0]||a.y!==n[1]||a.k!==n[2]);return Promise.resolve(s)}function $o(e,t,n,r,o,i){let a=o;const s=r.get(a)||new Map;r.set(a,s.set(n,t)),a=`${o}-${e}`;const c=r.get(a)||new Map;if(r.set(a,c.set(n,t)),i){a=`${o}-${e}-${i}`;const s=r.get(a)||new Map;r.set(a,s.set(n,t))}}function Io(e,t,n){e.clear(),t.clear();for(const r of n){const{source:n,target:o,sourceHandle:i=null,targetHandle:a=null}=r,s={edgeId:r.id,source:n,target:o,sourceHandle:i,targetHandle:a},c=`${n}-${i}--${o}-${a}`;$o("source",s,`${o}-${a}--${n}-${i}`,e,n,i),$o("target",s,c,e,o,a),t.set(r.id,r)}}function zo(e,t){if(!e.parentId)return!1;const n=t.get(e.parentId);return!!n&&(!!n.selected||zo(n,t))}function To(e,t,n){let r=e;do{if(r?.matches?.(t))return!0;if(r===n)return!1;r=r?.parentElement}while(r);return!1}function Po({nodeId:e,dragItems:t,nodeLookup:n,dragging:r=!0}){const o=[];for(const[a,s]of t){const e=n.get(a)?.internals.userNode;e&&o.push({...e,position:s.position,dragging:r})}if(!e)return[o[0],o];const i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:r}:o[0],o]}function jo({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:r,onDragStop:o}){let i={x:null,y:null},a=0,s=new Map,c=!1,l={x:0,y:0},u=null,d=!1,h=null,f=!1;return{update:function({noDragClassName:m,handleSelector:p,domNode:g,isSelectable:y,nodeId:v,nodeClickDistance:x=0}){function w({x:e,y:n},o){const{nodeLookup:a,nodeExtent:c,snapGrid:l,snapToGrid:u,nodeOrigin:d,onNodeDrag:h,onSelectionDrag:f,onError:m,updateNodePositions:p}=t();i={x:e,y:n};let g=!1,y={x:0,y:0,x2:0,y2:0};if(s.size>1&&c){const e=fr(s);y=Sr(e)}for(const[t,r]of s){if(!a.has(t))continue;let o={x:e-r.distance.x,y:n-r.distance.y};u&&(o=Pr(o,l));let i=[[c[0][0],c[0][1]],[c[1][0],c[1][1]]];if(s.size>1&&c&&!r.extent){const{positionAbsolute:e}=r.internals,t=e.x-y.x+c[0][0],n=e.x+r.measured.width-y.x2+c[1][0];i=[[t,e.y-y.y+c[0][1]],[n,e.y+r.measured.height-y.y2+c[1][1]]]}const{position:h,positionAbsolute:f}=vr({nodeId:t,nextPosition:o,nodeLookup:a,nodeExtent:i,nodeOrigin:d,onError:m});g=g||r.position.x!==h.x||r.position.y!==h.y,r.position=h,r.internals.positionAbsolute=f}if(g&&(p(s,!0),o&&(r||h||!v&&f))){const[e,t]=Po({nodeId:v,dragItems:s,nodeLookup:a});r?.(o,s,e,t),h?.(o,e,t),v||f?.(o,t)}}async function b(){if(!u)return;const{transform:e,panBy:n,autoPanSpeed:r,autoPanOnNodeDrag:o}=t();if(!o)return c=!1,void cancelAnimationFrame(a);const[s,d]=kr(l,u,r);0===s&&0===d||(i.x=(i.x??0)-s/e[2],i.y=(i.y??0)-d/e[2],await n({x:s,y:d})&&w(i,null)),a=requestAnimationFrame(b)}function E(r){const{nodeLookup:o,multiSelectionActive:a,nodesDraggable:c,transform:l,snapGrid:h,snapToGrid:f,selectNodesOnDrag:m,onNodeDragStart:p,onSelectionDragStart:g,unselectNodesAndEdges:x}=t();d=!0,m&&y||a||!v||o.get(v)?.selected||x(),y&&m&&v&&e?.(v);const w=Xr(r.sourceEvent,{transform:l,snapGrid:h,snapToGrid:f,containerBounds:u});if(i=w,s=function(e,t,n,r){const o=new Map;for(const[i,a]of e)if((a.selected||a.id===r)&&(!a.parentId||!zo(a,e))&&(a.draggable||t&&void 0===a.draggable)){const t=e.get(i);t&&o.set(i,{id:i,position:t.position||{x:0,y:0},distance:{x:n.x-t.internals.positionAbsolute.x,y:n.y-t.internals.positionAbsolute.y},extent:t.extent,parentId:t.parentId,origin:t.origin,expandParent:t.expandParent,internals:{positionAbsolute:t.internals.positionAbsolute||{x:0,y:0}},measured:{width:t.measured.width??0,height:t.measured.height??0}})}return o}(o,c,w,v),s.size>0&&(n||p||!v&&g)){const[e,t]=Po({nodeId:v,dragItems:s,nodeLookup:o});n?.(r.sourceEvent,s,e,t),p?.(r.sourceEvent,e,t),v||g?.(r.sourceEvent,t)}}h=Ee(g);const _=je().clickDistance(x).on("start",(e=>{const{domNode:n,nodeDragThreshold:r,transform:o,snapGrid:a,snapToGrid:s}=t();u=n?.getBoundingClientRect()||null,f=!1,0===r&&E(e);const c=Xr(e.sourceEvent,{transform:o,snapGrid:a,snapToGrid:s,containerBounds:u});i=c,l=Kr(e.sourceEvent,u)})).on("drag",(e=>{const{autoPanOnNodeDrag:n,transform:r,snapGrid:o,snapToGrid:a,nodeDragThreshold:h,nodeLookup:m}=t(),p=Xr(e.sourceEvent,{transform:r,snapGrid:o,snapToGrid:a,containerBounds:u});if(("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1||v&&!m.has(v))&&(f=!0),!f){if(!c&&n&&d&&(c=!0,b()),!d){const t=p.xSnapped-(i.x??0),n=p.ySnapped-(i.y??0);Math.sqrt(t*t+n*n)>h&&E(e)}(i.x!==p.xSnapped||i.y!==p.ySnapped)&&s&&d&&(l=Kr(e.sourceEvent,u),w(p,e.sourceEvent))}})).on("end",(e=>{if(d&&!f&&(c=!1,d=!1,cancelAnimationFrame(a),s.size>0)){const{nodeLookup:n,updateNodePositions:r,onNodeDragStop:i,onSelectionDragStop:a}=t();if(r(s,!1),o||i||!v&&a){const[t,r]=Po({nodeId:v,dragItems:s,nodeLookup:n,dragging:!1});o?.(e.sourceEvent,s,t,r),i?.(e.sourceEvent,t,r),v||a?.(e.sourceEvent,r)}}})).filter((e=>{const t=e.target;return!e.button&&(!m||!To(t,`.${m}`,g))&&(!p||To(t,p,g))}));h.call(_)},destroy:function(){h?.on(".drag",null)}}}function Lo(e,t,n,r){let o=[],i=1/0;const a=function(e,t,n){const r=[],o={x:e.x-n,y:e.y-n,width:2*n,height:2*n};for(const i of t.values())$r(o,Or(i))>0&&r.push(i);return r}(e,n,t+250);for(const s of a){const n=[...s.internals.handleBounds?.source??[],...s.internals.handleBounds?.target??[]];for(const a of n){if(r.nodeId===a.nodeId&&r.type===a.type&&r.id===a.id)continue;const{x:n,y:c}=yo(s,a,a.position,!0),l=Math.sqrt(Math.pow(n-e.x,2)+Math.pow(c-e.y,2));l>t||(l<i?(o=[{...a,x:n,y:c}],i=l):l===i&&o.push({...a,x:n,y:c}))}}if(!o.length)return null;if(o.length>1){const e="source"===r.type?"target":"source";return o.find((t=>t.type===e))??o[0]}return o[0]}function Ro(e,t,n,r,o,i=!1){const a=r.get(e);if(!a)return null;const s="strict"===o?a.internals.handleBounds?.[t]:[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]],c=(n?s?.find((e=>e.id===n)):s?.[0])??null;return c&&i?{...c,...yo(a,c,c.position,!0)}:c}function Do(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}const Ho=()=>!0;function Bo(e,{handle:t,connectionMode:n,fromNodeId:r,fromHandleId:o,fromType:i,doc:a,lib:s,flowId:c,isValidConnection:l=Ho,nodeLookup:u}){const d="target"===i,h=t?a.querySelector(`.${s}-flow__handle[data-id="${c}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:f,y:m}=Kr(e),p=a.elementFromPoint(f,m),g=p?.classList.contains(`${s}-flow__handle`)?p:h,y={handleDomNode:g,isValid:!1,connection:null,toHandle:null};if(g){const e=Do(void 0,g),t=g.getAttribute("data-nodeid"),i=g.getAttribute("data-handleid"),a=g.classList.contains("connectable"),s=g.classList.contains("connectableend");if(!t||!e)return y;const c={source:d?t:r,sourceHandle:d?i:o,target:d?r:t,targetHandle:d?o:i};y.connection=c;const h=a&&s&&(n===Qn.Strict?d&&"source"===e||!d&&"target"===e:t!==r||i!==o);y.isValid=h&&l(c),y.toHandle=Ro(t,e,i,u,n,!1)}return y}const Fo={onPointerDown:function(e,{connectionMode:t,connectionRadius:n,handleId:r,nodeId:o,edgeUpdaterType:i,isTarget:a,domNode:s,nodeLookup:c,lib:l,autoPanOnConnect:u,flowId:d,panBy:h,cancelConnection:f,onConnectStart:m,onConnect:p,onConnectEnd:g,isValidConnection:y=Ho,onReconnectEnd:v,updateConnection:x,getTransform:w,getFromHandle:b,autoPanSpeed:E}){const _=qr(e.target);let k,N=0;const{x:S,y:M}=Kr(e),O=_?.elementFromPoint(S,M),C=Do(i,O),A=s?.getBoundingClientRect();if(!A||!C)return;const $=Ro(o,C,r,c,t);if(!$)return;let I=Kr(e,A),z=!1,T=null,P=!1,j=null;function L(){if(!u||!A)return;const[e,t]=kr(I,A,E);h({x:e,y:t}),N=requestAnimationFrame(L)}const R={...$,nodeId:o,type:C,position:$.position},D=c.get(o),H={inProgress:!0,isValid:null,from:yo(D,R,ir.Left,!0),fromHandle:R,fromPosition:R.position,fromNode:D,to:I,toHandle:null,toPosition:ar[R.position],toNode:null};x(H);let B=H;function F(e){if(!b()||!R)return void V(e);const i=w();I=Kr(e,A),k=Lo(jr(I,i,!1,[1,1]),n,c,R),z||(L(),z=!0);const s=Bo(e,{handle:k,connectionMode:t,fromNodeId:o,fromHandleId:r,fromType:a?"target":"source",isValidConnection:y,doc:_,lib:l,flowId:d,nodeLookup:c});j=s.handleDomNode,T=s.connection,P=function(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}(!!k,s.isValid);const u={...B,isValid:P,to:k&&P?Lr({x:k.x,y:k.y},i):I,toHandle:s.toHandle,toPosition:P&&s.toHandle?s.toHandle.position:ar[R.position],toNode:s.toHandle?c.get(s.toHandle.nodeId):null};P&&k&&B.toHandle&&u.toHandle&&B.toHandle.type===u.toHandle.type&&B.toHandle.nodeId===u.toHandle.nodeId&&B.toHandle.id===u.toHandle.id&&B.to.x===u.to.x&&B.to.y===u.to.y||(x(u),B=u)}function V(e){(k||j)&&T&&P&&p?.(T);const{inProgress:t,...n}=B,r={...n,toPosition:B.toHandle?B.toPosition:null};g?.(e,r),i&&v?.(e,r),f(),cancelAnimationFrame(N),z=!1,P=!1,T=null,j=null,_.removeEventListener("mousemove",F),_.removeEventListener("mouseup",V),_.removeEventListener("touchmove",F),_.removeEventListener("touchend",V)}m?.(e,{nodeId:o,handleId:r,handleType:C}),_.addEventListener("mousemove",F),_.addEventListener("mouseup",V),_.addEventListener("touchmove",F),_.addEventListener("touchend",V)},isValid:Bo};function Vo({domNode:e,panZoom:t,getTransform:n,getViewScale:r}){const o=Ee(e);return{update:function({translateExtent:e,width:i,height:a,zoomStep:s=10,pannable:c=!0,zoomable:l=!0,inversePan:u=!1}){let d=[0,0];const h=Un().on("start",(e=>{"mousedown"!==e.sourceEvent.type&&"touchstart"!==e.sourceEvent.type||(d=[e.sourceEvent.clientX??e.sourceEvent.touches[0].clientX,e.sourceEvent.clientY??e.sourceEvent.touches[0].clientY])})).on("zoom",c?o=>{const s=n();if("mousemove"!==o.sourceEvent.type&&"touchmove"!==o.sourceEvent.type||!t)return;const c=[o.sourceEvent.clientX??o.sourceEvent.touches[0].clientX,o.sourceEvent.clientY??o.sourceEvent.touches[0].clientY],l=[c[0]-d[0],c[1]-d[1]];d=c;const h=r()*Math.max(s[2],Math.log(s[2]))*(u?-1:1),f={x:s[0]-l[0]*h,y:s[1]-l[1]*h},m=[[0,0],[i,a]];t.setViewportConstrained({x:f.x,y:f.y,zoom:s[2]},m,e)}:null).on("zoom.wheel",l?e=>{const r=n();if("wheel"!==e.sourceEvent.type||!t)return;const o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*s,i=r[2]*Math.pow(2,o);t.scaleTo(i)}:null);o.call(h,{})},destroy:function(){o.on("zoom",null)},pointer:_e}}const Yo=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,Xo=e=>({x:e.x,y:e.y,zoom:e.k}),Go=({x:e,y:t,zoom:n})=>Dn.translate(e,t).scale(n),qo=(e,t)=>e.target.closest(`.${t}`),Wo=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Uo=(e,t=0,n=()=>{})=>{const r="number"==typeof t&&t>0;return r||n(),r?e.transition().duration(t).on("end",n):e},Zo=e=>{const t=e.ctrlKey&&Dr()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t};function Ko({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:r,translateExtent:o,viewport:i,onPanZoom:a,onPanZoomStart:s,onPanZoomEnd:c,onDraggingChange:l}){const u={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},d=e.getBoundingClientRect(),h=Un().clickDistance(!zr(r)||r<0?0:r).scaleExtent([t,n]).translateExtent(o),f=Ee(e).call(h);v({x:i.x,y:i.y,zoom:wr(i.zoom,t,n)},[[0,0],[d.width,d.height]],o);const m=f.on("wheel.zoom"),p=f.on("dblclick.zoom");function g(e,t){return f?new Promise((n=>{h?.transform(Uo(f,t?.duration,(()=>n(!0))),e)})):Promise.resolve(!1)}function y(){h.on("zoom",null)}async function v(e,t,n){const r=Go(e),o=h?.constrain()(r,t,n);return o&&await g(o),new Promise((e=>e(o)))}return h.wheelDelta(Zo),{update:function({noWheelClassName:e,noPanClassName:t,onPaneContextMenu:n,userSelectionActive:r,panOnScroll:o,panOnDrag:i,panOnScrollMode:d,panOnScrollSpeed:g,preventScrolling:v,zoomOnPinch:x,zoomOnScroll:w,zoomOnDoubleClick:b,zoomActivationKeyPressed:E,lib:_,onTransformChange:k}){r&&!u.isZoomingOrPanning&&y();const N=o&&!E&&!r?function({zoomPanValues:e,noWheelClassName:t,d3Selection:n,d3Zoom:r,panOnScrollMode:o,panOnScrollSpeed:i,zoomOnPinch:a,onPanZoomStart:s,onPanZoom:c,onPanZoomEnd:l}){return u=>{if(qo(u,t))return!1;u.preventDefault(),u.stopImmediatePropagation();const d=n.property("__zoom").k||1;if(u.ctrlKey&&a){const e=_e(u),t=Zo(u),o=d*Math.pow(2,t);return void r.scaleTo(n,o,e,u)}const h=1===u.deltaMode?20:1;let f=o===er.Vertical?0:u.deltaX*h,m=o===er.Horizontal?0:u.deltaY*h;!Dr()&&u.shiftKey&&o!==er.Vertical&&(f=u.deltaY*h,m=0),r.translateBy(n,-f/d*i,-m/d*i,{internal:!0});const p=Xo(n.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,s?.(u,p)),e.isPanScrolling&&(c?.(u,p),e.panScrollTimeout=setTimeout((()=>{l?.(u,p),e.isPanScrolling=!1}),150))}}({zoomPanValues:u,noWheelClassName:e,d3Selection:f,d3Zoom:h,panOnScrollMode:d,panOnScrollSpeed:g,zoomOnPinch:x,onPanZoomStart:s,onPanZoom:a,onPanZoomEnd:c}):function({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(r,o){if(!t&&"wheel"===r.type&&!r.ctrlKey||qo(r,e))return null;r.preventDefault(),n.call(this,r,o)}}({noWheelClassName:e,preventScrolling:v,d3ZoomHandler:m});if(f.on("wheel.zoom",N,{passive:!1}),!r){const e=function({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return r=>{if(r.sourceEvent?.internal)return;const o=Xo(r.transform);e.mouseButton=r.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=o,"mousedown"===r.sourceEvent?.type&&t(!0),n&&n?.(r.sourceEvent,o)}}({zoomPanValues:u,onDraggingChange:l,onPanZoomStart:s});h.on("start",e);const t=function({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:r,onPanZoom:o}){return i=>{e.usedRightMouseButton=!(!n||!Wo(t,e.mouseButton??0)),i.sourceEvent?.sync||r([i.transform.x,i.transform.y,i.transform.k]),o&&!i.sourceEvent?.internal&&o?.(i.sourceEvent,Xo(i.transform))}}({zoomPanValues:u,panOnDrag:i,onPaneContextMenu:!!n,onPanZoom:a,onTransformChange:k});h.on("zoom",t);const r=function({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:r,onPanZoomEnd:o,onPaneContextMenu:i}){return a=>{if(!a.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&Wo(t,e.mouseButton??0)&&!e.usedRightMouseButton&&a.sourceEvent&&i(a.sourceEvent),e.usedRightMouseButton=!1,r(!1),o&&Yo(e.prevViewport,a.transform))){const t=Xo(a.transform);e.prevViewport=t,clearTimeout(e.timerId),e.timerId=setTimeout((()=>{o?.(a.sourceEvent,t)}),n?150:0)}}}({zoomPanValues:u,panOnDrag:i,panOnScroll:o,onPaneContextMenu:n,onPanZoomEnd:c,onDraggingChange:l});h.on("end",r)}const S=function({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:r,panOnScroll:o,zoomOnDoubleClick:i,userSelectionActive:a,noWheelClassName:s,noPanClassName:c,lib:l}){return u=>{const d=e||t,h=n&&u.ctrlKey;if(1===u.button&&"mousedown"===u.type&&(qo(u,`${l}-flow__node`)||qo(u,`${l}-flow__edge`)))return!0;if(!(r||d||o||i||n))return!1;if(a)return!1;if(qo(u,s)&&"wheel"===u.type)return!1;if(qo(u,c)&&("wheel"!==u.type||o&&"wheel"===u.type&&!e))return!1;if(!n&&u.ctrlKey&&"wheel"===u.type)return!1;if(!n&&"touchstart"===u.type&&u.touches?.length>1)return u.preventDefault(),!1;if(!d&&!o&&!h&&"wheel"===u.type)return!1;if(!r&&("mousedown"===u.type||"touchstart"===u.type))return!1;if(Array.isArray(r)&&!r.includes(u.button)&&"mousedown"===u.type)return!1;const f=Array.isArray(r)&&r.includes(u.button)||!u.button||u.button<=1;return(!u.ctrlKey||"wheel"===u.type)&&f}}({zoomActivationKeyPressed:E,panOnDrag:i,zoomOnScroll:w,panOnScroll:o,zoomOnDoubleClick:b,zoomOnPinch:x,userSelectionActive:r,noPanClassName:t,noWheelClassName:e,lib:_});h.filter(S),b?f.on("dblclick.zoom",p):f.on("dblclick.zoom",null)},destroy:y,setViewport:async function(e,t){const n=Go(e);return await g(n,t),new Promise((e=>e(n)))},setViewportConstrained:v,getViewport:function(){const e=f?Hn(f.node()):{x:0,y:0,k:1};return{x:e.x,y:e.y,zoom:e.k}},scaleTo:function(e,t){return f?new Promise((n=>{h?.scaleTo(Uo(f,t?.duration,(()=>n(!0))),e)})):Promise.resolve(!1)},scaleBy:function(e,t){return f?new Promise((n=>{h?.scaleBy(Uo(f,t?.duration,(()=>n(!0))),e)})):Promise.resolve(!1)},setScaleExtent:function(e){h?.scaleExtent(e)},setTranslateExtent:function(e){h?.translateExtent(e)},syncViewport:function(e){if(f){const t=Go(e),n=f.property("__zoom");n.k===e.zoom&&n.x===e.x&&n.y===e.y||h?.transform(f,t,null,{sync:!0})}},setClickDistance:function(e){const t=!zr(e)||e<0?0:e;h?.clickDistance(t)}}}var Jo;!function(e){e.Line="line",e.Handle="handle"}(Jo||(Jo={}));function Qo(e,t){return Math.max(0,t-e)}function ei(e,t){return Math.max(0,e-t)}function ti(e,t,n){return Math.max(0,t-e,e-n)}function ni(e,t){return e?!t:t}const ri={width:0,height:0,x:0,y:0},oi={...ri,pointerX:0,pointerY:0,aspectRatio:1};function ii(e,t,n){const r=t.position.x+e.position.x,o=t.position.y+e.position.y,i=e.measured.width??0,a=e.measured.height??0,s=n[0]*i,c=n[1]*a;return[[r-s,o-c],[r+i-s,o+a-c]]}function ai({domNode:e,nodeId:t,getStoreItems:n,onChange:r,onEnd:o}){const i=Ee(e);return{update:function({controlPosition:e,boundaries:a,keepAspectRatio:s,onResizeStart:c,onResize:l,onResizeEnd:u,shouldResize:d}){let h={...ri},f={...oi};const m=function(e){return{isHorizontal:e.includes("right")||e.includes("left"),isVertical:e.includes("bottom")||e.includes("top"),affectsX:e.includes("left"),affectsY:e.includes("top")}}(e);let p,g,y,v,x=null,w=[];const b=je().on("start",(e=>{const{nodeLookup:r,transform:o,snapGrid:i,snapToGrid:a,nodeOrigin:s,paneDomNode:l}=n();if(p=r.get(t),!p)return;x=l?.getBoundingClientRect()??null;const{xSnapped:u,ySnapped:d}=Xr(e.sourceEvent,{transform:o,snapGrid:i,snapToGrid:a,containerBounds:x});h={width:p.measured.width??0,height:p.measured.height??0,x:p.position.x??0,y:p.position.y??0},f={...h,pointerX:u,pointerY:d,aspectRatio:h.width/h.height},g=void 0,p.parentId&&("parent"===p.extent||p.expandParent)&&(g=r.get(p.parentId),y=g&&"parent"===p.extent?function(e){return[[0,0],[e.measured.width,e.measured.height]]}(g):void 0),w=[],v=void 0;for(const[n,c]of r)if(c.parentId===t&&(w.push({id:n,position:{...c.position},extent:c.extent}),"parent"===c.extent||c.expandParent)){const e=ii(c,p,c.origin??s);v=v?[[Math.min(e[0][0],v[0][0]),Math.min(e[0][1],v[0][1])],[Math.max(e[1][0],v[1][0]),Math.max(e[1][1],v[1][1])]]:e}c?.(e,{...h})})).on("drag",(e=>{const{transform:t,snapGrid:o,snapToGrid:i,nodeOrigin:c}=n(),u=Xr(e.sourceEvent,{transform:t,snapGrid:o,snapToGrid:i,containerBounds:x}),b=[];if(!p)return;const{x:E,y:_,width:k,height:N}=h,S={},M=p.origin??c,{width:O,height:C,x:A,y:$}=function(e,t,n,r,o,i,a,s){let{affectsX:c,affectsY:l}=t;const{isHorizontal:u,isVertical:d}=t,h=u&&d,{xSnapped:f,ySnapped:m}=n,{minWidth:p,maxWidth:g,minHeight:y,maxHeight:v}=r,{x:x,y:w,width:b,height:E,aspectRatio:_}=e;let k=Math.floor(u?f-e.pointerX:0),N=Math.floor(d?m-e.pointerY:0);const S=b+(c?-k:k),M=E+(l?-N:N),O=-i[0]*b,C=-i[1]*E;let A=ti(S,p,g),$=ti(M,y,v);if(a){let e=0,t=0;c&&k<0?e=Qo(x+k+O,a[0][0]):!c&&k>0&&(e=ei(x+S+O,a[1][0])),l&&N<0?t=Qo(w+N+C,a[0][1]):!l&&N>0&&(t=ei(w+M+C,a[1][1])),A=Math.max(A,e),$=Math.max($,t)}if(s){let e=0,t=0;c&&k>0?e=ei(x+k,s[0][0]):!c&&k<0&&(e=Qo(x+S,s[1][0])),l&&N>0?t=ei(w+N,s[0][1]):!l&&N<0&&(t=Qo(w+M,s[1][1])),A=Math.max(A,e),$=Math.max($,t)}if(o){if(u){const e=ti(S/_,y,v)*_;if(A=Math.max(A,e),a){let e=0;e=!c&&!l||c&&!l&&h?ei(w+C+S/_,a[1][1])*_:Qo(w+C+(c?k:-k)/_,a[0][1])*_,A=Math.max(A,e)}if(s){let e=0;e=!c&&!l||c&&!l&&h?Qo(w+S/_,s[1][1])*_:ei(w+(c?k:-k)/_,s[0][1])*_,A=Math.max(A,e)}}if(d){const e=ti(M*_,p,g)/_;if($=Math.max($,e),a){let e=0;e=!c&&!l||l&&!c&&h?ei(x+M*_+O,a[1][0])/_:Qo(x+(l?N:-N)*_+O,a[0][0])/_,$=Math.max($,e)}if(s){let e=0;e=!c&&!l||l&&!c&&h?Qo(x+M*_,s[1][0])/_:ei(x+(l?N:-N)*_,s[0][0])/_,$=Math.max($,e)}}}N+=N<0?$:-$,k+=k<0?A:-A,o&&(h?S>M*_?N=(ni(c,l)?-k:k)/_:k=(ni(c,l)?-N:N)*_:u?(N=k/_,l=c):(k=N*_,c=l));const I=c?x+k:x,z=l?w+N:w;return{width:b+(c?-k:k),height:E+(l?-N:N),x:i[0]*k*(c?-1:1)+I,y:i[1]*N*(l?-1:1)+z}}(f,m,u,a,s,M,y,v),I=O!==k,z=C!==N,T=A!==E&&I,P=$!==_&&z;if(!(T||P||I||z))return;if((T||P||1===M[0]||1===M[1])&&(S.x=T?A:h.x,S.y=P?$:h.y,h.x=S.x,h.y=S.y,w.length>0)){const e=A-E,t=$-_;for(const n of w)n.position={x:n.position.x-e+M[0]*(O-k),y:n.position.y-t+M[1]*(C-N)},b.push(n)}if((I||z)&&(S.width=I?O:h.width,S.height=z?C:h.height,h.width=S.width,h.height=S.height),g&&p.expandParent){const e=M[0]*(S.width??0);S.x&&S.x<e&&(h.x=e,f.x=f.x-(S.x-e));const t=M[1]*(S.height??0);S.y&&S.y<t&&(h.y=t,f.y=f.y-(S.y-t))}const j=function({width:e,prevWidth:t,height:n,prevHeight:r,affectsX:o,affectsY:i}){const a=e-t,s=n-r,c=[a>0?1:a<0?-1:0,s>0?1:s<0?-1:0];return a&&o&&(c[0]=-1*c[0]),s&&i&&(c[1]=-1*c[1]),c}({width:h.width,prevWidth:k,height:h.height,prevHeight:N,affectsX:m.affectsX,affectsY:m.affectsY}),L={...h,direction:j},R=d?.(e,L);!1!==R&&(l?.(e,L),r(S,b))})).on("end",(e=>{u?.(e,{...h}),o?.()}));i.call(b)},destroy:function(){i.on(".drag",null)}}}},2377:function(e,t,n){var r=n(6239);function o(e){return e.nodes().map((function(t){var n=e.node(t),r=e.parent(t),o={v:t};return void 0!==n&&(o.value=n),void 0!==r&&(o.parent=r),o}))}function i(e){return e.edges().map((function(t){var n=e.edge(t),r={v:t.v,w:t.w};return void 0!==t.name&&(r.name=t.name),void 0!==n&&(r.value=n),r}))}e.exports={write:function(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:o(e),edges:i(e)};void 0!==e.graph()&&(t.value=structuredClone(e.graph()));return t},read:function(e){var t=new r(e.options).setGraph(e.value);return e.nodes.forEach((function(e){t.setNode(e.v,e.value),e.parent&&t.setParent(e.v,e.parent)})),e.edges.forEach((function(e){t.setEdge({v:e.v,w:e.w,name:e.name},e.value)})),t}}},2667:function(e,t,n){"use strict";let r=n(4809);e.exports=function(e){let t={},n=e.nodes().filter((t=>!e.children(t).length)),o=n.map((t=>e.node(t).rank)),i=r.applyWithChunking(Math.max,o),a=r.range(i+1).map((()=>[]));return n.sort(((t,n)=>e.node(t).rank-e.node(n).rank)).forEach((function n(r){if(t[r])return;t[r]=!0;let o=e.node(r);a[o.rank].push(r),e.successors(r).forEach(n)})),a}},2806:function(e,t,n){"use strict";let r=n(6743).Graph,o=n(4809);function i(e,t){let n={};return t.length&&t.reduce((function(t,r){let o=0,i=0,a=t.length,c=r[r.length-1];return r.forEach(((t,l)=>{let u=function(e,t){if(e.node(t).dummy)return e.predecessors(t).find((t=>e.node(t).dummy))}(e,t),d=u?e.node(u).order:a;(u||t===c)&&(r.slice(i,l+1).forEach((t=>{e.predecessors(t).forEach((r=>{let i=e.node(r),a=i.order;!(a<o||d<a)||i.dummy&&e.node(t).dummy||s(n,r,t)}))})),i=l+1,o=d)})),r})),n}function a(e,t){let n={};function r(t,r,i,a,c){let l;o.range(r,i).forEach((r=>{l=t[r],e.node(l).dummy&&e.predecessors(l).forEach((t=>{let r=e.node(t);r.dummy&&(r.order<a||r.order>c)&&s(n,t,l)}))}))}return t.length&&t.reduce((function(t,n){let o,i=-1,a=0;return n.forEach(((s,c)=>{if("border"===e.node(s).dummy){let t=e.predecessors(s);t.length&&(o=e.node(t[0]).order,r(n,a,c,i,o),a=c,i=o)}r(n,a,n.length,o,t.length)})),n})),n}function s(e,t,n){if(t>n){let e=t;t=n,n=e}let r=e[t];r||(e[t]=r={}),r[n]=!0}function c(e,t,n){if(t>n){let e=t;t=n,n=e}return!!e[t]&&Object.hasOwn(e[t],n)}function l(e,t,n,r){let o={},i={},a={};return t.forEach((e=>{e.forEach(((e,t)=>{o[e]=e,i[e]=e,a[e]=t}))})),t.forEach((e=>{let t=-1;e.forEach((e=>{let s=r(e);if(s.length){s=s.sort(((e,t)=>a[e]-a[t]));let r=(s.length-1)/2;for(let l=Math.floor(r),u=Math.ceil(r);l<=u;++l){let r=s[l];i[e]===e&&t<a[r]&&!c(n,e,r)&&(i[r]=e,i[e]=o[e]=o[r],t=a[r])}}}))})),{root:o,align:i}}function u(e,t,n,o,i){let a={},s=function(e,t,n,o){let i=new r,a=e.graph(),s=function(e,t,n){return(r,o,i)=>{let a,s=r.node(o),c=r.node(i),l=0;if(l+=s.width/2,Object.hasOwn(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":a=-s.width/2;break;case"r":a=s.width/2}if(a&&(l+=n?a:-a),a=0,l+=(s.dummy?t:e)/2,l+=(c.dummy?t:e)/2,l+=c.width/2,Object.hasOwn(c,"labelpos"))switch(c.labelpos.toLowerCase()){case"l":a=c.width/2;break;case"r":a=-c.width/2}return a&&(l+=n?a:-a),a=0,l}}(a.nodesep,a.edgesep,o);return t.forEach((t=>{let r;t.forEach((t=>{let o=n[t];if(i.setNode(o),r){var a=n[r],c=i.edge(a,o);i.setEdge(a,o,Math.max(s(e,t,r),c||0))}r=t}))})),i}(e,t,n,i),c=i?"borderLeft":"borderRight";function l(e,t){let n=s.nodes(),r=n.pop(),o={};for(;r;)o[r]?e(r):(o[r]=!0,n.push(r),n=n.concat(t(r))),r=n.pop()}return l((function(e){a[e]=s.inEdges(e).reduce(((e,t)=>Math.max(e,a[t.v]+s.edge(t))),0)}),s.predecessors.bind(s)),l((function(t){let n=s.outEdges(t).reduce(((e,t)=>Math.min(e,a[t.w]-s.edge(t))),Number.POSITIVE_INFINITY),r=e.node(t);n!==Number.POSITIVE_INFINITY&&r.borderType!==c&&(a[t]=Math.max(a[t],n))}),s.successors.bind(s)),Object.keys(o).forEach((e=>a[e]=a[n[e]])),a}function d(e,t){return Object.values(t).reduce(((t,n)=>{let r=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;Object.entries(n).forEach((([t,n])=>{let i=function(e,t){return e.node(t).width}(e,t)/2;r=Math.max(n+i,r),o=Math.min(n-i,o)}));const i=r-o;return i<t[0]&&(t=[i,n]),t}),[Number.POSITIVE_INFINITY,null])[1]}function h(e,t){let n=Object.values(t),r=o.applyWithChunking(Math.min,n),i=o.applyWithChunking(Math.max,n);["u","d"].forEach((n=>{["l","r"].forEach((a=>{let s=n+a,c=e[s];if(c===t)return;let l=Object.values(c),u=r-o.applyWithChunking(Math.min,l);"l"!==a&&(u=i-o.applyWithChunking(Math.max,l)),u&&(e[s]=o.mapValues(c,(e=>e+u)))}))}))}function f(e,t){return o.mapValues(e.ul,((n,r)=>{if(t)return e[t.toLowerCase()][r];{let t=Object.values(e).map((e=>e[r])).sort(((e,t)=>e-t));return(t[1]+t[2])/2}}))}e.exports={positionX:function(e){let t,n=o.buildLayerMatrix(e),r=Object.assign(i(e,n),a(e,n)),s={};["u","d"].forEach((i=>{t="u"===i?n:Object.values(n).reverse(),["l","r"].forEach((n=>{"r"===n&&(t=t.map((e=>Object.values(e).reverse())));let a=("u"===i?e.predecessors:e.successors).bind(e),c=l(e,t,r,a),d=u(e,t,c.root,c.align,"r"===n);"r"===n&&(d=o.mapValues(d,(e=>-e))),s[i+n]=d}))}));let c=d(e,s);return h(s,c),f(s,e.graph().align)},findType1Conflicts:i,findType2Conflicts:a,addConflict:s,hasConflict:c,verticalAlignment:l,horizontalCompaction:u,alignCoordinates:h,findSmallestWidthAlignment:d,balance:f}},2839:function(e,t,n){var r=n(4168);e.exports=function(e){return r(e).filter((function(t){return t.length>1||1===t.length&&e.hasEdge(t[0],t[0])}))}},3061:function(e,t,n){"use strict";var r=n(3776),o=n(9294).slack,i=n(9294).longestPath,a=n(6743).alg.preorder,s=n(6743).alg.postorder,c=n(4809).simplify;function l(e){e=c(e),i(e);var t,n=r(e);for(h(n),u(n,e);t=m(n);)g(n,e,t,p(n,e,t))}function u(e,t){var n=s(e,e.nodes());(n=n.slice(0,n.length-1)).forEach((n=>function(e,t,n){var r=e.node(n),o=r.parent;e.edge(n,o).cutvalue=d(e,t,n)}(e,t,n)))}function d(e,t,n){var r=e.node(n).parent,o=!0,i=t.edge(n,r),a=0;return i||(o=!1,i=t.edge(r,n)),a=i.weight,t.nodeEdges(n).forEach((i=>{var s,c,l=i.v===n,u=l?i.w:i.v;if(u!==r){var d=l===o,h=t.edge(i).weight;if(a+=d?h:-h,s=n,c=u,e.hasEdge(s,c)){var f=e.edge(n,u).cutvalue;a+=d?-f:f}}})),a}function h(e,t){arguments.length<2&&(t=e.nodes()[0]),f(e,{},1,t)}function f(e,t,n,r,o){var i=n,a=e.node(r);return t[r]=!0,e.neighbors(r).forEach((o=>{Object.hasOwn(t,o)||(n=f(e,t,n,o,r))})),a.low=i,a.lim=n++,o?a.parent=o:delete a.parent,n}function m(e){return e.edges().find((t=>e.edge(t).cutvalue<0))}function p(e,t,n){var r=n.v,i=n.w;t.hasEdge(r,i)||(r=n.w,i=n.v);var a=e.node(r),s=e.node(i),c=a,l=!1;a.lim>s.lim&&(c=s,l=!0);var u=t.edges().filter((t=>l===y(e,e.node(t.v),c)&&l!==y(e,e.node(t.w),c)));return u.reduce(((e,n)=>o(t,n)<o(t,e)?n:e))}function g(e,t,n,r){var o=n.v,i=n.w;e.removeEdge(o,i),e.setEdge(r.v,r.w,{}),h(e),u(e,t),function(e,t){var n=e.nodes().find((e=>!t.node(e).parent)),r=a(e,n);(r=r.slice(1)).forEach((n=>{var r=e.node(n).parent,o=t.edge(n,r),i=!1;o||(o=t.edge(r,n),i=!0),t.node(n).rank=t.node(r).rank+(i?o.minlen:-o.minlen)}))}(e,t)}function y(e,t,n){return n.low<=t.lim&&t.lim<=n.lim}e.exports=l,l.initLowLimValues=h,l.initCutValues=u,l.calcCutValue=d,l.leaveEdge=m,l.enterEdge=p,l.exchangeEdges=g},3535:function(e,t,n){let r=n(6361),o=n(3900),i=n(7204);e.exports=function e(t,n,a,s){let c=t.children(n),l=t.node(n),u=l?l.borderLeft:void 0,d=l?l.borderRight:void 0,h={};u&&(c=c.filter((e=>e!==u&&e!==d)));let f=r(t,c);f.forEach((n=>{if(t.children(n.v).length){let i=e(t,n.v,a,s);h[n.v]=i,Object.hasOwn(i,"barycenter")&&(o=i,void 0!==(r=n).barycenter?(r.barycenter=(r.barycenter*r.weight+o.barycenter*o.weight)/(r.weight+o.weight),r.weight+=o.weight):(r.barycenter=o.barycenter,r.weight=o.weight))}var r,o}));let m=o(f,a);!function(e,t){e.forEach((e=>{e.vs=e.vs.flatMap((e=>t[e]?t[e].vs:e))}))}(m,h);let p=i(m,s);if(u&&(p.vs=[u,p.vs,d].flat(!0),t.predecessors(u).length)){let e=t.node(t.predecessors(u)[0]),n=t.node(t.predecessors(d)[0]);Object.hasOwn(p,"barycenter")||(p.barycenter=0,p.weight=0),p.barycenter=(p.barycenter*p.weight+e.order+n.order)/(p.weight+2),p.weight+=2}return p}},3776:function(e,t,n){"use strict";var r=n(6743).Graph,o=n(9294).slack;function i(e,t){return e.nodes().forEach((function n(r){t.nodeEdges(r).forEach((i=>{var a=i.v,s=r===a?i.w:a;e.hasNode(s)||o(t,i)||(e.setNode(s,{}),e.setEdge(r,s,{}),n(s))}))})),e.nodeCount()}function a(e,t){return t.edges().reduce(((n,r)=>{let i=Number.POSITIVE_INFINITY;return e.hasNode(r.v)!==e.hasNode(r.w)&&(i=o(t,r)),i<n[0]?[i,r]:n}),[Number.POSITIVE_INFINITY,null])[1]}function s(e,t,n){e.nodes().forEach((e=>t.node(e).rank+=n))}e.exports=function(e){var t,n,c=new r({directed:!1}),l=e.nodes()[0],u=e.nodeCount();c.setNode(l,{});for(;i(c,e)<u;)t=a(c,e),n=c.hasNode(t.v)?o(e,t):-o(e,t),s(c,e,n);return c}},3843:function(e,t,n){let r=n(6743).Graph,o=n(4809);e.exports=function(e,t,n){let i=function(e){var t;for(;e.hasNode(t=o.uniqueId("_root")););return t}(e),a=new r({compound:!0}).setGraph({root:i}).setDefaultNodeLabel((t=>e.node(t)));return e.nodes().forEach((r=>{let o=e.node(r),s=e.parent(r);(o.rank===t||o.minRank<=t&&t<=o.maxRank)&&(a.setNode(r),a.setParent(r,s||i),e[n](r).forEach((t=>{let n=t.v===r?t.w:t.v,o=a.edge(n,r),i=void 0!==o?o.weight:0;a.setEdge(n,r,{weight:e.edge(t).weight+i})})),Object.hasOwn(o,"minRank")&&a.setNode(r,{borderLeft:o.borderLeft[t],borderRight:o.borderRight[t]}))})),a}},3900:function(e,t,n){"use strict";let r=n(4809);e.exports=function(e,t){let n={};return e.forEach(((e,t)=>{let r=n[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:t};void 0!==e.barycenter&&(r.barycenter=e.barycenter,r.weight=e.weight)})),t.edges().forEach((e=>{let t=n[e.v],r=n[e.w];void 0!==t&&void 0!==r&&(r.indegree++,t.out.push(n[e.w]))})),function(e){let t=[];function n(e){return t=>{t.merged||(void 0===t.barycenter||void 0===e.barycenter||t.barycenter>=e.barycenter)&&function(e,t){let n=0,r=0;e.weight&&(n+=e.barycenter*e.weight,r+=e.weight);t.weight&&(n+=t.barycenter*t.weight,r+=t.weight);e.vs=t.vs.concat(e.vs),e.barycenter=n/r,e.weight=r,e.i=Math.min(t.i,e.i),t.merged=!0}(e,t)}}function o(t){return n=>{n.in.push(t),0==--n.indegree&&e.push(n)}}for(;e.length;){let r=e.pop();t.push(r),r.in.reverse().forEach(n(r)),r.out.forEach(o(r))}return t.filter((e=>!e.merged)).map((e=>r.pick(e,["vs","i","barycenter","weight"])))}(Object.values(n).filter((e=>!e.indegree)))}},3933:function(e){function t(e,t,n,o){for(var i=[[e,!1]];i.length>0;){var a=i.pop();a[1]?o.push(a[0]):Object.hasOwn(n,a[0])||(n[a[0]]=!0,i.push([a[0],!0]),r(t(a[0]),(e=>i.push([e,!1]))))}}function n(e,t,n,o){for(var i=[e];i.length>0;){var a=i.pop();Object.hasOwn(n,a)||(n[a]=!0,o.push(a),r(t(a),(e=>i.push(e))))}}function r(e,t){for(var n=e.length;n--;)t(e[n],n,e);return e}e.exports=function(e,r,o){Array.isArray(r)||(r=[r]);var i=e.isDirected()?t=>e.successors(t):t=>e.neighbors(t),a="post"===o?t:n,s=[],c={};return r.forEach((t=>{if(!e.hasNode(t))throw new Error("Graph does not have node: "+t);a(t,i,c,s)})),s}},3973:function(e,t,n){"use strict";function r(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}n.d(t,{x:function(){return r}})},4060:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},4168:function(e){e.exports=function(e){var t=0,n=[],r={},o=[];function i(a){var s=r[a]={onStack:!0,lowlink:t,index:t++};if(n.push(a),e.successors(a).forEach((function(e){Object.hasOwn(r,e)?r[e].onStack&&(s.lowlink=Math.min(s.lowlink,r[e].index)):(i(e),s.lowlink=Math.min(s.lowlink,r[e].lowlink))})),s.lowlink===s.index){var c,l=[];do{c=n.pop(),r[c].onStack=!1,l.push(c)}while(a!==c);o.push(l)}}return e.nodes().forEach((function(e){Object.hasOwn(r,e)||i(e)})),o}},4318:function(e,t,n){var r=n(6316);e.exports=function(e,t,n,i){return function(e,t,n,o){var i,a,s={},c=new r,l=function(e){var t=e.v!==i?e.v:e.w,r=s[t],o=n(e),l=a.distance+o;if(o<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+e+" Weight: "+o);l<r.distance&&(r.distance=l,r.predecessor=i,c.decrease(t,l))};e.nodes().forEach((function(e){var n=e===t?0:Number.POSITIVE_INFINITY;s[e]={distance:n},c.add(e,n)}));for(;c.size()>0&&(i=c.removeMin(),(a=s[i]).distance!==Number.POSITIVE_INFINITY);)o(i).forEach(l);return s}(e,String(t),n||o,i||function(t){return e.outEdges(t)})};var o=()=>1},4371:function(e,t,n){"use strict";n.d(t,{h:function(){return h},n:function(){return u}});var r=n(6540),o=n(9242);const i=e=>{let t;const n=new Set,r=(e,r)=>{const o="function"==typeof e?e(t):e;if(!Object.is(o,t)){const e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},a=e=>e?i(e):i;const{useDebugValue:s}=r,{useSyncExternalStoreWithSelector:c}=o,l=e=>e;function u(e,t=l,n){const r=c(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return s(r),r}const d=(e,t)=>{const n=a(e),r=(e,r=t)=>u(n,e,r);return Object.assign(r,n),r},h=(e,t)=>e?d(e,t):d},4529:function(e){e.exports="1.1.4"},4570:function(e,t,n){let r=n(6743).Graph,o=n(6796);e.exports=function(e,t){if(e.nodeCount()<=1)return[];let n=function(e,t){let n=new r,i=0,a=0;e.nodes().forEach((e=>{n.setNode(e,{v:e,in:0,out:0})})),e.edges().forEach((e=>{let r=n.edge(e.v,e.w)||0,o=t(e),s=r+o;n.setEdge(e.v,e.w,s),a=Math.max(a,n.node(e.v).out+=o),i=Math.max(i,n.node(e.w).in+=o)}));let c=function(e){const t=[];for(let n=0;n<e;n++)t.push(n);return t}(a+i+3).map((()=>new o)),l=i+1;return n.nodes().forEach((e=>{s(c,l,n.node(e))})),{graph:n,buckets:c,zeroIdx:l}}(e,t||i);return function(e,t,n){let r,o=[],i=t[t.length-1],s=t[0];for(;e.nodeCount();){for(;r=s.dequeue();)a(e,t,n,r);for(;r=i.dequeue();)a(e,t,n,r);if(e.nodeCount())for(let i=t.length-2;i>0;--i)if(r=t[i].dequeue(),r){o=o.concat(a(e,t,n,r,!0));break}}return o}(n.graph,n.buckets,n.zeroIdx).flatMap((t=>e.outEdges(t.v,t.w)))};let i=()=>1;function a(e,t,n,r,o){let i=o?[]:void 0;return e.inEdges(r.v).forEach((r=>{let a=e.edge(r),c=e.node(r.v);o&&i.push({v:r.v,w:r.w}),c.out-=a,s(t,n,c)})),e.outEdges(r.v).forEach((r=>{let o=e.edge(r),i=r.w,a=e.node(i);a.in-=o,s(t,n,a)})),e.removeNode(r.v),i}function s(e,t,n){n.out?n.in?e[n.out-n.in+t].enqueue(n):e[e.length-1].enqueue(n):e[0].enqueue(n)}},4718:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},4809:function(e,t,n){"use strict";let r=n(6743).Graph;function o(e,t,n,r){let o;do{o=l(r)}while(e.hasNode(o));return n.dummy=t,e.setNode(o,n),o}e.exports={addBorderNode:function(e,t,n,r){let i={width:0,height:0};arguments.length>=4&&(i.rank=n,i.order=r);return o(e,"border",i,t)},addDummyNode:o,applyWithChunking:a,asNonCompoundGraph:function(e){let t=new r({multigraph:e.isMultigraph()}).setGraph(e.graph());return e.nodes().forEach((n=>{e.children(n).length||t.setNode(n,e.node(n))})),e.edges().forEach((n=>{t.setEdge(n,e.edge(n))})),t},buildLayerMatrix:function(e){let t=u(s(e)+1).map((()=>[]));return e.nodes().forEach((n=>{let r=e.node(n),o=r.rank;void 0!==o&&(t[o][r.order]=n)})),t},intersectRect:function(e,t){let n,r,o=e.x,i=e.y,a=t.x-o,s=t.y-i,c=e.width/2,l=e.height/2;if(!a&&!s)throw new Error("Not possible to find intersection inside of the rectangle");Math.abs(s)*c>Math.abs(a)*l?(s<0&&(l=-l),n=l*a/s,r=l):(a<0&&(c=-c),n=c,r=c*s/a);return{x:o+n,y:i+r}},mapValues:function(e,t){let n=t;"string"==typeof t&&(n=e=>e[t]);return Object.entries(e).reduce(((e,[t,r])=>(e[t]=n(r,t),e)),{})},maxRank:s,normalizeRanks:function(e){let t=e.nodes().map((t=>{let n=e.node(t).rank;return void 0===n?Number.MAX_VALUE:n})),n=a(Math.min,t);e.nodes().forEach((t=>{let r=e.node(t);Object.hasOwn(r,"rank")&&(r.rank-=n)}))},notime:function(e,t){return t()},partition:function(e,t){let n={lhs:[],rhs:[]};return e.forEach((e=>{t(e)?n.lhs.push(e):n.rhs.push(e)})),n},pick:function(e,t){const n={};for(const r of t)void 0!==e[r]&&(n[r]=e[r]);return n},predecessorWeights:function(e){let t=e.nodes().map((t=>{let n={};return e.inEdges(t).forEach((t=>{n[t.v]=(n[t.v]||0)+e.edge(t).weight})),n}));return d(e.nodes(),t)},range:u,removeEmptyRanks:function(e){let t=e.nodes().map((t=>e.node(t).rank)),n=a(Math.min,t),r=[];e.nodes().forEach((t=>{let o=e.node(t).rank-n;r[o]||(r[o]=[]),r[o].push(t)}));let o=0,i=e.graph().nodeRankFactor;Array.from(r).forEach(((t,n)=>{void 0===t&&n%i!=0?--o:void 0!==t&&o&&t.forEach((t=>e.node(t).rank+=o))}))},simplify:function(e){let t=(new r).setGraph(e.graph());return e.nodes().forEach((n=>t.setNode(n,e.node(n)))),e.edges().forEach((n=>{let r=t.edge(n.v,n.w)||{weight:0,minlen:1},o=e.edge(n);t.setEdge(n.v,n.w,{weight:r.weight+o.weight,minlen:Math.max(r.minlen,o.minlen)})})),t},successorWeights:function(e){let t=e.nodes().map((t=>{let n={};return e.outEdges(t).forEach((t=>{n[t.w]=(n[t.w]||0)+e.edge(t).weight})),n}));return d(e.nodes(),t)},time:function(e,t){let n=Date.now();try{return t()}finally{console.log(e+" time: "+(Date.now()-n)+"ms")}},uniqueId:l,zipObject:d};const i=65535;function a(e,t){if(t.length>i){const n=function(e,t=i){const n=[];for(let r=0;r<e.length;r+=t){const o=e.slice(r,r+t);n.push(o)}return n}(t);return e.apply(null,n.map((t=>e.apply(null,t))))}return e.apply(null,t)}function s(e){const t=e.nodes().map((t=>{let n=e.node(t).rank;return void 0===n?Number.MIN_VALUE:n}));return a(Math.max,t)}let c=0;function l(e){var t=++c;return toString(e)+t}function u(e,t,n=1){null==t&&(t=e,e=0);let r=e=>e<t;n<0&&(r=e=>t<e);const o=[];for(let i=e;r(i);i+=n)o.push(i);return o}function d(e,t){return e.reduce(((e,n,r)=>(e[n]=t[r],e)),{})}},4822:function(e,t,n){"use strict";let r=n(2667),o=n(7764),i=n(3535),a=n(3843),s=n(477),c=n(6743).Graph,l=n(4809);function u(e,t,n){return t.map((function(t){return a(e,t,n)}))}function d(e,t){let n=new c;e.forEach((function(e){let r=e.graph().root,o=i(e,r,n,t);o.vs.forEach(((t,n)=>e.node(t).order=n)),s(e,n,o.vs)}))}function h(e,t){Object.values(t).forEach((t=>t.forEach(((t,n)=>e.node(t).order=n))))}e.exports=function e(t,n){if(n&&"function"==typeof n.customOrder)return void n.customOrder(t,e);let i=l.maxRank(t),a=u(t,l.range(1,i+1),"inEdges"),s=u(t,l.range(i-1,-1,-1),"outEdges"),c=r(t);if(h(t,c),n&&n.disableOptimalOrderHeuristic)return;let f,m=Number.POSITIVE_INFINITY;for(let r=0,u=0;u<4;++r,++u){d(r%2?a:s,r%4>=2),c=l.buildLayerMatrix(t);let e=o(t,c);e<m&&(u=0,f=Object.assign({},c),m=e)}h(t,f)}},4867:function(e){e.exports=function(e){let t=function(e){let t={},n=0;function r(o){let i=n;e.children(o).forEach(r),t[o]={low:i,lim:n++}}return e.children().forEach(r),t}(e);e.graph().dummyChains.forEach((n=>{let r=e.node(n),o=r.edgeObj,i=function(e,t,n,r){let o,i,a=[],s=[],c=Math.min(t[n].low,t[r].low),l=Math.max(t[n].lim,t[r].lim);o=n;do{o=e.parent(o),a.push(o)}while(o&&(t[o].low>c||l>t[o].lim));i=o,o=r;for(;(o=e.parent(o))!==i;)s.push(o);return{path:a.concat(s.reverse()),lca:i}}(e,t,o.v,o.w),a=i.path,s=i.lca,c=0,l=a[c],u=!0;for(;n!==o.w;){if(r=e.node(n),u){for(;(l=a[c])!==s&&e.node(l).maxRank<r.rank;)c++;l===s&&(u=!1)}if(!u){for(;c<a.length-1&&e.node(l=a[c+1]).minRank<=r.rank;)c++;l=a[c]}e.setParent(n,l),n=e.successors(n)[0]}}))}},5456:function(e,t,n){var r=n(6239),o=n(6316);e.exports=function(e,t){var n,i=new r,a={},s=new o;function c(e){var r=e.v===n?e.w:e.v,o=s.priority(r);if(void 0!==o){var i=t(e);i<o&&(a[r]=n,s.decrease(r,i))}}if(0===e.nodeCount())return i;e.nodes().forEach((function(e){s.add(e,Number.POSITIVE_INFINITY),i.setNode(e)})),s.decrease(e.nodes()[0],0);var l=!1;for(;s.size()>0;){if(n=s.removeMin(),Object.hasOwn(a,n))i.setEdge(n,a[n]);else{if(l)throw new Error("Input graph is not connected: "+e);l=!0}e.nodeEdges(n).forEach(c)}return i}},5677:function(e){"use strict";function t(e){e.nodes().forEach((t=>n(e.node(t)))),e.edges().forEach((t=>n(e.edge(t))))}function n(e){let t=e.width;e.width=e.height,e.height=t}function r(e){e.y=-e.y}function o(e){let t=e.x;e.x=e.y,e.y=t}e.exports={adjust:function(e){let n=e.graph().rankdir.toLowerCase();"lr"!==n&&"rl"!==n||t(e)},undo:function(e){let n=e.graph().rankdir.toLowerCase();"bt"!==n&&"rl"!==n||function(e){e.nodes().forEach((t=>r(e.node(t)))),e.edges().forEach((t=>{let n=e.edge(t);n.points.forEach(r),Object.hasOwn(n,"y")&&r(n)}))}(e);"lr"!==n&&"rl"!==n||(!function(e){e.nodes().forEach((t=>o(e.node(t)))),e.edges().forEach((t=>{let n=e.edge(t);n.points.forEach(o),Object.hasOwn(n,"x")&&o(n)}))}(e),t(e))}}},5944:function(e){e.exports=function(e){var t,n={},r=[];function o(r){Object.hasOwn(n,r)||(n[r]=!0,t.push(r),e.successors(r).forEach(o),e.predecessors(r).forEach(o))}return e.nodes().forEach((function(e){t=[],o(e),t.length&&r.push(t)})),r}},6043:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]])},6100:function(e,t,n){"use strict";function r(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n,o=0;o<e.length;o++)""!==(n=r(e[o]))&&(t+=(t&&" ")+n);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}n.d(t,{A:function(){return r}})},6239:function(e){"use strict";var t="\0";function n(e,t){e[t]?e[t]++:e[t]=1}function r(e,t){--e[t]||delete e[t]}function o(e,t,n,r){var o=""+t,i=""+n;if(!e&&o>i){var a=o;o=i,i=a}return o+""+i+""+(void 0===r?"\0":r)}function i(e,t){return o(e,t.v,t.w,t.name)}e.exports=class{_isDirected=!0;_isMultigraph=!1;_isCompound=!1;_label;_defaultNodeLabelFn=()=>{};_defaultEdgeLabelFn=()=>{};_nodes={};_in={};_preds={};_out={};_sucs={};_edgeObjs={};_edgeLabels={};_nodeCount=0;_edgeCount=0;_parent;_children;constructor(e){e&&(this._isDirected=!Object.hasOwn(e,"directed")||e.directed,this._isMultigraph=!!Object.hasOwn(e,"multigraph")&&e.multigraph,this._isCompound=!!Object.hasOwn(e,"compound")&&e.compound),this._isCompound&&(this._parent={},this._children={},this._children[t]={})}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){return this._label=e,this}graph(){return this._label}setDefaultNodeLabel(e){return this._defaultNodeLabelFn=e,"function"!=typeof e&&(this._defaultNodeLabelFn=()=>e),this}nodeCount(){return this._nodeCount}nodes(){return Object.keys(this._nodes)}sources(){var e=this;return this.nodes().filter((t=>0===Object.keys(e._in[t]).length))}sinks(){var e=this;return this.nodes().filter((t=>0===Object.keys(e._out[t]).length))}setNodes(e,t){var n=arguments,r=this;return e.forEach((function(e){n.length>1?r.setNode(e,t):r.setNode(e)})),this}setNode(e,n){return Object.hasOwn(this._nodes,e)?(arguments.length>1&&(this._nodes[e]=n),this):(this._nodes[e]=arguments.length>1?n:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]=t,this._children[e]={},this._children[t][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount,this)}node(e){return this._nodes[e]}hasNode(e){return Object.hasOwn(this._nodes,e)}removeNode(e){var t=this;if(Object.hasOwn(this._nodes,e)){var n=e=>t.removeEdge(t._edgeObjs[e]);delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],this.children(e).forEach((function(e){t.setParent(e)})),delete this._children[e]),Object.keys(this._in[e]).forEach(n),delete this._in[e],delete this._preds[e],Object.keys(this._out[e]).forEach(n),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this}setParent(e,n){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(void 0===n)n=t;else{for(var r=n+="";void 0!==r;r=this.parent(r))if(r===e)throw new Error("Setting "+n+" as parent of "+e+" would create a cycle");this.setNode(n)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=n,this._children[n][e]=!0,this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var n=this._parent[e];if(n!==t)return n}}children(e=t){if(this._isCompound){var n=this._children[e];if(n)return Object.keys(n)}else{if(e===t)return this.nodes();if(this.hasNode(e))return[]}}predecessors(e){var t=this._preds[e];if(t)return Object.keys(t)}successors(e){var t=this._sucs[e];if(t)return Object.keys(t)}neighbors(e){var t=this.predecessors(e);if(t){const r=new Set(t);for(var n of this.successors(e))r.add(n);return Array.from(r.values())}}isLeaf(e){return 0===(this.isDirected()?this.successors(e):this.neighbors(e)).length}filterNodes(e){var t=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});t.setGraph(this.graph());var n=this;Object.entries(this._nodes).forEach((function([n,r]){e(n)&&t.setNode(n,r)})),Object.values(this._edgeObjs).forEach((function(e){t.hasNode(e.v)&&t.hasNode(e.w)&&t.setEdge(e,n.edge(e))}));var r={};function o(e){var i=n.parent(e);return void 0===i||t.hasNode(i)?(r[e]=i,i):i in r?r[i]:o(i)}return this._isCompound&&t.nodes().forEach((e=>t.setParent(e,o(e)))),t}setDefaultEdgeLabel(e){return this._defaultEdgeLabelFn=e,"function"!=typeof e&&(this._defaultEdgeLabelFn=()=>e),this}edgeCount(){return this._edgeCount}edges(){return Object.values(this._edgeObjs)}setPath(e,t){var n=this,r=arguments;return e.reduce((function(e,o){return r.length>1?n.setEdge(e,o,t):n.setEdge(e,o),o})),this}setEdge(){var e,t,r,i,a=!1,s=arguments[0];"object"==typeof s&&null!==s&&"v"in s?(e=s.v,t=s.w,r=s.name,2===arguments.length&&(i=arguments[1],a=!0)):(e=s,t=arguments[1],r=arguments[3],arguments.length>2&&(i=arguments[2],a=!0)),e=""+e,t=""+t,void 0!==r&&(r=""+r);var c=o(this._isDirected,e,t,r);if(Object.hasOwn(this._edgeLabels,c))return a&&(this._edgeLabels[c]=i),this;if(void 0!==r&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[c]=a?i:this._defaultEdgeLabelFn(e,t,r);var l=function(e,t,n,r){var o=""+t,i=""+n;if(!e&&o>i){var a=o;o=i,i=a}var s={v:o,w:i};r&&(s.name=r);return s}(this._isDirected,e,t,r);return e=l.v,t=l.w,Object.freeze(l),this._edgeObjs[c]=l,n(this._preds[t],e),n(this._sucs[e],t),this._in[t][c]=l,this._out[e][c]=l,this._edgeCount++,this}edge(e,t,n){var r=1===arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return this._edgeLabels[r]}edgeAsObj(){const e=this.edge(...arguments);return"object"!=typeof e?{label:e}:e}hasEdge(e,t,n){var r=1===arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return Object.hasOwn(this._edgeLabels,r)}removeEdge(e,t,n){var a=1===arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n),s=this._edgeObjs[a];return s&&(e=s.v,t=s.w,delete this._edgeLabels[a],delete this._edgeObjs[a],r(this._preds[t],e),r(this._sucs[e],t),delete this._in[t][a],delete this._out[e][a],this._edgeCount--),this}inEdges(e,t){var n=this._in[e];if(n){var r=Object.values(n);return t?r.filter((e=>e.v===t)):r}}outEdges(e,t){var n=this._out[e];if(n){var r=Object.values(n);return t?r.filter((e=>e.w===t)):r}}nodeEdges(e,t){var n=this.inEdges(e,t);if(n)return n.concat(this.outEdges(e,t))}}},6316:function(e){e.exports=class{_arr=[];_keyIndices={};size(){return this._arr.length}keys(){return this._arr.map((function(e){return e.key}))}has(e){return Object.hasOwn(this._keyIndices,e)}priority(e){var t=this._keyIndices[e];if(void 0!==t)return this._arr[t].priority}min(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key}add(e,t){var n=this._keyIndices;if(e=String(e),!Object.hasOwn(n,e)){var r=this._arr,o=r.length;return n[e]=o,r.push({key:e,priority:t}),this._decrease(o),!0}return!1}removeMin(){this._swap(0,this._arr.length-1);var e=this._arr.pop();return delete this._keyIndices[e.key],this._heapify(0),e.key}decrease(e,t){var n=this._keyIndices[e];if(t>this._arr[n].priority)throw new Error("New priority is greater than current priority. Key: "+e+" Old: "+this._arr[n].priority+" New: "+t);this._arr[n].priority=t,this._decrease(n)}_heapify(e){var t=this._arr,n=2*e,r=n+1,o=e;n<t.length&&(o=t[n].priority<t[o].priority?n:o,r<t.length&&(o=t[r].priority<t[o].priority?r:o),o!==e&&(this._swap(e,o),this._heapify(o)))}_decrease(e){for(var t,n=this._arr,r=n[e].priority;0!==e&&!(n[t=e>>1].priority<r);)this._swap(e,t),e=t}_swap(e,t){var n=this._arr,r=this._keyIndices,o=n[e],i=n[t];n[e]=i,n[t]=o,r[i.key]=e,r[o.key]=t}}},6361:function(e){e.exports=function(e,t=[]){return t.map((t=>{let n=e.inEdges(t);if(n.length){let r=n.reduce(((t,n)=>{let r=e.edge(n),o=e.node(n.v);return{sum:t.sum+r.weight*o.order,weight:t.weight+r.weight}}),{sum:0,weight:0});return{v:t,barycenter:r.sum/r.weight,weight:r.weight}}return{v:t}}))}},6554:function(e,t,n){let r=n(4809);function o(e,t,n,i,a,s,c){let l=e.children(c);if(!l.length)return void(c!==t&&e.setEdge(t,c,{weight:0,minlen:n}));let u=r.addBorderNode(e,"_bt"),d=r.addBorderNode(e,"_bb"),h=e.node(c);e.setParent(u,c),h.borderTop=u,e.setParent(d,c),h.borderBottom=d,l.forEach((r=>{o(e,t,n,i,a,s,r);let l=e.node(r),h=l.borderTop?l.borderTop:r,f=l.borderBottom?l.borderBottom:r,m=l.borderTop?i:2*i,p=h!==f?1:a-s[c]+1;e.setEdge(u,h,{weight:m,minlen:p,nestingEdge:!0}),e.setEdge(f,d,{weight:m,minlen:p,nestingEdge:!0})})),e.parent(c)||e.setEdge(t,u,{weight:0,minlen:a+s[c]})}e.exports={run:function(e){let t=r.addDummyNode(e,"root",{},"_root"),n=function(e){var t={};function n(r,o){var i=e.children(r);i&&i.length&&i.forEach((e=>n(e,o+1))),t[r]=o}return e.children().forEach((e=>n(e,1))),t}(e),i=Object.values(n),a=r.applyWithChunking(Math.max,i)-1,s=2*a+1;e.graph().nestingRoot=t,e.edges().forEach((t=>e.edge(t).minlen*=s));let c=function(e){return e.edges().reduce(((t,n)=>t+e.edge(n).weight),0)}(e)+1;e.children().forEach((r=>o(e,t,s,c,a,n,r))),e.graph().nodeRankFactor=s},cleanup:function(e){var t=e.graph();e.removeNode(t.nestingRoot),delete t.nestingRoot,e.edges().forEach((t=>{e.edge(t).nestingEdge&&e.removeEdge(t)}))}}},6743:function(e,t,n){var r=n(9351);e.exports={Graph:r.Graph,json:n(2377),alg:n(7174),version:r.version}},6796:function(e){function t(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function n(e,t){if("_next"!==e&&"_prev"!==e)return t}e.exports=class{constructor(){let e={};e._next=e._prev=e,this._sentinel=e}dequeue(){let e=this._sentinel,n=e._prev;if(n!==e)return t(n),n}enqueue(e){let n=this._sentinel;e._prev&&e._next&&t(e),e._next=n._next,n._next._prev=e,n._next=e,e._prev=n}toString(){let e=[],t=this._sentinel,r=t._prev;for(;r!==t;)e.push(JSON.stringify(r,n)),r=r._prev;return"["+e.join(", ")+"]"}}},7015:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("TextSearch",[["path",{d:"M21 6H3",key:"1jwq7v"}],["path",{d:"M10 12H3",key:"1ulcyk"}],["path",{d:"M10 18H3",key:"13769t"}],["circle",{cx:"17",cy:"15",r:"3",key:"1upz2a"}],["path",{d:"m21 19-1.9-1.9",key:"dwi7p8"}]])},7174:function(e,t,n){e.exports={components:n(5944),dijkstra:n(4318),dijkstraAll:n(9552),findCycles:n(2839),floydWarshall:n(8929),isAcyclic:n(7681),postorder:n(7448),preorder:n(9439),prim:n(5456),tarjan:n(4168),topsort:n(9441)}},7204:function(e,t,n){let r=n(4809);function o(e,t,n){let r;for(;t.length&&(r=t[t.length-1]).i<=n;)t.pop(),e.push(r.vs),n++;return n}e.exports=function(e,t){let n=r.partition(e,(e=>Object.hasOwn(e,"barycenter"))),i=n.lhs,a=n.rhs.sort(((e,t)=>t.i-e.i)),s=[],c=0,l=0,u=0;i.sort((d=!!t,(e,t)=>e.barycenter<t.barycenter?-1:e.barycenter>t.barycenter?1:d?t.i-e.i:e.i-t.i)),u=o(s,a,u),i.forEach((e=>{u+=e.vs.length,s.push(e.vs),c+=e.barycenter*e.weight,l+=e.weight,u=o(s,a,u)}));var d;let h={vs:s.flat(!0)};l&&(h.barycenter=c/l,h.weight=l);return h}},7277:function(e,t,n){"use strict";let r=n(4809),o=n(2806).positionX;e.exports=function(e){(function(e){let t=r.buildLayerMatrix(e),n=e.graph().ranksep,o=0;t.forEach((t=>{const r=t.reduce(((t,n)=>{const r=e.node(n).height;return t>r?t:r}),0);t.forEach((t=>e.node(t).y=o+r/2)),o+=r+n}))})(e=r.asNonCompoundGraph(e)),Object.entries(o(e)).forEach((([t,n])=>e.node(t).x=n))}},7448:function(e,t,n){var r=n(3933);e.exports=function(e,t){return r(e,t,"post")}},7681:function(e,t,n){var r=n(9441);e.exports=function(e){try{r(e)}catch(t){if(t instanceof r.CycleException)return!1;throw t}return!0}},7764:function(e,t,n){"use strict";let r=n(4809).zipObject;function o(e,t,n){let o=r(n,n.map(((e,t)=>t))),i=t.flatMap((t=>e.outEdges(t).map((t=>({pos:o[t.w],weight:e.edge(t).weight}))).sort(((e,t)=>e.pos-t.pos)))),a=1;for(;a<n.length;)a<<=1;let s=2*a-1;a-=1;let c=new Array(s).fill(0),l=0;return i.forEach((e=>{let t=e.pos+a;c[t]+=e.weight;let n=0;for(;t>0;)t%2&&(n+=c[t+1]),t=t-1>>1,c[t]+=e.weight;l+=e.weight*n})),l}e.exports=function(e,t){let n=0;for(let r=1;r<t.length;++r)n+=o(e,t[r-1],t[r]);return n}},8057:function(e,t,n){e.exports={graphlib:n(6743),layout:n(9507),debug:n(334),util:{time:n(4809).time,notime:n(4809).notime},version:n(4529)}},8280:function(){},8351:function(e,t,n){"use strict";t.A=n.p+"static/welcome-eb6c9c3c9d3b4710ff4a6026a37c39ce.svg"},8493:function(e,t,n){"use strict";var r=n(6540);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,s=r.useLayoutEffect,c=r.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(r){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return s((function(){o.value=n,o.getSnapshot=t,l(o)&&u({inst:o})}),[e,n,t]),a((function(){return l(o)&&u({inst:o}),e((function(){l(o)&&u({inst:o})}))}),[e]),c(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},8603:function(e,t,n){"use strict";n.d(t,{A:function(){return g}});var r=n(7550),o=n(6540),i=n(2318),a=n(6942),s=n.n(a),c=n(2941),l=n(2279),u=n(2702),d=n(6327),h=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const f=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:a}=o.useContext(l.QO),{prefixCls:f,type:m="default",danger:p,disabled:g,loading:y,onClick:v,htmlType:x,children:w,className:b,menu:E,arrow:_,autoFocus:k,overlay:N,trigger:S,align:M,open:O,onOpenChange:C,placement:A,getPopupContainer:$,href:I,icon:z=o.createElement(i.A,null),title:T,buttonsRender:P=e=>e,mouseEnterDelay:j,mouseLeaveDelay:L,overlayClassName:R,overlayStyle:D,destroyPopupOnHide:H,dropdownRender:B}=e,F=h(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),V=n("dropdown",f),Y=`${V}-button`,X={menu:E,arrow:_,autoFocus:k,align:M,disabled:g,trigger:g?[]:S,onOpenChange:C,getPopupContainer:$||t,mouseEnterDelay:j,mouseLeaveDelay:L,overlayClassName:R,overlayStyle:D,destroyPopupOnHide:H,dropdownRender:B},{compactSize:G,compactItemClassnames:q}=(0,d.RQ)(V,a),W=s()(Y,q,b);"overlay"in e&&(X.overlay=N),"open"in e&&(X.open=O),X.placement="placement"in e?A:"rtl"===a?"bottomLeft":"bottomRight";const U=o.createElement(c.Ay,{type:m,danger:p,disabled:g,loading:y,onClick:v,htmlType:x,href:I,title:T},w),Z=o.createElement(c.Ay,{type:m,danger:p,icon:z}),[K,J]=P([U,Z]);return o.createElement(u.A.Compact,Object.assign({className:W,size:G,block:!0},F),K,o.createElement(r.A,Object.assign({},X),J))};f.__ANT_BUTTON=!0;var m=f;const p=r.A;p.Button=m;var g=p},8929:function(e){e.exports=function(e,n,r){return function(e,t,n){var r={},o=e.nodes();return o.forEach((function(e){r[e]={},r[e][e]={distance:0},o.forEach((function(t){e!==t&&(r[e][t]={distance:Number.POSITIVE_INFINITY})})),n(e).forEach((function(n){var o=n.v===e?n.w:n.v,i=t(n);r[e][o]={distance:i,predecessor:e}}))})),o.forEach((function(e){var t=r[e];o.forEach((function(n){var i=r[n];o.forEach((function(n){var r=i[e],o=t[n],a=i[n],s=r.distance+o.distance;s<a.distance&&(a.distance=s,a.predecessor=o.predecessor)}))}))})),r}(e,n||t,r||function(t){return e.outEdges(t)})};var t=()=>1},9207:function(e){e.exports="2.2.4"},9242:function(e,t,n){"use strict";e.exports=n(2162)},9294:function(e,t,n){"use strict";const{applyWithChunking:r}=n(4809);e.exports={longestPath:function(e){var t={};e.sources().forEach((function n(o){var i=e.node(o);if(Object.hasOwn(t,o))return i.rank;t[o]=!0;let a=e.outEdges(o).map((t=>null==t?Number.POSITIVE_INFINITY:n(t.w)-e.edge(t).minlen));var s=r(Math.min,a);return s===Number.POSITIVE_INFINITY&&(s=0),i.rank=s}))},slack:function(e,t){return e.node(t.w).rank-e.node(t.v).rank-e.edge(t).minlen}}},9351:function(e,t,n){e.exports={Graph:n(6239),version:n(9207)}},9439:function(e,t,n){var r=n(3933);e.exports=function(e,t){return r(e,t,"pre")}},9441:function(e){function t(e){var t={},r={},o=[];if(e.sinks().forEach((function i(a){if(Object.hasOwn(r,a))throw new n;Object.hasOwn(t,a)||(r[a]=!0,t[a]=!0,e.predecessors(a).forEach(i),delete r[a],o.push(a))})),Object.keys(t).length!==e.nodeCount())throw new n;return o}class n extends Error{constructor(){super(...arguments)}}e.exports=t,t.CycleException=n},9492:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},9507:function(e,t,n){"use strict";let r=n(41),o=n(258),i=n(860),a=n(4809).normalizeRanks,s=n(4867),c=n(4809).removeEmptyRanks,l=n(6554),u=n(1236),d=n(5677),h=n(4822),f=n(7277),m=n(4809),p=n(6743).Graph;e.exports=function(e,t){let n=t&&t.debugTiming?m.time:m.notime;n("layout",(()=>{let S=n("  buildLayoutGraph",(()=>function(e){let t=new p({multigraph:!0,compound:!0}),n=N(e.graph());return t.setGraph(Object.assign({},y,k(n,g),m.pick(n,v))),e.nodes().forEach((n=>{const r=k(N(e.node(n)),x);Object.keys(w).forEach((e=>{void 0===r[e]&&(r[e]=w[e])})),t.setNode(n,r),t.setParent(n,e.parent(n))})),e.edges().forEach((n=>{let r=N(e.edge(n));t.setEdge(n,Object.assign({},E,k(r,b),m.pick(r,_)))})),t}(e)));n("  runLayout",(()=>function(e,t,n){t("    makeSpaceForEdgeLabels",(()=>function(e){let t=e.graph();t.ranksep/=2,e.edges().forEach((n=>{let r=e.edge(n);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===t.rankdir||"BT"===t.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)}))}(e))),t("    removeSelfEdges",(()=>function(e){e.edges().forEach((t=>{if(t.v===t.w){var n=e.node(t.v);n.selfEdges||(n.selfEdges=[]),n.selfEdges.push({e:t,label:e.edge(t)}),e.removeEdge(t)}}))}(e))),t("    acyclic",(()=>r.run(e))),t("    nestingGraph.run",(()=>l.run(e))),t("    rank",(()=>i(m.asNonCompoundGraph(e)))),t("    injectEdgeLabelProxies",(()=>function(e){e.edges().forEach((t=>{let n=e.edge(t);if(n.width&&n.height){let n=e.node(t.v),r={rank:(e.node(t.w).rank-n.rank)/2+n.rank,e:t};m.addDummyNode(e,"edge-proxy",r,"_ep")}}))}(e))),t("    removeEmptyRanks",(()=>c(e))),t("    nestingGraph.cleanup",(()=>l.cleanup(e))),t("    normalizeRanks",(()=>a(e))),t("    assignRankMinMax",(()=>function(e){let t=0;e.nodes().forEach((n=>{let r=e.node(n);r.borderTop&&(r.minRank=e.node(r.borderTop).rank,r.maxRank=e.node(r.borderBottom).rank,t=Math.max(t,r.maxRank))})),e.graph().maxRank=t}(e))),t("    removeEdgeLabelProxies",(()=>function(e){e.nodes().forEach((t=>{let n=e.node(t);"edge-proxy"===n.dummy&&(e.edge(n.e).labelRank=n.rank,e.removeNode(t))}))}(e))),t("    normalize.run",(()=>o.run(e))),t("    parentDummyChains",(()=>s(e))),t("    addBorderSegments",(()=>u(e))),t("    order",(()=>h(e,n))),t("    insertSelfEdges",(()=>function(e){var t=m.buildLayerMatrix(e);t.forEach((t=>{var n=0;t.forEach(((t,r)=>{var o=e.node(t);o.order=r+n,(o.selfEdges||[]).forEach((t=>{m.addDummyNode(e,"selfedge",{width:t.label.width,height:t.label.height,rank:o.rank,order:r+ ++n,e:t.e,label:t.label},"_se")})),delete o.selfEdges}))}))}(e))),t("    adjustCoordinateSystem",(()=>d.adjust(e))),t("    position",(()=>f(e))),t("    positionSelfEdges",(()=>function(e){e.nodes().forEach((t=>{var n=e.node(t);if("selfedge"===n.dummy){var r=e.node(n.e.v),o=r.x+r.width/2,i=r.y,a=n.x-o,s=r.height/2;e.setEdge(n.e,n.label),e.removeNode(t),n.label.points=[{x:o+2*a/3,y:i-s},{x:o+5*a/6,y:i-s},{x:o+a,y:i},{x:o+5*a/6,y:i+s},{x:o+2*a/3,y:i+s}],n.label.x=n.x,n.label.y=n.y}}))}(e))),t("    removeBorderNodes",(()=>function(e){e.nodes().forEach((t=>{if(e.children(t).length){let n=e.node(t),r=e.node(n.borderTop),o=e.node(n.borderBottom),i=e.node(n.borderLeft[n.borderLeft.length-1]),a=e.node(n.borderRight[n.borderRight.length-1]);n.width=Math.abs(a.x-i.x),n.height=Math.abs(o.y-r.y),n.x=i.x+n.width/2,n.y=r.y+n.height/2}})),e.nodes().forEach((t=>{"border"===e.node(t).dummy&&e.removeNode(t)}))}(e))),t("    normalize.undo",(()=>o.undo(e))),t("    fixupEdgeLabelCoords",(()=>function(e){e.edges().forEach((t=>{let n=e.edge(t);if(Object.hasOwn(n,"x"))switch("l"!==n.labelpos&&"r"!==n.labelpos||(n.width-=n.labeloffset),n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset}}))}(e))),t("    undoCoordinateSystem",(()=>d.undo(e))),t("    translateGraph",(()=>function(e){let t=Number.POSITIVE_INFINITY,n=0,r=Number.POSITIVE_INFINITY,o=0,i=e.graph(),a=i.marginx||0,s=i.marginy||0;function c(e){let i=e.x,a=e.y,s=e.width,c=e.height;t=Math.min(t,i-s/2),n=Math.max(n,i+s/2),r=Math.min(r,a-c/2),o=Math.max(o,a+c/2)}e.nodes().forEach((t=>c(e.node(t)))),e.edges().forEach((t=>{let n=e.edge(t);Object.hasOwn(n,"x")&&c(n)})),t-=a,r-=s,e.nodes().forEach((n=>{let o=e.node(n);o.x-=t,o.y-=r})),e.edges().forEach((n=>{let o=e.edge(n);o.points.forEach((e=>{e.x-=t,e.y-=r})),Object.hasOwn(o,"x")&&(o.x-=t),Object.hasOwn(o,"y")&&(o.y-=r)})),i.width=n-t+a,i.height=o-r+s}(e))),t("    assignNodeIntersects",(()=>function(e){e.edges().forEach((t=>{let n,r,o=e.edge(t),i=e.node(t.v),a=e.node(t.w);o.points?(n=o.points[0],r=o.points[o.points.length-1]):(o.points=[],n=a,r=i),o.points.unshift(m.intersectRect(i,n)),o.points.push(m.intersectRect(a,r))}))}(e))),t("    reversePoints",(()=>function(e){e.edges().forEach((t=>{let n=e.edge(t);n.reversed&&n.points.reverse()}))}(e))),t("    acyclic.undo",(()=>r.undo(e)))}(S,n,t))),n("  updateInputGraph",(()=>function(e,t){e.nodes().forEach((n=>{let r=e.node(n),o=t.node(n);r&&(r.x=o.x,r.y=o.y,r.rank=o.rank,t.children(n).length&&(r.width=o.width,r.height=o.height))})),e.edges().forEach((n=>{let r=e.edge(n),o=t.edge(n);r.points=o.points,Object.hasOwn(o,"x")&&(r.x=o.x,r.y=o.y)})),e.graph().width=t.graph().width,e.graph().height=t.graph().height}(e,S)))}))};let g=["nodesep","edgesep","ranksep","marginx","marginy"],y={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},v=["acyclicer","ranker","rankdir","align"],x=["width","height"],w={width:0,height:0},b=["minlen","weight","width","height","labeloffset"],E={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},_=["labelpos"];function k(e,t){return m.mapValues(m.pick(e,t),Number)}function N(e){var t={};return e&&Object.entries(e).forEach((([e,n])=>{"string"==typeof e&&(e=e.toLowerCase()),t[e]=n})),t}},9552:function(e,t,n){var r=n(4318);e.exports=function(e,t,n){return e.nodes().reduce((function(o,i){return o[i]=r(e,i,t,n),o}),{})}},9850:function(e,t,n){"use strict";n.d(t,{j:function(){return i}});var r=n(7387);let o=function(e){function t(){return e.apply(this,arguments)||this}(0,r.A)(t,e);var n=t.prototype;return n.listSessions=async function(e){const t=await fetch(`${this.getBaseUrl()}/sessions/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch sessions");return n.data},n.getSession=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/sessions/${e}?user_id=${t}`,{headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to fetch session");return r.data},n.createSession=async function(e,t){const n={...e,user_id:t},r=await fetch(`${this.getBaseUrl()}/sessions/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify(n)}),o=await r.json();if(!o.status)throw new Error(o.message||"Failed to create session");return o.data},n.updateSession=async function(e,t,n){const r={...t,id:e,user_id:n},o=await fetch(`${this.getBaseUrl()}/sessions/${e}?user_id=${n}`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(r)}),i=await o.json();if(!i.status)throw new Error(i.message||"Failed to update session");return i.data},n.getSessionRuns=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/sessions/${e}/runs?user_id=${t}`,{headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to fetch session runs");return r.data},n.deleteSession=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/sessions/${e}?user_id=${t}`,{method:"DELETE",headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to delete session")},n.listSessionMessages=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/sessions/${e}/messages?user_id=${t}`,{headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to fetch messages");return r.data},n.createRun=async function(e,t){const n={session_id:e,user_id:t},r=await fetch(`${this.getBaseUrl()}/runs/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify(n)}),o=await r.json();if(!o.status)throw new Error(o.message||"Failed to create run");return o.data.run_id},t}(n(3838).y);const i=new o},9888:function(e,t,n){"use strict";e.exports=n(8493)}}]);
//# sourceMappingURL=d8f268ac9f2b87d121a872ac85fce383f250aa9f-fc7486aa69dd43bf2d43.js.map