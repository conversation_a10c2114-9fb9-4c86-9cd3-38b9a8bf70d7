/*! For license information please see cbfeb58e892aec0ea1b2f284fde7da1224b6b055-a8e76a0802733d810496.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[360],{1196:function(e,n,t){t.d(n,{A:function(){return A}});var r=t(6540),o=t(6942),a=t.n(o),l=t(8873),i=t(8719),c=t(57),s=t(4424),d=t(2279),u=t(8119),p=t(934),b=t(4241);var f=r.createContext(null),v=t(7391),m=t(6827),h=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]])}return t};const g=(e,n)=>{var t;const{prefixCls:o,className:g,rootClassName:y,children:C,indeterminate:$=!1,style:k,onMouseEnter:x,onMouseLeave:O,skipGroup:S=!1,disabled:A}=e,w=h(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:E,direction:j,checkbox:N}=r.useContext(d.QO),P=r.useContext(f),{isFormItemInput:I}=r.useContext(b.$W),z=r.useContext(u.A),M=null!==(t=(null==P?void 0:P.disabled)||A)&&void 0!==t?t:z,D=r.useRef(w.value),R=r.useRef(null),B=(0,i.K4)(n,R);r.useEffect((()=>{null==P||P.registerValue(w.value)}),[]),r.useEffect((()=>{if(!S)return w.value!==D.current&&(null==P||P.cancelValue(D.current),null==P||P.registerValue(w.value),D.current=w.value),()=>null==P?void 0:P.cancelValue(w.value)}),[w.value]),r.useEffect((()=>{var e;(null===(e=R.current)||void 0===e?void 0:e.input)&&(R.current.input.indeterminate=$)}),[$]);const V=E("checkbox",o),F=(0,p.A)(V),[H,q,G]=(0,v.Ay)(V,F),T=Object.assign({},w);P&&!S&&(T.onChange=function(){w.onChange&&w.onChange.apply(w,arguments),P.toggleOption&&P.toggleOption({label:C,value:w.value})},T.name=P.name,T.checked=P.value.includes(w.value));const L=a()(`${V}-wrapper`,{[`${V}-rtl`]:"rtl"===j,[`${V}-wrapper-checked`]:T.checked,[`${V}-wrapper-disabled`]:M,[`${V}-wrapper-in-form-item`]:I},null==N?void 0:N.className,g,y,G,F,q),W=a()({[`${V}-indeterminate`]:$},s.D,q),[X,_]=(0,m.A)(T.onClick);return H(r.createElement(c.A,{component:"Checkbox",disabled:M},r.createElement("label",{className:L,style:Object.assign(Object.assign({},null==N?void 0:N.style),k),onMouseEnter:x,onMouseLeave:O,onClick:X},r.createElement(l.A,Object.assign({},T,{onClick:_,prefixCls:V,className:W,disabled:M,ref:B})),void 0!==C&&r.createElement("span",{className:`${V}-label`},C))))};var y=r.forwardRef(g),C=t(436),$=t(9853),k=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]])}return t};const x=r.forwardRef(((e,n)=>{const{defaultValue:t,children:o,options:l=[],prefixCls:i,className:c,rootClassName:s,style:u,onChange:b}=e,m=k(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:h,direction:g}=r.useContext(d.QO),[x,O]=r.useState(m.value||t||[]),[S,A]=r.useState([]);r.useEffect((()=>{"value"in m&&O(m.value||[])}),[m.value]);const w=r.useMemo((()=>l.map((e=>"string"==typeof e||"number"==typeof e?{label:e,value:e}:e))),[l]),E=h("checkbox",i),j=`${E}-group`,N=(0,p.A)(E),[P,I,z]=(0,v.Ay)(E,N),M=(0,$.A)(m,["value","disabled"]),D=l.length?w.map((e=>r.createElement(y,{prefixCls:E,key:e.value.toString(),disabled:"disabled"in e?e.disabled:m.disabled,value:e.value,checked:x.includes(e.value),onChange:e.onChange,className:`${j}-item`,style:e.style,title:e.title,id:e.id,required:e.required},e.label))):o,R={toggleOption:e=>{const n=x.indexOf(e.value),t=(0,C.A)(x);-1===n?t.push(e.value):t.splice(n,1),"value"in m||O(t),null==b||b(t.filter((e=>S.includes(e))).sort(((e,n)=>w.findIndex((n=>n.value===e))-w.findIndex((e=>e.value===n)))))},value:x,disabled:m.disabled,name:m.name,registerValue:e=>{A((n=>[].concat((0,C.A)(n),[e])))},cancelValue:e=>{A((n=>n.filter((n=>n!==e))))}},B=a()(j,{[`${j}-rtl`]:"rtl"===g},c,s,z,N,I);return P(r.createElement("div",Object.assign({className:B,style:u},M,{ref:n}),r.createElement(f.Provider,{value:R},D)))}));var O=x;const S=y;S.Group=O,S.__ANT_CHECKBOX=!0;var A=S},6827:function(e,n,t){t.d(n,{A:function(){return a}});var r=t(6540),o=t(5371);function a(e){const n=r.useRef(null),t=()=>{o.A.cancel(n.current),n.current=null};return[()=>{t(),n.current=(0,o.A)((()=>{n.current=null}))},r=>{n.current&&(r.stopPropagation(),t()),null==e||e(r)}]}},7391:function(e,n,t){t.d(n,{gd:function(){return c}});var r=t(2187),o=t(5905),a=t(4277),l=t(7358);const i=e=>{const{checkboxCls:n}=e,t=`${n}-wrapper`;return[{[`${n}-group`]:Object.assign(Object.assign({},(0,o.dF)(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[t]:Object.assign(Object.assign({},(0,o.dF)(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${t}`]:{marginInlineStart:0},[`&${t}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[n]:Object.assign(Object.assign({},(0,o.dF)(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${n}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${n}-inner`]:Object.assign({},(0,o.jk)(e))},[`${n}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${(0,r.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${(0,r.zA)(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`\n        ${t}:not(${t}-disabled),\n        ${n}:not(${n}-disabled)\n      `]:{[`&:hover ${n}-inner`]:{borderColor:e.colorPrimary}},[`${t}:not(${t}-disabled)`]:{[`&:hover ${n}-checked:not(${n}-disabled) ${n}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${n}-checked:not(${n}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${n}-checked`]:{[`${n}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`\n        ${t}-checked:not(${t}-disabled),\n        ${n}-checked:not(${n}-disabled)\n      `]:{[`&:hover ${n}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[n]:{"&-indeterminate":{[`${n}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorBorder} !important`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${n}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorPrimary} !important`}}}},{[`${t}-disabled`]:{cursor:"not-allowed"},[`${n}-disabled`]:{[`&, ${n}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${n}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${n}-indeterminate ${n}-inner::after`]:{background:e.colorTextDisabled}}}]};function c(e,n){const t=(0,a.oX)(n,{checkboxCls:`.${e}`,checkboxSize:n.controlInteractiveSize});return[i(t)]}n.Ay=(0,l.OF)("Checkbox",((e,n)=>{let{prefixCls:t}=n;return[c(t,e)]}))},8852:function(e,n,t){t.d(n,{A:function(){return r}});const r=(0,t(1788).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},8873:function(e,n,t){var r=t(8168),o=t(9379),a=t(4467),l=t(5544),i=t(3986),c=t(6942),s=t.n(c),d=t(2533),u=t(6540),p=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],b=(0,u.forwardRef)((function(e,n){var t=e.prefixCls,c=void 0===t?"rc-checkbox":t,b=e.className,f=e.style,v=e.checked,m=e.disabled,h=e.defaultChecked,g=void 0!==h&&h,y=e.type,C=void 0===y?"checkbox":y,$=e.title,k=e.onChange,x=(0,i.A)(e,p),O=(0,u.useRef)(null),S=(0,u.useRef)(null),A=(0,d.A)(g,{value:v}),w=(0,l.A)(A,2),E=w[0],j=w[1];(0,u.useImperativeHandle)(n,(function(){return{focus:function(e){var n;null===(n=O.current)||void 0===n||n.focus(e)},blur:function(){var e;null===(e=O.current)||void 0===e||e.blur()},input:O.current,nativeElement:S.current}}));var N=s()(c,b,(0,a.A)((0,a.A)({},"".concat(c,"-checked"),E),"".concat(c,"-disabled"),m));return u.createElement("span",{className:N,title:$,style:f,ref:S},u.createElement("input",(0,r.A)({},x,{className:"".concat(c,"-input"),ref:O,onChange:function(n){m||("checked"in e||j(n.target.checked),null==k||k({target:(0,o.A)((0,o.A)({},e),{},{type:C,checked:n.target.checked}),stopPropagation:function(){n.stopPropagation()},preventDefault:function(){n.preventDefault()},nativeEvent:n.nativeEvent}))},disabled:m,checked:!!E,type:C})),u.createElement("span",{className:"".concat(c,"-inner")}))}));n.A=b}}]);
//# sourceMappingURL=cbfeb58e892aec0ea1b2f284fde7da1224b6b055-a8e76a0802733d810496.js.map