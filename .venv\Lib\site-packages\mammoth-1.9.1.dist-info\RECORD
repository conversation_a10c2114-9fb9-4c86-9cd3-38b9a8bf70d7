../../Scripts/mammoth.exe,sha256=hrd8tkCqTISNKpcUBAifgTlqlA8RZlSsmRq4JTSJ46A,108410
mammoth-1.9.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mammoth-1.9.1.dist-info/METADATA,sha256=xxGPEprqa244GMRZ-SNOGWSCBdgxSc4zumGaqFtZLnM,24409
mammoth-1.9.1.dist-info/RECORD,,
mammoth-1.9.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
mammoth-1.9.1.dist-info/entry_points.txt,sha256=uvuGZjpkv3IX-Gc39mGuq-mrEyuKg9DKx20yeaeZE3c,45
mammoth-1.9.1.dist-info/licenses/LICENSE,sha256=ZmO70EkgXTiklsystBKhUZgLREYn043iGLO4Ca7zMPE,1307
mammoth-1.9.1.dist-info/top_level.txt,sha256=bzpnNCrDpj3-sVobchossMHbU2p4dM4HboFD1chO_Go,8
mammoth/__init__.py,sha256=8O-rdzMb7O_iSnz4l3U-cr3AMAv9-y_9QVPTe_gRf_g,1463
mammoth/__pycache__/__init__.cpython-312.pyc,,
mammoth/__pycache__/cli.cpython-312.pyc,,
mammoth/__pycache__/conversion.cpython-312.pyc,,
mammoth/__pycache__/document_matchers.cpython-312.pyc,,
mammoth/__pycache__/documents.cpython-312.pyc,,
mammoth/__pycache__/html_paths.cpython-312.pyc,,
mammoth/__pycache__/images.cpython-312.pyc,,
mammoth/__pycache__/lists.cpython-312.pyc,,
mammoth/__pycache__/options.cpython-312.pyc,,
mammoth/__pycache__/raw_text.cpython-312.pyc,,
mammoth/__pycache__/results.cpython-312.pyc,,
mammoth/__pycache__/transforms.cpython-312.pyc,,
mammoth/__pycache__/underline.cpython-312.pyc,,
mammoth/__pycache__/zips.cpython-312.pyc,,
mammoth/cli.py,sha256=L6ZNOsYNESxaDZtvHLNH7heSv3haKPN1VieLGV7xgo0,3095
mammoth/conversion.py,sha256=gpl2ZaevSEk5Fdx1A9qOX-PO6dH-8e8coSdDztEboWg,14189
mammoth/document_matchers.py,sha256=CwyJFR0SzOUINRkDV_2zJK3T9DeUVutmT03lNFUcP1I,2052
mammoth/documents.py,sha256=JQH9UO1MBtiFyfehDsrYZQLQgwYPCTJvZ11M2QtqU-E,6170
mammoth/docx/__init__.py,sha256=fM-NmZbrgC2lCwTwRs9m7JRpY2rRHNwXM3wtuuL_Vv4,6342
mammoth/docx/__pycache__/__init__.cpython-312.pyc,,
mammoth/docx/__pycache__/body_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/comments_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/complex_fields.cpython-312.pyc,,
mammoth/docx/__pycache__/content_types_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/dingbats.cpython-312.pyc,,
mammoth/docx/__pycache__/document_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/files.cpython-312.pyc,,
mammoth/docx/__pycache__/notes_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/numbering_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/office_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/relationships_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/style_map.cpython-312.pyc,,
mammoth/docx/__pycache__/styles_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/uris.cpython-312.pyc,,
mammoth/docx/__pycache__/xmlparser.cpython-312.pyc,,
mammoth/docx/body_xml.py,sha256=jH1dmMvrWxIDPGu1dx6RdgHBwjf--64LXdFWA4ZHP8M,25166
mammoth/docx/comments_xml.py,sha256=tX-XJ0pLRqoYb6XpJmnv7-l1CeW65qiNZXVA2xXU_ZE,849
mammoth/docx/complex_fields.py,sha256=FCWAFGc-jDoYdXzTPKDvwDg4c6kMQ6xdtyVGu2UhN1Q,478
mammoth/docx/content_types_xml.py,sha256=0k1SaG3dkm5e4996UqEjcz78a18ZxCWcm1q9AQo_ST4,1632
mammoth/docx/dingbats.py,sha256=fbU_Pgr-yAWQ3DRqkiIuweke5zUgRwClLFzp5hh6gtI,32817
mammoth/docx/document_xml.py,sha256=Ys77sBAUX91cEZWtyiVAR3ZK0besMpgmrvFRk6ghtMo,616
mammoth/docx/files.py,sha256=hSZJAT1bTSItuahAlK-8ml00mARG2qrM1797qLzP3qg,1029
mammoth/docx/notes_xml.py,sha256=wN4c8Td11z3m75XyRoGgFr2Hal730_YymRIeGQx9VR0,979
mammoth/docx/numbering_xml.py,sha256=IKsO8r2dS7ip9Lgk_50H19nGmRCWWPo5vwAOqNKuUNg,3704
mammoth/docx/office_xml.py,sha256=up5_8uzEXDm-TBTKyxAuo-1aq7tVPnI18vdQHJfPfcs,1920
mammoth/docx/relationships_xml.py,sha256=5GBsm4F4nROj2KPirtaTlec9-CAintinyomiy0ORVSk,1194
mammoth/docx/style_map.py,sha256=3Dz1cMkwTTqvvp33AAIYTzwxuBALGfxTSN050CUYSr0,2514
mammoth/docx/styles_xml.py,sha256=GIzKAbCHOIb2FyaeF6FBPGdTgpybmdLpVKd0_azDw4o,2990
mammoth/docx/uris.py,sha256=3ebe1GJEr5VIxPmpCH-PJfrmhOj3XRrYYFYASnhDApw,289
mammoth/docx/xmlparser.py,sha256=AX3CDbiAQkqlrMInnxSkTueSKwX0AsG0ZTWcvBUCIec,3156
mammoth/html/__init__.py,sha256=AIUT63690bLA55KYE3q-mXegENIC52x823xY6XEqzVE,3516
mammoth/html/__pycache__/__init__.cpython-312.pyc,,
mammoth/html/__pycache__/nodes.cpython-312.pyc,,
mammoth/html/nodes.py,sha256=I4AgPCnYwOg2yYBc0Bl2m5XHIZdduhvaPKZSgHt_wIc,1053
mammoth/html_paths.py,sha256=-wH5zHAi5VddaO50hKN4HnLBDmVhoR4xg7ceEVDY5EQ,1180
mammoth/images.py,sha256=C-qAZeKolzhtfUyE2xZ1NEGpXX84vR5aw53a8HMBfns,638
mammoth/lists.py,sha256=cM5nJR0f9U0oQ7GOOVD5lAQ-MLyf0FLH-XLmELp3E1M,785
mammoth/options.py,sha256=7PWkCOAuo2CUpbj3RoMZ1nfhxcYJZxTpL8lVFFdyrVU,3111
mammoth/raw_text.py,sha256=HnqOLKVQhryHCrn5FO1nWP2BPm38a8k50B4qYgPQo80,435
mammoth/results.py,sha256=W4XUcfPkxIWBn-0-29ohQp_msSAvv7WDq4t_psd__l0,905
mammoth/styles/__init__.py,sha256=7ooo29U6IqSjlcnbo_aYguezbYEsAJGiTtOiUMX6hx8,184
mammoth/styles/__pycache__/__init__.cpython-312.pyc,,
mammoth/styles/parser/__init__.py,sha256=4mi7ZCbPGZENHa6WkgcprYtGEsGNpZq-taJ53s2IVsE,514
mammoth/styles/parser/__pycache__/__init__.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/document_matcher_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/errors.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/html_path_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/style_mapping_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/token_iterator.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/token_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/tokeniser.cpython-312.pyc,,
mammoth/styles/parser/document_matcher_parser.py,sha256=dbs7V1LIsXgfb1SAJmGm9kWYZqBG6_xOEreRz8cEm6s,4308
mammoth/styles/parser/errors.py,sha256=bm58riBwVSBpPuqG7l42WcmmsRiJA6Pss6pTxukZ7v4,42
mammoth/styles/parser/html_path_parser.py,sha256=tJtqUX4XLpQPLDgIK7yo35RxqVS1nlCCNUSdQnctdUs,3232
mammoth/styles/parser/style_mapping_parser.py,sha256=-olAaSEIThPlct59AsBPGTr4DY5HChGDJv-UKMpmcuk,498
mammoth/styles/parser/token_iterator.py,sha256=xjNMiw-5r53hdQ3wH9dr4QVipDuoAWi-Q2DCb5NGyIw,1853
mammoth/styles/parser/token_parser.py,sha256=f0fClB75ZWgoUalugxE8yngtW5vFduI0-tq1zPiDo-E,793
mammoth/styles/parser/tokeniser.py,sha256=w2XBiGrwBzKVrlOyPi-s-PhtxjV7ojgKFe4OWSGzt6g,1652
mammoth/transforms.py,sha256=6_3KrjYgbj4aoSWJI6kQhwiYJv2zEkpXC-H6kgbG2RE,1416
mammoth/underline.py,sha256=Fo0sIWhT-Y_l-vhVO7CuYv930k4ZpaSIh15_fbFEvM4,171
mammoth/writers/__init__.py,sha256=0eUvliuUfAHuBZ60A-MKK5puPcWXU22ZvY8yiRCQ7qU,320
mammoth/writers/__pycache__/__init__.cpython-312.pyc,,
mammoth/writers/__pycache__/abc.cpython-312.pyc,,
mammoth/writers/__pycache__/html.cpython-312.pyc,,
mammoth/writers/__pycache__/markdown.cpython-312.pyc,,
mammoth/writers/abc.py,sha256=x-SeNYD8Ugr1fijb-j0lLnxuC5D7BR1L74qE-DuhSU0,554
mammoth/writers/html.py,sha256=fQjZoNHJauVIZwJdZ7R7m4rM48u3wABO989hbV011ZE,1183
mammoth/writers/markdown.py,sha256=imAq1GY3EUZ_e1ZvxbVflMlUYPENruJ07B6-0WhpPyo,5300
mammoth/zips.py,sha256=X-Alu6klsYOUhSdyimLPBE9hoxlw6BMno2aqv08ax9w,1802
