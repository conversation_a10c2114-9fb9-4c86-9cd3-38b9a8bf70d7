{"version": 3, "file": "component---src-pages-login-tsx-e7b19ce014e96a866900.js", "mappings": "+PAEA,EADqB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,ipBAAqpB,KAAQ,SAAU,MAAS,Y,UCMx0B,EAAiB,SAAwBA,EAAOC,GAClD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,oBCN5C,MAAM,MAAEC,EAAK,KAAEC,GAASC,EAAAA,EAmIxB,MA9HkBC,IAAmB,IAAlB,KAAEC,GAAWD,EAC9B,MAAM,gBAAEE,EAAe,UAAEC,EAAS,MAAEC,EAAK,SAAEC,IAAaC,EAAAA,EAAAA,MAClD,EAACC,EAAW,EAAEC,IAAkBC,EAAAA,EAAAA,WAAS,IAE/CC,EAAAA,EAAAA,YAAU,KAEJR,IAAoBC,IACtBQ,EAAAA,EAAAA,UAAS,IACX,GACC,CAACT,EAAiBC,KAGrBO,EAAAA,EAAAA,YAAU,KACS,SAAbL,GAAwBF,IAC1BQ,EAAAA,EAAAA,UAAS,IACX,GACC,CAACN,EAAUF,IAgDd,OAAIA,EAEAS,EAAAA,cAACC,EAAAA,EAAM,CAACC,KAAMb,EAAKc,KAAKC,aAAcC,MAAM,QAAQC,KAAK,UACvDN,EAAAA,cAAA,OAAKO,UAAU,6CACbP,EAAAA,cAACQ,EAAAA,EAAI,CAACC,KAAK,QAAQC,IAAI,iBAO7BV,EAAAA,cAACC,EAAAA,EAAM,CACLC,KAAMb,EAAKc,KAAKC,aAChBC,MAAM,QACNC,KAAK,SACLK,YAAY,EACZC,YAAY,GAEZZ,EAAAA,cAAA,OAAKO,UAAU,0DACbP,EAAAA,cAAA,OAAKO,UAAU,uDACbP,EAAAA,cAAA,OAAKO,UAAU,oBACbP,EAAAA,cAAA,OAAKO,UAAU,QACbP,EAAAA,cAACa,EAAAA,EAAI,CAAC7B,KAAK,MAAMyB,KAAM,MAEzBT,EAAAA,cAAA,OAAKO,UAAU,4CAA2C,cAC5ClB,EAAKc,KAAKC,aAAaC,OAErCL,EAAAA,cAAA,OAAKO,UAAU,0BACZ,IAAI,iDAKTP,EAAAA,cAACc,EAAAA,EAAK,CAACC,UAAU,WAAWR,UAAU,UACpCP,EAAAA,cAACgB,EAAAA,GAAM,CACLC,KAAK,UACLR,KAAK,QACLzB,KAAMgB,EAAAA,cAACkB,EAAc,MACrBC,QApFQC,UAClB,IACExB,GAAe,GACf,MAAMyB,QAAiB7B,IAEvB,IAAK6B,EAGH,OAFAC,EAAAA,GAAQC,MAAM,gCACd3B,GAAe,GAKjB,MAAM4B,EAAQ,IACRC,EAAS,IACTC,EAAOC,OAAOC,OAAOJ,MAAQ,EAAIA,EAAQ,EACzCK,EAAMF,OAAOC,OAAOH,OAAS,EAAIA,EAAS,EAE1CK,EAAQH,OAAOI,KACnBV,EACA,cACA,SAASG,YAAgBC,SAAcI,UAAYH,KAIrD,IAAKI,GAASA,EAAME,aAAkC,IAAjBF,EAAME,OAKzC,OAJAV,EAAAA,GAAQC,MACN,yEAEF3B,GAAe,GAKjB,MAAMqC,EAAqBC,aAAY,KACjCJ,EAAME,SACRG,cAAcF,GACdrC,GAAe,GACjB,GACC,IACL,CAAE,MAAO2B,GACPa,QAAQb,MAAM,eAAgBA,GAC9BD,EAAAA,GAAQC,MAAM,4BACd3B,GAAe,EACjB,GA0CUyC,QAAS1C,EACT2C,OAAK,GAEJ3C,EAAc,0BAA4B,0BAK5C,C", "sources": ["webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/GithubOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/GithubOutlined.js", "webpack://autogentstudio/./src/pages/login.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar GithubOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z\" } }] }, \"name\": \"github\", \"theme\": \"outlined\" };\nexport default GithubOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GithubOutlinedSvg from \"@ant-design/icons-svg/es/asn/GithubOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GithubOutlined = function GithubOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GithubOutlinedSvg\n  }));\n};\n\n/**![github](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMS42IDc2LjNDMjY0LjMgNzYuMiA2NCAyNzYuNCA2NCA1MjMuNSA2NCA3MTguOSAxODkuMyA4ODUgMzYzLjggOTQ2YzIzLjUgNS45IDE5LjktMTAuOCAxOS45LTIyLjJ2LTc3LjVjLTEzNS43IDE1LjktMTQxLjItNzMuOS0xNTAuMy04OC45QzIxNSA3MjYgMTcxLjUgNzE4IDE4NC41IDcwM2MzMC45LTE1LjkgNjIuNCA0IDk4LjkgNTcuOSAyNi40IDM5LjEgNzcuOSAzMi41IDEwNCAyNiA1LjctMjMuNSAxNy45LTQ0LjUgMzQuNy02MC44LTE0MC42LTI1LjItMTk5LjItMTExLTE5OS4yLTIxMyAwLTQ5LjUgMTYuMy05NSA0OC4zLTEzMS43LTIwLjQtNjAuNSAxLjktMTEyLjMgNC45LTEyMCA1OC4xLTUuMiAxMTguNSA0MS42IDEyMy4yIDQ1LjMgMzMtOC45IDcwLjctMTMuNiAxMTIuOS0xMy42IDQyLjQgMCA4MC4yIDQuOSAxMTMuNSAxMy45IDExLjMtOC42IDY3LjMtNDguOCAxMjEuMy00My45IDIuOSA3LjcgMjQuNyA1OC4zIDUuNSAxMTggMzIuNCAzNi44IDQ4LjkgODIuNyA0OC45IDEzMi4zIDAgMTAyLjItNTkgMTg4LjEtMjAwIDIxMi45YTEyNy41IDEyNy41IDAgMDEzOC4xIDkxdjExMi41Yy44IDkgMCAxNy45IDE1IDE3LjkgMTc3LjEtNTkuNyAzMDQuNi0yMjcgMzA0LjYtNDI0LjEgMC0yNDcuMi0yMDAuNC00NDcuMy00NDcuNS00NDcuM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GithubOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GithubOutlined';\n}\nexport default RefIcon;", "import React, { useEffect, useState } from \"react\";\nimport { useAuth } from \"../auth/context\";\nimport { navigate } from \"gatsby\";\nimport { Button, Card, Typography, Space, Spin, message } from \"antd\";\nimport { GithubOutlined } from \"@ant-design/icons\";\nimport Layout from \"../components/layout\";\nimport { graphql } from \"gatsby\";\nimport Icon from \"../components/icons\";\n\nconst { Title, Text } = Typography;\n\n// Use the same token key as in context.tsx\nconst TOKEN_KEY = \"auth_token\";\n\nconst LoginPage = ({ data }: any) => {\n  const { isAuthenticated, isLoading, login, authType } = useAuth();\n  const [isLoggingIn, setIsLoggingIn] = useState(false);\n\n  useEffect(() => {\n    // If user is already authenticated, redirect to home\n    if (isAuthenticated && !isLoading) {\n      navigate(\"/\");\n    }\n  }, [isAuthenticated, isLoading]);\n\n  // If auth type is 'none', redirect to home\n  useEffect(() => {\n    if (authType === \"none\" && !isLoading) {\n      navigate(\"/\");\n    }\n  }, [authType, isLoading]);\n\n  const handleLogin = async () => {\n    try {\n      setIsLoggingIn(true);\n      const loginUrl = await login();\n\n      if (!loginUrl) {\n        message.error(\"Failed to get login URL\");\n        setIsLoggingIn(false);\n        return;\n      }\n\n      // Open a popup window for auth\n      const width = 600;\n      const height = 700;\n      const left = window.screen.width / 2 - width / 2;\n      const top = window.screen.height / 2 - height / 2;\n\n      const popup = window.open(\n        loginUrl,\n        \"github-auth\",\n        `width=${width},height=${height},top=${top},left=${left}`\n      );\n\n      // Check if popup was blocked\n      if (!popup || popup.closed || typeof popup.closed === \"undefined\") {\n        message.error(\n          \"Popup was blocked by browser. Please allow popups for this site.\"\n        );\n        setIsLoggingIn(false);\n        return;\n      }\n\n      // Set a timer to check if the popup is closed without completing authentication\n      const checkPopupInterval = setInterval(() => {\n        if (popup.closed) {\n          clearInterval(checkPopupInterval);\n          setIsLoggingIn(false);\n        }\n      }, 1000);\n    } catch (error) {\n      console.error(\"Login error:\", error);\n      message.error(\"Failed to initiate login\");\n      setIsLoggingIn(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Layout meta={data.site.siteMetadata} title=\"Login\" link=\"/login\">\n        <div className=\"flex items-center justify-center h-screen\">\n          <Spin size=\"large\" tip=\"Loading...\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout\n      meta={data.site.siteMetadata}\n      title=\"Login\"\n      link=\"/login\"\n      showHeader={true}\n      restricted={false}\n    >\n      <div className=\"flex items-center justify-center h-[calc(100vh-164px)]\">\n        <div className=\"w-full rounded bg-secondary max-w-md p-8 sxhadow-sm\">\n          <div className=\"text-center mb-8\">\n            <div className=\"mb-3\">\n              <Icon icon=\"app\" size={12} />\n            </div>\n            <div className=\"text-2xl mb-1 font-semibold text-primary\">\n              Sign in to {data.site.siteMetadata.title}\n            </div>\n            <div className=\"text-secondary text-sm\">\n              {\" \"}\n              Build and prototype multi-agent applications\n            </div>\n          </div>\n\n          <Space direction=\"vertical\" className=\"w-full\">\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              icon={<GithubOutlined />}\n              onClick={handleLogin}\n              loading={isLoggingIn}\n              block\n            >\n              {isLoggingIn ? \"Connecting to GitHub...\" : \"Sign in with GitHub\"}\n            </Button>\n          </Space>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport const query = graphql`\n  query LoginPageQuery {\n    site {\n      siteMetadata {\n        description\n        title\n      }\n    }\n  }\n`;\n\nexport default LoginPage;\n"], "names": ["props", "ref", "AntdIcon", "A", "icon", "Title", "Text", "Typography", "_ref", "data", "isAuthenticated", "isLoading", "login", "authType", "useAuth", "isLoggingIn", "setIsLoggingIn", "useState", "useEffect", "navigate", "React", "Layout", "meta", "site", "siteMetadata", "title", "link", "className", "Spin", "size", "tip", "showHeader", "restricted", "Icon", "Space", "direction", "<PERSON><PERSON>", "type", "GithubOutlined", "onClick", "async", "loginUrl", "message", "error", "width", "height", "left", "window", "screen", "top", "popup", "open", "closed", "checkPopupInterval", "setInterval", "clearInterval", "console", "loading", "block"], "sourceRoot": ""}