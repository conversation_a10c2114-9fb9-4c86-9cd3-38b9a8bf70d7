{"version": 3, "file": "component---src-pages-404-tsx-e439faaf3e893845325c.js", "mappings": "sLAGA,MAAMA,EAAa,CACjBC,MAAO,UACPC,QAAS,OACTC,WAAY,4CAERC,EAAgB,CACpBC,UAAW,EACXC,aAAc,GACdC,SAAU,KAGNC,EAAkB,CACtBF,aAAc,IA+BhB,UArB0CG,IAEtCC,EAAAA,cAAA,QAAMC,MAAOX,GACXU,EAAAA,cAAA,MAAIC,MAAOP,GAAe,kBAC1BM,EAAAA,cAAA,KAAGC,MAAOH,GAAiB,wDAEzBE,EAAAA,cAAA,WAOI,KACJA,EAAAA,cAAA,WACAA,EAAAA,cAACE,EAAAA,KAAI,CAACC,GAAG,KAAI,WAAc,MAQ5B,MAAMC,EAAeA,IAAMJ,EAAAA,cAAA,aAAO,Y", "sources": ["webpack://autogentstudio/./src/pages/404.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Link, HeadFC, PageProps } from \"gatsby\"\n\nconst pageStyles = {\n  color: \"#232129\",\n  padding: \"96px\",\n  fontFamily: \"-apple-system, Roboto, sans-serif, serif\",\n}\nconst headingStyles = {\n  marginTop: 0,\n  marginBottom: 64,\n  maxWidth: 320,\n}\n\nconst paragraphStyles = {\n  marginBottom: 48,\n}\nconst codeStyles = {\n  color: \"#8A6534\",\n  padding: 4,\n  backgroundColor: \"#FFF4DB\",\n  fontSize: \"1.25rem\",\n  borderRadius: 4,\n}\n\nconst NotFoundPage: React.FC<PageProps> = () => {\n  return (\n    <main style={pageStyles}>\n      <h1 style={headingStyles}>Page not found</h1>\n      <p style={paragraphStyles}>\n        Sorry 😔, we couldn’t find what you were looking for.\n        <br />\n        {process.env.NODE_ENV === \"development\" ? (\n          <>\n            <br />\n            Try creating a page in <code style={codeStyles}>src/pages/</code>.\n            <br />\n          </>\n        ) : null}\n        <br />\n        <Link to=\"/\">Go home</Link>.\n      </p>\n    </main>\n  )\n}\n\nexport default NotFoundPage\n\nexport const Head: HeadFC = () => <title>Not found</title>\n"], "names": ["pageStyles", "color", "padding", "fontFamily", "headingStyles", "marginTop", "marginBottom", "max<PERSON><PERSON><PERSON>", "paragraphStyles", "NotFoundPage", "React", "style", "Link", "to", "Head"], "sourceRoot": ""}