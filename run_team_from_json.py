import asyncio
import os
import json
from autogen_core import Component<PERSON>oader
from autogen_agentchat.ui import Console

async def main():
    """Load and run the AI development team from JSON configuration"""

    # Set up environment variables (replace with your actual values)
    os.environ["OPENAI_API_KEY"] = "your-openai-api-key-here"

    try:
        # Load the team configuration from JSON file
        with open("ai_dev_team_config.json", "r") as f:
            config_data = json.load(f)

        # Load the team using ComponentLoader
        loader = ComponentLoader()
        team = loader.load_component(config_data)

        # Define the task
        task = """
        Build me a full FastAPI application with the following requirements:
        1. Supabase backend integration with PostgreSQL and pgvector
        2. RAG-powered FAQ system for intelligent responses
        3. SMS integration via Kudocity API
        4. Clean, scalable architecture with proper error handling
        5. API documentation and testing setup

        Please start by designing the overall architecture and then proceed with implementation.
        """

        # Run the team and stream the conversation to console
        print("🚀 Starting AI Development Team...")
        print("=" * 60)

        stream = team.run_stream(task=task)
        await <PERSON>sole(stream)

        print("=" * 60)
        print("✅ Team execution completed!")

    except Exception as e:
        print(f"❌ Error running team: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting tips:")
        print("1. Make sure your OPENAI_API_KEY is set correctly")
        print("2. Verify ai_dev_team_config.json exists and is valid")
        print("3. Check that all required dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())
