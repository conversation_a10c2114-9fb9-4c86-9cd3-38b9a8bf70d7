from autogen import config_list_from_json, AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager, load_team_config

# Load your team config
team_config = load_team_config("your_team_config.json")

# Access the agents
user = team_config["agents"]["user"]
manager = team_config["manager"]

# Start the chat
user.initiate_chat(manager, message="""
Build me a full FastAPI app with Supabase backend, Zoho integration, RAG-powered FAQ responses, and SMS via Kudacity.
""")
