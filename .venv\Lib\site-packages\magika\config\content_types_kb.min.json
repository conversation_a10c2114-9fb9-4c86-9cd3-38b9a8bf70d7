{"3gp": {"mime_type": "video/3gpp", "group": "video", "description": "3GPP multimedia file", "extensions": ["3gp"], "is_text": false}, "3ds": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Nintendo 3DS roms", "extensions": ["3ds"], "is_text": false}, "3dsx": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Nintendo 3DS homebrew", "extensions": ["3dsx"], "is_text": false}, "3dsm": {"mime_type": "application/x-3ds", "group": "image", "description": "3D studio Max", "extensions": ["3ds"], "is_text": false}, "3mf": {"mime_type": "application/vnd.ms-package.3dmanufacturing-3dmodel+xml", "group": "image", "description": "3D Manufacturing Format", "extensions": ["3mf"], "is_text": false}, "abnf": {"mime_type": "text/plain", "group": null, "description": "augmented Backus–Naur form", "extensions": ["abnf"], "is_text": false}, "ace": {"mime_type": "application/x-ace-compressed", "group": "archive", "description": "ACE archive", "extensions": ["ace"], "is_text": false}, "ada": {"mime_type": "text/x-ada", "group": "code", "description": "ADA source", "extensions": [], "is_text": false}, "aff": {"mime_type": "text/plain", "group": null, "description": "Hunspell Affix", "extensions": ["aff"], "is_text": true}, "ai": {"mime_type": "application/pdf", "group": "document", "description": "Adobe Illustrator Artwork", "extensions": ["ai"], "is_text": false}, "aidl": {"mime_type": "text/plain", "group": null, "description": "Android Interface Definition Language", "extensions": ["aidl"], "is_text": true}, "algol68": {"mime_type": null, "group": null, "description": null, "extensions": ["a68"], "is_text": false}, "ani": {"mime_type": "application/x-navi-animation", "group": null, "description": "Animated cursor", "extensions": ["ani"], "is_text": false}, "apk": {"mime_type": "application/vnd.android.package-archive", "group": "executable", "description": "Android package", "extensions": ["apk"], "is_text": false}, "applebplist": {"mime_type": "application/x-bplist", "group": "application", "description": "Apple binary property list", "extensions": ["bplist", "plist"], "is_text": false}, "appledouble": {"mime_type": "multipart/appledouble", "group": "unknown", "description": "AppleDouble", "extensions": [], "is_text": false}, "appleplist": {"mime_type": "application/x-plist", "group": "application", "description": "Apple property list", "extensions": ["plist"], "is_text": true}, "applesingle": {"mime_type": "application/applefile", "group": "unknown", "description": "AppleSingle", "extensions": [], "is_text": false}, "ar": {"mime_type": "application/x-archive", "group": "archive", "description": "AR Archive", "extensions": [], "is_text": false}, "arc": {"mime_type": "application/x-arc", "group": "archive", "description": "Arc", "extensions": ["arc"], "is_text": false}, "arj": {"mime_type": "application/arj", "group": "archive", "description": "<PERSON><PERSON><PERSON>", "extensions": [], "is_text": false}, "arrow": {"mime_type": "vnd.apache.arrow.file", "group": null, "description": null, "extensions": [], "is_text": false}, "asc": {"mime_type": "application/pgp-signature", "group": "text", "description": "PGP", "extensions": ["asc"], "is_text": true}, "asd": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "au": {"mime_type": "audio/basic", "group": "audio", "description": "NeXT/Sun AU", "extensions": ["au"], "is_text": false}, "asf": {"mime_type": "video/x-ms-wma", "group": "application", "description": "Microsoft Advanced Systems Format", "extensions": ["asf"], "is_text": false}, "asm": {"mime_type": "text/x-asm", "group": "code", "description": "Assembly", "extensions": ["s", "S", "asm"], "is_text": true}, "asp": {"mime_type": "text/html", "group": "code", "description": "ASP source", "extensions": ["aspx", "asp"], "is_text": true}, "autohotkey": {"mime_type": "text/plain", "group": "code", "description": "AutoHotKey script", "extensions": [], "is_text": true}, "autoit": {"mime_type": "text/plain", "group": "code", "description": "AutoIt script", "extensions": ["au3"], "is_text": true}, "avi": {"mime_type": "video/x-msvideo", "group": "video", "description": "Audio Video Interleave", "extensions": ["avi"], "is_text": false}, "avif": {"mime_type": "image/avif", "group": "video", "description": "AV1 Image File Format", "extensions": ["avif", "avifs"], "is_text": false}, "avro": {"mime_type": "application/x-avro-binary", "group": null, "description": "Apache Avro binary", "extensions": ["avro"], "is_text": false}, "awk": {"mime_type": "text/plain", "group": "code", "description": "Awk", "extensions": ["awk"], "is_text": true}, "ax": {"mime_type": "application/x-dosexec", "group": "executable", "description": "Directshow filter", "extensions": ["ax"], "is_text": false}, "batch": {"mime_type": "text/x-msdos-batch", "group": "code", "description": "DOS batch file", "extensions": ["bat"], "is_text": true}, "bazel": {"mime_type": "text/plain", "group": "code", "description": "Bazel build file", "extensions": ["bzl"], "is_text": true}, "bcad": {"mime_type": "application/octet-stream", "group": "document", "description": "bCAD Drawing", "extensions": ["bdf"], "is_text": false}, "bib": {"mime_type": "text/x-bibtex", "group": "text", "description": "BibTeX", "extensions": ["bib"], "is_text": true}, "bmp": {"mime_type": "image/bmp", "group": "image", "description": "BMP image data", "extensions": ["bmp"], "is_text": false}, "bpg": {"mime_type": "image/bpg", "group": "image", "description": "BPG", "extensions": ["bpg"], "is_text": false}, "bpl": {"mime_type": null, "group": "unknown", "description": null, "extensions": ["bpl"], "is_text": false}, "brainfuck": {"mime_type": "text/x-brainfuck", "group": "code", "description": "Brainfuck source", "extensions": ["b", "bf"], "is_text": true}, "brf": {"mime_type": "text/plain", "group": "text", "description": "Braille Ready Format", "extensions": ["brf", "bfm"], "is_text": false}, "bzip": {"mime_type": "application/x-bzip2", "group": "archive", "description": "bzip2 compressed data", "extensions": ["bz2", "tbz2", "tar.bz2"], "is_text": false}, "bzip3": {"mime_type": "application/x-bzip3", "group": "archive", "description": "bzip3 compressed data", "extensions": ["bz3"], "is_text": false}, "c": {"mime_type": "text/x-c", "group": "code", "description": "C source", "extensions": ["c"], "is_text": true}, "cab": {"mime_type": "application/vnd.ms-cab-compressed", "group": "archive", "description": "Microsoft Cabinet archive data", "extensions": ["cab"], "is_text": false}, "cad": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "cat": {"mime_type": "application/octet-stream", "group": "application", "description": "Windows Catalog file", "extensions": ["cat"], "is_text": false}, "cdf": {"mime_type": null, "group": "archive", "description": null, "extensions": [], "is_text": false}, "chm": {"mime_type": "application/chm", "group": "application", "description": "MS Windows HtmlHelp Data", "extensions": ["chm"], "is_text": false}, "clojure": {"mime_type": "text/x-clojure", "group": "code", "description": "Clojure", "extensions": ["clj", "cljs", "cljc", "cljr"], "is_text": true}, "cmake": {"mime_type": "text/x-cmake", "group": "code", "description": "CMake build file", "extensions": ["cmake"], "is_text": true}, "cobol": {"mime_type": "text/x-cobol", "group": "code", "description": "Cobol", "extensions": ["cbl", "cob", "cpy", "CBL", "COB", "CPY"], "is_text": true}, "coff": {"mime_type": "application/x-coff", "group": "executable", "description": "Intel 80386 COFF", "extensions": ["obj", "o"], "is_text": false}, "coffeescript": {"mime_type": "text/coffeescript", "group": "code", "description": "CoffeeScript", "extensions": ["coffee"], "is_text": true}, "com": {"mime_type": "application/x-dosexec", "group": "executable", "description": null, "extensions": [], "is_text": false}, "cpl": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE Windows executable", "extensions": ["cpl"], "is_text": false}, "cpp": {"mime_type": "text/x-c", "group": "code", "description": "C++ source", "extensions": ["cc", "cpp", "cxx", "c++", "cppm", "ixx"], "is_text": true}, "crt": {"mime_type": "application/x-x509-ca-cert", "group": "text", "description": "Certificates (binary format)", "extensions": ["der", "cer", "crt"], "is_text": false}, "crx": {"mime_type": "application/x-chrome-extension", "group": "executable", "description": "Google Chrome extension", "extensions": ["crx"], "is_text": false}, "cs": {"mime_type": "text/plain", "group": "code", "description": "C# source", "extensions": ["cs", "csx"], "is_text": true}, "csproj": {"mime_type": "text/plain", "group": "code", "description": ".NET project config", "extensions": ["c<PERSON><PERSON><PERSON>"], "is_text": true}, "css": {"mime_type": "text/css", "group": "code", "description": "CSS source", "extensions": ["css"], "is_text": true}, "csv": {"mime_type": "text/csv", "group": "code", "description": "CSV document", "extensions": ["csv"], "is_text": true}, "ctl": {"mime_type": "application/octet-stream", "group": null, "description": null, "extensions": [], "is_text": false}, "dart": {"mime_type": "text/plain", "group": "code", "description": "Dart source", "extensions": ["dart"], "is_text": true}, "deb": {"mime_type": "application/vnd.debian.binary-package", "group": "archive", "description": "Debian binary package", "extensions": ["deb"], "is_text": false}, "dex": {"mime_type": "application/x-android-dex", "group": "executable", "description": "Dalvik dex file", "extensions": ["dex"], "is_text": false}, "dey": {"mime_type": "application/x-android-dey", "group": "executable", "description": "Dalvik dex file", "extensions": [], "is_text": false}, "dicom": {"mime_type": "application/dicom", "group": "image", "description": "DICOM", "extensions": ["dcm"], "is_text": false}, "diff": {"mime_type": "text/plain", "group": "text", "description": "Diff file", "extensions": ["diff", "patch"], "is_text": true}, "directory": {"mime_type": "inode/directory", "group": "inode", "description": "A directory", "extensions": [], "is_text": false}, "django": {"mime_type": "text/x-django", "group": "code", "description": "Django source", "extensions": [], "is_text": false}, "dll": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE Windows executable", "extensions": ["dll"], "is_text": false}, "dm": {"mime_type": "text/plain", "group": "code", "description": "Dream Maker", "extensions": ["dm"], "is_text": true}, "dmigd": {"mime_type": "text/plain", "group": "text", "description": "Dominion Mods", "extensions": ["dm"], "is_text": true}, "dmg": {"mime_type": "application/x-apple-diskimage", "group": "archive", "description": "Apple disk image", "extensions": ["dmg"], "is_text": false}, "dmscript": {"mime_type": "text/plain", "group": "code", "description": "Digital Micrograph Script", "extensions": ["s"], "is_text": true}, "doc": {"mime_type": "application/msword", "group": "document", "description": "Microsoft Word CDF document", "extensions": ["doc"], "is_text": false}, "dockerfile": {"mime_type": "text/x-dockerfile", "group": "code", "description": "Dockerfile", "extensions": [], "is_text": true}, "docx": {"mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "group": "document", "description": "Microsoft Word 2007+ document", "extensions": ["docx", "docm"], "is_text": false}, "dosmbr": {"mime_type": "application/octet-stream", "group": null, "description": "Master boot record", "extensions": [], "is_text": false}, "dotx": {"mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.template", "group": "document", "description": "Office Word 2007 template", "extensions": ["dotx"], "is_text": false}, "dsstore": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Application Desktop Services Store", "extensions": [], "is_text": false}, "dwg": {"mime_type": "image/x-dwg", "group": "image", "description": "Autocad Drawing", "extensions": ["dwg"], "is_text": false}, "dxf": {"mime_type": "image/vnd.dxf", "group": "image", "description": "Audocad Drawing Exchange Format", "extensions": ["dxf"], "is_text": true}, "dylib": {"mime_type": "application/x-mach-o", "group": "executable", "description": "Mach-O executable", "extensions": ["dylib"], "is_text": false}, "ebml": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Extensible Binary Meta Language", "extensions": [], "is_text": false}, "elf": {"mime_type": "application/x-executable-elf", "group": "executable", "description": "ELF executable", "extensions": ["elf"], "is_text": false}, "elixir": {"mime_type": "text/plain", "group": "code", "description": "Elixir script", "extensions": ["exs"], "is_text": true}, "emf": {"mime_type": "application/octet-stream", "group": "application", "description": "Windows Enhanced Metafile image data", "extensions": ["emf"], "is_text": false}, "eml": {"mime_type": "message/rfc822", "group": "text", "description": "RFC 822 mail", "extensions": ["eml"], "is_text": true}, "empty": {"mime_type": "inode/x-empty", "group": "inode", "description": "Empty file", "extensions": [], "is_text": false}, "epub": {"mime_type": "application/epub+zip", "group": "document", "description": "EPUB document", "extensions": ["epub"], "is_text": false}, "erb": {"mime_type": "text/x-ruby", "group": "code", "description": "Embedded Ruby source", "extensions": ["erb"], "is_text": true}, "erlang": {"mime_type": "text/x-erlang", "group": "code", "description": "Erlang source", "extensions": ["erl", "hrl"], "is_text": true}, "ese": {"mime_type": "application/x-ms-ese", "group": null, "description": "ESE Db", "extensions": ["dat"], "is_text": false}, "exe": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE executable", "extensions": ["exe"], "is_text": false}, "exp": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "flac": {"mime_type": "audio/flac", "group": "audio", "description": "FLAC audio bitstream data", "extensions": ["flac"], "is_text": false}, "flutter": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "flv": {"mime_type": "video/x-flv", "group": "video", "description": "Flash Video", "extensions": ["flv"], "is_text": false}, "fortran": {"mime_type": "text/x-fortran", "group": "document", "description": "Fortran", "extensions": ["f90", "f95", "f03", "F90"], "is_text": true}, "fpx": {"mime_type": null, "group": "image", "description": "Flashpix", "extensions": ["fpx"], "is_text": false}, "gemfile": {"mime_type": "text/plain", "group": "code", "description": "Gemfile file", "extensions": [], "is_text": true}, "gemspec": {"mime_type": "text/plain", "group": "code", "description": "Gemspec file", "extensions": ["gemspec"], "is_text": true}, "gif": {"mime_type": "image/gif", "group": "image", "description": "GIF image data", "extensions": ["gif"], "is_text": false}, "gitattributes": {"mime_type": "text/plain", "group": "code", "description": "Gitattributes file", "extensions": [], "is_text": true}, "gitmodules": {"mime_type": "text/plain", "group": "code", "description": "Gitmodules file", "extensions": [], "is_text": true}, "gleam": {"mime_type": null, "group": "code", "description": "Gleam source", "extensions": ["gleam"], "is_text": true}, "go": {"mime_type": "text/x-golang", "group": "code", "description": "Golang source", "extensions": ["go"], "is_text": true}, "gpx": {"mime_type": null, "group": null, "description": "XML document", "extensions": ["gpx"], "is_text": false}, "gradle": {"mime_type": "text/x-groovy", "group": "code", "description": "Gradle source", "extensions": ["gradle"], "is_text": true}, "groovy": {"mime_type": "text/x-groovy", "group": "code", "description": "Groovy source", "extensions": ["groovy"], "is_text": true}, "gzip": {"mime_type": "application/gzip", "group": "archive", "description": "gzip compressed data", "extensions": ["gz", "gzip", "tgz", "tar.gz"], "is_text": false}, "h": {"mime_type": "text/x-c", "group": "code", "description": "C header source", "extensions": ["h"], "is_text": true}, "h5": {"mime_type": "application/x-hdf5", "group": "archive", "description": "Hierarchical Data Format v5", "extensions": ["h5", "hdf5"], "is_text": false}, "handlebars": {"mime_type": "text/x-handlebars-template", "group": "code", "description": "Handlebars source", "extensions": ["hbs", "handlebars"], "is_text": true}, "haskell": {"mime_type": "text/plain", "group": "code", "description": "Haskell source", "extensions": ["hs", "lhs"], "is_text": true}, "hcl": {"mime_type": "text/x-hcl", "group": "code", "description": "HashiCorp configuration language", "extensions": ["hcl"], "is_text": true}, "heif": {"mime_type": "image/heic", "group": "image", "description": "High Efficiency Image File", "extensions": ["heif", "heifs", "heic", "heics"], "is_text": false}, "hfs": {"mime_type": "application/x-hfs", "group": null, "description": null, "extensions": ["hfs"], "is_text": false}, "hlp": {"mime_type": "application/winhlp", "group": "application", "description": "MS Windows help", "extensions": ["hlp"], "is_text": false}, "hpp": {"mime_type": "text/x-h", "group": "code", "description": null, "extensions": ["hh", "hpp", "hxx", "h++"], "is_text": true}, "hta": {"mime_type": "application/hta", "group": "code", "description": "HTML Application", "extensions": ["hta"], "is_text": false}, "htaccess": {"mime_type": "text/x-apache-conf", "group": "code", "description": "Apache access configuration", "extensions": [], "is_text": true}, "html": {"mime_type": "text/html", "group": "code", "description": "HTML document", "extensions": ["html", "htm", "xhtml", "xht"], "is_text": true}, "hve": {"mime_type": null, "group": "unknown", "description": null, "extensions": [], "is_text": false}, "hwp": {"mime_type": "application/x-hwp", "group": "document", "description": "Hangul Word Processor", "extensions": ["hwp"], "is_text": false}, "icc": {"mime_type": "application/vnd.iccprofile", "group": null, "description": "ICC profile", "extensions": ["icc"], "is_text": false}, "icns": {"mime_type": "image/x-icns", "group": "image", "description": "Mac OS X icon", "extensions": ["icns"], "is_text": false}, "ico": {"mime_type": "image/vnd.microsoft.icon", "group": "image", "description": "MS Windows icon resource", "extensions": ["ico"], "is_text": false}, "ics": {"mime_type": "text/calendar", "group": "application", "description": "Internet Calendaring and Scheduling", "extensions": ["ics"], "is_text": true}, "ignorefile": {"mime_type": "text/plain", "group": "code", "description": "Ignorefile", "extensions": [], "is_text": true}, "img": {"mime_type": null, "group": null, "description": null, "extensions": ["img"], "is_text": false}, "ini": {"mime_type": "text/plain", "group": "text", "description": "INI configuration file", "extensions": ["ini"], "is_text": true}, "internetshortcut": {"mime_type": "application/x-mswinurl", "group": "application", "description": "MS Windows Internet shortcut", "extensions": ["url"], "is_text": true}, "iosapp": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "ipynb": {"mime_type": "application/json", "group": "code", "description": "<PERSON><PERSON><PERSON> notebook", "extensions": ["ipynb"], "is_text": true}, "iso": {"mime_type": "application/x-iso9660-image", "group": "archive", "description": "ISO 9660 CD-ROM filesystem data", "extensions": ["iso"], "is_text": false}, "jar": {"mime_type": "application/java-archive", "group": "archive", "description": "Java archive data (JAR)", "extensions": ["jar", "klib"], "is_text": false}, "java": {"mime_type": "text/x-java", "group": "code", "description": "Java source", "extensions": ["java"], "is_text": true}, "javabytecode": {"mime_type": "application/x-java-applet", "group": "executable", "description": "Java compiled bytecode", "extensions": ["class"], "is_text": false}, "javascript": {"mime_type": "application/javascript", "group": "code", "description": "JavaScript source", "extensions": ["js", "mjs", "cjs"], "is_text": true}, "jinja": {"mime_type": "text/x-jinja2-template", "group": "code", "description": "<PERSON><PERSON>", "extensions": ["jinja", "jinja2", "j2"], "is_text": true}, "jng": {"mime_type": "image/jng", "group": "image", "description": "JPEG network graphics", "extensions": ["jng"], "is_text": false}, "jnlp": {"mime_type": "application/x-java-jnlp-file", "group": "code", "description": "Java Network Launch Protocol", "extensions": ["jnlp"], "is_text": true}, "jp2": {"mime_type": "image/jpeg2000", "group": "image", "description": "jpeg2000", "extensions": ["jp2"], "is_text": false}, "jpeg": {"mime_type": "image/jpeg", "group": "image", "description": "JPEG image data", "extensions": ["jpg", "jpeg"], "is_text": false}, "json": {"mime_type": "application/json", "group": "code", "description": "JSON document", "extensions": ["json"], "is_text": true}, "jsonc": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "jsonl": {"mime_type": "application/json", "group": "code", "description": "JSONL document", "extensions": ["jsonl", "j<PERSON>ld"], "is_text": true}, "jsx": {"mime_type": "application/javascript", "group": "code", "description": "JSX source", "extensions": ["jsx", "mjsx", "cjsx"], "is_text": true}, "julia": {"mime_type": "text/x-julia", "group": "code", "description": "Julia source", "extensions": ["jl"], "is_text": true}, "jxl": {"mime_type": "image/jxl", "group": "image", "description": "JPEG XL", "extensions": ["jxl"], "is_text": false}, "ko": {"mime_type": "application/x-executable-elf", "group": "executable", "description": "ELF executable, kernel object", "extensions": ["ko"], "is_text": false}, "kotlin": {"mime_type": "text/plain", "group": "code", "description": "Kotlin source", "extensions": ["kt", "kts"], "is_text": true}, "ks": {"mime_type": null, "group": null, "description": "Tyrano", "extensions": ["ks"], "is_text": true}, "latex": {"mime_type": "text/x-tex", "group": "text", "description": "LaTeX document", "extensions": ["tex", "sty"], "is_text": true}, "latexaux": {"mime_type": null, "group": null, "description": null, "extensions": ["aux"], "is_text": false}, "less": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "lha": {"mime_type": "application/x-lha", "group": "archive", "description": "LHarc archive", "extensions": ["lha", "lzh"], "is_text": false}, "license": {"mime_type": "text/plain", "group": "text", "description": "License file", "extensions": [], "is_text": true}, "lisp": {"mime_type": "text/x-lisp", "group": "code", "description": "Lisp source", "extensions": ["lisp", "lsp", "l", "cl"], "is_text": true}, "litcs": {"mime_type": null, "group": null, "description": "Literate CS", "extensions": ["litcoffee"], "is_text": false}, "lnk": {"mime_type": "application/x-ms-shortcut", "group": "application", "description": "MS Windows shortcut", "extensions": ["lnk"], "is_text": false}, "lock": {"mime_type": "text/plain", "group": "application", "description": "Lock file", "extensions": ["lock"], "is_text": true}, "lrz": {"mime_type": "application/x-lrzip", "group": null, "description": "LRZip", "extensions": ["lrz"], "is_text": false}, "lua": {"mime_type": "text/plain", "group": "code", "description": "<PERSON><PERSON>", "extensions": ["lua"], "is_text": true}, "lz": {"mime_type": "application/x-lzip", "group": "archive", "description": "LZip", "extensions": ["lz"], "is_text": false}, "lz4": {"mime_type": "application/x-lz4", "group": "archive", "description": "LZ4", "extensions": ["lz4"], "is_text": false}, "lzx": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "m3u": {"mime_type": "text/plain", "group": "application", "description": "M3U playlist", "extensions": ["m3u8", "m3u"], "is_text": true}, "m4": {"mime_type": "text/plain", "group": "code", "description": "GNU Macro", "extensions": ["m4"], "is_text": true}, "macho": {"mime_type": "application/x-mach-o", "group": "executable", "description": "Mach-O executable", "extensions": [], "is_text": false}, "maff": {"mime_type": "application/x-maff", "group": null, "description": null, "extensions": ["maff"], "is_text": false}, "makefile": {"mime_type": "text/x-makefile", "group": "code", "description": "Makefile source", "extensions": [], "is_text": true}, "markdown": {"mime_type": "text/markdown", "group": "text", "description": "Markdown document", "extensions": ["md", "markdown"], "is_text": true}, "matlab": {"mime_type": "text/x-matlab", "group": "code", "description": "Matlab Source", "extensions": ["m", "matlab"], "is_text": true}, "mht": {"mime_type": "application/x-mimearchive", "group": "code", "description": "MHTML document", "extensions": ["mht"], "is_text": true}, "midi": {"mime_type": "audio/midi", "group": "audio", "description": "Midi", "extensions": ["mid"], "is_text": false}, "mkv": {"mime_type": "video/x-matroska", "group": "video", "description": "<PERSON><PERSON><PERSON>", "extensions": ["mkv"], "is_text": false}, "mp2": {"mime_type": null, "group": null, "description": "MP2 stream", "extensions": ["mp2"], "is_text": false}, "mp3": {"mime_type": "audio/mpeg", "group": "audio", "description": "MP3 media file", "extensions": ["mp3"], "is_text": false}, "mp4": {"mime_type": "video/mp4", "group": "video", "description": "MP4 media file", "extensions": ["mp4"], "is_text": false}, "mpegts": {"mime_type": "video/MP2T", "group": "video", "description": "MPEG Transport stream", "extensions": ["ts", "tsv", "tsa", "m2t"], "is_text": false}, "mscompress": {"mime_type": "application/x-ms-compress-szdd", "group": "archive", "description": "MS Compress archive data", "extensions": [], "is_text": false}, "msi": {"mime_type": "application/x-msi", "group": "archive", "description": "Microsoft Installer file", "extensions": ["msi"], "is_text": false}, "msix": {"mime_type": "application/msix", "group": "application", "description": "Windows app package", "extensions": ["msix"], "is_text": false}, "mst": {"mime_type": null, "group": null, "description": null, "extensions": ["mst"], "is_text": false}, "mui": {"mime_type": "application/x-dosexec", "group": "application", "description": "PE Windows executable", "extensions": ["mui"], "is_text": false}, "mum": {"mime_type": "text/xml", "group": "application", "description": "Windows Update Package file", "extensions": ["mum"], "is_text": true}, "mun": {"mime_type": null, "group": null, "description": null, "extensions": ["mun"], "is_text": false}, "nim": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "npy": {"mime_type": "application/octet-stream", "group": "archive", "description": "<PERSON><PERSON><PERSON>", "extensions": ["npy"], "is_text": false}, "npz": {"mime_type": "application/octet-stream", "group": "archive", "description": "Numpy Arrays Archive", "extensions": ["npz"], "is_text": false}, "null": {"mime_type": null, "group": null, "description": null, "extensions": ["null"], "is_text": false}, "nupkg": {"mime_type": "application/octet-stream", "group": null, "description": "NuGet Package", "extensions": ["nupkg"], "is_text": false}, "object": {"mime_type": null, "group": null, "description": null, "extensions": ["o"], "is_text": false}, "objectivec": {"mime_type": "text/x-objcsrc", "group": "code", "description": "ObjectiveC source", "extensions": ["m", "mm"], "is_text": true}, "ocaml": {"mime_type": "text-ocaml", "group": "code", "description": "OCaml", "extensions": ["ml", "mli"], "is_text": true}, "ocx": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE Windows executable", "extensions": ["ocx"], "is_text": false}, "odex": {"mime_type": "application/x-executable-elf", "group": "executable", "description": "ODEX ELF executable", "extensions": ["odex"], "is_text": false}, "odin": {"mime_type": null, "group": "code", "description": "<PERSON><PERSON>", "extensions": ["odin"], "is_text": true}, "odp": {"mime_type": "application/vnd.oasis.opendocument.presentation", "group": "document", "description": "OpenDocument Presentation", "extensions": ["odp"], "is_text": false}, "ods": {"mime_type": "application/vnd.oasis.opendocument.spreadsheet", "group": "document", "description": "OpenDocument Spreadsheet", "extensions": ["ods"], "is_text": false}, "odt": {"mime_type": "application/vnd.oasis.opendocument.text", "group": "document", "description": "OpenDocument Text", "extensions": ["odt"], "is_text": false}, "ogg": {"mime_type": "audio/ogg", "group": "audio", "description": "Ogg data", "extensions": ["ogg"], "is_text": false}, "ole": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "one": {"mime_type": "application/msonenote", "group": "document", "description": "One Note", "extensions": ["one"], "is_text": false}, "onnx": {"mime_type": "application/octet-stream", "group": "archive", "description": "Open Neural Network Exchange", "extensions": ["onnx"], "is_text": false}, "ooxml": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "otf": {"mime_type": "font/otf", "group": "font", "description": "OpenType font", "extensions": ["otf"], "is_text": false}, "outlook": {"mime_type": "application/vnd.ms-outlook", "group": "application", "description": "MS Outlook Message", "extensions": [], "is_text": false}, "palmos": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "parquet": {"mime_type": "application/vnd.apache.parquet", "group": "unknown", "description": "Apache Parquet", "extensions": ["pqt", "parquet"], "is_text": false}, "pascal": {"mime_type": "text/x-pascal", "group": "code", "description": "Pascal source", "extensions": ["pas", "pp"], "is_text": true}, "pbm": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "pcap": {"mime_type": "application/vnd.tcpdump.pcap", "group": "application", "description": "pcap capture file", "extensions": ["pcap", "pcapng"], "is_text": false}, "pdb": {"mime_type": "application/octet-stream", "group": "application", "description": "Windows Program Database", "extensions": ["pdb"], "is_text": false}, "pdf": {"mime_type": "application/pdf", "group": "document", "description": "PDF document", "extensions": ["pdf"], "is_text": false}, "pebin": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE Windows executable", "extensions": ["exe", "dll"], "is_text": false}, "pem": {"mime_type": "application/x-pem-file", "group": "application", "description": "PEM certificate", "extensions": ["pem", "pub", "gpg"], "is_text": true}, "perl": {"mime_type": "text/x-perl", "group": "code", "description": "Perl source", "extensions": ["pl"], "is_text": true}, "pgp": {"mime_type": "application/pgp-keys", "group": null, "description": "PGP", "extensions": ["gpg", "pgp"], "is_text": false}, "php": {"mime_type": "text/x-php", "group": "code", "description": "PHP source", "extensions": ["php"], "is_text": true}, "pickle": {"mime_type": "application/octet-stream", "group": "application", "description": "Python pickle", "extensions": ["pickle", "pkl"], "is_text": false}, "png": {"mime_type": "image/png", "group": "image", "description": "PNG image", "extensions": ["png"], "is_text": false}, "po": {"mime_type": "text/gettext-translation", "group": "application", "description": "Portable Object (PO) for i18n", "extensions": ["po"], "is_text": true}, "postscript": {"mime_type": "application/postscript", "group": "document", "description": "PostScript document", "extensions": ["ps"], "is_text": false}, "powershell": {"mime_type": "application/x-powershell", "group": "code", "description": "Powershell source", "extensions": ["ps1"], "is_text": true}, "ppt": {"mime_type": "application/vnd.ms-powerpoint", "group": "document", "description": "Microsoft PowerPoint CDF document", "extensions": ["ppt"], "is_text": false}, "pptx": {"mime_type": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "group": "document", "description": "Microsoft PowerPoint 2007+ document", "extensions": ["pptx", "pptm"], "is_text": false}, "printfox": {"mime_type": null, "group": null, "description": "c64", "extensions": [], "is_text": false}, "prolog": {"mime_type": "text/x-prolog", "group": "code", "description": "Prolog source", "extensions": ["pl", "pro", "P"], "is_text": true}, "proteindb": {"mime_type": "application/octet-stream", "group": "application", "description": "Protein DB", "extensions": ["pdb"], "is_text": true}, "proto": {"mime_type": "text/x-proto", "group": "code", "description": "Protocol buffer definition", "extensions": ["proto"], "is_text": true}, "protobuf": {"mime_type": "application/protobuf", "group": "unknown", "description": "Protocol buffers", "extensions": ["protobuf", "pb"], "is_text": false}, "psd": {"mime_type": "image/vnd.adobe.photoshop", "group": "image", "description": "Adobe Photoshop", "extensions": ["psd"], "is_text": false}, "pytorch": {"mime_type": "application/octet-stream", "group": "application", "description": "Pytorch storage file", "extensions": ["pt", "pth"], "is_text": false}, "pub": {"mime_type": "application/x-mspublisher", "group": null, "description": null, "extensions": ["pub"], "is_text": false}, "python": {"mime_type": "text/x-python", "group": "code", "description": "Python source", "extensions": ["py", "pyi"], "is_text": true}, "pythonbytecode": {"mime_type": "application/x-bytecode.python", "group": "executable", "description": "Python compiled bytecode", "extensions": ["pyc", "pyo"], "is_text": false}, "pythonpar": {"mime_type": null, "group": null, "description": null, "extensions": ["par"], "is_text": false}, "qoi": {"mime_type": "image/x-qoi", "group": "image", "description": "Quite Ok Image", "extensions": ["qoi"], "is_text": false}, "qt": {"mime_type": "video/quicktime", "group": "video", "description": "QuickTime", "extensions": ["mov"], "is_text": false}, "r": {"mime_type": "text/x-R", "group": "code", "description": "R (language)", "extensions": ["R"], "is_text": true}, "randomascii": {"mime_type": "text/plain", "group": "text", "description": "Random ASCII characters", "extensions": [], "is_text": true}, "randombytes": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Random bytes", "extensions": [], "is_text": false}, "randomtxt": {"mime_type": "text/plain", "group": "text", "description": "Random text", "extensions": [], "is_text": true}, "rar": {"mime_type": "application/x-rar", "group": "archive", "description": "RAR archive data", "extensions": ["rar"], "is_text": false}, "rdf": {"mime_type": "application/rdf+xml", "group": "text", "description": "Resource Description Framework document (RDF)", "extensions": ["rdf"], "is_text": true}, "rdp": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "riff": {"mime_type": "application/x-riff", "group": null, "description": null, "extensions": [], "is_text": false}, "rlib": {"mime_type": "application/x-archive", "group": "archive", "description": "rust library", "extensions": ["rlib"], "is_text": false}, "rll": {"mime_type": null, "group": "executable", "description": "Resource Library", "extensions": ["rll"], "is_text": false}, "rpm": {"mime_type": "application/x-rpm", "group": "archive", "description": "RedHat Package Manager archive (RPM)", "extensions": ["rpm"], "is_text": false}, "rst": {"mime_type": "text/x-rst", "group": "text", "description": "ReStructuredText document", "extensions": ["rst"], "is_text": true}, "rtf": {"mime_type": "text/rtf", "group": "text", "description": "Rich Text Format document", "extensions": ["rtf"], "is_text": true}, "ruby": {"mime_type": "application/x-ruby", "group": "code", "description": "Ruby source", "extensions": ["rb"], "is_text": true}, "rust": {"mime_type": "application/x-rust", "group": "code", "description": "Rust source", "extensions": ["rs"], "is_text": true}, "rzip": {"mime_type": null, "group": null, "description": "Rzip", "extensions": ["rz"], "is_text": false}, "scala": {"mime_type": "application/x-scala", "group": "code", "description": "Scala source", "extensions": ["scala"], "is_text": true}, "scheme": {"mime_type": "text/x-scheme", "group": "code", "description": null, "extensions": ["scm", "ss"], "is_text": false}, "scr": {"mime_type": "application/x-dosexec", "group": "executable", "description": "PE Windows executable", "extensions": ["scr"], "is_text": false}, "scriptwsf": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "scss": {"mime_type": "text/x-scss", "group": "code", "description": "SCSS source", "extensions": ["scss"], "is_text": true}, "sevenzip": {"mime_type": "application/x-7z-compressed", "group": "archive", "description": "7-zip archive data", "extensions": ["7z"], "is_text": false}, "sgml": {"mime_type": "application/sgml", "group": "text", "description": "sgml", "extensions": ["sgml"], "is_text": true}, "sh3d": {"mime_type": null, "group": null, "description": null, "extensions": ["sh3d"], "is_text": false}, "shell": {"mime_type": "text/x-shellscript", "group": "code", "description": "Shell script", "extensions": ["sh"], "is_text": true}, "smali": {"mime_type": "application/x-smali", "group": "code", "description": "Smali source", "extensions": ["smali"], "is_text": true}, "snap": {"mime_type": "application/octet-stream", "group": "archive", "description": "Snap archive", "extensions": ["snap"], "is_text": false}, "so": {"mime_type": "application/x-executable-elf", "group": "executable", "description": "ELF executable, shared library", "extensions": ["so"], "is_text": false}, "solidity": {"mime_type": null, "group": "code", "description": "Solidity source", "extensions": ["sol"], "is_text": true}, "sql": {"mime_type": "application/x-sql", "group": "code", "description": "SQL source", "extensions": ["sql"], "is_text": true}, "sqlite": {"mime_type": null, "group": "application", "description": "SQLITE database", "extensions": ["sqlite", "sqlite3"], "is_text": false}, "squashfs": {"mime_type": "application/octet-stream", "group": "archive", "description": "Squash filesystem", "extensions": [], "is_text": false}, "srt": {"mime_type": "text/srt", "group": "application", "description": "SubRip Text Format", "extensions": ["srt"], "is_text": true}, "stlbinary": {"mime_type": "application/sla", "group": "image", "description": "Stereolithography CAD (binary)", "extensions": ["stl"], "is_text": false}, "stltext": {"mime_type": "application/sla", "group": "image", "description": "Stereolithography CAD (text)", "extensions": ["stl"], "is_text": true}, "sum": {"mime_type": null, "group": "application", "description": "Checksum file", "extensions": ["sum"], "is_text": true}, "svd": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "svg": {"mime_type": "image/svg+xml", "group": "image", "description": "SVG Scalable Vector Graphics image data", "extensions": ["svg"], "is_text": true}, "swf": {"mime_type": "application/x-shockwave-flash", "group": "executable", "description": "Small Web File", "extensions": ["swf"], "is_text": false}, "swift": {"mime_type": "text/x-swift", "group": "code", "description": "Swift", "extensions": ["swift"], "is_text": true}, "symlink": {"mime_type": "inode/symlink", "group": "inode", "description": "Symbolic link", "extensions": [], "is_text": false}, "symlinktext": {"mime_type": "text/plain", "group": "application", "description": "Symbolic link (textual representation)", "extensions": [], "is_text": true}, "sys": {"mime_type": "application/x-windows-driver", "group": "executable", "description": "PE Windows executable", "extensions": ["sys"], "is_text": false}, "tar": {"mime_type": "application/x-tar", "group": "archive", "description": "POSIX tar archive", "extensions": ["tar"], "is_text": false}, "tcl": {"mime_type": "application/x-tcl", "group": "code", "description": "Tickle", "extensions": ["tcl"], "is_text": true}, "textproto": {"mime_type": "text/plain", "group": "code", "description": "Text protocol buffer", "extensions": ["textproto", "textpb", "pbtxt"], "is_text": true}, "tga": {"mime_type": "image/x-tga", "group": "image", "description": "Targa image data", "extensions": ["tga"], "is_text": false}, "thumbsdb": {"mime_type": "image/vnd.ms-thumb", "group": "application", "description": "Windows thumbnail cache", "extensions": [], "is_text": false}, "tiff": {"mime_type": "image/tiff", "group": "image", "description": "TIFF image data", "extensions": ["tiff", "tif"], "is_text": false}, "tmdx": {"mime_type": null, "group": null, "description": null, "extensions": ["tmdx", "tmvx"], "is_text": false}, "toml": {"mime_type": "application/toml", "group": "text", "description": "<PERSON>'s obvious, minimal language", "extensions": ["toml"], "is_text": true}, "torrent": {"mime_type": "application/x-bittorrent", "group": "application", "description": "BitTorrent file", "extensions": ["torrent"], "is_text": false}, "troff": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "tsv": {"mime_type": "text/tsv", "group": "code", "description": "TSV document", "extensions": ["tsv"], "is_text": true}, "tsx": {"mime_type": "text/x-typescript", "group": "code", "description": "TSX source", "extensions": ["tsx", "mtsx", "ctsx"], "is_text": true}, "ttf": {"mime_type": "font/sfnt", "group": "font", "description": "TrueType Font data", "extensions": ["ttf", "ttc"], "is_text": false}, "twig": {"mime_type": "text/x-twig", "group": "code", "description": "Twig template", "extensions": ["twig"], "is_text": true}, "txt": {"mime_type": "text/plain", "group": "text", "description": "Generic text document", "extensions": ["txt"], "is_text": true}, "txtascii": {"mime_type": "text/plain", "group": "text", "description": "Generic text document encoded in ASCII", "extensions": ["txt"], "is_text": true}, "txtutf16": {"mime_type": "text/plain", "group": "text", "description": "Generic text document encoded in UTF-16", "extensions": ["txt"], "is_text": true}, "txtutf8": {"mime_type": "text/plain", "group": "text", "description": "Generic text document encoded in UTF-8", "extensions": ["txt"], "is_text": true}, "typescript": {"mime_type": "application/typescript", "group": "code", "description": "TypeScript source", "extensions": ["ts", "mts", "cts"], "is_text": true}, "udf": {"mime_type": "application/x-udf-image", "group": null, "description": "Universal Disc Format", "extensions": [], "is_text": false}, "undefined": {"mime_type": "application/undefined", "group": "undefined", "description": "Undefined", "extensions": [], "is_text": false}, "unixcompress": {"mime_type": "application/x-compress", "group": null, "description": null, "extensions": ["z"], "is_text": false}, "unknown": {"mime_type": "application/octet-stream", "group": "unknown", "description": "Unknown binary data", "extensions": [], "is_text": false}, "vba": {"mime_type": "text/vbscript", "group": "code", "description": "MS Visual Basic source (VBA)", "extensions": ["vbs", "vba", "vb"], "is_text": true}, "vbe": {"mime_type": null, "group": "code", "description": "EncryptedVBS", "extensions": ["vbe"], "is_text": false}, "vcard": {"mime_type": "text/vcard", "group": null, "description": null, "extensions": ["vcard"], "is_text": false}, "vcs": {"mime_type": null, "group": null, "description": null, "extensions": [], "is_text": false}, "vcxproj": {"mime_type": "application/xml", "group": "code", "description": "Visual Studio MSBuild project", "extensions": ["vcxproj"], "is_text": true}, "verilog": {"mime_type": "text/x-verilog", "group": "code", "description": "Verilog source", "extensions": ["v", "verilog", "vlg", "vh"], "is_text": true}, "vhd": {"mime_type": "application/x-vhd", "group": null, "description": "Virtual Hard Disk", "extensions": [], "is_text": false}, "vhdl": {"mime_type": "text/x-vhdl", "group": "code", "description": "VHDL source", "extensions": ["vhd"], "is_text": true}, "visio": {"mime_type": "application/vnd.ms-visio.drawing.main+xml", "group": "document", "description": "Microsoft Visio", "extensions": ["vsd", "vsdm", "vsdx", "vdw"], "is_text": false}, "vtt": {"mime_type": "text/vtt", "group": "text", "description": "Web Video Text Tracks", "extensions": ["vtt", "webvtt"], "is_text": true}, "vue": {"mime_type": "application/javascript", "group": "code", "description": "Vue source", "extensions": ["vue"], "is_text": true}, "wad": {"mime_type": "application/wad", "group": "archive", "description": "WAD", "extensions": ["wad"], "is_text": false}, "wasm": {"mime_type": "application/wasm", "group": "executable", "description": "Web Assembly", "extensions": ["wasm"], "is_text": false}, "wav": {"mime_type": "audio/x-wav", "group": "audio", "description": "Waveform Audio file (WAV)", "extensions": ["wav"], "is_text": false}, "webm": {"mime_type": "video/webm", "group": "video", "description": "WebM media file", "extensions": ["webm"], "is_text": false}, "webp": {"mime_type": "image/webp", "group": "image", "description": "WebP media file", "extensions": ["webp"], "is_text": false}, "webtemplate": {"mime_type": "text/plain", "group": "code", "description": "Web templating language", "extensions": [], "is_text": true}, "wim": {"mime_type": "application/x-ms-wim", "group": "unknown", "description": "Windows Imaging Format", "extensions": ["wim", "swm", "esd"], "is_text": false}, "winregistry": {"mime_type": "text/x-ms-regedit", "group": "application", "description": "Windows Registry text", "extensions": ["reg"], "is_text": true}, "wma": {"mime_type": "audio/x-ms-wma", "group": "audio", "description": "Windows Media Audio", "extensions": ["wma"], "is_text": false}, "wmf": {"mime_type": "image/wmf", "group": "image", "description": "Windows metafile", "extensions": ["wmf"], "is_text": false}, "wmv": {"mime_type": "video/x-ms-wmv", "group": "video", "description": "Windows Media Video", "extensions": ["wmv"], "is_text": false}, "woff": {"mime_type": "font/woff", "group": "font", "description": "Web Open Font Format", "extensions": ["woff"], "is_text": false}, "woff2": {"mime_type": "font/woff2", "group": "font", "description": "Web Open Font Format v2", "extensions": ["woff2"], "is_text": false}, "xar": {"mime_type": "application/x-xar", "group": "archive", "description": "XAR archive compressed data", "extensions": ["pkg", "xar"], "is_text": false}, "xcf": {"mime_type": "image/x-xcf", "group": "image", "description": "Gimp image", "extensions": ["xcf"], "is_text": false}, "xls": {"mime_type": "application/vnd.ms-excel", "group": "document", "description": "Microsoft Excel CDF document", "extensions": ["xls"], "is_text": false}, "xlsb": {"mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "group": "document", "description": "Microsoft Excel 2007+ document (binary format)", "extensions": ["xlsb"], "is_text": false}, "xlsx": {"mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "group": "document", "description": "Microsoft Excel 2007+ document", "extensions": ["xlsx", "xlsm"], "is_text": false}, "xml": {"mime_type": "text/xml", "group": "code", "description": "XML document", "extensions": ["xml"], "is_text": true}, "xpi": {"mime_type": "application/zip", "group": "archive", "description": "Compressed installation archive (XPI)", "extensions": ["xpi"], "is_text": false}, "xsd": {"mime_type": null, "group": null, "description": null, "extensions": ["xsd"], "is_text": false}, "xz": {"mime_type": "application/x-xz", "group": "archive", "description": "XZ compressed data", "extensions": ["xz"], "is_text": false}, "yaml": {"mime_type": "application/x-yaml", "group": "code", "description": "YAML source", "extensions": ["yml", "yaml"], "is_text": true}, "yara": {"mime_type": "text/x-yara", "group": "code", "description": "YARA rule", "extensions": ["yar", "yara"], "is_text": true}, "zig": {"mime_type": "text/zig", "group": "code", "description": "Zig source", "extensions": ["zig"], "is_text": true}, "zip": {"mime_type": "application/zip", "group": "archive", "description": "Zip archive data", "extensions": ["zip"], "is_text": false}, "zlibstream": {"mime_type": "application/zlib", "group": "application", "description": "zlib compressed data", "extensions": [], "is_text": false}, "zst": {"mime_type": "application/zstd", "group": "archive", "description": "Zstandard", "extensions": ["zst"], "is_text": false}}