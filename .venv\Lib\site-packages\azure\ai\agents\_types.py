# pylint: disable=line-too-long,useless-suppression
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import List, TYPE_CHECKING, Union

if TYPE_CHECKING:
    from . import models as _models
MessageInputContent = Union[str, List["_models.MessageInputContentBlock"]]
MessageAttachmentToolDefinition = Union["_models.CodeInterpreterToolDefinition", "_models.FileSearchToolDefinition"]
AgentsToolChoiceOption = Union[str, str, "_models.AgentsToolChoiceOptionMode", "_models.AgentsNamedToolChoice"]
AgentsResponseFormatOption = Union[
    str, str, "_models.AgentsResponseFormatMode", "_models.AgentsResponseFormat", "_models.ResponseFormatJsonSchemaType"
]
