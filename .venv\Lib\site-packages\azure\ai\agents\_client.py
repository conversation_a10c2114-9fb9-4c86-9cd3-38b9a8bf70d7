# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from copy import deepcopy
from typing import Any, TYPE_CHECKING
from typing_extensions import Self

from azure.core import PipelineClient
from azure.core.pipeline import policies
from azure.core.rest import HttpRequest, HttpResponse

from ._configuration import AgentsClientConfiguration
from ._utils.serialization import Deserializer, Serializer
from .operations import (
    AgentsClientOperationsMixin,
    FilesOperations,
    MessagesOperations,
    RunStepsOperations,
    RunsOperations,
    ThreadsOperations,
    VectorStoreFileBatchesOperations,
    VectorStoreFilesOperations,
    VectorStoresOperations,
)

if TYPE_CHECKING:
    from azure.core.credentials import TokenCredential


class AgentsClient(AgentsClientOperationsMixin):  # pylint: disable=too-many-instance-attributes
    """AgentsClient.

    :ivar threads: ThreadsOperations operations
    :vartype threads: azure.ai.agents.operations.ThreadsOperations
    :ivar messages: MessagesOperations operations
    :vartype messages: azure.ai.agents.operations.MessagesOperations
    :ivar runs: RunsOperations operations
    :vartype runs: azure.ai.agents.operations.RunsOperations
    :ivar run_steps: RunStepsOperations operations
    :vartype run_steps: azure.ai.agents.operations.RunStepsOperations
    :ivar files: FilesOperations operations
    :vartype files: azure.ai.agents.operations.FilesOperations
    :ivar vector_stores: VectorStoresOperations operations
    :vartype vector_stores: azure.ai.agents.operations.VectorStoresOperations
    :ivar vector_store_files: VectorStoreFilesOperations operations
    :vartype vector_store_files: azure.ai.agents.operations.VectorStoreFilesOperations
    :ivar vector_store_file_batches: VectorStoreFileBatchesOperations operations
    :vartype vector_store_file_batches: azure.ai.agents.operations.VectorStoreFileBatchesOperations
    :param endpoint: Project endpoint in the form of:
     https://<aiservices-id>.services.ai.azure.com/api/projects/<project-name>. Required.
    :type endpoint: str
    :param credential: Credential used to authenticate requests to the service. Required.
    :type credential: ~azure.core.credentials.TokenCredential
    :keyword api_version: The API version to use for this operation. Default value is
     "2025-05-15-preview". Note that overriding this default value may result in unsupported
     behavior.
    :paramtype api_version: str
    """

    def __init__(self, endpoint: str, credential: "TokenCredential", **kwargs: Any) -> None:
        _endpoint = "{endpoint}"
        self._config = AgentsClientConfiguration(endpoint=endpoint, credential=credential, **kwargs)

        _policies = kwargs.pop("policies", None)
        if _policies is None:
            _policies = [
                policies.RequestIdPolicy(**kwargs),
                self._config.headers_policy,
                self._config.user_agent_policy,
                self._config.proxy_policy,
                policies.ContentDecodePolicy(**kwargs),
                self._config.redirect_policy,
                self._config.retry_policy,
                self._config.authentication_policy,
                self._config.custom_hook_policy,
                self._config.logging_policy,
                policies.DistributedTracingPolicy(**kwargs),
                policies.SensitiveHeaderCleanupPolicy(**kwargs) if self._config.redirect_policy else None,
                self._config.http_logging_policy,
            ]
        self._client: PipelineClient = PipelineClient(base_url=_endpoint, policies=_policies, **kwargs)

        self._serialize = Serializer()
        self._deserialize = Deserializer()
        self._serialize.client_side_validation = False
        self.threads = ThreadsOperations(self._client, self._config, self._serialize, self._deserialize)
        self.messages = MessagesOperations(self._client, self._config, self._serialize, self._deserialize)
        self.runs = RunsOperations(self._client, self._config, self._serialize, self._deserialize)
        self.run_steps = RunStepsOperations(self._client, self._config, self._serialize, self._deserialize)
        self.files = FilesOperations(self._client, self._config, self._serialize, self._deserialize)
        self.vector_stores = VectorStoresOperations(self._client, self._config, self._serialize, self._deserialize)
        self.vector_store_files = VectorStoreFilesOperations(
            self._client, self._config, self._serialize, self._deserialize
        )
        self.vector_store_file_batches = VectorStoreFileBatchesOperations(
            self._client, self._config, self._serialize, self._deserialize
        )

    def send_request(self, request: HttpRequest, *, stream: bool = False, **kwargs: Any) -> HttpResponse:
        """Runs the network request through the client's chained policies.

        >>> from azure.core.rest import HttpRequest
        >>> request = HttpRequest("GET", "https://www.example.org/")
        <HttpRequest [GET], url: 'https://www.example.org/'>
        >>> response = client.send_request(request)
        <HttpResponse: 200 OK>

        For more information on this code flow, see https://aka.ms/azsdk/dpcodegen/python/send_request

        :param request: The network request you want to make. Required.
        :type request: ~azure.core.rest.HttpRequest
        :keyword bool stream: Whether the response payload will be streamed. Defaults to False.
        :return: The response of your network call. Does not do error handling on your response.
        :rtype: ~azure.core.rest.HttpResponse
        """

        request_copy = deepcopy(request)
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }

        request_copy.url = self._client.format_url(request_copy.url, **path_format_arguments)
        return self._client.send_request(request_copy, stream=stream, **kwargs)  # type: ignore

    def close(self) -> None:
        self._client.close()

    def __enter__(self) -> Self:
        self._client.__enter__()
        return self

    def __exit__(self, *exc_details: Any) -> None:
        self._client.__exit__(*exc_details)
