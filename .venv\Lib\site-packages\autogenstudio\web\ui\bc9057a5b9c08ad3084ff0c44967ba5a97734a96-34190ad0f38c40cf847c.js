"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[281],{2605:function(e,t,n){n.d(t,{A:function(){return ot}});var r=n(4241),o=n(436),l=n(6540),i=n(6942),a=n.n(i),s=n(754),c=n(3723),u=n(934);function d(e){const[t,n]=l.useState(e);return l.useEffect((()=>{const t=setTimeout((()=>{n(e)}),e.length?0:10);return()=>{clearTimeout(t)}}),[e]),t}var f=n(2187),m=n(5905),p=n(9077),g=n(977),h=n(4277),b=n(7358);var $=e=>{const{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},\n                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},\n                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}};const y=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,f.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${(0,f.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),v=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},x=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,m.dF)(e)),y(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},v(e,e.controlHeightSM)),"&-large":Object.assign({},v(e,e.controlHeightLG))})}},w=e=>{const{formItemCls:t,iconCls:n,componentCls:r,rootPrefixCls:o,antCls:l,labelRequiredMarkColor:i,labelColor:a,labelFontSize:s,labelHeight:c,labelColonMarginInlineStart:u,labelColonMarginInlineEnd:d,itemMarginBottom:f}=e;return{[t]:Object.assign(Object.assign({},(0,m.dF)(e)),{marginBottom:f,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,\n        &-hidden${l}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset"},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:c,color:a,fontSize:s,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required:not(${t}-required-mark-optional)::before`]:{display:"inline-block",marginInlineEnd:e.marginXXS,color:i,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"',[`${r}-hide-required-mark &`]:{display:"none"}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`${r}-hide-required-mark &`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:u,marginInlineEnd:d},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${o}-col-'"]):not([class*="' ${o}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:p.nF,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},O=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},C=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,\n        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},E=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),j=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:E(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},S=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,\n        ${r}-col-24${n}-label,\n        ${r}-col-xl-24${n}-label`]:E(e)}},[`@media (max-width: ${(0,f.zA)(e.screenXSMax)})`]:[j(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:E(e)}}}],[`@media (max-width: ${(0,f.zA)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:E(e)}}},[`@media (max-width: ${(0,f.zA)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:E(e)}}},[`@media (max-width: ${(0,f.zA)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:E(e)}}}}},M=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,\n      ${n}-col-24${t}-label,\n      ${n}-col-xl-24${t}-label`]:E(e),[`@media (max-width: ${(0,f.zA)(e.screenXSMax)})`]:[j(e),{[t]:{[`${n}-col-xs-24${t}-label`]:E(e)}}],[`@media (max-width: ${(0,f.zA)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:E(e)}},[`@media (max-width: ${(0,f.zA)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:E(e)}},[`@media (max-width: ${(0,f.zA)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:E(e)}}}},k=(e,t)=>(0,h.oX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t});var A=(0,b.OF)("Form",((e,t)=>{let{rootPrefixCls:n}=t;const r=k(e,n);return[x(r),w(r),$(r),O(r,r.componentCls),O(r,r.formItemCls),C(r),S(r),M(r),(0,g.A)(r),p.nF]}),(e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0})),{order:-1e3});const I=[];function F(e,t,n){return{key:"string"==typeof e?e:`${t}-${arguments.length>3&&void 0!==arguments[3]?arguments[3]:0}`,error:e,errorStatus:n}}var N=e=>{let{help:t,helpStatus:n,errors:i=I,warnings:f=I,className:m,fieldId:p,onVisibleChanged:g}=e;const{prefixCls:h}=l.useContext(r.hb),b=`${h}-item-explain`,$=(0,u.A)(h),[y,v,x]=A(h,$),w=l.useMemo((()=>(0,c.A)(h)),[h]),O=d(i),C=d(f),E=l.useMemo((()=>null!=t?[F(t,"help",n)]:[].concat((0,o.A)(O.map(((e,t)=>F(e,"error","error",t)))),(0,o.A)(C.map(((e,t)=>F(e,"warning","warning",t)))))),[t,n,O,C]),j=l.useMemo((()=>{const e={};return E.forEach((t=>{let{key:n}=t;e[n]=(e[n]||0)+1})),E.map(((t,n)=>Object.assign(Object.assign({},t),{key:e[t.key]>1?`${t.key}-fallback-${n}`:t.key})))}),[E]),S={};return p&&(S.id=`${p}_help`),y(l.createElement(s.Ay,{motionDeadline:w.motionDeadline,motionName:`${h}-show-help`,visible:!!j.length,onVisibleChanged:g},(e=>{const{className:t,style:n}=e;return l.createElement("div",Object.assign({},S,{className:a()(b,t,x,$,m,v),style:n}),l.createElement(s.aF,Object.assign({keys:j},(0,c.A)(h),{motionName:`${h}-show-help-item`,component:!1}),(e=>{const{key:t,error:n,errorStatus:r,className:o,style:i}=e;return l.createElement("div",{key:t,className:a()(o,{[`${b}-${r}`]:r}),style:i},n)})))})))},P=n(3592),z=n(2279),R=n(8119),W=n(829),H=n(8224),q=n(6588);const T=e=>"object"==typeof e&&null!=e&&1===e.nodeType,_=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,L=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return _(n.overflowY,t)||_(n.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},D=(e,t,n,r,o,l,i,a)=>l<e&&i>t||l>e&&i<t?0:l<=e&&a<=n||i>=t&&a>=n?l-e-r:i>t&&a<n||l<e&&a>n?i-t+o:0,B=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t},V=(e,t)=>{var n,r,o,l;if("undefined"==typeof document)return[];const{scrollMode:i,block:a,inline:s,boundary:c,skipOverflowHiddenElements:u}=t,d="function"==typeof c?c:e=>e!==c;if(!T(e))throw new TypeError("Invalid target");const f=document.scrollingElement||document.documentElement,m=[];let p=e;for(;T(p)&&d(p);){if(p=B(p),p===f){m.push(p);break}null!=p&&p===document.body&&L(p)&&!L(document.documentElement)||null!=p&&L(p,u)&&m.push(p)}const g=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,h=null!=(l=null==(o=window.visualViewport)?void 0:o.height)?l:innerHeight,{scrollX:b,scrollY:$}=window,{height:y,width:v,top:x,right:w,bottom:O,left:C}=e.getBoundingClientRect(),{top:E,right:j,bottom:S,left:M}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let k="start"===a||"nearest"===a?x-E:"end"===a?O+S:x+y/2-E+S,A="center"===s?C+v/2-M+j:"end"===s?w+j:C-M;const I=[];for(let F=0;F<m.length;F++){const e=m[F],{height:t,width:n,top:r,right:o,bottom:l,left:c}=e.getBoundingClientRect();if("if-needed"===i&&x>=0&&C>=0&&O<=h&&w<=g&&(e===f&&!L(e)||x>=r&&O<=l&&C>=c&&w<=o))return I;const u=getComputedStyle(e),d=parseInt(u.borderLeftWidth,10),p=parseInt(u.borderTopWidth,10),E=parseInt(u.borderRightWidth,10),j=parseInt(u.borderBottomWidth,10);let S=0,M=0;const N="offsetWidth"in e?e.offsetWidth-e.clientWidth-d-E:0,P="offsetHeight"in e?e.offsetHeight-e.clientHeight-p-j:0,z="offsetWidth"in e?0===e.offsetWidth?0:n/e.offsetWidth:0,R="offsetHeight"in e?0===e.offsetHeight?0:t/e.offsetHeight:0;if(f===e)S="start"===a?k:"end"===a?k-h:"nearest"===a?D($,$+h,h,p,j,$+k,$+k+y,y):k-h/2,M="start"===s?A:"center"===s?A-g/2:"end"===s?A-g:D(b,b+g,g,d,E,b+A,b+A+v,v),S=Math.max(0,S+$),M=Math.max(0,M+b);else{S="start"===a?k-r-p:"end"===a?k-l+j+P:"nearest"===a?D(r,l,t,p,j+P,k,k+y,y):k-(r+t/2)+P/2,M="start"===s?A-c-d:"center"===s?A-(c+n/2)+N/2:"end"===s?A-o+E+N:D(c,o,n,d,E+N,A,A+v,v);const{scrollLeft:i,scrollTop:u}=e;S=0===R?0:Math.max(0,Math.min(u+S/R,e.scrollHeight-t/R+P)),M=0===z?0:Math.max(0,Math.min(i+M/z,e.scrollWidth-n/z+N)),k+=u-S,A+=i-M}I.push({el:e,top:S,left:M})}return I};function X(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;const n=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if((e=>"object"==typeof e&&"function"==typeof e.behavior)(t))return t.behavior(V(e,t));const r="boolean"==typeof t||null==t?void 0:t.behavior;for(const{el:o,top:l,left:i}of V(e,(e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"})(t))){const e=l-n.top+n.bottom,t=i-n.left+n.right;o.scroll({top:e,left:t,behavior:r})}}const K=["parentNode"];function G(e){return void 0===e||!1===e?[]:Array.isArray(e)?e:[e]}function Y(e,t){if(!e.length)return;const n=e.join("_");if(t)return`${t}_${n}`;return K.includes(n)?`form_item_${n}`:n}function J(e,t,n,r,o,l){let i=r;return void 0!==l?i=l:n.validating?i="validating":e.length?i="error":t.length?i="warning":(n.touched||o&&n.validated)&&(i="success"),i}var Q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function U(e){return G(e).join("_")}function Z(e,t){const n=t.getFieldInstance(e),r=(0,q.rb)(n);if(r)return r;const o=Y(G(e),t.__INTERNAL__.name);return o?document.getElementById(o):void 0}function ee(e){const[t]=(0,P.mN)(),n=l.useRef({}),r=l.useMemo((()=>null!=e?e:Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:e=>t=>{const r=U(e);t?n.current[r]=t:delete n.current[r]}},scrollToField:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{focus:n}=t,o=Q(t,["focus"]),l=Z(e,r);l&&(X(l,Object.assign({scrollMode:"if-needed",block:"nearest"},o)),n&&r.focusField(e))},focusField:e=>{var t,n;const o=r.getFieldInstance(e);"function"==typeof(null==o?void 0:o.focus)?o.focus():null===(n=null===(t=Z(e,r))||void 0===t?void 0:t.focus)||void 0===n||n.call(t)},getFieldInstance:e=>{const t=U(e);return n.current[t]}})),[e,t]);return[r]}var te=n(9407),ne=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const re=(e,t)=>{const n=l.useContext(R.A),{getPrefixCls:o,direction:i,requiredMark:s,colon:c,scrollToFirstError:d,className:f,style:m}=(0,z.TP)("form"),{prefixCls:p,className:g,rootClassName:h,size:b,disabled:$=n,form:y,colon:v,labelAlign:x,labelWrap:w,labelCol:O,wrapperCol:C,hideRequiredMark:E,layout:j="horizontal",scrollToFirstError:S,requiredMark:M,onFinishFailed:k,name:I,style:F,feedbackIcons:N,variant:q}=e,T=ne(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),_=(0,W.A)(b),L=l.useContext(te.A);const D=l.useMemo((()=>void 0!==M?M:!E&&(void 0===s||s)),[E,M,s]),B=null!=v?v:c,V=o("form",p),X=(0,u.A)(V),[K,G,Y]=A(V,X),J=a()(V,`${V}-${j}`,{[`${V}-hide-required-mark`]:!1===D,[`${V}-rtl`]:"rtl"===i,[`${V}-${_}`]:_},Y,X,G,f,g,h),[Q]=ee(y),{__INTERNAL__:U}=Q;U.name=I;const Z=l.useMemo((()=>({name:I,labelAlign:x,labelCol:O,labelWrap:w,wrapperCol:C,vertical:"vertical"===j,colon:B,requiredMark:D,itemRef:U.itemRef,form:Q,feedbackIcons:N})),[I,x,O,C,j,B,D,Q,N]),re=l.useRef(null);l.useImperativeHandle(t,(()=>{var e;return Object.assign(Object.assign({},Q),{nativeElement:null===(e=re.current)||void 0===e?void 0:e.nativeElement})}));const oe=(e,t)=>{if(e){let n={block:"nearest"};"object"==typeof e&&(n=Object.assign(Object.assign({},n),e)),Q.scrollToField(t,n)}};return K(l.createElement(r.Pp.Provider,{value:q},l.createElement(R.X,{disabled:$},l.createElement(H.A.Provider,{value:_},l.createElement(r.Op,{validateMessages:L},l.createElement(r.cK.Provider,{value:Z},l.createElement(P.Ay,Object.assign({id:I},T,{name:I,onFinishFailed:e=>{if(null==k||k(e),e.errorFields.length){const t=e.errorFields[0].name;if(void 0!==S)return void oe(S,t);void 0!==d&&oe(d,t)}},form:Q,ref:re,style:Object.assign(Object.assign({},m),F),className:J}))))))))};var oe=l.forwardRef(re),le=n(1233),ie=n(8719),ae=n(682),se=n(8877),ce=n(2546);const ue=()=>{const{status:e,errors:t=[],warnings:n=[]}=l.useContext(r.$W);return{status:e,errors:t,warnings:n}};ue.Context=r.$W;var de=ue,fe=n(5371);var me=n(2467),pe=n(981),ge=n(9853),he=n(4945),be=n(8551);var $e=(0,l.createContext)({}),ye=n(5006),ve=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function xe(e,t){const[n,r]=l.useState("string"==typeof e?e:"");return l.useEffect((()=>{(()=>{if("string"==typeof e&&r(e),"object"==typeof e)for(let n=0;n<he.ye.length;n++){const o=he.ye[n];if(!t||!t[o])continue;const l=e[o];if(void 0!==l)return void r(l)}})()}),[JSON.stringify(e),t]),n}const we=l.forwardRef(((e,t)=>{const{prefixCls:n,justify:r,align:o,className:i,style:s,children:c,gutter:u=0,wrap:d}=e,f=ve(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:m,direction:p}=l.useContext(z.QO),g=(0,be.A)(!0,null),h=xe(o,g),b=xe(r,g),$=m("row",n),[y,v,x]=(0,ye.L3)($),w=function(e,t){const n=[void 0,void 0],r=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return r.forEach(((e,t)=>{if("object"==typeof e&&null!==e)for(let r=0;r<he.ye.length;r++){const l=he.ye[r];if(o[l]&&void 0!==e[l]){n[t]=e[l];break}}else n[t]=e})),n}(u,g),O=a()($,{[`${$}-no-wrap`]:!1===d,[`${$}-${b}`]:b,[`${$}-${h}`]:h,[`${$}-rtl`]:"rtl"===p},i,v,x),C={},E=null!=w[0]&&w[0]>0?w[0]/-2:void 0;E&&(C.marginLeft=E,C.marginRight=E);const[j,S]=w;C.rowGap=S;const M=l.useMemo((()=>({gutter:[j,S],wrap:d})),[j,S,d]);return y(l.createElement($e.Provider,{value:M},l.createElement("div",Object.assign({},f,{className:O,style:Object.assign(Object.assign({},C),s),ref:t}),c)))}));var Oe=we,Ce=n(1470),Ee=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function je(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const Se=["xs","sm","md","lg","xl","xxl"],Me=l.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:r}=l.useContext(z.QO),{gutter:o,wrap:i}=l.useContext($e),{prefixCls:s,span:c,order:u,offset:d,push:f,pull:m,className:p,children:g,flex:h,style:b}=e,$=Ee(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),y=n("col",s),[v,x,w]=(0,ye.xV)(y),O={};let C={};Se.forEach((t=>{let n={};const o=e[t];"number"==typeof o?n.span=o:"object"==typeof o&&(n=o||{}),delete $[t],C=Object.assign(Object.assign({},C),{[`${y}-${t}-${n.span}`]:void 0!==n.span,[`${y}-${t}-order-${n.order}`]:n.order||0===n.order,[`${y}-${t}-offset-${n.offset}`]:n.offset||0===n.offset,[`${y}-${t}-push-${n.push}`]:n.push||0===n.push,[`${y}-${t}-pull-${n.pull}`]:n.pull||0===n.pull,[`${y}-rtl`]:"rtl"===r}),n.flex&&(C[`${y}-${t}-flex`]=!0,O[`--${y}-${t}-flex`]=je(n.flex))}));const E=a()(y,{[`${y}-${c}`]:void 0!==c,[`${y}-order-${u}`]:u,[`${y}-offset-${d}`]:d,[`${y}-push-${f}`]:f,[`${y}-pull-${m}`]:m},p,C,x,w),j={};if(o&&o[0]>0){const e=o[0]/2;j.paddingLeft=e,j.paddingRight=e}return h&&(j.flex=je(h),!1!==i||j.minWidth||(j.minWidth=0)),v(l.createElement("div",Object.assign({},$,{style:Object.assign(Object.assign(Object.assign({},j),b),O),className:E,ref:t}),g))}));var ke=Me;const Ae=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}};var Ie=(0,b.bf)(["Form","item-item"],((e,t)=>{let{rootPrefixCls:n}=t;const r=k(e,n);return[Ae(r)]})),Fe=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var Ne=e=>{const{prefixCls:t,status:n,labelCol:o,wrapperCol:i,children:s,errors:c,warnings:u,_internalItemRender:d,extra:f,help:m,fieldId:p,marginBottom:g,onErrorVisibleChanged:h,label:b}=e,$=`${t}-item`,y=l.useContext(r.cK),v=l.useMemo((()=>{let e=Object.assign({},i||y.wrapperCol||{});if(null===b&&!o&&!i&&y.labelCol){[void 0,"xs","sm","md","lg","xl","xxl"].forEach((t=>{const n=t?[t]:[],r=(0,Ce.Jt)(y.labelCol,n),o="object"==typeof r?r:{},l=(0,Ce.Jt)(e,n);"span"in o&&!("offset"in("object"==typeof l?l:{}))&&o.span<24&&(e=(0,Ce.hZ)(e,[].concat(n,["offset"]),o.span))}))}return e}),[i,y]),x=a()(`${$}-control`,v.className),w=l.useMemo((()=>{const{labelCol:e,wrapperCol:t}=y;return Fe(y,["labelCol","wrapperCol"])}),[y]),O=l.useRef(null),[C,E]=l.useState(0);(0,pe.A)((()=>{f&&O.current?E(O.current.clientHeight):E(0)}),[f]);const j=l.createElement("div",{className:`${$}-control-input`},l.createElement("div",{className:`${$}-control-input-content`},s)),S=l.useMemo((()=>({prefixCls:t,status:n})),[t,n]),M=null!==g||c.length||u.length?l.createElement(r.hb.Provider,{value:S},l.createElement(N,{fieldId:p,errors:c,warnings:u,help:m,helpStatus:n,className:`${$}-explain-connected`,onVisibleChanged:h})):null,k={};p&&(k.id=`${p}_extra`);const A=f?l.createElement("div",Object.assign({},k,{className:`${$}-extra`,ref:O}),f):null,I=M||A?l.createElement("div",{className:`${$}-additional`,style:g?{minHeight:g+C}:{}},M,A):null,F=d&&"pro_table_render"===d.mark&&d.render?d.render(e,{input:j,errorList:M,extra:A}):l.createElement(l.Fragment,null,j,I);return l.createElement(r.cK.Provider,{value:w},l.createElement(ke,Object.assign({},v,{className:x}),F),l.createElement(Ie,{prefixCls:t}))},Pe=n(8168),ze={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"},Re=n(7064),We=function(e,t){return l.createElement(Re.A,(0,Pe.A)({},e,{ref:t,icon:ze}))};var He=l.forwardRef(We),qe=n(9155),Te=n(8055),_e=n(955),Le=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var De=e=>{let{prefixCls:t,label:n,htmlFor:o,labelCol:i,labelAlign:s,colon:c,required:u,requiredMark:d,tooltip:f,vertical:m}=e;var p;const[g]=(0,qe.A)("Form"),{labelAlign:h,labelCol:b,labelWrap:$,colon:y}=l.useContext(r.cK);if(!n)return null;const v=i||b||{},x=s||h,w=`${t}-item-label`,O=a()(w,"left"===x&&`${w}-left`,v.className,{[`${w}-wrap`]:!!$});let C=n;const E=!0===c||!1!==y&&!1!==c;E&&!m&&"string"==typeof n&&n.trim()&&(C=n.replace(/[:|：]\s*$/,""));const j=function(e){return e?"object"!=typeof e||l.isValidElement(e)?{title:e}:e:null}(f);if(j){const{icon:e=l.createElement(He,null)}=j,n=Le(j,["icon"]),r=l.createElement(_e.A,Object.assign({},n),l.cloneElement(e,{className:`${t}-item-tooltip`,title:"",onClick:e=>{e.preventDefault()},tabIndex:null}));C=l.createElement(l.Fragment,null,C,r)}const S="optional"===d,M="function"==typeof d;M?C=d(C,{required:!!u}):S&&!u&&(C=l.createElement(l.Fragment,null,C,l.createElement("span",{className:`${t}-item-optional`,title:""},(null==g?void 0:g.optional)||(null===(p=Te.A.Form)||void 0===p?void 0:p.optional))));const k=a()({[`${t}-item-required`]:u,[`${t}-item-required-mark-optional`]:S||M,[`${t}-item-no-colon`]:!E});return l.createElement(ke,Object.assign({},v,{className:O}),l.createElement("label",{htmlFor:o,className:k,title:"string"==typeof n?n:""},C))},Be=n(8811),Ve=n(6029),Xe=n(7541),Ke=n(3567);const Ge={success:Be.A,warning:Xe.A,error:Ve.A,validating:Ke.A};function Ye(e){let{children:t,errors:n,warnings:o,hasFeedback:i,validateStatus:s,prefixCls:c,meta:u,noStyle:d}=e;const f=`${c}-item`,{feedbackIcons:m}=l.useContext(r.cK),p=J(n,o,u,null,!!i,s),{isFormItemInput:g,status:h,hasFeedback:b,feedbackIcon:$}=l.useContext(r.$W),y=l.useMemo((()=>{var e;let t;if(i){const r=!0!==i&&i.icons||m,s=p&&(null===(e=null==r?void 0:r({status:p,errors:n,warnings:o}))||void 0===e?void 0:e[p]),c=p&&Ge[p];t=!1!==s&&c?l.createElement("span",{className:a()(`${f}-feedback-icon`,`${f}-feedback-icon-${p}`)},s||l.createElement(c,null)):null}const r={status:p||"",errors:n,warnings:o,hasFeedback:!!i,feedbackIcon:t,isFormItemInput:!0};return d&&(r.status=(null!=p?p:h)||"",r.isFormItemInput=g,r.hasFeedback=!!(null!=i?i:b),r.feedbackIcon=void 0!==i?r.feedbackIcon:$),r}),[p,i,d,g,h]);return l.createElement(r.$W.Provider,{value:y},t)}var Je=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function Qe(e){const{prefixCls:t,className:n,rootClassName:o,style:i,help:s,errors:c,warnings:u,validateStatus:f,meta:m,hasFeedback:p,hidden:g,children:h,fieldId:b,required:$,isRequired:y,onSubItemMetaChange:v,layout:x}=e,w=Je(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),O=`${t}-item`,{requiredMark:C,vertical:E}=l.useContext(r.cK),j=E||"vertical"===x,S=l.useRef(null),M=d(c),k=d(u),A=null!=s,I=!!(A||c.length||u.length),F=!!S.current&&(0,me.A)(S.current),[N,P]=l.useState(null);(0,pe.A)((()=>{if(I&&S.current){const e=getComputedStyle(S.current);P(parseInt(e.marginBottom,10))}}),[I,F]);const z=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return J(e?M:m.errors,e?k:m.warnings,m,"",!!p,f)}(),R=a()(O,n,o,{[`${O}-with-help`]:A||M.length||k.length,[`${O}-has-feedback`]:z&&p,[`${O}-has-success`]:"success"===z,[`${O}-has-warning`]:"warning"===z,[`${O}-has-error`]:"error"===z,[`${O}-is-validating`]:"validating"===z,[`${O}-hidden`]:g,[`${O}-${x}`]:x});return l.createElement("div",{className:R,style:i,ref:S},l.createElement(Oe,Object.assign({className:`${O}-row`},(0,ge.A)(w,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),l.createElement(De,Object.assign({htmlFor:b},e,{requiredMark:C,required:null!=$?$:y,prefixCls:t,vertical:j})),l.createElement(Ne,Object.assign({},e,m,{errors:M,warnings:k,prefixCls:t,status:z,help:s,marginBottom:N,onErrorVisibleChanged:e=>{e||P(null)}}),l.createElement(r.jC.Provider,{value:v},l.createElement(Ye,{prefixCls:t,meta:m,errors:m.errors,warnings:m.warnings,hasFeedback:p,validateStatus:z},h)))),!!N&&l.createElement("div",{className:`${O}-margin-offset`,style:{marginBottom:-N}}))}const Ue=l.memo((e=>{let{children:t}=e;return t}),((e,t)=>function(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every((n=>{const r=e[n],o=t[n];return r===o||"function"==typeof r||"function"==typeof o}))}(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every(((e,n)=>e===t.childProps[n]))));const Ze=function(e){const{name:t,noStyle:n,className:i,dependencies:s,prefixCls:c,shouldUpdate:d,rules:f,children:m,required:p,label:g,messageVariables:h,trigger:b="onChange",validateTrigger:$,hidden:y,help:v,layout:x}=e,{getPrefixCls:w}=l.useContext(z.QO),{name:O}=l.useContext(r.cK),C=function(e){if("function"==typeof e)return e;const t=(0,ce.A)(e);return t.length<=1?t[0]:t}(m),E="function"==typeof C,j=l.useContext(r.jC),{validateTrigger:S}=l.useContext(P._z),M=void 0!==$?$:S,k=!(null==t),I=w("form",c),F=(0,u.A)(I),[N,R,W]=A(I,F);(0,se.rJ)("Form.Item");const H=l.useContext(P.EF),q=l.useRef(null),[T,_]=function(e){const[t,n]=l.useState(e),r=l.useRef(null),o=l.useRef([]),i=l.useRef(!1);return l.useEffect((()=>(i.current=!1,()=>{i.current=!0,fe.A.cancel(r.current),r.current=null})),[]),[t,function(e){i.current||(null===r.current&&(o.current=[],r.current=(0,fe.A)((()=>{r.current=null,n((e=>{let t=e;return o.current.forEach((e=>{t=e(t)})),t}))}))),o.current.push(e))}]}({}),[L,D]=(0,le.A)((()=>({errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}))),B=(e,t)=>{_((n=>{const r=Object.assign({},n),l=[].concat((0,o.A)(e.name.slice(0,-1)),(0,o.A)(t)).join("__SPLIT__");return e.destroy?delete r[l]:r[l]=e,r}))},[V,X]=l.useMemo((()=>{const e=(0,o.A)(L.errors),t=(0,o.A)(L.warnings);return Object.values(T).forEach((n=>{e.push.apply(e,(0,o.A)(n.errors||[])),t.push.apply(t,(0,o.A)(n.warnings||[]))})),[e,t]}),[T,L.errors,L.warnings]),K=function(){const{itemRef:e}=l.useContext(r.cK),t=l.useRef({});return function(n,r){const o=r&&"object"==typeof r&&(0,ie.A9)(r),l=n.join("_");return t.current.name===l&&t.current.originRef===o||(t.current.name=l,t.current.originRef=o,t.current.ref=(0,ie.K4)(e(n),o)),t.current.ref}}();function J(t,r,o){return n&&!y?l.createElement(Ye,{prefixCls:I,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:L,errors:V,warnings:X,noStyle:!0},t):l.createElement(Qe,Object.assign({key:"row"},e,{className:a()(i,W,F,R),prefixCls:I,fieldId:r,isRequired:o,errors:V,warnings:X,meta:L,onSubItemMetaChange:B,layout:x}),t)}if(!k&&!E&&!s)return N(J(C));let Q={};return"string"==typeof g?Q.label=g:t&&(Q.label=String(t)),h&&(Q=Object.assign(Object.assign({},Q),h)),N(l.createElement(P.D0,Object.assign({},e,{messageVariables:Q,trigger:b,validateTrigger:M,onMetaChange:e=>{const t=null==H?void 0:H.getKey(e.name);if(D(e.destroy?{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}:e,!0),n&&!1!==v&&j){let n=e.name;if(e.destroy)n=q.current||n;else if(void 0!==t){const[e,r]=t;n=[e].concat((0,o.A)(r)),q.current=n}j(e,n)}}}),((n,r,i)=>{const a=G(t).length&&r?r.name:[],c=Y(a,O),u=void 0!==p?p:!!(null==f?void 0:f.some((e=>{if(e&&"object"==typeof e&&e.required&&!e.warningOnly)return!0;if("function"==typeof e){const t=e(i);return(null==t?void 0:t.required)&&!(null==t?void 0:t.warningOnly)}return!1}))),m=Object.assign({},n);let g=null;if(Array.isArray(C)&&k)g=C;else if(E&&(!d&&!s||k));else if(!s||E||k)if(l.isValidElement(C)){const t=Object.assign(Object.assign({},C.props),m);if(t.id||(t.id=c),v||V.length>0||X.length>0||e.extra){const n=[];(v||V.length>0)&&n.push(`${c}_help`),e.extra&&n.push(`${c}_extra`),t["aria-describedby"]=n.join(" ")}V.length>0&&(t["aria-invalid"]="true"),u&&(t["aria-required"]="true"),(0,ie.f3)(C)&&(t.ref=K(a,C));new Set([].concat((0,o.A)(G(b)),(0,o.A)(G(M)))).forEach((e=>{t[e]=function(){for(var t,n,r,o,l,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];null===(r=m[e])||void 0===r||(t=r).call.apply(t,[m].concat(a)),null===(l=(o=C.props)[e])||void 0===l||(n=l).call.apply(n,[o].concat(a))}}));const n=[t["aria-required"],t["aria-invalid"],t["aria-describedby"]];g=l.createElement(Ue,{control:m,update:C,childProps:n},(0,ae.Ob)(C,t))}else g=E&&(d||s)&&!k?C(i):C;else;return J(g,c,u)})))};Ze.useStatus=de;var et=Ze,tt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var nt=e=>{var{prefixCls:t,children:n}=e,o=tt(e,["prefixCls","children"]);const{getPrefixCls:i}=l.useContext(z.QO),a=i("form",t),s=l.useMemo((()=>({prefixCls:a,status:"error"})),[a]);return l.createElement(P.B8,Object.assign({},o),((e,t,o)=>l.createElement(r.hb.Provider,{value:s},n(e.map((e=>Object.assign(Object.assign({},e),{fieldKey:e.key}))),t,{errors:o.errors,warnings:o.warnings}))))};const rt=oe;rt.Item=et,rt.List=nt,rt.ErrorList=N,rt.useForm=ee,rt.useFormInstance=function(){const{form:e}=l.useContext(r.cK);return e},rt.useWatch=P.FH,rt.Provider=r.Op,rt.create=()=>{};var ot=rt},4945:function(e,t,n){n.d(t,{Ay:function(){return s},ye:function(){return l}});var r=n(6540),o=n(1320);const l=["xxl","xl","lg","md","sm","xs"],i=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}),a=e=>{const t=e,n=[].concat(l).reverse();return n.forEach(((e,r)=>{const o=e.toUpperCase(),l=`screen${o}Min`,i=`screen${o}`;if(!(t[l]<=t[i]))throw new Error(`${l}<=${i} fails : !(${t[l]}<=${t[i]})`);if(r<n.length-1){const e=`screen${o}Max`;if(!(t[i]<=t[e]))throw new Error(`${i}<=${e} fails : !(${t[i]}<=${t[e]})`);const l=`screen${n[r+1].toUpperCase()}Min`;if(!(t[e]<=t[l]))throw new Error(`${e}<=${l} fails : !(${t[e]}<=${t[l]})`)}})),e};function s(){const[,e]=(0,o.Ay)(),t=i(a(e));return r.useMemo((()=>{const e=new Map;let n=-1,r={};return{matchHandlers:{},dispatch(t){return r=t,e.forEach((e=>e(r))),e.size>=1},subscribe(t){return e.size||this.register(),n+=1,e.set(n,t),t(r),n},unsubscribe(t){e.delete(t),e.size||this.unregister()},unregister(){Object.keys(t).forEach((e=>{const n=t[e],r=this.matchHandlers[n];null==r||r.mql.removeListener(null==r?void 0:r.listener)})),e.clear()},register(){Object.keys(t).forEach((e=>{const n=t[e],o=t=>{let{matches:n}=t;this.dispatch(Object.assign(Object.assign({},r),{[e]:n}))},l=window.matchMedia(n);l.addListener(o),this.matchHandlers[n]={mql:l,listener:o},o(l)}))},responsiveMap:t}}),[e])}},8551:function(e,t,n){var r=n(6540),o=n(981),l=n(7447),i=n(4945);t.A=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=(0,r.useRef)(t),a=(0,l.A)(),s=(0,i.Ay)();return(0,o.A)((()=>{const t=s.subscribe((t=>{n.current=t,e&&a()}));return()=>s.unsubscribe(t)}),[]),n.current}}}]);
//# sourceMappingURL=bc9057a5b9c08ad3084ff0c44967ba5a97734a96-34190ad0f38c40cf847c.js.map