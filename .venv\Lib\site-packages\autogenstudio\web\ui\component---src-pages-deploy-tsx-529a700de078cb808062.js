/*! For license information please see component---src-pages-deploy-tsx-529a700de078cb808062.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[362],{418:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4060:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},5404:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6389:function(e,t,n){n.r(t),n.d(t,{default:function(){return w}});var o=n(6540),a=n(5312),r=n(7677),i=n(418),c=n(955),l=n(9910),s=n(9644),d=n(4060),m=n(7213);const u=e=>{let{isOpen:t,guides:n,currentGuide:a,onToggle:r,onSelectGuide:i,isLoading:u=!1}=e;return t?o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-primary font-medium"},"Guides")),o.createElement(c.A,{title:"Close Sidebar"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(s.A,{strokeWidth:1.5,className:"h-6 w-6"})))),u&&o.createElement("div",{className:"p-4"},o.createElement(d.A,{className:"w-4 h-4 inline-block animate-spin"})),!u&&0===n.length&&o.createElement("div",{className:"p-2 m-2 text-center text-secondary text-sm border border-dashed rounded"},o.createElement(m.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No deployment guide available"),o.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)] mt-4"},n.map((e=>o.createElement("div",{key:e.id,className:"relative"},o.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80 rounded "+((null==a?void 0:a.id)===e.id?"bg-accent":"bg-tertiary")}),o.createElement("div",{className:"group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary "+((null==a?void 0:a.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>i(e)},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm truncate"},e.title)))))))):o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"p-2 -ml-2"},o.createElement(c.A,{title:"Documentation"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(l.A,{strokeWidth:1.5,className:"h-6 w-6"})))))},p=[{id:"python-setup",title:"Python",type:"python"},{id:"docker-setup",title:"Docker",type:"docker"}];var g=n(5404),f=n(7260),h=n(8309);var y=()=>o.createElement("div",{className:""},o.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"Using AutoGen Studio Teams in Python Code and REST API"),o.createElement(f.A,{className:"mb-6",message:"Prerequisites",description:o.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},o.createElement("li",null,"AutoGen Studio installed")),type:"info"}),o.createElement("div",{className:"my-3 text-sm"}," ","You can reuse the declarative specifications of agent teams created in AutoGen studio in your python application by using the TeamManager class. . In TeamBuilder, select a team configuration and click download."," ",o.createElement(h.A,{className:"h-4 w-4 inline-block"})," "),o.createElement(k,{title:"1. Build Your Team in Python, Export as JSON",description:"Here is an example of building an Agent Team in python and exporting it as a JSON file.",code:'\nfrom autogen_agentchat.agents import AssistantAgent\nfrom autogen_agentchat.teams import RoundRobinGroupChat\nfrom autogen_agentchat.ui import Console\nfrom autogen_ext.models.openai import OpenAIChatCompletionClient\nfrom autogen_agentchat.conditions import  TextMentionTermination\n \nagent = AssistantAgent(\n        name="weather_agent",\n        model_client=OpenAIChatCompletionClient(\n            model="gpt-4o-mini", \n        ), \n    ) \nagent_team = RoundRobinGroupChat([agent], termination_condition=TextMentionTermination("TERMINATE"))\nconfig = agent_team.dump_component()\nprint(config.model_dump_json())',onCopy:E}),o.createElement("div",{className:"space-y-6"},o.createElement(k,{title:"2. Run a Team in Python",description:"Here's a simple example of using the TeamManager class from AutoGen Studio in your python code.",code:'\nfrom autogenstudio.teammanager import TeamManager\n\n# Initialize the TeamManager\nmanager = TeamManager()\n\n# Run a task with a specific team configuration\nresult = await manager.run(\ntask="What is the weather in New York?",\nteam_config="team.json"\n)\nprint(result)',onCopy:E}),o.createElement(k,{title:"3. Serve a Team as a REST API",description:o.createElement("div",null,"AutoGen Studio offers a convenience CLI command to serve a team as a REST API endpoint."," "),code:"\nautogenstudio serve --team path/to/team.json --port 8084  \n          ",onCopy:E})));var b=()=>o.createElement("div",{className:"max-w-4xl"},o.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"Docker Container Setup"),o.createElement(f.A,{className:"mb-6",message:"Prerequisites",description:o.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},o.createElement("li",null,"Docker installed on your system")),type:"info"}),o.createElement(k,{title:"1. Dockerfile",description:o.createElement("div",null,"AutoGen Studio provides a",o.createElement("a",{href:"https://github.com/microsoft/autogen/blob/main/python/packages/autogen-studio/Dockerfile",target:"_blank",rel:"noreferrer",className:"text-accent underline px-1"},"Dockerfile"),"that you can use to build your Docker container."," "),code:'FROM python:3.10-slim\n\nWORKDIR /code\n\nRUN pip install -U gunicorn autogenstudio\n\nRUN useradd -m -u 1000 user\nUSER user\nENV HOME=/home/<USER>/home/<USER>/.local/bin:$PATH \n    AUTOGENSTUDIO_APPDIR=/home/<USER>/app\n\nWORKDIR $HOME/app\n\nCOPY --chown=user . $HOME/app\n\nCMD gunicorn -w $((2 * $(getconf _NPROCESSORS_ONLN) + 1)) --timeout 12600 -k uvicorn.workers.UvicornWorker autogenstudio.web.app:app --bind "0.0.0.0:8081"',onCopy:E}),o.createElement(k,{title:"2. Build and Run",description:"Build and run your Docker container:",code:"docker build -t autogenstudio .\ndocker run -p 8081:8081 autogenstudio",onCopy:E})),v=n(9872);const E=e=>{navigator.clipboard.writeText(e)},x=e=>{let{guide:t}=e;switch(t.id){case"python-setup":return o.createElement(y,null);case"docker-setup":return o.createElement(b,null);default:return o.createElement("div",{className:"text-secondary"},"A Guide with the title ",o.createElement("strong",null,t.title)," is work in progress!")}},N=o.createRef(),k=e=>{let{title:t,description:n,code:a,onCopy:r,language:i="python"}=e;return o.createElement("section",{className:"mt-6 bg-seco"},o.createElement("h2",{className:"text-md font-semibold mb-3"},t),n&&o.createElement("div",{className:"  mb-3"},n),a&&o.createElement("div",{className:"relative bg-secondary text-sm p-4 rounded overflow-auto scroll h-72"},o.createElement(v.T,{language:i,editorRef:N,value:a}),o.createElement("button",{onClick:()=>r(a),className:"absolute right-2 top-2 p-2  bg-secondary hover:bg-primary rounded-md"},o.createElement(g.A,{className:"w-4 h-4 hover:text-accent transition duration-100"}))))};var A=()=>{const{0:e,1:t}=(0,o.useState)(!1),{0:n,1:a}=(0,o.useState)(p),{0:c,1:l}=(0,o.useState)(null),{0:s,1:d}=(0,o.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("deploySidebar");return null===e||JSON.parse(e)}return!0}));return(0,o.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("deploySidebar",JSON.stringify(s))}),[s]),(0,o.useEffect)((()=>{!c&&n.length>0&&l(n[0])}),[n,c]),o.createElement("div",{className:"relative    flex h-full w-full"},o.createElement("div",{className:"absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out "+(s?"w-64":"w-12")},o.createElement(u,{isOpen:s,guides:n,currentGuide:c,onToggle:()=>d(!s),onSelectGuide:l,isLoading:e})),o.createElement("div",{className:"flex-1 transition-all max-w-5xl  -mr-6 duration-200 "+(s?"ml-64":"ml-12")},o.createElement("div",{className:"p-4 pt-2"},o.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},o.createElement("span",{className:"text-primary font-medium"},"Deploy"),c&&o.createElement(o.Fragment,null,o.createElement(r.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-secondary"},c.title))),o.createElement("div",{className:"rounded border border-secondary border-dashed p-2 text-sm mb-4"},o.createElement(i.A,{className:"w-4 h-4 inline-block mr-2 -mt-1 text-secondary "})," ","The deployment guide section is work in progress."),c?o.createElement(x,{guide:c}):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"Select a guide from the sidebar to get started"))))};var w=e=>{let{data:t}=e;return o.createElement(a.A,{meta:t.site.siteMetadata,title:"Home",link:"/deploy"},o.createElement("main",{style:{height:"100%"},className:" h-full "},o.createElement(A,null)))}},7213:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7260:function(e,t,n){n.d(t,{A:function(){return G}});var o=n(6540),a=n(8811),r=n(6029),i=n(7852),c=n(7541),l=n(7850),s=n(6942),d=n.n(s),m=n(754),u=n(2065),p=n(8719),g=n(682),f=n(2279),h=n(2187),y=n(5905),b=n(7358);const v=(e,t,n,o,a)=>({background:e,border:`${(0,h.zA)(o.lineWidth)} ${o.lineType} ${t}`,[`${a}-icon`]:{color:n}}),E=e=>{const{componentCls:t,motionDurationSlow:n,marginXS:o,marginSM:a,fontSize:r,fontSizeLG:i,lineHeight:c,borderRadiusLG:l,motionEaseInOutCirc:s,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,y.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:r,lineHeight:c},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${s}, opacity ${n} ${s},\n        padding-top ${n} ${s}, padding-bottom ${n} ${s},\n        margin-bottom ${n} ${s}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:a,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:u,fontSize:i},[`${t}-description`]:{display:"block",color:m}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},x=e=>{const{componentCls:t,colorSuccess:n,colorSuccessBorder:o,colorSuccessBg:a,colorWarning:r,colorWarningBorder:i,colorWarningBg:c,colorError:l,colorErrorBorder:s,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[t]:{"&-success":v(a,o,n,e,t),"&-info":v(p,u,m,e,t),"&-warning":v(c,i,r,e,t),"&-error":Object.assign(Object.assign({},v(d,s,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},N=e=>{const{componentCls:t,iconCls:n,motionDurationMid:o,marginXS:a,fontSizeIcon:r,colorIcon:i,colorIconHover:c}=e;return{[t]:{"&-action":{marginInlineStart:a},[`${t}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:r,lineHeight:(0,h.zA)(r),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:i,transition:`color ${o}`,"&:hover":{color:c}}},"&-close-text":{color:i,transition:`color ${o}`,"&:hover":{color:c}}}}};var k=(0,b.OF)("Alert",(e=>[E(e),x(e),N(e)]),(e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}))),A=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]])}return n};const w={success:a.A,info:l.A,error:r.A,warning:c.A},C=e=>{const{icon:t,prefixCls:n,type:a}=e,r=w[a]||null;return t?(0,g.fx)(t,o.createElement("span",{className:`${n}-icon`},t),(()=>({className:d()(`${n}-icon`,t.props.className)}))):o.createElement(r,{className:`${n}-icon`})},S=e=>{const{isClosable:t,prefixCls:n,closeIcon:a,handleClose:r,ariaProps:c}=e,l=!0===a||void 0===a?o.createElement(i.A,null):a;return t?o.createElement("button",Object.assign({type:"button",onClick:r,className:`${n}-close-icon`,tabIndex:0},c),l):null},$=o.forwardRef(((e,t)=>{const{description:n,prefixCls:a,message:r,banner:i,className:c,rootClassName:l,style:s,onMouseEnter:g,onMouseLeave:h,onClick:y,afterClose:b,showIcon:v,closable:E,closeText:x,closeIcon:N,action:w,id:$}=e,I=A(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,M]=o.useState(!1);const T=o.useRef(null);o.useImperativeHandle(t,(()=>({nativeElement:T.current})));const{getPrefixCls:R,direction:j,closable:P,closeIcon:D,className:H,style:z}=(0,f.TP)("alert"),G=R("alert",a),[_,B,L]=k(G),W=t=>{var n;M(!0),null===(n=e.onClose)||void 0===n||n.call(e,t)},U=o.useMemo((()=>void 0!==e.type?e.type:i?"warning":"info"),[e.type,i]),q=o.useMemo((()=>!("object"!=typeof E||!E.closeIcon)||(!!x||("boolean"==typeof E?E:!1!==N&&null!=N||!!P))),[x,N,E,P]),F=!(!i||void 0!==v)||v,J=d()(G,`${G}-${U}`,{[`${G}-with-description`]:!!n,[`${G}-no-icon`]:!F,[`${G}-banner`]:!!i,[`${G}-rtl`]:"rtl"===j},H,c,l,L,B),Y=(0,u.A)(I,{aria:!0,data:!0}),K=o.useMemo((()=>"object"==typeof E&&E.closeIcon?E.closeIcon:x||(void 0!==N?N:"object"==typeof P&&P.closeIcon?P.closeIcon:D)),[N,E,x,D]),V=o.useMemo((()=>{const e=null!=E?E:P;if("object"==typeof e){const{closeIcon:t}=e;return A(e,["closeIcon"])}return{}}),[E,P]);return _(o.createElement(m.Ay,{visible:!O,motionName:`${G}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:b},((t,a)=>{let{className:i,style:c}=t;return o.createElement("div",Object.assign({id:$,ref:(0,p.K4)(T,a),"data-show":!O,className:d()(J,i),style:Object.assign(Object.assign(Object.assign({},z),s),c),onMouseEnter:g,onMouseLeave:h,onClick:y,role:"alert"},Y),F?o.createElement(C,{description:n,icon:e.icon,prefixCls:G,type:U}):null,o.createElement("div",{className:`${G}-content`},r?o.createElement("div",{className:`${G}-message`},r):null,n?o.createElement("div",{className:`${G}-description`},n):null),w?o.createElement("div",{className:`${G}-action`},w):null,o.createElement(S,{isClosable:q,prefixCls:G,closeIcon:K,handleClose:W,ariaProps:V}))})))}));var I=$,O=n(3029),M=n(2901),T=n(3954),R=n(2176),j=n(6822);var P=n(5501);let D=function(e){function t(){var e,n,o,a;return(0,O.A)(this,t),n=this,o=t,a=arguments,o=(0,T.A)(o),(e=(0,j.A)(n,(0,R.A)()?Reflect.construct(o,a||[],(0,T.A)(n).constructor):o.apply(n,a))).state={error:void 0,info:{componentStack:""}},e}return(0,P.A)(t,e),(0,M.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){const{message:e,description:t,id:n,children:a}=this.props,{error:r,info:i}=this.state,c=(null==i?void 0:i.componentStack)||null,l=void 0===e?(r||"").toString():e,s=void 0===t?c:t;return r?o.createElement(I,{id:n,type:"error",message:l,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},s)}):a}}])}(o.Component);var H=D;const z=I;z.ErrorBoundary=H;var G=z},7677:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8309:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}}]);
//# sourceMappingURL=component---src-pages-deploy-tsx-529a700de078cb808062.js.map