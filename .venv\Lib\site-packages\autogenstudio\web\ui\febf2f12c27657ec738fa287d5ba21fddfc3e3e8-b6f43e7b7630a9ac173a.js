/*! For license information please see febf2f12c27657ec738fa287d5ba21fddfc3e3e8-b6f43e7b7630a9ac173a.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[307],{315:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g,c="";function u(e){return e?e.replace(s,c):c}e.exports=function(e,s){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];s=s||{};var f=1,d=1;function p(e){var t=e.match(n);t&&(f+=t.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function h(){var e={line:f,column:d};return function(t){return t.position=new m(e),b(),t}}function m(e){this.start=e,this.end={line:f,column:d},this.source=s.source}m.prototype.content=e;var g=[];function y(t){var n=new Error(s.source+":"+f+":"+d+": "+t);if(n.reason=t,n.filename=s.source,n.line=f,n.column=d,n.source=e,!s.silent)throw n;g.push(n)}function v(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function b(){v(r)}function x(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var t=h();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;c!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,c===e.charAt(n-1))return y("End of comment missing");var r=e.slice(2,n-2);return d+=2,p(r),e=e.slice(n),d+=2,t({type:"comment",comment:r})}}function w(){var e=h(),n=v(o);if(n){if(k(),!v(i))return y("property missing ':'");var r=v(l),s=e({type:"declaration",property:u(n[0].replace(t,c)),value:r?u(r[0].replace(t,c)):c});return v(a),s}}return b(),function(){var e,t=[];for(x(t);e=w();)!1!==e&&(t.push(e),x(t));return t}()}},2206:function(e,t,n){"use strict";n.d(t,{wx:function(){return Cl},XC:function(){return xl},PA:function(){return kl},vq:function(){return Sl}});var r={};n.r(r),n.d(r,{boolean:function(){return k},booleanish:function(){return w},commaOrSpaceSeparated:function(){return $},commaSeparated:function(){return E},number:function(){return S},overloadedBoolean:function(){return C},spaceSeparated:function(){return A}});var o={};n.r(o),n.d(o,{attentionMarkers:function(){return rn},contentInitial:function(){return Yt},disable:function(){return on},document:function(){return Qt},flow:function(){return Jt},flowInitial:function(){return Kt},insideSpan:function(){return nn},string:function(){return en},text:function(){return tn}});var i=n(6540),l=n(2102),a=n(5107),s=n(3324),c=n(8697);const u=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,f=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,d={};function p(e,t){return((t||d).jsx?f:u).test(e)}const h=/[ \t\n\f\r]/g;function m(e){return""===e.replace(h,"")}class g{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function y(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new g(n,r,t)}function v(e){return e.toLowerCase()}g.prototype.normal={},g.prototype.property={},g.prototype.space=void 0;class b{constructor(e,t){this.attribute=t,this.property=e}}b.prototype.attribute="",b.prototype.booleanish=!1,b.prototype.boolean=!1,b.prototype.commaOrSpaceSeparated=!1,b.prototype.commaSeparated=!1,b.prototype.defined=!1,b.prototype.mustUseProperty=!1,b.prototype.number=!1,b.prototype.overloadedBoolean=!1,b.prototype.property="",b.prototype.spaceSeparated=!1,b.prototype.space=void 0;let x=0;const k=O(),w=O(),C=O(),S=O(),A=O(),E=O(),$=O();function O(){return 2**++x}const I=Object.keys(r);class P extends b{constructor(e,t,n,o){let i=-1;if(super(e,t),T(this,"space",o),"number"==typeof n)for(;++i<I.length;){const e=I[i];T(this,I[i],(n&r[e])===r[e])}}}function T(e,t,n){n&&(e[t]=n)}function z(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new P(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[v(r)]=r,n[v(i.attribute)]=r}return new g(t,n,e.space)}P.prototype.defined=!0;const D=z({properties:{ariaActiveDescendant:null,ariaAtomic:w,ariaAutoComplete:null,ariaBusy:w,ariaChecked:w,ariaColCount:S,ariaColIndex:S,ariaColSpan:S,ariaControls:A,ariaCurrent:null,ariaDescribedBy:A,ariaDetails:null,ariaDisabled:w,ariaDropEffect:A,ariaErrorMessage:null,ariaExpanded:w,ariaFlowTo:A,ariaGrabbed:w,ariaHasPopup:null,ariaHidden:w,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:A,ariaLevel:S,ariaLive:null,ariaModal:w,ariaMultiLine:w,ariaMultiSelectable:w,ariaOrientation:null,ariaOwns:A,ariaPlaceholder:null,ariaPosInSet:S,ariaPressed:w,ariaReadOnly:w,ariaRelevant:null,ariaRequired:w,ariaRoleDescription:A,ariaRowCount:S,ariaRowIndex:S,ariaRowSpan:S,ariaSelected:w,ariaSetSize:S,ariaSort:null,ariaValueMax:S,ariaValueMin:S,ariaValueNow:S,ariaValueText:null,role:null},transform(e,t){return"role"===t?t:"aria-"+t.slice(4).toLowerCase()}});function j(e,t){return t in e?e[t]:t}function F(e,t){return j(e,t.toLowerCase())}const N=z({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:E,acceptCharset:A,accessKey:A,action:null,allow:null,allowFullScreen:k,allowPaymentRequest:k,allowUserMedia:k,alt:null,as:null,async:k,autoCapitalize:null,autoComplete:A,autoFocus:k,autoPlay:k,blocking:A,capture:null,charSet:null,checked:k,cite:null,className:A,cols:S,colSpan:null,content:null,contentEditable:w,controls:k,controlsList:A,coords:S|E,crossOrigin:null,data:null,dateTime:null,decoding:null,default:k,defer:k,dir:null,dirName:null,disabled:k,download:C,draggable:w,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:k,formTarget:null,headers:A,height:S,hidden:k,high:S,href:null,hrefLang:null,htmlFor:A,httpEquiv:A,id:null,imageSizes:null,imageSrcSet:null,inert:k,inputMode:null,integrity:null,is:null,isMap:k,itemId:null,itemProp:A,itemRef:A,itemScope:k,itemType:A,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:k,low:S,manifest:null,max:null,maxLength:S,media:null,method:null,min:null,minLength:S,multiple:k,muted:k,name:null,nonce:null,noModule:k,noValidate:k,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:k,optimum:S,pattern:null,ping:A,placeholder:null,playsInline:k,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:k,referrerPolicy:null,rel:A,required:k,reversed:k,rows:S,rowSpan:S,sandbox:A,scope:null,scoped:k,seamless:k,selected:k,shadowRootClonable:k,shadowRootDelegatesFocus:k,shadowRootMode:null,shape:null,size:S,sizes:null,slot:null,span:S,spellCheck:w,src:null,srcDoc:null,srcLang:null,srcSet:null,start:S,step:null,style:null,tabIndex:S,target:null,title:null,translate:null,type:null,typeMustMatch:k,useMap:null,value:w,width:S,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:A,axis:null,background:null,bgColor:null,border:S,borderColor:null,bottomMargin:S,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:k,declare:k,event:null,face:null,frame:null,frameBorder:null,hSpace:S,leftMargin:S,link:null,longDesc:null,lowSrc:null,marginHeight:S,marginWidth:S,noResize:k,noHref:k,noShade:k,noWrap:k,object:null,profile:null,prompt:null,rev:null,rightMargin:S,rules:null,scheme:null,scrolling:w,standby:null,summary:null,text:null,topMargin:S,valueType:null,version:null,vAlign:null,vLink:null,vSpace:S,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:k,disableRemotePlayback:k,prefix:null,property:null,results:S,security:null,unselectable:null},space:"html",transform:F}),L=z({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:$,accentHeight:S,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:S,amplitude:S,arabicForm:null,ascent:S,attributeName:null,attributeType:null,azimuth:S,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:S,by:null,calcMode:null,capHeight:S,className:A,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:S,diffuseConstant:S,direction:null,display:null,dur:null,divisor:S,dominantBaseline:null,download:k,dx:null,dy:null,edgeMode:null,editable:null,elevation:S,enableBackground:null,end:null,event:null,exponent:S,externalResourcesRequired:null,fill:null,fillOpacity:S,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:E,g2:E,glyphName:E,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:S,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:S,horizOriginX:S,horizOriginY:S,id:null,ideographic:S,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:S,k:S,k1:S,k2:S,k3:S,k4:S,kernelMatrix:$,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:S,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:S,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:S,overlineThickness:S,paintOrder:null,panose1:null,path:null,pathLength:S,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:A,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:S,pointsAtY:S,pointsAtZ:S,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:$,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:$,rev:$,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:$,requiredFeatures:$,requiredFonts:$,requiredFormats:$,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:S,specularExponent:S,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:S,strikethroughThickness:S,string:null,stroke:null,strokeDashArray:$,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:S,strokeOpacity:S,strokeWidth:null,style:null,surfaceScale:S,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:$,tabIndex:S,tableValues:null,target:null,targetX:S,targetY:S,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:$,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:S,underlineThickness:S,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:S,values:null,vAlphabetic:S,vMathematical:S,vectorEffect:null,vHanging:S,vIdeographic:S,version:null,vertAdvY:S,vertOriginX:S,vertOriginY:S,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:S,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:j}),M=z({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),R=z({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:F}),B=z({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),_=y([D,N,M,R,B],"html"),H=y([D,L,M,R,B],"svg"),U=/[A-Z]/g,q=/-[a-z]/g,V=/^data[-\w.:]+$/i;function W(e){return"-"+e.toLowerCase()}function X(e){return e.charAt(1).toUpperCase()}const Z={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var G=n(5229);Y("end");const Q=Y("start");function Y(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function K(e){return ee(e&&e.line)+":"+ee(e&&e.column)}function J(e){return K(e&&e.start)+"-"+K(e&&e.end)}function ee(e){return e&&"number"==typeof e?e:1}class te extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;var a;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=((a=o.place)&&"object"==typeof a?"position"in a||"type"in a?J(a.position):"start"in a||"end"in a?J(a):"line"in a||"column"in a?K(a):"":"")||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}te.prototype.file="",te.prototype.name="",te.prototype.reason="",te.prototype.message="",te.prototype.stack="",te.prototype.column=void 0,te.prototype.line=void 0,te.prototype.ancestors=void 0,te.prototype.cause=void 0,te.prototype.fatal=void 0,te.prototype.place=void 0,te.prototype.ruleId=void 0,te.prototype.source=void 0;const ne={}.hasOwnProperty,re=new Map,oe=/[A-Z]/g,ie=new Set(["table","tbody","thead","tfoot","tr"]),le=new Set(["td","th"]),ae="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function se(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,t){return n;function n(n,r,o,i){const l=Array.isArray(o.children),a=Q(n);return t(r,o,i,l,{columnNumber:a?a.column-1:void 0,fileName:e,lineNumber:a?a.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");r=function(e,t,n){return r;function r(e,r,o,i){const l=Array.isArray(o.children)?n:t;return i?l(r,o,i):l(r,o)}}(0,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?H:_,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},i=ce(o,e,void 0);return i&&"string"!=typeof i?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function ce(e,t,n){return"element"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(o=H,e.schema=o);e.ancestors.push(t);const i=he(e,t.tagName,!1),l=function(e,t){const n={};let r,o;for(o in t.properties)if("children"!==o&&ne.call(t.properties,o)){const i=pe(e,o,t.properties[o]);if(i){const[o,l]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof l&&le.has(t.tagName)?r=l:n[o]=l}}if(r){(n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r}return n}(e,t);let a=de(e,t);ie.has(t.tagName)&&(a=a.filter((function(e){return"string"!=typeof e||!("object"==typeof(t=e)?"text"===t.type&&m(t.value):m(t));var t})));return ue(e,l,i,t),fe(l,a),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}me(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.name&&"html"===r.space&&(o=H,e.schema=o);e.ancestors.push(t);const i=null===t.name?e.Fragment:he(e,t.name,!0),l=function(e,t){const n={};for(const r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const t=r.data.estree.body[0];t.type;const o=t.expression;o.type;const i=o.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else me(e,t.position);else{const o=r.name;let i;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else me(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),a=de(e,t);return ue(e,l,i,t),fe(l,a),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);me(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const r={};return fe(r,de(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function ue(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function fe(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function de(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:re;for(;++r<t.children.length;){const i=t.children[r];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=o.get(e)||0;l=e+"-"+t,o.set(e,t+1)}}const a=ce(e,i,l);void 0!==a&&n.push(a)}return n}function pe(e,t,n){const r=function(e,t){const n=v(t);let r=t,o=b;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&V.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(q,X);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!q.test(e)){let n=e.replace(U,W);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}o=P}return new o(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){const n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return G(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const t=n,r=new te("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw r.file=e.filePath||void 0,r.url=ae+"#cannot-parse-style-attribute",r}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)ne.call(e,n)&&(t[ge(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?Z[r.property]||r.property:r.attribute,n]}}function he(e,t,n){let r;if(n)if(t.includes(".")){const e=t.split(".");let n,o=-1;for(;++o<e.length;){const t=p(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(o&&"Literal"===t.type),optional:!1}:t}r=n}else r=p(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){const t=r.value;return ne.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);me(e)}function me(e,t){const n=new te("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=ae+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ge(e){let t=e.replace(oe,ye);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function ye(e){return"-"+e.toLowerCase()}const ve={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var be=n(4848);const xe={};function ke(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return we(e.children,t,n)}return Array.isArray(e)?we(e,t,n):""}function we(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=ke(e[o],t,n);return r.join("")}function Ce(e,t,n,r){const o=e.length;let i,l=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)i=r.slice(l,l+1e4),i.unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function Se(e,t){return e.length>0?(Ce(e,e.length,0,t),e):t}class Ae{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const r=t||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&Ee(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Ee(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Ee(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);Ee(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Ee(this.left,t.reverse())}}}function Ee(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function $e(e){const t={};let n,r,o,i,l,a,s,c=-1;const u=new Ae(e);for(;++c<u.length;){for(;c in t;)c=t[c];if(n=u.get(c),c&&"chunkFlow"===n[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&(a=n[1]._tokenizer.events,o=0,o<a.length&&"lineEndingBlank"===a[o][1].type&&(o+=2),o<a.length&&"content"===a[o][1].type))for(;++o<a.length&&"content"!==a[o][1].type;)"chunkText"===a[o][1].type&&(a[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,Oe(u,c)),c=t[c],s=!0);else if(n[1]._container){for(o=c,r=void 0;o--;)if(i=u.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(r&&(u.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);else if("linePrefix"!==i[1].type&&"listItemIndent"!==i[1].type)break;r&&(n[1].end={...u.get(r)[1].start},l=u.slice(r,c),l.unshift(n),u.splice(r,c-r+1,l))}}return Ce(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!s}function Oe(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[];let l=n._tokenizer;l||(l=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));const a=l.events,s=[],c={};let u,f,d=-1,p=n,h=0,m=0;const g=[m];for(;p;){for(;e.get(++o)[1]!==p;);i.push(o),p._tokenizer||(u=r.sliceStream(p),p.next||u.push(null),f&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(u),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),f=p,p=p.next}for(p=n;++d<a.length;)"exit"===a[d][0]&&"enter"===a[d-1][0]&&a[d][1].type===a[d-1][1].type&&a[d][1].start.line!==a[d][1].end.line&&(m=d+1,g.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),d=g.length;d--;){const t=a.slice(g[d],g[d+1]),n=i.pop();s.push([n,n+t.length-1]),e.splice(n,2,t)}for(s.reverse(),d=-1;++d<s.length;)c[h+s[d][0]]=h+s[d][1],h+=s[d][1]-s[d][0]-1;return c}const Ie={}.hasOwnProperty;function Pe(e,t){let n;for(n in t){const r=(Ie.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){Ie.call(r,i)||(r[i]=[]);const e=o[i];Te(r[i],Array.isArray(e)?e:e?[e]:[])}}}function Te(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);Ce(e,0,0,r)}De(/[A-Za-z]/),De(/[\dA-Za-z]/),De(/[#-'*+\--9=?A-Z^-~]/);De(/\d/),De(/[\dA-Fa-f]/),De(/[!-/:-@[-`{-~]/);function ze(e){return-2===e||-1===e||32===e}De(/\p{P}|\p{S}/u),De(/\s/);function De(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function je(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(ze(r))return e.enter(n),l(r);return t(r)};function l(r){return ze(r)&&i++<o?(e.consume(r),l):(e.exit(n),t(r))}}const Fe=Ve(/[A-Za-z]/),Ne=Ve(/[\dA-Za-z]/),Le=Ve(/[#-'*+\--9=?A-Z^-~]/);function Me(e){return null!==e&&(e<32||127===e)}const Re=Ve(/\d/),Be=Ve(/[\dA-Fa-f]/),_e=Ve(/[!-/:-@[-`{-~]/);function He(e){return null!==e&&e<-2}function Ue(e){return null!==e&&(e<0||32===e)}function qe(e){return-2===e||-1===e||32===e}Ve(/\p{P}|\p{S}/u),Ve(/\s/);function Ve(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const We={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,(function(n){if(null===n)return void e.consume(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),je(e,t,"linePrefix")}),(function(t){return e.enter("paragraph"),r(t)}));let n;return t;function r(t){const r=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=r),n=r,o(t)}function o(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):He(t)?(e.consume(t),e.exit("chunkText"),r):(e.consume(t),o)}}};const Xe={tokenize:function(e){const t=this,n=[];let r,o,i,l=0;return a;function a(r){if(l<n.length){const o=n[l];return t.containerState=o[1],e.attempt(o[0].continuation,s,c)(r)}return c(r)}function s(e){if(l++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&v();const n=t.events.length;let o,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){o=t.events[i][1].end;break}y(l);let a=n;for(;a<t.events.length;)t.events[a][1].end={...o},a++;return Ce(t.events,i+1,0,t.events.slice(n)),t.events.length=a,c(e)}return a(e)}function c(o){if(l===n.length){if(!r)return d(o);if(r.currentConstruct&&r.currentConstruct.concrete)return h(o);t.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Ze,u,f)(o)}function u(e){return r&&v(),y(l),d(e)}function f(e){return t.parser.lazy[t.now().line]=l!==n.length,i=t.now().offset,h(e)}function d(n){return t.containerState={},e.attempt(Ze,p,h)(n)}function p(e){return l++,n.push([t.currentConstruct,t.containerState]),d(e)}function h(n){return null===n?(r&&v(),y(0),void e.consume(n)):(r=r||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(n))}function m(n){return null===n?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(n)):He(n)?(e.consume(n),g(e.exit("chunkFlow")),l=0,t.interrupt=void 0,a):(e.consume(n),m)}function g(e,n){const a=t.sliceStream(e);if(n&&a.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(a),t.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const n=t.events.length;let o,a,s=n;for(;s--;)if("exit"===t.events[s][0]&&"chunkFlow"===t.events[s][1].type){if(o){a=t.events[s][1].end;break}o=!0}for(y(l),e=n;e<t.events.length;)t.events[e][1].end={...a},e++;Ce(t.events,s+1,0,t.events.slice(n)),t.events.length=e}}function y(r){let o=n.length;for(;o-- >r;){const r=n[o];t.containerState=r[1],r[0].exit.call(t,e)}n.length=r}function v(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}},Ze={tokenize:function(e,t,n){return je(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};const Ge={partial:!0,tokenize:function(e,t,n){return function(t){return qe(t)?je(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||He(e)?t(e):n(e)}}};const Qe={resolve:function(e){return $e(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?o(t):He(t)?e.check(Ye,i,o)(t):(e.consume(t),r)}function o(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Ye={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),je(e,o,"linePrefix")};function o(o){if(null===o||He(o))return n(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}};const Ke={tokenize:function(e){const t=this,n=e.attempt(Ge,(function(r){if(null===r)return void e.consume(r);return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}),e.attempt(this.parser.constructs.flowInitial,r,je(e,e.attempt(this.parser.constructs.flow,r,e.attempt(Qe,r)),"linePrefix")));return n;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(r)}}};const Je={resolveAll:rt()},et=nt("string"),tt=nt("text");function nt(e){return{resolveAll:rt("text"===e?ot:void 0),tokenize:function(t){const n=this,r=this.parser.constructs[e],o=t.attempt(r,i,l);return i;function i(e){return s(e)?o(e):l(e)}function l(e){if(null!==e)return t.enter("data"),t.consume(e),a;t.consume(e)}function a(e){return s(e)?(t.exit("data"),o(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;const t=r[e];let o=-1;if(t)for(;++o<t.length;){const e=t[o];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function rt(e){return function(t,n){let r,o=-1;for(;++o<=t.length;)void 0===r?t[o]&&"data"===t[o][1].type&&(r=o,o++):t[o]&&"data"===t[o][1].type||(o!==r+2&&(t[r][1].end=t[o-1][1].end,t.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(t,n):t}}function ot(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const r=e[n-1][1],o=t.sliceStream(r);let i,l=o.length,a=-1,s=0;for(;l--;){const e=o[l];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)i=!0,s++;else if(-1!==e){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){const o={type:n===e.length||i||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?a:r.start._bufferIndex+a,_index:r.start._index+l,line:r.end.line,column:r.end.column-s,offset:r.end.offset-s},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}const it={name:"thematicBreak",tokenize:function(e,t,n){let r,o=0;return function(t){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(t)};function i(i){return i===r?(e.enter("thematicBreakSequence"),l(i)):o>=3&&(null===i||He(i))?(e.exit("thematicBreak"),t(i)):n(i)}function l(t){return t===r?(e.consume(t),o++,l):(e.exit("thematicBreakSequence"),qe(t)?je(e,i,"whitespace")(t):i(t))}}};const lt={continuation:{tokenize:function(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Ge,o,i);function o(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,je(e,t,"listItemIndent",r.containerState.size+1)(n)}function i(n){return r.containerState.furtherBlankLines||!qe(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,l(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(st,t,l)(n))}function l(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,je(e,e.attempt(lt,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,l=0;return function(t){const o=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||t===r.containerState.marker:Re(t)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===t||45===t?e.check(it,n,s)(t):s(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(t)}return n(t)};function a(t){return Re(t)&&++l<10?(e.consume(t),a):(!r.interrupt||l<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),s(t)):n(t)}function s(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(Ge,r.interrupt?n:c,e.attempt(at,f,u))}function c(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function u(t){return qe(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),f):n(t)}function f(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},at={partial:!0,tokenize:function(e,t,n){const r=this;return je(e,(function(e){const o=r.events[r.events.length-1];return!qe(e)&&o&&"listItemPrefixWhitespace"===o[1].type?t(e):n(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},st={partial:!0,tokenize:function(e,t,n){const r=this;return je(e,(function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(e):n(e)}),"listItemIndent",r.containerState.size+1)}};const ct={continuation:{tokenize:function(e,t,n){const r=this;return function(t){if(qe(t))return je(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t);return o(t)};function o(r){return e.attempt(ct,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const r=this;return function(t){if(62===t){const n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),o}return n(t)};function o(n){return qe(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function ut(e,t,n,r,o,i,l,a,s){const c=s||Number.POSITIVE_INFINITY;let u=0;return function(t){if(60===t)return e.enter(r),e.enter(o),e.enter(i),e.consume(t),e.exit(i),f;if(null===t||32===t||41===t||Me(t))return n(t);return e.enter(r),e.enter(l),e.enter(a),e.enter("chunkString",{contentType:"string"}),h(t)};function f(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||He(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(o){return u||null!==o&&41!==o&&!Ue(o)?u<c&&40===o?(e.consume(o),u++,h):41===o?(e.consume(o),u--,h):null===o||32===o||40===o||Me(o)?n(o):(e.consume(o),92===o?m:h):(e.exit("chunkString"),e.exit(a),e.exit(l),e.exit(r),t(o))}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function ft(e,t,n,r,o,i){const l=this;let a,s=0;return function(t){return e.enter(r),e.enter(o),e.consume(t),e.exit(o),e.enter(i),c};function c(f){return s>999||null===f||91===f||93===f&&!a||94===f&&!s&&"_hiddenFootnoteSupport"in l.parser.constructs?n(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),t):He(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(f))}function u(t){return null===t||91===t||93===t||He(t)||s++>999?(e.exit("chunkString"),c(t)):(e.consume(t),a||(a=!qe(t)),92===t?f:u)}function f(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}}function dt(e,t,n,r,o,i){let l;return function(t){if(34===t||39===t||40===t)return e.enter(r),e.enter(o),e.consume(t),e.exit(o),l=40===t?41:t,a;return n(t)};function a(n){return n===l?(e.enter(o),e.consume(n),e.exit(o),e.exit(r),t):(e.enter(i),s(n))}function s(t){return t===l?(e.exit(i),a(l)):null===t?n(t):He(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),je(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===l||null===t||He(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return t===l||92===t?(e.consume(t),c):c(t)}}function pt(e,t){let n;return function r(o){if(He(o))return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r;if(qe(o))return je(e,r,n?"linePrefix":"lineSuffix")(o);return t(o)}}function ht(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const mt={name:"definition",tokenize:function(e,t,n){const r=this;let o;return function(t){return e.enter("definition"),function(t){return ft.call(r,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return o=ht(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return Ue(t)?pt(e,a)(t):a(t)}function a(t){return ut(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(gt,c,c)(t)}function c(t){return qe(t)?je(e,u,"whitespace")(t):u(t)}function u(i){return null===i||He(i)?(e.exit("definition"),r.parser.defined.push(o),t(i)):n(i)}}},gt={partial:!0,tokenize:function(e,t,n){return function(t){return Ue(t)?pt(e,r)(t):n(t)};function r(t){return dt(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function o(t){return qe(t)?je(e,i,"whitespace")(t):i(t)}function i(e){return null===e||He(e)?t(e):n(e)}}};const yt={name:"codeIndented",tokenize:function(e,t,n){const r=this;return function(t){return e.enter("codeIndented"),je(e,o,"linePrefix",5)(t)};function o(e){const t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?a(t):He(t)?e.attempt(vt,i,a)(t):(e.enter("codeFlowValue"),l(t))}function l(t){return null===t||He(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),l)}function a(n){return e.exit("codeIndented"),t(n)}}},vt={partial:!0,tokenize:function(e,t,n){const r=this;return o;function o(t){return r.parser.lazy[r.now().line]?n(t):He(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):je(e,i,"linePrefix",5)(t)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):He(e)?o(e):n(e)}}};const bt={name:"headingAtx",resolve:function(e,t){let n,r,o=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);o-2>i&&"whitespace"===e[o][1].type&&(o-=2);"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4);o>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},Ce(e,i,o-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]]));return e},tokenize:function(e,t,n){let r=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),o(t)}(t)};function o(t){return 35===t&&r++<6?(e.consume(t),o):null===t||Ue(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),l(n)):null===n||He(n)?(e.exit("atxHeading"),t(n)):qe(n)?je(e,i,"whitespace")(n):(e.enter("atxHeadingText"),a(n))}function l(t){return 35===t?(e.consume(t),l):(e.exit("atxHeadingSequence"),i(t))}function a(t){return null===t||35===t||Ue(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),a)}}};const xt={name:"setextUnderline",resolveTo:function(e,t){let n,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const l={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",l,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=l;return e.push(["exit",l,t]),e},tokenize:function(e,t,n){const r=this;let o;return function(t){let l,a=r.events.length;for(;a--;)if("lineEnding"!==r.events[a][1].type&&"linePrefix"!==r.events[a][1].type&&"content"!==r.events[a][1].type){l="paragraph"===r.events[a][1].type;break}if(!r.parser.lazy[r.now().line]&&(r.interrupt||l))return e.enter("setextHeadingLine"),o=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t);return n(t)};function i(t){return t===o?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),qe(t)?je(e,l,"lineSuffix")(t):l(t))}function l(r){return null===r||He(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}};const kt=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],wt=["pre","script","style","textarea"],Ct={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2));return e},tokenize:function(e,t,n){const r=this;let o,i,l,a,s;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),c}(t)};function c(a){return 33===a?(e.consume(a),u):47===a?(e.consume(a),i=!0,p):63===a?(e.consume(a),o=3,r.interrupt?t:j):Fe(a)?(e.consume(a),l=String.fromCharCode(a),h):n(a)}function u(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,a=0,d):Fe(i)?(e.consume(i),o=4,r.interrupt?t:j):n(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?t:j):n(o)}function d(o){const i="CDATA[";return o===i.charCodeAt(a++)?(e.consume(o),6===a?r.interrupt?t:E:d):n(o)}function p(t){return Fe(t)?(e.consume(t),l=String.fromCharCode(t),h):n(t)}function h(a){if(null===a||47===a||62===a||Ue(a)){const s=47===a,c=l.toLowerCase();return s||i||!wt.includes(c)?kt.includes(l.toLowerCase())?(o=6,s?(e.consume(a),m):r.interrupt?t(a):E(a)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(a):i?g(a):y(a)):(o=1,r.interrupt?t(a):E(a))}return 45===a||Ne(a)?(e.consume(a),l+=String.fromCharCode(a),h):n(a)}function m(o){return 62===o?(e.consume(o),r.interrupt?t:E):n(o)}function g(t){return qe(t)?(e.consume(t),g):S(t)}function y(t){return 47===t?(e.consume(t),S):58===t||95===t||Fe(t)?(e.consume(t),v):qe(t)?(e.consume(t),y):S(t)}function v(t){return 45===t||46===t||58===t||95===t||Ne(t)?(e.consume(t),v):b(t)}function b(t){return 61===t?(e.consume(t),x):qe(t)?(e.consume(t),b):y(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,k):qe(t)?(e.consume(t),x):w(t)}function k(t){return t===s?(e.consume(t),s=null,C):null===t||He(t)?n(t):(e.consume(t),k)}function w(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||Ue(t)?b(t):(e.consume(t),w)}function C(e){return 47===e||62===e||qe(e)?y(e):n(e)}function S(t){return 62===t?(e.consume(t),A):n(t)}function A(t){return null===t||He(t)?E(t):qe(t)?(e.consume(t),A):n(t)}function E(t){return 45===t&&2===o?(e.consume(t),P):60===t&&1===o?(e.consume(t),T):62===t&&4===o?(e.consume(t),F):63===t&&3===o?(e.consume(t),j):93===t&&5===o?(e.consume(t),D):!He(t)||6!==o&&7!==o?null===t||He(t)?(e.exit("htmlFlowData"),$(t)):(e.consume(t),E):(e.exit("htmlFlowData"),e.check(St,N,$)(t))}function $(t){return e.check(At,O,N)(t)}function O(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),I}function I(t){return null===t||He(t)?$(t):(e.enter("htmlFlowData"),E(t))}function P(t){return 45===t?(e.consume(t),j):E(t)}function T(t){return 47===t?(e.consume(t),l="",z):E(t)}function z(t){if(62===t){const n=l.toLowerCase();return wt.includes(n)?(e.consume(t),F):E(t)}return Fe(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),z):E(t)}function D(t){return 93===t?(e.consume(t),j):E(t)}function j(t){return 62===t?(e.consume(t),F):45===t&&2===o?(e.consume(t),j):E(t)}function F(t){return null===t||He(t)?(e.exit("htmlFlowData"),N(t)):(e.consume(t),F)}function N(n){return e.exit("htmlFlow"),t(n)}}},St={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(Ge,t,n)}}},At={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(He(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o;return n(t)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}};const Et={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(null===t)return n(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},$t={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const r=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return l;function l(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s}function s(t){return e.enter("codeFencedFence"),qe(t)?je(e,c,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):c(t)}function c(t){return t===i?(e.enter("codeFencedFenceSequence"),u(t)):n(t)}function u(t){return t===i?(o++,e.consume(t),u):o>=a?(e.exit("codeFencedFenceSequence"),qe(t)?je(e,f,"whitespace")(t):f(t)):n(t)}function f(r){return null===r||He(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}};let i,l=0,a=0;return function(t){return function(t){const n=r.events[r.events.length-1];return l=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(t)}(t)};function s(t){return t===i?(a++,e.consume(t),s):a<3?n(t):(e.exit("codeFencedFenceSequence"),qe(t)?je(e,c,"whitespace")(t):c(t))}function c(n){return null===n||He(n)?(e.exit("codeFencedFence"),r.interrupt?t(n):e.check(Et,p,v)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),u(n))}function u(t){return null===t||He(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),c(t)):qe(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),je(e,f,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),u)}function f(t){return null===t||He(t)?c(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),d(t))}function d(t){return null===t||He(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),c(t)):96===t&&t===i?n(t):(e.consume(t),d)}function p(t){return e.attempt(o,v,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),m}function m(t){return l>0&&qe(t)?je(e,g,"linePrefix",l+1)(t):g(t)}function g(t){return null===t||He(t)?e.check(Et,p,v)(t):(e.enter("codeFlowValue"),y(t))}function y(t){return null===t||He(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),y)}function v(n){return e.exit("codeFenced"),t(n)}}};const Ot=document.createElement("i");function It(e){const t="&"+e+";";Ot.innerHTML=t;const n=Ot.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&(n!==t&&n)}const Pt={name:"characterReference",tokenize:function(e,t,n){const r=this;let o,i,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),o=31,i=Ne,c(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=Be,c):(e.enter("characterReferenceValue"),o=7,i=Re,c(t))}function c(a){if(59===a&&l){const o=e.exit("characterReferenceValue");return i!==Ne||It(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&l++<o?(e.consume(a),c):n(a)}}};const Tt={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return _e(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}};const zt={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),je(e,t,"linePrefix")}}};function Dt(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const jt={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",t+=e}}e.length!==n.length&&Ce(e,0,e.length,n);return e},resolveTo:function(e,t){let n,r,o,i,l=e.length,a=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(o=l);const s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},u={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",s,t],["enter",c,t]],i=Se(i,e.slice(r+1,r+a+3)),i=Se(i,[["enter",u,t]]),i=Se(i,Dt(t.parser.constructs.insideSpan.null,e.slice(r+a+4,o-3),t)),i=Se(i,[["exit",u,t],e[o-2],e[o-1],["exit",c,t]]),i=Se(i,e.slice(o+1)),i=Se(i,[["exit",s,t]]),Ce(e,r,e.length,i),e},tokenize:function(e,t,n){const r=this;let o,i,l=r.events.length;for(;l--;)if(("labelImage"===r.events[l][1].type||"labelLink"===r.events[l][1].type)&&!r.events[l][1]._balanced){o=r.events[l][1];break}return function(t){if(!o)return n(t);if(o._inactive)return u(t);return i=r.parser.defined.includes(ht(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a};function a(t){return 40===t?e.attempt(Ft,c,i?c:u)(t):91===t?e.attempt(Nt,c,i?s:u)(t):i?c(t):u(t)}function s(t){return e.attempt(Lt,c,u)(t)}function c(e){return t(e)}function u(e){return o._balanced=!0,n(e)}}},Ft={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return Ue(t)?pt(e,o)(t):o(t)}function o(t){return 41===t?c(t):ut(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return Ue(t)?pt(e,a)(t):c(t)}function l(e){return n(e)}function a(t){return 34===t||39===t||40===t?dt(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function s(t){return Ue(t)?pt(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},Nt={tokenize:function(e,t,n){const r=this;return function(t){return ft.call(r,e,o,i,"reference","referenceMarker","referenceString")(t)};function o(e){return r.parser.defined.includes(ht(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},Lt={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}};const Mt={name:"labelStartImage",resolveAll:jt.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),o};function o(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};_t(/[A-Za-z]/),_t(/[\dA-Za-z]/),_t(/[#-'*+\--9=?A-Z^-~]/);_t(/\d/),_t(/[\dA-Fa-f]/),_t(/[!-/:-@[-`{-~]/);const Rt=_t(/\p{P}|\p{S}/u),Bt=_t(/\s/);function _t(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Ht(e){return null===e||function(e){return null!==e&&(e<0||32===e)}(e)||Bt(e)?1:Rt(e)?2:void 0}const Ut={name:"attention",resolveAll:function(e,t){let n,r,o,i,l,a,s,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close)for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;const f={...e[n][1].end},d={...e[u][1].start};qt(f,-a),qt(d,a),i={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},l={type:a>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:d},o={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:a>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[n][1].end={...i.start},e[u][1].start={...l.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=Se(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=Se(s,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",o,t]]),s=Se(s,Dt(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),s=Se(s,[["exit",o,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,s=Se(s,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,Ce(e,n-1,u-n+3,s),u=n+s.length-c-2;break}u=-1;for(;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=Ht(r);let i;return function(t){return i=t,e.enter("attentionSequence"),l(t)};function l(a){if(a===i)return e.consume(a),l;const s=e.exit("attentionSequence"),c=Ht(a),u=!c||2===c&&o||n.includes(a),f=!o||2===o&&c||n.includes(r);return s._open=Boolean(42===i?u:u&&(o||!f)),s._close=Boolean(42===i?f:f&&(c||!u)),t(a)}}};function qt(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Vt={name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(t){return Fe(t)?(e.consume(t),i):64===t?n(t):s(t)}function i(e){return 43===e||45===e||46===e||Ne(e)?(r=1,l(e)):s(e)}function l(t){return 58===t?(e.consume(t),r=0,a):(43===t||45===t||46===t||Ne(t))&&r++<32?(e.consume(t),l):(r=0,s(t))}function a(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||Me(r)?n(r):(e.consume(r),a)}function s(t){return 64===t?(e.consume(t),c):Le(t)?(e.consume(t),s):n(t)}function c(e){return Ne(e)?u(e):n(e)}function u(n){return 46===n?(e.consume(n),r=0,c):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):f(n)}function f(t){if((45===t||Ne(t))&&r++<63){const n=45===t?f:u;return e.consume(t),n}return n(t)}}};const Wt={name:"htmlText",tokenize:function(e,t,n){const r=this;let o,i,l;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),x):63===t?(e.consume(t),v):Fe(t)?(e.consume(t),C):n(t)}function s(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,p):Fe(t)?(e.consume(t),y):n(t)}function c(t){return 45===t?(e.consume(t),d):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),f):He(t)?(l=u,z(t)):(e.consume(t),u)}function f(t){return 45===t?(e.consume(t),d):u(t)}function d(e){return 62===e?T(e):45===e?f(e):u(e)}function p(t){const r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),6===i?h:p):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):He(t)?(l=h,z(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?T(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?T(t):He(t)?(l=y,z(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),b):He(t)?(l=v,z(t)):(e.consume(t),v)}function b(e){return 62===e?T(e):v(e)}function x(t){return Fe(t)?(e.consume(t),k):n(t)}function k(t){return 45===t||Ne(t)?(e.consume(t),k):w(t)}function w(t){return He(t)?(l=w,z(t)):qe(t)?(e.consume(t),w):T(t)}function C(t){return 45===t||Ne(t)?(e.consume(t),C):47===t||62===t||Ue(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),T):58===t||95===t||Fe(t)?(e.consume(t),A):He(t)?(l=S,z(t)):qe(t)?(e.consume(t),S):T(t)}function A(t){return 45===t||46===t||58===t||95===t||Ne(t)?(e.consume(t),A):E(t)}function E(t){return 61===t?(e.consume(t),$):He(t)?(l=E,z(t)):qe(t)?(e.consume(t),E):S(t)}function $(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,O):He(t)?(l=$,z(t)):qe(t)?(e.consume(t),$):(e.consume(t),I)}function O(t){return t===o?(e.consume(t),o=void 0,P):null===t?n(t):He(t)?(l=O,z(t)):(e.consume(t),O)}function I(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||Ue(t)?S(t):(e.consume(t),I)}function P(e){return 47===e||62===e||Ue(e)?S(e):n(e)}function T(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function z(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return qe(t)?je(e,j,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):j(t)}function j(t){return e.enter("htmlTextData"),l(t)}}};const Xt={name:"labelStartLink",resolveAll:jt.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const Zt={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return He(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}};const Gt={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(t=o;++t<r;)if("codeTextData"===e[t][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}t=o-1,r++;for(;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):t!==r&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,o,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),l(t)};function l(t){return 96===t?(e.consume(t),i++,l):(e.exit("codeTextSequence"),a(t))}function a(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),a):96===t?(o=e.enter("codeTextSequence"),r=0,c(t)):He(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a):(e.enter("codeTextData"),s(t))}function s(t){return null===t||32===t||96===t||He(t)?(e.exit("codeTextData"),a(t)):(e.consume(t),s)}function c(n){return 96===n?(e.consume(n),r++,c):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(o.type="codeTextData",s(n))}}};const Qt={42:lt,43:lt,45:lt,48:lt,49:lt,50:lt,51:lt,52:lt,53:lt,54:lt,55:lt,56:lt,57:lt,62:ct},Yt={91:mt},Kt={[-2]:yt,[-1]:yt,32:yt},Jt={35:bt,42:it,45:[xt,it],60:Ct,61:xt,95:it,96:$t,126:$t},en={38:Pt,92:Tt},tn={[-5]:zt,[-4]:zt,[-3]:zt,33:Mt,38:Pt,42:Ut,60:[Vt,Wt],91:Xt,92:[Zt,Tt],93:jt,95:Ut,96:Gt},nn={null:[Ut,Je]},rn={null:[42,95]},on={null:[]};function ln(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let l=[],a=[],s=!0;const c={attempt:v((function(e,t){b(e,t.from)})),check:v(y),consume:function(e){He(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,x()):-1!==e&&(r.column++,r.offset++);r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++));u.previous=e,s=!0},enter:function(e,t){const n=t||{};return n.type=e,n.start=h(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){const t=a.pop();return t.end=h(),u.events.push(["exit",t,u]),t},interrupt:v(y,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,x()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=t?" ":"\t";break;case-1:if(!t&&o)continue;l=" ";break;default:l=String.fromCharCode(i)}o=-2===i,r.push(l)}return r.join("")}(p(e),t)},sliceStream:p,write:function(e){if(l=Se(l,e),m(),null!==l[l.length-1])return[];return b(t,0),u.events=Dt(i,u.events,u),u.events}};let f,d=t.tokenize.call(u,c);return t.resolveAll&&i.push(t),u;function p(e){return function(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let l;if(n===o)l=[e[n].slice(r,i)];else{if(l=e.slice(n,o),r>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(r):l.shift()}i>0&&l.push(e[o].slice(0,i))}return l}(l,e)}function h(){const{_bufferIndex:e,_index:t,line:n,column:o,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:o,offset:i}}function m(){let e;for(;r._index<l.length;){const t=l[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;)g(t.charCodeAt(r._bufferIndex));else g(t)}}function g(e){s=void 0,f=e,d=d(e)}function y(e,t){t.restore()}function v(e,t){return function(n,o,i){let l,f,d,p;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):function(e){return t;function t(t){const n=null!==t&&e[t],r=null!==t&&e.null;return m([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(r)?r:r?[r]:[]])(t)}}(n);function m(e){return l=e,f=0,0===e.length?i:g(e[f])}function g(e){return function(n){p=function(){const e=h(),t=u.previous,n=u.currentConstruct,o=u.events.length,i=Array.from(a);return{from:o,restore:l};function l(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=o,a=i,x()}}(),d=e,e.partial||(u.currentConstruct=e);if(e.name&&u.parser.constructs.disable.null.includes(e.name))return v(n);return e.tokenize.call(t?Object.assign(Object.create(u),t):u,c,y,v)(n)}}function y(t){return s=!0,e(d,p),o}function v(e){return s=!0,p.restore(),++f<l.length?g(l[f]):i}}}function b(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&Ce(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function x(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}function an(e){const t=function(e){const t={};let n=-1;for(;++n<e.length;)Pe(t,e[n]);return t}([o,...(e||{}).extensions||[]]),n={constructs:t,content:r(We),defined:[],document:r(Xe),flow:r(Ke),lazy:{},string:r(et),text:r(tt)};return n;function r(e){return function(t){return ln(n,e,t)}}}const sn=/[\0\t\n\r]/g;function cn(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}const un=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function fn(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return cn(n.slice(t?2:1),t?16:10)}return It(n)||e}function dn(e){return e&&"object"==typeof e?"position"in e||"type"in e?hn(e.position):"start"in e||"end"in e?hn(e):"line"in e||"column"in e?pn(e):"":""}function pn(e){return mn(e&&e.line)+":"+mn(e&&e.column)}function hn(e){return pn(e&&e.start)+"-"+pn(e&&e.end)}function mn(e){return e&&"number"==typeof e?e:1}const gn={}.hasOwnProperty;function yn(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:A,autolinkEmail:A,atxHeading:i(Y),blockQuote:i(W),characterEscape:A,characterReference:A,codeFenced:i(X),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(X,l),codeText:i(Z,l),codeTextData:A,data:A,codeFlowValue:A,definition:i(G),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(Q),hardBreakEscape:i(K),hardBreakTrailing:i(K),htmlFlow:i(J,l),htmlFlowData:A,htmlText:i(J,l),htmlTextData:A,image:i(ee),label:l,link:i(te),listItem:i(re),listItemValue:d,listOrdered:i(ne,f),listUnordered:i(ne),paragraph:i(oe),reference:R,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(Y),strong:i(ie),thematicBreak:i(ae)},exit:{atxHeading:s(),atxHeadingSequence:k,autolink:s(),autolinkEmail:V,autolinkProtocol:q,blockQuote:s(),characterEscapeValue:E,characterReferenceMarkerHexadecimal:_,characterReferenceMarkerNumeric:_,characterReferenceValue:H,characterReference:U,codeFenced:s(g),codeFencedFence:m,codeFencedFenceInfo:p,codeFencedFenceMeta:h,codeFlowValue:E,codeIndented:s(y),codeText:s(T),codeTextData:E,data:E,definition:s(),definitionDestinationString:x,definitionLabelString:v,definitionTitleString:b,emphasis:s(),hardBreakEscape:s(O),hardBreakTrailing:s(O),htmlFlow:s(I),htmlFlowData:E,htmlText:s(P),htmlTextData:E,image:s(D),label:F,labelText:j,lineEnding:$,link:s(z),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:B,resourceDestinationString:N,resourceTitleString:L,resource:M,setextHeading:s(S),setextHeadingLineSequence:C,setextHeadingText:w,strong:s(),thematicBreak:s()}};bn(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(e){let r={type:"root",children:[]};const i={stack:[r],tokenStack:[],config:t,enter:a,exit:c,buffer:l,resume:u,data:n},s=[];let f=-1;for(;++f<e.length;)if("listOrdered"===e[f][1].type||"listUnordered"===e[f][1].type)if("enter"===e[f][0])s.push(f);else{f=o(e,s.pop(),f)}for(f=-1;++f<e.length;){const n=t[e[f][0]];gn.call(n,e[f][1].type)&&n[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},i),e[f][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||kn).call(i,void 0,e[0])}for(r.position={start:vn(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:vn(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<t.transforms.length;)r=t.transforms[f](r)||r;return r}function o(e,t,n){let r,o,i,l,a=t-1,s=-1,c=!1;for(;++a<=n;){const t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||s||i||(i=a),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=a;for(o=void 0;l--;){const t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;o&&(e[o][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",o=l}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:t[1].end),e.splice(o||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=o,e.splice(a,0,["enter",o,t[2]]),a++,n++,i=void 0,l=!0}}}return e[t][1]._spread=c,n}function i(e,t){return n;function n(n){a.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:vn(t.start),end:void 0}}function s(e){return t;function t(t){e&&e.call(this,t),c.call(this,t)}}function c(e,t){const n=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+dn({start:e.start,end:e.end})+"): it’s not open");if(r[0].type!==e.type)if(t)t.call(this,e,r[0]);else{(r[1]||kn).call(this,e,r[0])}n.position.end=vn(e.end)}function u(){return function(e,t){const n=t||xe;return ke(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function d(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function p(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function h(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function m(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function v(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=ht(this.sliceSerialize(e)).toLowerCase()}function b(){const e=this.resume();this.stack[this.stack.length-1].title=e}function x(){const e=this.resume();this.stack[this.stack.length-1].url=e}function k(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function C(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function S(){this.data.setextHeadingSlurpLineEnding=void 0}function A(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n=le(),n.position={start:vn(e.start),end:void 0},t.push(n)),this.stack.push(n)}function E(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=vn(e.end)}function $(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak){return n.children[n.children.length-1].position.end=vn(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(A.call(this,e),E.call(this,e))}function O(){this.data.atHardBreak=!0}function I(){const e=this.resume();this.stack[this.stack.length-1].value=e}function P(){const e=this.resume();this.stack[this.stack.length-1].value=e}function T(){const e=this.resume();this.stack[this.stack.length-1].value=e}function z(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function D(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function j(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(un,fn)}(t),n.identifier=ht(t).toLowerCase()}function F(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t}function N(){const e=this.resume();this.stack[this.stack.length-1].url=e}function L(){const e=this.resume();this.stack[this.stack.length-1].title=e}function M(){this.data.inReference=void 0}function R(){this.data.referenceType="collapsed"}function B(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=ht(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function _(e){this.data.characterReferenceType=e.type}function H(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let r;if(n)r=cn(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0;else{r=It(t)}this.stack[this.stack.length-1].value+=r}function U(e){this.stack.pop().position.end=vn(e.end)}function q(e){E.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function V(e){E.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function W(){return{type:"blockquote",children:[]}}function X(){return{type:"code",lang:null,meta:null,value:""}}function Z(){return{type:"inlineCode",value:""}}function G(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Q(){return{type:"emphasis",children:[]}}function Y(){return{type:"heading",depth:0,children:[]}}function K(){return{type:"break"}}function J(){return{type:"html",value:""}}function ee(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function ne(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function re(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function oe(){return{type:"paragraph",children:[]}}function ie(){return{type:"strong",children:[]}}function le(){return{type:"text",value:""}}function ae(){return{type:"thematicBreak"}}}(n)(function(e){for(;!$e(e););return e}(an(n).document().write(function(){let e,t=1,n="",r=!0;return function(o,i,l){const a=[];let s,c,u,f,d;for(o=n+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),u=0,n="",r&&(65279===o.charCodeAt(0)&&u++,r=void 0);u<o.length;){if(sn.lastIndex=u,s=sn.exec(o),f=s&&void 0!==s.index?s.index:o.length,d=o.charCodeAt(f),!s){n=o.slice(u);break}if(10===d&&u===f&&e)a.push(-3),e=void 0;else switch(e&&(a.push(-5),e=void 0),u<f&&(a.push(o.slice(u,f)),t+=f-u),d){case 0:a.push(65533),t++;break;case 9:for(c=4*Math.ceil(t/4),a.push(-2);t++<c;)a.push(-1);break;case 10:a.push(-4),t=1;break;default:e=!0,t=1}u=f+1}return l&&(e&&a.push(-5),n&&a.push(n),a.push(null)),a}}()(e,t,!0))))}function vn(e){return{line:e.line,column:e.column,offset:e.offset}}function bn(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?bn(e,r):xn(e,r)}}function xn(e,t){let n;for(n in t)if(gn.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function kn(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+dn({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+dn({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+dn({start:t.start,end:t.end})+") is still open")}function wn(e){const t=this;t.parser=function(n){return yn(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}const Cn="object"==typeof self?self:globalThis,Sn=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);const[i,l]=t[o];switch(i){case 0:case-1:return n(l,o);case 1:{const e=n([],o);for(const t of l)e.push(r(t));return e}case 2:{const e=n({},o);for(const[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),o);case 4:{const{source:e,flags:t}=l;return n(new RegExp(e,t),o)}case 5:{const e=n(new Map,o);for(const[t,n]of l)e.set(r(t),r(n));return e}case 6:{const e=n(new Set,o);for(const t of l)e.add(r(t));return e}case 7:{const{name:e,message:t}=l;return n(new Cn[e](t),o)}case 8:return n(BigInt(l),o);case"BigInt":return n(Object(BigInt(l)),o);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{const{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new Cn[i](l),o)};return r})(new Map,e)(0),An="",{toString:En}={},{keys:$n}=Object,On=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=En.call(e).slice(8,-1);switch(n){case"Array":return[1,An];case"Object":return[2,An];case"Date":return[3,An];case"RegExp":return[4,An];case"Map":return[5,An];case"Set":return[6,An];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},In=([e,t])=>0===e&&("function"===t||"symbol"===t),Pn=(e,{json:t,lossy:n}={})=>{const r=[];return((e,t,n,r)=>{const o=(e,t)=>{const o=r.push(e)-1;return n.set(t,o),o},i=r=>{if(n.has(r))return n.get(r);let[l,a]=On(r);switch(l){case 0:{let t=r;switch(a){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+a);t=null;break;case"undefined":return o([-1],r)}return o([l,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),o([a,[...e]],r)}const e=[],t=o([l,e],r);for(const n of r)e.push(i(n));return t}case 2:{if(a)switch(a){case"BigInt":return o([a,r.toString()],r);case"Boolean":case"Number":case"String":return o([a,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());const n=[],s=o([l,n],r);for(const t of $n(r))!e&&In(On(r[t]))||n.push([i(t),i(r[t])]);return s}case 3:return o([l,r.toISOString()],r);case 4:{const{source:e,flags:t}=r;return o([l,{source:e,flags:t}],r)}case 5:{const t=[],n=o([l,t],r);for(const[o,l]of r)(e||!In(On(o))&&!In(On(l)))&&t.push([i(o),i(l)]);return n}case 6:{const t=[],n=o([l,t],r);for(const o of r)!e&&In(On(o))||t.push(i(o));return n}}const{message:s}=r;return o([l,{name:a,message:s}],r)};return i})(!(t||n),!!t,new Map,r)(e),r};var Tn="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?Sn(Pn(e,t)):structuredClone(e):(e,t)=>Sn(Pn(e,t));Dn(/[A-Za-z]/);const zn=Dn(/[\dA-Za-z]/);Dn(/[#-'*+\--9=?A-Z^-~]/);Dn(/\d/),Dn(/[\dA-Fa-f]/),Dn(/[!-/:-@[-`{-~]/);Dn(/\p{P}|\p{S}/u),Dn(/\s/);function Dn(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function jn(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let l="";if(37===i&&zn(e.charCodeAt(n+1))&&zn(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),o=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+o+1,l=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function Fn(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function Nn(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const Ln=function(e){if(null==e)return Rn;if("function"==typeof e)return Mn(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Ln(e[n]);return Mn(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return Mn(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return Mn(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function Mn(e){return function(t,n,r){return Boolean(Bn(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function Rn(){return!0}function Bn(e){return null!==e&&"object"==typeof e&&"type"in e}const _n=[],Hn=!0,Un=!1;function qn(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=Ln(o),l=r?-1:1;!function e(o,a,s){const c=o&&"object"==typeof o?o:{};if("string"==typeof c.type){const e="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(u,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return u;function u(){let c,u,f,d=_n;if((!t||i(o,a,s[s.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[Hn,e];return null==e?_n:[e]}(n(o,s)),d[0]===Un))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(u=(r?t.children.length:-1)+l,f=s.concat(t);u>-1&&u<t.children.length;){const n=t.children[u];if(c=e(n,u,f)(),c[0]===Un)return c;u="number"==typeof c[1]?c[1]:u+l}}return d}}(e,void 0,[])()}const Vn=Xn("end"),Wn=Xn("start");function Xn(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Zn(e,t){const n=t.referenceType;let r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const l=o[o.length-1];return l&&"text"===l.type?l.value+=r:o.push({type:"text",value:r}),o}function Gn(e){const t=e.spread;return null==t?e.children.length>1:t}function Qn(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(Yn(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(Yn(t.slice(o),o>0,!1)),i.join("")}function Yn(e,t,n){let r=0,o=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(o-1);for(;9===t||32===t;)o--,t=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const Kn={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=jn(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let l,a=e.footnoteCounts.get(r);void 0===a?(a=0,e.footnoteOrder.push(r),l=e.footnoteOrder.length):l=i+1,a+=1,e.footnoteCounts.set(r,a);const s={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,s);const c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Zn(e,t);const o={src:jn(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:jn(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Zn(e,t);const o={href:jn(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:jn(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){const r=e.all(t),o=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Gn(n[r])}return t}(n):Gn(t),i={},l=[];if("boolean"==typeof t.checked){const e=r[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},r.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let a=-1;for(;++a<r.length;){const e=r[a];(o||0!==a||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?l.push(e):l.push(...e.children)}const s=r[r.length-1];s&&(o||"element"!==s.type||"p"!==s.tagName)&&l.push({type:"text",value:"\n"});const c={type:"element",tagName:"li",properties:i,children:l};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){const n={},r=e.all(t);let o=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),o.push(n)}if(n.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=Wn(t.children[1]),l=Vn(t.children[t.children.length-1]);i&&l&&(r.position={start:i,end:l}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const r=n?n.children:void 0,o=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,l=i?i.length:t.children.length;let a=-1;const s=[];for(;++a<l;){const n=t.children[a],r={},l=i?i[a]:void 0;l&&(r.align=l);let c={type:"element",tagName:o,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),s.push(c)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){const n={type:"text",value:Qn(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:Jn,yaml:Jn,definition:Jn,footnoteDefinition:Jn};function Jn(){}const er={}.hasOwnProperty,tr={};function nr(e,t){const n=t||tr,r=new Map,o=new Map,i=new Map,l={...Kn,...n.handlers},a={all:function(e){const t=[];if("children"in e){const n=e.children;let r=-1;for(;++r<n.length;){const o=a.one(n[r],e);if(o){if(r&&"break"===n[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=ar(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=ar(e.value))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t},applyData:or,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,t){const n=e.type,r=a.handlers[n];if(er.call(a.handlers,n)&&r)return r(a,e,t);if(a.options.passThrough&&a.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,r=Tn(n);return r.children=a.all(e),r}return Tn(e)}return(a.options.unknownHandler||ir)(a,e,t)},options:n,patch:rr,wrap:lr};return function(e,t,n,r){let o,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,o=n):(i=t,l=n,o=r),qn(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)}),o)}(e,(function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?r:o,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}})),a}function rr(e,t){e.position&&(t.position=function(e){const t=Wn(e),n=Vn(e);if(t&&n)return{start:t,end:n}}(e))}function or(e,t){let n=t;if(e&&e.data){const t=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;if("string"==typeof t)if("element"===n.type)n.tagName=t;else{n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}}"element"===n.type&&o&&Object.assign(n.properties,Tn(o)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ir(e,t){const n=t.data||{},r=!("value"in t)||er.call(n,"hProperties")||er.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,r),e.applyData(t,r)}function lr(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function ar(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function sr(e,t){const n=nr(e,t),r=n.one(e,void 0),o=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||Fn,r=e.options.footnoteBackLabel||Nn,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[];let s=-1;for(;++s<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[s]);if(!o)continue;const i=e.all(o),l=String(o.identifier).toUpperCase(),c=jn(l.toLowerCase());let u=0;const f=[],d=e.footnoteCounts.get(l);for(;void 0!==d&&++u<=d;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,u);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){const e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...f)}else i.push(...f);const h={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(i,!0)};e.patch(o,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...Tn(l),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:"\n"},o),i}function cr(e,t){return e&&"run"in e?async function(n,r){const o=sr(n,{file:r,...t});await e.run(o,r)}:function(n,r){return sr(n,{file:r,...e||t})}}function ur(e){if(e)throw e}var fr=n(2849);function dr(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}function pr(){const e=[],t={run:function(...t){let n=-1;const r=t.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...l){const a=e[++n];let s=-1;if(i)r(i);else{for(;++s<t.length;)null!==l[s]&&void 0!==l[s]||(l[s]=t[s]);t=l,a?function(e,t){let n;return r;function r(...t){const r=e.length>t.length;let a;r&&t.push(o);try{a=e.apply(this,t)}catch(i){if(r&&n)throw i;return o(i)}r||(a&&a.then&&"function"==typeof a.then?a.then(l,o):a instanceof Error?o(a):l(a))}function o(e,...r){n||(n=!0,t(e,...r))}function l(e){o(null,e)}}(a,o)(...l):r(null,...l)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}function hr(e){return gr(e&&e.line)+":"+gr(e&&e.column)}function mr(e){return hr(e&&e.start)+"-"+hr(e&&e.end)}function gr(e){return e&&"number"==typeof e?e:1}class yr extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;var a;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=((a=o.place)&&"object"==typeof a?"position"in a||"type"in a?mr(a.position):"start"in a||"end"in a?mr(a):"line"in a||"column"in a?hr(a):"":"")||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}yr.prototype.file="",yr.prototype.name="",yr.prototype.reason="",yr.prototype.message="",yr.prototype.stack="",yr.prototype.column=void 0,yr.prototype.line=void 0,yr.prototype.ancestors=void 0,yr.prototype.cause=void 0,yr.prototype.fatal=void 0,yr.prototype.place=void 0,yr.prototype.ruleId=void 0,yr.prototype.source=void 0;const vr={basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');br(e);let n,r=0,o=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else o<0&&(n=!0,o=i+1);return o<0?"":e.slice(r,o)}if(t===e)return"";let l=-1,a=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),a>-1&&(e.codePointAt(i)===t.codePointAt(a--)?a<0&&(o=i):(a=-1,o=l));r===o?o=l:o<0&&(o=e.length);return e.slice(r,o)},dirname:function(e){if(br(e),0===e.length)return".";let t,n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){br(e);let t,n=e.length,r=-1,o=0,i=-1,l=0;for(;n--;){const a=e.codePointAt(n);if(47!==a)r<0&&(t=!0,r=n+1),46===a?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1);else if(t){o=n+1;break}}if(i<0||r<0||0===l||1===l&&i===r-1&&i===o+1)return"";return e.slice(i,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)br(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){br(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,r,o="",i=0,l=-1,a=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else{if(47===n)break;n=47}if(47===n){if(l===s-1||1===a);else if(l!==s-1&&2===a){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),l=s,a=0;continue}}else if(o.length>0){o="",i=0,l=s,a=0;continue}t&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(l+1,s):o=e.slice(l+1,s),i=s-l-1;l=s,a=0}else 46===n&&a>-1?a++:a=-1}return o}(e,!t);0!==n.length||t||(n=".");n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/");return t?"/"+n:n}(t)},sep:"/"};function br(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const xr={cwd:function(){return"/"}};function kr(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function wr(e){if("string"==typeof e)e=new URL(e);else if(!kr(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}const Cr=["history","path","basename","stem","extname","dirname"];class Sr{constructor(e){let t;t=e?kr(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":xr.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,r=-1;for(;++r<Cr.length;){const e=Cr[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)Cr.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?vr.basename(this.path):void 0}set basename(e){Er(e,"basename"),Ar(e,"basename"),this.path=vr.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?vr.dirname(this.path):void 0}set dirname(e){$r(this.basename,"dirname"),this.path=vr.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?vr.extname(this.path):void 0}set extname(e){if(Ar(e,"extname"),$r(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=vr.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){kr(e)&&(e=wr(e)),Er(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?vr.basename(this.path,this.extname):void 0}set stem(e){Er(e,"stem"),Ar(e,"stem"),this.path=vr.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){const r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){const r=new yr(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function Ar(e,t){if(e&&e.includes(vr.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+vr.sep+"`")}function Er(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function $r(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const Or=function(e){const t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},Ir={}.hasOwnProperty;class Pr extends Or{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=pr()}copy(){const e=new Pr;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(fr(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(jr("data",this.frozen),this.namespace[e]=t,this):Ir.call(this.namespace,e)&&this.namespace[e]||void 0:e?(jr("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const r=t.call(e,...n);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=Lr(e),n=this.parser||this.Parser;return zr("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),zr("process",this.parser||this.Parser),Dr("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){const i=Lr(e),l=n.parse(i);function a(e,n){e||!n?o(e):r?r(n):t(void 0,n)}n.run(l,i,(function(e,t,r){if(e||!t||!r)return a(e);const o=t,i=n.stringify(o,r);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?r.value=i:r.result=i,a(e,r)}))}}processSync(e){let t,n=!1;return this.freeze(),zr("processSync",this.parser||this.Parser),Dr("processSync",this.compiler||this.Compiler),this.process(e,(function(e,r){n=!0,ur(e),t=r})),Nr("processSync","process",n),t}run(e,t,n){Fr(e),this.freeze();const r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,i){const l=Lr(t);r.run(e,l,(function(t,r,l){const a=r||e;t?i(t):o?o(a):n(void 0,a,l)}))}}runSync(e,t){let n,r=!1;return this.run(e,t,(function(e,t){ur(e),n=t,r=!0})),Nr("runSync","run",r),n}stringify(e,t){this.freeze();const n=Lr(t),r=this.compiler||this.Compiler;return Dr("stringify",r),Fr(e),r(e,n)}use(e,...t){const n=this.attachers,r=this.namespace;if(jr("use",this.frozen),null==e);else if("function"==typeof e)a(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function o(e){if("function"==typeof e)a(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;a(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=fr(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;){o(e[t])}}}function a(e,t){let r=-1,o=-1;for(;++r<n.length;)if(n[r][0]===e){o=r;break}if(-1===o)n.push([e,...t]);else if(t.length>0){let[r,...i]=t;const l=n[o][1];dr(l)&&dr(r)&&(r=fr(!0,l,r)),n[o]=[e,r,...i]}}}}const Tr=(new Pr).freeze();function zr(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function Dr(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function jr(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Fr(e){if(!dr(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function Nr(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Lr(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new Sr(e)}const Mr=[],Rr={allowDangerousHtml:!0},Br=/^(https?|ircs?|mailto|xmpp)$/i,_r=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Hr(e){const t=Ur(e),n=qr(e);return Vr(t.runSync(t.parse(n),n),e)}function Ur(e){const t=e.rehypePlugins||Mr,n=e.remarkPlugins||Mr,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Rr}:Rr;return Tr().use(wn).use(n).use(cr,r).use(t)}function qr(e){const t=e.children||"",n=new Sr;return"string"==typeof t&&(n.value=t),n}function Vr(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,l=t.skipHtml,a=t.unwrapDisallowed,s=t.urlTransform||Wr;for(const c of _r)Object.hasOwn(t,c.from)&&(c.from,c.to&&c.to,c.id);return t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),function(e,t,n,r){let o,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,o=n):(i=t,l=n,o=r),qn(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)}),o)}(e,(function(e,t,o){if("raw"===e.type&&o&&"number"==typeof t)return l?o.children.splice(t,1):o.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ve)if(Object.hasOwn(ve,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],r=ve[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,o)),l&&o&&"number"==typeof t)return a&&e.children?o.children.splice(t,1,...e.children):o.children.splice(t,1),t}})),se(e,{Fragment:be.Fragment,components:o,ignoreInvalidStyle:!0,jsx:be.jsx,jsxs:be.jsxs,passKeys:!0,passNode:!0})}function Wr(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return-1===t||-1!==o&&t>o||-1!==n&&t>n||-1!==r&&t>r||Br.test(e.slice(0,t))?e:""}var Xr=n(955);function Zr(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;-1!==o;)r++,o=n.indexOf(t,o+t.length);return r}Yr(/[A-Za-z]/),Yr(/[\dA-Za-z]/),Yr(/[#-'*+\--9=?A-Z^-~]/);Yr(/\d/),Yr(/[\dA-Fa-f]/),Yr(/[!-/:-@[-`{-~]/);const Gr=Yr(/\p{P}|\p{S}/u),Qr=Yr(/\s/);function Yr(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Kr=function(e){if(null==e)return eo;if("function"==typeof e)return Jr(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Kr(e[n]);return Jr(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return Jr(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return Jr(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function Jr(e){return function(t,n,r){return Boolean(to(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function eo(){return!0}function to(e){return null!==e&&"object"==typeof e&&"type"in e}function no(e,t,n){const r=Kr((n||{}).ignore||[]),o=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([ro(e[0]),oo(e[1])])}return t}(t);let i=-1;for(;++i<o.length;)qn(e,"text",l);function l(e,t){let n,l=-1;for(;++l<t.length;){const e=t[l],o=n?n.children:void 0;if(r(e,o?o.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=o[i][0],l=o[i][1];let a=0;const s=n.children.indexOf(e);let c=!1,u=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,o={index:f.index,input:f.input,stack:[...t,e]};let i=l(...f,o);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?r.lastIndex=n+1:(a!==n&&u.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(i)?u.push(...i):i&&u.push(i),a=n+f[0].length,c=!0),!r.global)break;f=r.exec(e.value)}c?(a<e.value.length&&u.push({type:"text",value:e.value.slice(a)}),n.children.splice(s,1,...u)):u=[e];return s+u.length}(e,t)}}function ro(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function oo(e){return"function"==typeof e?e:function(){return e}}const io="phrasing",lo=["autolink","link","image","label"];function ao(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function so(e){this.config.enter.autolinkProtocol.call(this,e)}function co(e){this.config.exit.autolinkProtocol.call(this,e)}function uo(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function fo(e){this.config.exit.autolinkEmail.call(this,e)}function po(e){this.exit(e)}function ho(e){no(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,mo],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,go]],{ignore:["link","linkReference"]})}function mo(e,t,n,r,o){let i="";if(!yo(o))return!1;if(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const l=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const o=Zr(e,"(");let i=Zr(e,")");for(;-1!==r&&o>i;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),i++;return[e,n]}(n+r);if(!l[0])return!1;const a={type:"link",title:null,url:i+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[a,{type:"text",value:l[1]}]:a}function go(e,t,n,r){return!(!yo(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function yo(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||Qr(n)||Gr(n))&&(!t||47!==n)}function vo(){this.buffer()}function bo(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function xo(){this.buffer()}function ko(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function wo(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ht(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Co(e){this.exit(e)}function So(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ht(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Ao(e){this.exit(e)}function Eo(e,t,n,r){const o=n.createTracker(r);let i=o.move("[^");const l=n.enter("footnoteReference"),a=n.enter("reference");return i+=o.move(n.safe(n.associationId(e),{after:"]",before:i})),a(),l(),i+=o.move("]"),i}function $o(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,o){const i=r.createTracker(o);let l=i.move("[^");const a=r.enter("footnoteDefinition"),s=r.enter("label");l+=i.move(r.safe(r.associationId(e),{before:l,after:"]"})),s(),l+=i.move("]:"),e.children&&e.children.length>0&&(i.shift(4),l+=i.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,i.current()),t?Io:Oo)));return a(),l},footnoteReference:Eo},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function Oo(e,t,n){return 0===t?e:Io(e,t,n)}function Io(e,t,n){return(n?"":"    ")+e}Eo.peek=function(){return"["};const Po=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function To(e){this.enter({type:"delete",children:[]},e)}function zo(e){this.exit(e)}function Do(e,t,n,r){const o=n.createTracker(r),i=n.enter("strikethrough");let l=o.move("~~");return l+=n.containerPhrasing(e,{...o.current(),before:l,after:"~"}),l+=o.move("~~"),i(),l}function jo(e){return e.length}function Fo(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function No(e,t,n){return">"+(n?"":" ")+e}function Lo(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Mo(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&(i=n.stack,l=n.unsafe[o],Lo(i,l.inConstruct,!0)&&!Lo(i,l.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var i,l;return"\\\n"}function Ro(e,t,n){return(n?"":"    ")+e}function Bo(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function _o(e){return"&#x"+e.toString(16).toUpperCase()+";"}function Ho(e,t,n){const r=Ht(e),o=Ht(t);return void 0===r?void 0===o?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function Uo(e,t,n,r){const o=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),l=n.createTracker(r),a=l.move(o);let s=l.move(n.containerPhrasing(e,{after:o,before:a,...l.current()}));const c=s.charCodeAt(0),u=Ho(r.before.charCodeAt(r.before.length-1),c,o);u.inside&&(s=_o(c)+s.slice(1));const f=s.charCodeAt(s.length-1),d=Ho(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+_o(f));const p=l.move(o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:u.outside},a+s+p}Do.peek=function(){return"~"},Uo.peek=function(e,t,n){return n.options.emphasis||"*"};const qo={};function Vo(e,t){const n=t||qo;return Wo(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function Wo(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Xo(e.children,t,n)}return Array.isArray(e)?Xo(e,t,n):""}function Xo(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=Wo(e[o],t,n);return r.join("")}function Zo(e,t){let n=!1;return function(e,t,n,r){let o,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,o=n):(i=t,l=n,o=r),qn(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)}),o)}(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,Un})),Boolean((!e.depth||e.depth<3)&&Vo(e)&&(t.options.setext||n))}function Go(e){return e.value||""}function Qo(e,t,n,r){const o=Bo(n),i='"'===o?"Quote":"Apostrophe",l=n.enter("image");let a=n.enter("label");const s=n.createTracker(r);let c=s.move("![");return c+=s.move(n.safe(e.alt,{before:c,after:"]",...s.current()})),c+=s.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),a(),e.title&&(a=n.enter(`title${i}`),c+=s.move(" "+o),c+=s.move(n.safe(e.title,{before:c,after:o,...s.current()})),c+=s.move(o),a()),c+=s.move(")"),l(),c}function Yo(e,t,n,r){const o=e.referenceType,i=n.enter("imageReference");let l=n.enter("label");const a=n.createTracker(r);let s=a.move("![");const c=n.safe(e.alt,{before:s,after:"]",...a.current()});s+=a.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...a.current()});return l(),n.stack=u,i(),"full"!==o&&c&&c===f?"shortcut"===o?s=s.slice(0,-1):s+=a.move("]"):s+=a.move(f+"]"),s}function Ko(e,t,n){let r=e.value||"",o="`",i=-1;for(;new RegExp("(^|[^`])"+o+"([^`]|$)").test(r);)o+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let o;if(e.atBreak)for(;o=t.exec(r);){let e=o.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(o.index+1)}}return o+r+o}function Jo(e,t){const n=Vo(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function ei(e,t,n,r){const o=Bo(n),i='"'===o?"Quote":"Apostrophe",l=n.createTracker(r);let a,s;if(Jo(e,n)){const t=n.stack;n.stack=[],a=n.enter("autolink");let r=l.move("<");return r+=l.move(n.containerPhrasing(e,{before:r,after:">",...l.current()})),r+=l.move(">"),a(),n.stack=t,r}a=n.enter("link"),s=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()})),c+=l.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(s=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),s(),e.title&&(s=n.enter(`title${i}`),c+=l.move(" "+o),c+=l.move(n.safe(e.title,{before:c,after:o,...l.current()})),c+=l.move(o),s()),c+=l.move(")"),a(),c}function ti(e,t,n,r){const o=e.referenceType,i=n.enter("linkReference");let l=n.enter("label");const a=n.createTracker(r);let s=a.move("[");const c=n.containerPhrasing(e,{before:s,after:"]",...a.current()});s+=a.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...a.current()});return l(),n.stack=u,i(),"full"!==o&&c&&c===f?"shortcut"===o?s=s.slice(0,-1):s+=a.move("]"):s+=a.move(f+"]"),s}function ni(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function ri(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}Go.peek=function(){return"<"},Qo.peek=function(){return"!"},Yo.peek=function(){return"!"},Ko.peek=function(){return"`"},ei.peek=function(e,t,n){return Jo(e,n)?"<":"["},ti.peek=function(){return"["};const oi=function(e){if(null==e)return li;if("function"==typeof e)return ii(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=oi(e[n]);return ii(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return ii(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return ii(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function ii(e){return function(t,n,r){return Boolean(ai(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function li(){return!0}function ai(e){return null!==e&&"object"==typeof e&&"type"in e}const si=oi(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function ci(e,t,n,r){const o=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),l=n.createTracker(r),a=l.move(o+o);let s=l.move(n.containerPhrasing(e,{after:o,before:a,...l.current()}));const c=s.charCodeAt(0),u=Ho(r.before.charCodeAt(r.before.length-1),c,o);u.inside&&(s=_o(c)+s.slice(1));const f=s.charCodeAt(s.length-1),d=Ho(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+_o(f));const p=l.move(o+o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:u.outside},a+s+p}ci.peek=function(e,t,n){return n.options.strong||"*"};const ui={blockquote:function(e,t,n,r){const o=n.enter("blockquote"),i=n.createTracker(r);i.move("> "),i.shift(2);const l=n.indentLines(n.containerFlow(e,i.current()),No);return o(),l},break:Mo,code:function(e,t,n,r){const o=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",l="`"===o?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,Ro);return e(),t}const a=n.createTracker(r),s=o.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,l=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>l&&(l=i):i=1,o=r+t.length,r=n.indexOf(t,o);return l}(i,o)+1,3)),c=n.enter("codeFenced");let u=a.move(s);if(e.lang){const t=n.enter(`codeFencedLang${l}`);u+=a.move(n.safe(e.lang,{before:u,after:" ",encode:["`"],...a.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${l}`);u+=a.move(" "),u+=a.move(n.safe(e.meta,{before:u,after:"\n",encode:["`"],...a.current()})),t()}return u+=a.move("\n"),i&&(u+=a.move(i+"\n")),u+=a.move(s),c(),u},definition:function(e,t,n,r){const o=Bo(n),i='"'===o?"Quote":"Apostrophe",l=n.enter("definition");let a=n.enter("label");const s=n.createTracker(r);let c=s.move("[");return c+=s.move(n.safe(n.associationId(e),{before:c,after:"]",...s.current()})),c+=s.move("]: "),a(),!e.url||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...s.current()}))),a(),e.title&&(a=n.enter(`title${i}`),c+=s.move(" "+o),c+=s.move(n.safe(e.title,{before:c,after:o,...s.current()})),c+=s.move(o),a()),l(),c},emphasis:Uo,hardBreak:Mo,heading:function(e,t,n,r){const o=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(r);if(Zo(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),l=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return r(),t(),l+"\n"+(1===o?"=":"-").repeat(l.length-(Math.max(l.lastIndexOf("\r"),l.lastIndexOf("\n"))+1))}const l="#".repeat(o),a=n.enter("headingAtx"),s=n.enter("phrasing");i.move(l+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(c)&&(c=_o(c.charCodeAt(0))+c.slice(1)),c=c?l+" "+c:l,n.options.closeAtx&&(c+=" "+l),s(),a(),c},html:Go,image:Qo,imageReference:Yo,inlineCode:Ko,link:ei,linkReference:ti,list:function(e,t,n,r){const o=n.enter("list"),i=n.bulletCurrent;let l=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):ni(n);const a=e.ordered?"."===l?")":".":function(e){const t=ni(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let s=!(!t||!n.bulletLastUsed)&&l===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==l&&"-"!==l||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(s=!0),ri(n)===l&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){s=!0;break}}}}s&&(l=a),n.bulletCurrent=l;const c=n.containerFlow(e,r);return n.bulletLastUsed=l,n.bulletCurrent=i,o(),c},listItem:function(e,t,n,r){const o=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||ni(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let l=i.length+1;("tab"===o||"mixed"===o&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));const a=n.createTracker(r);a.move(i+" ".repeat(l-i.length)),a.shift(l);const s=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,a.current()),(function(e,t,n){if(t)return(n?"":" ".repeat(l))+e;return(n?i:i+" ".repeat(l-i.length))+e}));return s(),c},paragraph:function(e,t,n,r){const o=n.enter("paragraph"),i=n.enter("phrasing"),l=n.containerPhrasing(e,r);return i(),o(),l},root:function(e,t,n,r){return(e.children.some((function(e){return si(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:ci,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(ri(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function fi(e){const t=e._align;this.enter({type:"table",align:t.map((function(e){return"none"===e?null:e})),children:[]},e),this.data.inTable=!0}function di(e){this.exit(e),this.data.inTable=void 0}function pi(e){this.enter({type:"tableRow",children:[]},e)}function hi(e){this.exit(e)}function mi(e){this.enter({type:"tableCell",children:[]},e)}function gi(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,yi));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function yi(e,t){return"|"===t?t:e}function vi(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,o=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=ui.inlineCode(e,t,n);n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&"));return r},table:function(e,t,n,r){return a(function(e,t,n){const r=e.children;let o=-1;const i=[],l=t.enter("table");for(;++o<r.length;)i[o]=s(r[o],t,n);return l(),i}(e,n,r),e.align)},tableCell:l,tableRow:function(e,t,n,r){const o=s(e,n,r),i=a([o]);return i.slice(0,i.indexOf("\n"))}}};function l(e,t,n,r){const o=n.enter("tableCell"),l=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:i,after:i});return l(),o(),a}function a(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),o=n.stringLength||jo,i=[],l=[],a=[],s=[];let c=0,u=-1;for(;++u<e.length;){const t=[],r=[];let i=-1;for(e[u].length>c&&(c=e[u].length);++i<e[u].length;){const l=null==(f=e[u][i])?"":String(f);if(!1!==n.alignDelimiters){const e=o(l);r[i]=e,(void 0===s[i]||e>s[i])&&(s[i]=e)}t.push(l)}l[u]=t,a[u]=r}var f;let d=-1;if("object"==typeof r&&"length"in r)for(;++d<c;)i[d]=Fo(r[d]);else{const e=Fo(r);for(;++d<c;)i[d]=e}d=-1;const p=[],h=[];for(;++d<c;){const e=i[d];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let o=!1===n.alignDelimiters?1:Math.max(1,s[d]-t.length-r.length);const l=t+"-".repeat(o)+r;!1!==n.alignDelimiters&&(o=t.length+o+r.length,o>s[d]&&(s[d]=o),h[d]=o),p[d]=l}l.splice(1,0,p),a.splice(1,0,h),u=-1;const m=[];for(;++u<l.length;){const e=l[u],t=a[u];d=-1;const r=[];for(;++d<c;){const o=e[d]||"";let l="",a="";if(!1!==n.alignDelimiters){const e=s[d]-(t[d]||0),n=i[d];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):(l=" ".repeat(e/2),a=l):a=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===o||!1===n.delimiterStart&&!d||r.push(" "),!1!==n.alignDelimiters&&r.push(l),r.push(o),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&d===c-1||r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:o})}function s(e,t,n){const r=e.children;let o=-1;const i=[],a=t.enter("tableRow");for(;++o<r.length;)i[o]=l(r[o],0,t,n);return a(),i}}function bi(e){const t=this.stack[this.stack.length-2];t.type,t.checked="taskListCheckValueChecked"===e.type}function xi(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];e.type;const n=e.children[0];if(n&&"text"===n.type){const r=t.children;let o,i=-1;for(;++i<r.length;){const e=r[i];if("paragraph"===e.type){o=e;break}}o===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function ki(e,t,n,r){const o=e.children[0],i="boolean"==typeof e.checked&&o&&"paragraph"===o.type,l="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);i&&a.move(l);let s=ui.listItem(e,t,n,{...r,...a.current()});return i&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,(function(e){return e+l}))),s}const wi={}.hasOwnProperty;function Ci(e,t){let n;for(n in t){const r=(wi.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){wi.call(r,i)||(r[i]=[]);const e=o[i];Si(r[i],Array.isArray(e)?e:e?[e]:[])}}}function Si(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);Ce(e,0,0,r)}const Ai=Pi(/[A-Za-z]/),Ei=Pi(/[\dA-Za-z]/);Pi(/[#-'*+\--9=?A-Z^-~]/);Pi(/\d/),Pi(/[\dA-Fa-f]/),Pi(/[!-/:-@[-`{-~]/);function $i(e){return null!==e&&(e<0||32===e)}const Oi=Pi(/\p{P}|\p{S}/u),Ii=Pi(/\s/);function Pi(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Ti={tokenize:function(e,t,n){let r=0;return function t(i){if((87===i||119===i)&&r<3)return r++,e.consume(i),t;if(46===i&&3===r)return e.consume(i),o;return n(i)};function o(e){return null===e?n(e):t(e)}},partial:!0},zi={tokenize:function(e,t,n){let r,o,i;return l;function l(t){return 46===t||95===t?e.check(ji,s,a)(t):null===t||$i(t)||Ii(t)||45!==t&&Oi(t)?s(t):(i=!0,e.consume(t),l)}function a(t){return 95===t?r=!0:(o=r,r=void 0),e.consume(t),l}function s(e){return o||r||!i?n(e):t(e)}},partial:!0},Di={tokenize:function(e,t){let n=0,r=0;return o;function o(l){return 40===l?(n++,e.consume(l),o):41===l&&r<n?i(l):33===l||34===l||38===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||60===l||63===l||93===l||95===l||126===l?e.check(ji,t,i)(l):null===l||$i(l)||Ii(l)?t(l):(e.consume(l),o)}function i(t){return 41===t&&r++,e.consume(t),o}},partial:!0},ji={tokenize:function(e,t,n){return r;function r(l){return 33===l||34===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||63===l||95===l||126===l?(e.consume(l),r):38===l?(e.consume(l),i):93===l?(e.consume(l),o):60===l||null===l||$i(l)||Ii(l)?t(l):n(l)}function o(e){return null===e||40===e||91===e||$i(e)||Ii(e)?t(e):r(e)}function i(e){return Ai(e)?l(e):n(e)}function l(t){return 59===t?(e.consume(t),r):Ai(t)?(e.consume(t),l):n(t)}},partial:!0},Fi={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return Ei(e)?n(e):t(e)}},partial:!0},Ni={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){if(87!==t&&119!==t||!_i.call(r,r.previous)||Vi(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(Ti,e.attempt(zi,e.attempt(Di,o),n),n)(t)};function o(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:_i},Li={name:"protocolAutolink",tokenize:function(e,t,n){const r=this;let o="",i=!1;return function(t){if((72===t||104===t)&&Hi.call(r,r.previous)&&!Vi(r.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),o+=String.fromCodePoint(t),e.consume(t),l;return n(t)};function l(t){if(Ai(t)&&o.length<5)return o+=String.fromCodePoint(t),e.consume(t),l;if(58===t){const n=o.toLowerCase();if("http"===n||"https"===n)return e.consume(t),a}return n(t)}function a(t){return 47===t?(e.consume(t),i?s:(i=!0,a)):n(t)}function s(t){return null===t||function(e){return null!==e&&(e<32||127===e)}(t)||$i(t)||Ii(t)||Oi(t)?n(t):e.attempt(zi,e.attempt(Di,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Hi},Mi={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let o,i;return function(t){if(!qi(t)||!Ui.call(r,r.previous)||Vi(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),l(t)};function l(t){return qi(t)?(e.consume(t),l):64===t?(e.consume(t),a):n(t)}function a(t){return 46===t?e.check(Fi,c,s)(t):45===t||95===t||Ei(t)?(i=!0,e.consume(t),a):c(t)}function s(t){return e.consume(t),o=!0,a}function c(l){return i&&o&&Ai(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):n(l)}},previous:Ui},Ri={};let Bi=48;for(;Bi<123;)Ri[Bi]=Mi,Bi++,58===Bi?Bi=65:91===Bi&&(Bi=97);function _i(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||$i(e)}function Hi(e){return!Ai(e)}function Ui(e){return!(47===e||qi(e))}function qi(e){return 43===e||45===e||46===e||95===e||Ei(e)}function Vi(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}Ri[43]=Mi,Ri[45]=Mi,Ri[46]=Mi,Ri[95]=Mi,Ri[72]=[Mi,Li],Ri[104]=[Mi,Li],Ri[87]=[Mi,Ni],Ri[119]=[Mi,Ni];Xi(/[A-Za-z]/),Xi(/[\dA-Za-z]/),Xi(/[#-'*+\--9=?A-Z^-~]/);Xi(/\d/),Xi(/[\dA-Fa-f]/),Xi(/[!-/:-@[-`{-~]/);function Wi(e){return null!==e&&(e<0||32===e)}Xi(/\p{P}|\p{S}/u),Xi(/\s/);function Xi(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Zi={partial:!0,tokenize:function(e,t,n){return function(t){return function(e){return-2===e||-1===e||32===e}(t)?je(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||function(e){return null!==e&&e<-2}(e)?t(e):n(e)}}};const Gi={tokenize:function(e,t,n){const r=this;return je(e,(function(e){const o=r.events[r.events.length-1];return o&&"gfmFootnoteDefinitionIndent"===o[1].type&&4===o[2].sliceSerialize(o[1],!0).length?t(e):n(e)}),"gfmFootnoteDefinitionIndent",5)},partial:!0};function Qi(e,t,n){const r=this;let o=r.events.length;const i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l;for(;o--;){const e=r.events[o][1];if("labelImage"===e.type){l=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!l||!l._balanced)return n(o);const a=ht(r.sliceSerialize({start:l.end,end:r.now()}));if(94!==a.codePointAt(0)||!i.includes(a.slice(1)))return n(o);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)}}function Yi(e,t){let n,r=e.length;for(;r--;)if("labelImage"===e[r][1].type&&"enter"===e[r][0]){n=e[r][1];break}e[r+1][1].type="data",e[r+3][1].type="gfmFootnoteCallLabelMarker";const o={type:"gfmFootnoteCall",start:Object.assign({},e[r+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[r+3][1].end),end:Object.assign({},e[r+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},s=[e[r+1],e[r+2],["enter",o,t],e[r+3],e[r+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",a,t],["exit",a,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",o,t]];return e.splice(r,e.length-r+1,...s),e}function Ki(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,l=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),a};function a(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(a){if(l>999||93===a&&!i||null===a||91===a||Wi(a))return n(a);if(93===a){e.exit("chunkString");const i=e.exit("gfmFootnoteCallString");return o.includes(ht(r.sliceSerialize(i)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(a)}return Wi(a)||(i=!0),l++,e.consume(a),92===a?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),l++,s):s(t)}}function Ji(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,l,a=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(a>999||93===t&&!l||null===t||91===t||Wi(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return i=ht(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),f}return Wi(t)||(l=!0),a++,e.consume(t),92===t?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),a++,c):c(t)}function f(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o.includes(i)||o.push(i),je(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function el(e,t,n){return e.check(Zi,t,e.attempt(Gi,t,n))}function tl(e){e.exit("gfmFootnoteDefinition")}function nl(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const o=this.previous,i=this.events;let l=0;return function(t){if(126===o&&"characterEscape"!==i[i.length-1][1].type)return r(t);return e.enter("strikethroughSequenceTemporary"),a(t)};function a(i){const s=Ht(o);if(126===i)return l>1?r(i):(e.consume(i),l++,a);if(l<2&&!t)return r(i);const c=e.exit("strikethroughSequenceTemporary"),u=Ht(i);return c._open=!u||2===u&&Boolean(s),c._close=!s||2===s&&Boolean(u),n(i)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const o={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},l=[["enter",o,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",i,t]],a=t.parser.constructs.insideSpan.null;a&&Ce(l,l.length,0,Dt(a,e.slice(r+1,n),t)),Ce(l,l.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",o,t]]),Ce(e,r-1,n-r+3,l),n=r+l.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}ll(/[A-Za-z]/),ll(/[\dA-Za-z]/),ll(/[#-'*+\--9=?A-Z^-~]/);ll(/\d/),ll(/[\dA-Fa-f]/),ll(/[!-/:-@[-`{-~]/);function rl(e){return null!==e&&e<-2}function ol(e){return null!==e&&(e<0||32===e)}function il(e){return-2===e||-1===e||32===e}ll(/\p{P}|\p{S}/u),ll(/\s/);function ll(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}class al{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let o=0;if(0===n&&0===r.length)return;for(;o<e.map.length;){if(e.map[o][0]===t)return e.map[o][1]+=n,void e.map[o][2].push(...r);o+=1}e.map.push([t,n,r])}(this,e,t,n)}consume(e){if(this.map.sort((function(e,t){return e[0]-t[0]})),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(const t of r)e.push(t);r=n.pop()}this.map.length=0}}function sl(e,t){let n=!1;const r=[];for(;t<e.length;){const o=e[t];if(n){if("enter"===o[0])"tableContent"===o[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===o[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===o[1].type)break}else"enter"===o[0]&&"tableDelimiterRow"===o[1].type&&(n=!0);t+=1}return r}function cl(e,t,n){const r=this;let o,i=0,l=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const o=t>-1?r.events[t][1].type:null,i="tableHead"===o||"tableRow"===o?x:a;if(i===x&&r.parser.lazy[r.now().line])return n(e);return i(e)};function a(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return s(e);return o=!0,l+=1,s(e)}(t)}function s(t){return null===t?n(t):rl(t)?l>1?(l=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),f):n(t):il(t)?je(e,s,"whitespace")(t):(l+=1,o&&(o=!1,i+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),o=!0,s):(e.enter("data"),c(t)))}function c(t){return null===t||124===t||ol(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return 92===t||124===t?(e.consume(t),c):c(t)}function f(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),o=!1,il(t)?je(e,d,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t))}function d(t){return 45===t||58===t?h(t):124===t?(o=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):b(t)}function p(t){return il(t)?je(e,h,"whitespace")(t):h(t)}function h(t){return 58===t?(l+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),m):45===t?(l+=1,m(t)):null===t||rl(t)?v(t):b(t)}function m(t){return 45===t?(e.enter("tableDelimiterFiller"),g(t)):b(t)}function g(t){return 45===t?(e.consume(t),g):58===t?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(t))}function y(t){return il(t)?je(e,v,"whitespace")(t):v(t)}function v(n){return 124===n?d(n):(null===n||rl(n))&&o&&i===l?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):b(n)}function b(e){return n(e)}function x(t){return e.enter("tableRow"),k(t)}function k(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),k):null===n||rl(n)?(e.exit("tableRow"),t(n)):il(n)?je(e,k,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||ol(t)?(e.exit("data"),k(t)):(e.consume(t),92===t?C:w)}function C(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function ul(e,t){let n,r,o,i=-1,l=!0,a=0,s=[0,0,0,0],c=[0,0,0,0],u=!1,f=0;const d=new al;for(;++i<e.length;){const p=e[i],h=p[1];"enter"===p[0]?"tableHead"===h.type?(u=!1,0!==f&&(dl(d,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(l=!0,o=void 0,s=[0,0,0,0],c=[0,i+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",r,t]])),a="tableDelimiterRow"===h.type?2:r?3:1):!a||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type?"tableCellDivider"===h.type&&(l?l=!1:(0!==s[1]&&(c[0]=c[1],o=fl(d,t,s,a,void 0,o)),s=c,c=[s[1],i,0,0])):(l=!1,0===c[2]&&(0!==s[1]&&(c[0]=c[1],o=fl(d,t,s,a,void 0,o),s=[0,0,0,0]),c[2]=i)):"tableHead"===h.type?(u=!0,f=i):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=i,0!==s[1]?(c[0]=c[1],o=fl(d,t,s,a,i,o)):0!==c[1]&&(o=fl(d,t,c,a,i,o)),a=0):!a||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type||(c[3]=i)}for(0!==f&&dl(d,t,f,n,r),d.consume(t.events),i=-1;++i<t.events.length;){const e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=sl(t.events,i))}return e}function fl(e,t,n,r,o,i){const l=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(i.end=Object.assign({},pl(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));const a=pl(t.events,n[1]);if(i={type:l,start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){const o=pl(t.events,n[2]),i=pl(t.events,n[3]),l={type:"tableContent",start:Object.assign({},o),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",l,t]]),2!==r){const r=t.events[n[2]],o=t.events[n[3]];if(r[1].end=Object.assign({},o[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",l,t]])}return void 0!==o&&(i.end=Object.assign({},pl(t.events,o)),e.add(o,0,[["exit",i,t]]),i=void 0),i}function dl(e,t,n,r,o){const i=[],l=pl(t.events,n);o&&(o.end=Object.assign({},l),i.push(["exit",o,t])),r.end=Object.assign({},l),i.push(["exit",r,t]),e.add(n+1,0,i)}function pl(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}hl(/[A-Za-z]/),hl(/[\dA-Za-z]/),hl(/[#-'*+\--9=?A-Z^-~]/);hl(/\d/),hl(/[\dA-Fa-f]/),hl(/[!-/:-@[-`{-~]/);hl(/\p{P}|\p{S}/u),hl(/\s/);function hl(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const ml={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),o};function o(t){return function(e){return null!==e&&(e<0||32===e)}(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(t)}function l(r){return function(e){return null!==e&&e<-2}(r)?t(r):function(e){return-2===e||-1===e||32===e}(r)?e.check({tokenize:gl},t,n)(r):n(r)}}};function gl(e,t,n){return je(e,(function(e){return null===e?n(e):t(e)}),"whitespace")}function yl(e){return function(e){const t={};let n=-1;for(;++n<e.length;)Ci(t,e[n]);return t}([{text:Ri},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Ji,continuation:{tokenize:el},exit:tl}},text:{91:{name:"gfmFootnoteCall",tokenize:Ki},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:Qi,resolveTo:Yi}}},nl(e),{flow:{null:{name:"table",tokenize:cl,resolveAll:ul}}},{text:{91:ml}}])}const vl={};function bl(e){const t=e||vl,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),o=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),i=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(yl(t)),o.push([{transforms:[ho],enter:{literalAutolink:ao,literalAutolinkEmail:so,literalAutolinkHttp:so,literalAutolinkWww:so},exit:{literalAutolink:po,literalAutolinkEmail:fo,literalAutolinkHttp:co,literalAutolinkWww:uo}},{enter:{gfmFootnoteCallString:vo,gfmFootnoteCall:bo,gfmFootnoteDefinitionLabelString:xo,gfmFootnoteDefinition:ko},exit:{gfmFootnoteCallString:wo,gfmFootnoteCall:Co,gfmFootnoteDefinitionLabelString:So,gfmFootnoteDefinition:Ao}},{canContainEols:["delete"],enter:{strikethrough:To},exit:{strikethrough:zo}},{enter:{table:fi,tableData:mi,tableHeader:mi,tableRow:pi},exit:{codeText:gi,table:di,tableData:hi,tableHeader:hi,tableRow:hi}},{exit:{taskListCheckValueChecked:bi,taskListCheckValueUnchecked:bi,paragraph:xi}}]),i.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:io,notInConstruct:lo},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:io,notInConstruct:lo},{character:":",before:"[ps]",after:"\\/",inConstruct:io,notInConstruct:lo}]},$o(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:Po}],handlers:{delete:Do}},vi(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:ki}}]}}(t))}const xl=e=>{let{size:t=8}=e;return i.createElement("span",{className:"inline-flex items-center gap-2"},i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s"}}),i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s",animationDelay:"0.2s"}}),i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s",animationDelay:"0.4s"}}))};const kl=(0,i.memo)((e=>{let{content:t="",isJson:n=!1,className:r="",jsonThreshold:o=1e3,textThreshold:u=500,showFullscreen:f=!0}=e;const{0:d,1:p}=(0,i.useState)(!1),{0:h,1:m}=(0,i.useState)(!1),g=n?o:u;var y;t=("object"==typeof(y=t)&&null!==y?JSON.stringify(y):y)+"";const v=t.length>g,b=v&&!d?t.slice(0,g)+"...":t,x=" dark:prose-invert prose-table:border-hidden prose-td:border-t prose-th:border-b prose-ul:list-disc prose-sm prose-ol:list-decimal ";return i.createElement("div",{className:"relative"},i.createElement("div",{className:`\n            transition-[max-height,opacity] overflow-auto scroll  duration-500 ease-in-out\n            ${v&&!d?"max-h-[300px]":"max-h-[10000px]"}\n            ${r}\n          `},i.createElement(Hr,{className:d?`mt-4 text-sm text-primary ${x}`:"",remarkPlugins:[bl]},b),v&&!d&&i.createElement("div",{className:"absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-secondary to-transparent opacity-20"})),v&&i.createElement("div",{className:"mt-2 flex items-center justify-end gap-2"},i.createElement(Xr.A,{title:d?"Show less":"Show more"},i.createElement("button",{type:"button",onClick:e=>{p(!d),e.stopPropagation()},className:"inline-flex items-center justify-center p-2 rounded bg-secondary text-primary hover:text-accent hover:scale-105 transition-all duration-300 z-10","aria-label":d?"Show less":"Show more"},d?i.createElement(l.A,{size:18}):i.createElement(a.A,{size:18}))),f&&i.createElement(Xr.A,{title:"Fullscreen"},i.createElement("button",{type:"button",onClick:()=>m(!0),className:"inline-flex items-center justify-center p-2 rounded bg-secondary text-primary hover:text-accent hover:scale-105 transition-all duration-300 z-10","aria-label":"Toggle fullscreen"},i.createElement(s.A,{size:18})))),h&&i.createElement("div",{className:"fixed inset-0 dark:bg-black/80 bg-black/10 z-50 flex items-center justify-center",onClick:()=>m(!1)},i.createElement("div",{className:"relative bg-primary scroll w-full h-full md:w-4/5 md:h-4/5 md:rounded-lg p-8 overflow-auto",style:{opacity:.95},onClick:e=>e.stopPropagation()},i.createElement(Xr.A,{title:"Close"},i.createElement("button",{onClick:()=>m(!1),className:"absolute top-4 right-4 p-2 rounded-full bg-black/50 hover:bg-black/70 text-primary transition-colors","aria-label":"Close fullscreen view"},i.createElement(c.A,{size:24}))),i.createElement("div",{className:`mt-8 text-base text-primary ${x}`},n?i.createElement("pre",{className:"whitespace-pre-wrap"},t):i.createElement(Hr,{className:"text-primary",remarkPlugins:[bl]},t)))))})),wl=e=>{let{src:t,alt:n,onClose:r}=e;return i.createElement("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center",onClick:r},i.createElement("button",{onClick:r,className:"absolute top-4 right-4 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors","aria-label":"Close fullscreen image"},i.createElement(c.A,{size:24})),i.createElement("img",{src:t,alt:n,className:"max-h-[90vh] max-w-[90vw] object-contain rounded-lg",onClick:e=>e.stopPropagation()}))},Cl=e=>{let{src:t,alt:n,className:r=""}=e;const{0:o,1:l}=(0,i.useState)(!1);return i.createElement(i.Fragment,null,i.createElement("img",{src:t,alt:n,className:`${r} cursor-pointer rounded hover:opacity-90 transition-opacity`,onClick:()=>l(!0)}),o&&i.createElement(wl,{src:t,alt:n,onClose:()=>l(!1)}))};function Sl(e){const t=new Date,n=new Date(e),r=t.getTime()-n.getTime(),o=Math.floor(r/1e3),i=Math.floor(o/60),l=Math.floor(i/60),a=Math.floor(l/24),s=Math.floor(a/30),c=Math.floor(a/365);return o<60?"just now":i<60?`${i} ${1===i?"minute":"minutes"} ago`:l<24?`${l} ${1===l?"hour":"hours"} ago`:a<30?`${a} ${1===a?"day":"days"} ago`:s<12?`${s} ${1===s?"month":"months"} ago`:`${c} ${1===c?"year":"years"} ago`}},2571:function(e,t,n){"use strict";n.d(t,{Z:function(){return o},f:function(){return i}});var r=n(7387);let o=function(e){function t(){return e.apply(this,arguments)||this}(0,r.A)(t,e);var n=t.prototype;return n.listGalleries=async function(e){const t=await fetch(`${this.getBaseUrl()}/gallery/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch galleries");return n.data},n.getGallery=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${t}`,{headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to fetch gallery");return r.data},n.createGallery=async function(e,t){const n={...e,user_id:t};console.log("Creating gallery with data:",n);const r=await fetch(`${this.getBaseUrl()}/gallery/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify(n)}),o=await r.json();if(!o.status)throw new Error(o.message||"Failed to create gallery");return o.data},n.updateGallery=async function(e,t,n){const r={...t,user_id:n},o=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${n}`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(r)}),i=await o.json();if(!i.status)throw new Error(i.message||"Failed to update gallery");return i.data},n.deleteGallery=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${t}`,{method:"DELETE",headers:this.getHeaders()}),r=await n.json();if(!r.status)throw new Error(r.message||"Failed to delete gallery")},n.syncGallery=async function(e){const t=await fetch(e);if(!t.ok)throw new Error(`Failed to sync gallery from ${e}`);return await t.json()},t}(n(3838).y);const i=new o},2849:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,o=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},l=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,o=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!o&&!i)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(o)return o(e,n).value}return e[n]};e.exports=function e(){var t,n,r,o,c,u,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(t=arguments[d]))for(n in t)r=s(f,n),f!==(o=s(t,n))&&(h&&o&&(l(o)||(c=i(o)))?(c?(c=!1,u=r&&i(r)?r:[]):u=r&&l(r)?r:{},a(f,{name:n,newValue:e(h,u,o)})):void 0!==o&&a(f,{name:n,newValue:o}));return f}},3324:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},4796:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},4870:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,o.default)(e),i="function"==typeof t;return r.forEach((function(e){if("declaration"===e.type){var r=e.property,o=e.value;i?t(r,o,e):o&&((n=n||{})[r]=o)}})),n};var o=r(n(315))},5229:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(4870)),o=n(8917);function i(e,t){var n={};return e&&"string"==typeof e?((0,r.default)(e,(function(e,r){e&&r&&(n[(0,o.camelCase)(e,t)]=r)})),n):n}i.default=i,e.exports=i},7213:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7677:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8355:function(e,t,n){"use strict";n.d(t,{A:function(){return zt}});var r=n(6540),o=n(436),i=n(961),l=n(6942),a=n.n(l),s=n(8168),c=n(3029),u=n(2901),f=n(9417),d=n(5501),p=n(9426),h=n(4467),m=n(9379),g=n(3986),y=n(2284),v=n(675),b=n(467),x=n(2065),k=n(8210),w=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",o=e.type||"",i=o.replace(/\/.*$/,"");return n.some((function(e){var t=e.trim();if(/^\*(\/\*)?$/.test(e))return!0;if("."===t.charAt(0)){var n=r.toLowerCase(),l=t.toLowerCase(),a=[l];return".jpg"!==l&&".jpeg"!==l||(a=[".jpg",".jpeg"]),a.some((function(e){return n.endsWith(e)}))}return/\/\*$/.test(t)?i===t.replace(/\/.*$/,""):o===t||!!/^\w+$/.test(t)&&((0,k.Ay)(!1,"Upload takes an invalidate 'accept' type '".concat(t,"'.Skip for check.")),!0)}))}return!0};function C(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(n){return t}}function S(e){var t=new XMLHttpRequest;e.onProgress&&t.upload&&(t.upload.onprogress=function(t){t.total>0&&(t.percent=t.loaded/t.total*100),e.onProgress(t)});var n=new FormData;e.data&&Object.keys(e.data).forEach((function(t){var r=e.data[t];Array.isArray(r)?r.forEach((function(e){n.append("".concat(t,"[]"),e)})):n.append(t,r)})),e.file instanceof Blob?n.append(e.filename,e.file,e.file.name):n.append(e.filename,e.file),t.onerror=function(t){e.onError(t)},t.onload=function(){return t.status<200||t.status>=300?e.onError(function(e,t){var n="cannot ".concat(e.method," ").concat(e.action," ").concat(t.status,"'"),r=new Error(n);return r.status=t.status,r.method=e.method,r.url=e.action,r}(e,t),C(t)):e.onSuccess(C(t),t)},t.open(e.method,e.action,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);var r=e.headers||{};return null!==r["X-Requested-With"]&&t.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(r).forEach((function(e){null!==r[e]&&t.setRequestHeader(e,r[e])})),t.send(n),{abort:function(){t.abort()}}}var A=function(){var e=(0,b.A)((0,v.A)().mark((function e(t,n){var r,i,l,a,s,c,u,f;return(0,v.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:c=function(){return(c=(0,b.A)((0,v.A)().mark((function e(t){return(0,v.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){t.file((function(r){n(r)?(t.fullPath&&!r.webkitRelativePath&&(Object.defineProperties(r,{webkitRelativePath:{writable:!0}}),r.webkitRelativePath=t.fullPath.replace(/^\//,""),Object.defineProperties(r,{webkitRelativePath:{writable:!1}})),e(r)):e(null)}))})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)},s=function(e){return c.apply(this,arguments)},a=function(){return(a=(0,b.A)((0,v.A)().mark((function e(t){var n,r,o,i,l;return(0,v.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.createReader(),r=[];case 2:return e.next=5,new Promise((function(e){n.readEntries(e,(function(){return e([])}))}));case 5:if(o=e.sent,i=o.length){e.next=9;break}return e.abrupt("break",12);case 9:for(l=0;l<i;l++)r.push(o[l]);e.next=2;break;case 12:return e.abrupt("return",r);case 13:case"end":return e.stop()}}),e)})))).apply(this,arguments)},l=function(e){return a.apply(this,arguments)},r=[],i=[],t.forEach((function(e){return i.push(e.webkitGetAsEntry())})),u=function(){var e=(0,b.A)((0,v.A)().mark((function e(t,n){var a,c;return(0,v.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:if(t.path=n||"",!t.isFile){e.next=10;break}return e.next=6,s(t);case 6:(a=e.sent)&&r.push(a),e.next=15;break;case 10:if(!t.isDirectory){e.next=15;break}return e.next=13,l(t);case 13:c=e.sent,i.push.apply(i,(0,o.A)(c));case 15:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),f=0;case 9:if(!(f<i.length)){e.next=15;break}return e.next=12,u(i[f]);case 12:f++,e.next=9;break;case 15:return e.abrupt("return",r);case 16:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),E=A,$=+new Date,O=0;function I(){return"rc-upload-".concat($,"-").concat(++O)}var P=["component","prefixCls","className","classNames","disabled","id","name","style","styles","multiple","accept","capture","children","directory","openFileDialogOnClick","onMouseEnter","onMouseLeave","hasControlInside"],T=function(e){(0,d.A)(n,e);var t=(0,p.A)(n);function n(){var e;(0,c.A)(this,n);for(var r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];return e=t.call.apply(t,[this].concat(i)),(0,h.A)((0,f.A)(e),"state",{uid:I()}),(0,h.A)((0,f.A)(e),"reqs",{}),(0,h.A)((0,f.A)(e),"fileInput",void 0),(0,h.A)((0,f.A)(e),"_isMounted",void 0),(0,h.A)((0,f.A)(e),"onChange",(function(t){var n=e.props,r=n.accept,i=n.directory,l=t.target.files,a=(0,o.A)(l).filter((function(e){return!i||w(e,r)}));e.uploadFiles(a),e.reset()})),(0,h.A)((0,f.A)(e),"onClick",(function(t){var n=e.fileInput;if(n){var r=t.target,o=e.props.onClick;if(r&&"BUTTON"===r.tagName)n.parentNode.focus(),r.blur();n.click(),o&&o(t)}})),(0,h.A)((0,f.A)(e),"onKeyDown",(function(t){"Enter"===t.key&&e.onClick(t)})),(0,h.A)((0,f.A)(e),"onFileDrop",function(){var t=(0,b.A)((0,v.A)().mark((function t(n){var r,i,l;return(0,v.A)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.props.multiple,n.preventDefault(),"dragover"!==n.type){t.next=4;break}return t.abrupt("return");case 4:if(!e.props.directory){t.next=11;break}return t.next=7,E(Array.prototype.slice.call(n.dataTransfer.items),(function(t){return w(t,e.props.accept)}));case 7:i=t.sent,e.uploadFiles(i),t.next=14;break;case 11:l=(0,o.A)(n.dataTransfer.files).filter((function(t){return w(t,e.props.accept)})),!1===r&&(l=l.slice(0,1)),e.uploadFiles(l);case 14:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),(0,h.A)((0,f.A)(e),"uploadFiles",(function(t){var n=(0,o.A)(t),r=n.map((function(t){return t.uid=I(),e.processFile(t,n)}));Promise.all(r).then((function(t){var n=e.props.onBatchStart;null==n||n(t.map((function(e){return{file:e.origin,parsedFile:e.parsedFile}}))),t.filter((function(e){return null!==e.parsedFile})).forEach((function(t){e.post(t)}))}))})),(0,h.A)((0,f.A)(e),"processFile",function(){var t=(0,b.A)((0,v.A)().mark((function t(n,r){var o,i,l,a,s,c,u,f,d;return(0,v.A)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(o=e.props.beforeUpload,i=n,!o){t.next=14;break}return t.prev=3,t.next=6,o(n,r);case 6:i=t.sent,t.next=12;break;case 9:t.prev=9,t.t0=t.catch(3),i=!1;case 12:if(!1!==i){t.next=14;break}return t.abrupt("return",{origin:n,parsedFile:null,action:null,data:null});case 14:if("function"!=typeof(l=e.props.action)){t.next=21;break}return t.next=18,l(n);case 18:a=t.sent,t.next=22;break;case 21:a=l;case 22:if("function"!=typeof(s=e.props.data)){t.next=29;break}return t.next=26,s(n);case 26:c=t.sent,t.next=30;break;case 29:c=s;case 30:return u="object"!==(0,y.A)(i)&&"string"!=typeof i||!i?n:i,f=u instanceof File?u:new File([u],n.name,{type:n.type}),(d=f).uid=n.uid,t.abrupt("return",{origin:n,data:c,parsedFile:d,action:a});case 35:case"end":return t.stop()}}),t,null,[[3,9]])})));return function(e,n){return t.apply(this,arguments)}}()),(0,h.A)((0,f.A)(e),"saveFileInput",(function(t){e.fileInput=t})),e}return(0,u.A)(n,[{key:"componentDidMount",value:function(){this._isMounted=!0}},{key:"componentWillUnmount",value:function(){this._isMounted=!1,this.abort()}},{key:"post",value:function(e){var t=this,n=e.data,r=e.origin,o=e.action,i=e.parsedFile;if(this._isMounted){var l=this.props,a=l.onStart,s=l.customRequest,c=l.name,u=l.headers,f=l.withCredentials,d=l.method,p=r.uid,h=s||S,m={action:o,filename:c,data:n,file:i,headers:u,withCredentials:f,method:d||"post",onProgress:function(e){var n=t.props.onProgress;null==n||n(e,i)},onSuccess:function(e,n){var r=t.props.onSuccess;null==r||r(e,i,n),delete t.reqs[p]},onError:function(e,n){var r=t.props.onError;null==r||r(e,n,i),delete t.reqs[p]}};a(r),this.reqs[p]=h(m)}}},{key:"reset",value:function(){this.setState({uid:I()})}},{key:"abort",value:function(e){var t=this.reqs;if(e){var n=e.uid?e.uid:e;t[n]&&t[n].abort&&t[n].abort(),delete t[n]}else Object.keys(t).forEach((function(e){t[e]&&t[e].abort&&t[e].abort(),delete t[e]}))}},{key:"render",value:function(){var e=this.props,t=e.component,n=e.prefixCls,o=e.className,i=e.classNames,l=void 0===i?{}:i,c=e.disabled,u=e.id,f=e.name,d=e.style,p=e.styles,y=void 0===p?{}:p,v=e.multiple,b=e.accept,k=e.capture,w=e.children,C=e.directory,S=e.openFileDialogOnClick,A=e.onMouseEnter,E=e.onMouseLeave,$=e.hasControlInside,O=(0,g.A)(e,P),I=a()((0,h.A)((0,h.A)((0,h.A)({},n,!0),"".concat(n,"-disabled"),c),o,o)),T=C?{directory:"directory",webkitdirectory:"webkitdirectory"}:{},z=c?{}:{onClick:S?this.onClick:function(){},onKeyDown:S?this.onKeyDown:function(){},onMouseEnter:A,onMouseLeave:E,onDrop:this.onFileDrop,onDragOver:this.onFileDrop,tabIndex:$?void 0:"0"};return r.createElement(t,(0,s.A)({},z,{className:I,role:$?void 0:"button",style:d}),r.createElement("input",(0,s.A)({},(0,x.A)(O,{aria:!0,data:!0}),{id:u,name:f,disabled:c,type:"file",ref:this.saveFileInput,onClick:function(e){return e.stopPropagation()},key:this.state.uid,style:(0,m.A)({display:"none"},y.input),className:l.input,accept:b},T,{multiple:v,onChange:this.onChange},null!=k?{capture:k}:{})),w)}}]),n}(r.Component),z=T;function D(){}var j=function(e){(0,d.A)(n,e);var t=(0,p.A)(n);function n(){var e;(0,c.A)(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e=t.call.apply(t,[this].concat(o)),(0,h.A)((0,f.A)(e),"uploader",void 0),(0,h.A)((0,f.A)(e),"saveUploader",(function(t){e.uploader=t})),e}return(0,u.A)(n,[{key:"abort",value:function(e){this.uploader.abort(e)}},{key:"render",value:function(){return r.createElement(z,(0,s.A)({},this.props,{ref:this.saveUploader}))}}]),n}(r.Component);(0,h.A)(j,"defaultProps",{component:"span",prefixCls:"rc-upload",data:{},headers:{},name:"file",multipart:!1,onStart:D,onError:D,onSuccess:D,multiple:!1,beforeUpload:null,customRequest:null,withCredentials:!1,openFileDialogOnClick:!0,hasControlInside:!1});var F=j,N=n(2533),L=n(2279),M=n(8119),R=n(9155),B=n(8055),_=n(5905),H=n(977),U=n(7358),q=n(4277),V=n(2187);var W=e=>{const{componentCls:t,iconCls:n}=e;return{[`${t}-wrapper`]:{[`${t}-drag`]:{position:"relative",width:"100%",height:"100%",textAlign:"center",background:e.colorFillAlter,border:`${(0,V.zA)(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[t]:{padding:e.padding},[`${t}-btn`]:{display:"table",width:"100%",height:"100%",outline:"none",borderRadius:e.borderRadiusLG,"&:focus-visible":{outline:`${(0,V.zA)(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`}},[`${t}-drag-container`]:{display:"table-cell",verticalAlign:"middle"},[`\n          &:not(${t}-disabled):hover,\n          &-hover:not(${t}-disabled)\n        `]:{borderColor:e.colorPrimaryHover},[`p${t}-drag-icon`]:{marginBottom:e.margin,[n]:{color:e.colorPrimary,fontSize:e.uploadThumbnailSize}},[`p${t}-text`]:{margin:`0 0 ${(0,V.zA)(e.marginXXS)}`,color:e.colorTextHeading,fontSize:e.fontSizeLG},[`p${t}-hint`]:{color:e.colorTextDescription,fontSize:e.fontSize},[`&${t}-disabled`]:{[`p${t}-drag-icon ${n},\n            p${t}-text,\n            p${t}-hint\n          `]:{color:e.colorTextDisabled}}}}}};var X=e=>{const{componentCls:t,iconCls:n,fontSize:r,lineHeight:o,calc:i}=e,l=`${t}-list-item`,a=`${l}-actions`,s=`${l}-action`;return{[`${t}-wrapper`]:{[`${t}-list`]:Object.assign(Object.assign({},(0,_.t6)()),{lineHeight:e.lineHeight,[l]:{position:"relative",height:i(e.lineHeight).mul(r).equal(),marginTop:e.marginXS,fontSize:r,display:"flex",alignItems:"center",transition:`background-color ${e.motionDurationSlow}`,borderRadius:e.borderRadiusSM,"&:hover":{backgroundColor:e.controlItemBgHover},[`${l}-name`]:Object.assign(Object.assign({},_.L9),{padding:`0 ${(0,V.zA)(e.paddingXS)}`,lineHeight:o,flex:"auto",transition:`all ${e.motionDurationSlow}`}),[a]:{whiteSpace:"nowrap",[s]:{opacity:0},[n]:{color:e.actionsColor,transition:`all ${e.motionDurationSlow}`},[`\n              ${s}:focus-visible,\n              &.picture ${s}\n            `]:{opacity:1}},[`${t}-icon ${n}`]:{color:e.colorTextDescription,fontSize:r},[`${l}-progress`]:{position:"absolute",bottom:e.calc(e.uploadProgressOffset).mul(-1).equal(),width:"100%",paddingInlineStart:i(r).add(e.paddingXS).equal(),fontSize:r,lineHeight:0,pointerEvents:"none","> div":{margin:0}}},[`${l}:hover ${s}`]:{opacity:1},[`${l}-error`]:{color:e.colorError,[`${l}-name, ${t}-icon ${n}`]:{color:e.colorError},[a]:{[`${n}, ${n}:hover`]:{color:e.colorError},[s]:{opacity:1}}},[`${t}-list-item-container`]:{transition:`opacity ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,"&::before":{display:"table",width:0,height:0,content:'""'}}})}}},Z=n(8680);var G=e=>{const{componentCls:t}=e,n=new V.Mo("uploadAnimateInlineIn",{from:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),r=new V.Mo("uploadAnimateInlineOut",{to:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),o=`${t}-animate-inline`;return[{[`${t}-wrapper`]:{[`${o}-appear, ${o}-enter, ${o}-leave`]:{animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseInOutCirc,animationFillMode:"forwards"},[`${o}-appear, ${o}-enter`]:{animationName:n},[`${o}-leave`]:{animationName:r}}},{[`${t}-wrapper`]:(0,Z.p9)(e)},n,r]},Q=n(5748);const Y=e=>{const{componentCls:t,iconCls:n,uploadThumbnailSize:r,uploadProgressOffset:o,calc:i}=e,l=`${t}-list`,a=`${l}-item`;return{[`${t}-wrapper`]:{[`\n        ${l}${l}-picture,\n        ${l}${l}-picture-card,\n        ${l}${l}-picture-circle\n      `]:{[a]:{position:"relative",height:i(r).add(i(e.lineWidth).mul(2)).add(i(e.paddingXS).mul(2)).equal(),padding:e.paddingXS,border:`${(0,V.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusLG,"&:hover":{background:"transparent"},[`${a}-thumbnail`]:Object.assign(Object.assign({},_.L9),{width:r,height:r,lineHeight:(0,V.zA)(i(r).add(e.paddingSM).equal()),textAlign:"center",flex:"none",[n]:{fontSize:e.fontSizeHeading2,color:e.colorPrimary},img:{display:"block",width:"100%",height:"100%",overflow:"hidden"}}),[`${a}-progress`]:{bottom:o,width:`calc(100% - ${(0,V.zA)(i(e.paddingSM).mul(2).equal())})`,marginTop:0,paddingInlineStart:i(r).add(e.paddingXS).equal()}},[`${a}-error`]:{borderColor:e.colorError,[`${a}-thumbnail ${n}`]:{[`svg path[fill='${Q.z1[0]}']`]:{fill:e.colorErrorBg},[`svg path[fill='${Q.z1.primary}']`]:{fill:e.colorError}}},[`${a}-uploading`]:{borderStyle:"dashed",[`${a}-name`]:{marginBottom:o}}},[`${l}${l}-picture-circle ${a}`]:{[`&, &::before, ${a}-thumbnail`]:{borderRadius:"50%"}}}}},K=e=>{const{componentCls:t,iconCls:n,fontSizeLG:r,colorTextLightSolid:o,calc:i}=e,l=`${t}-list`,a=`${l}-item`,s=e.uploadPicCardSize;return{[`\n      ${t}-wrapper${t}-picture-card-wrapper,\n      ${t}-wrapper${t}-picture-circle-wrapper\n    `]:Object.assign(Object.assign({},(0,_.t6)()),{display:"block",[`${t}${t}-select`]:{width:s,height:s,textAlign:"center",verticalAlign:"top",backgroundColor:e.colorFillAlter,border:`${(0,V.zA)(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[`> ${t}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",textAlign:"center"},[`&:not(${t}-disabled):hover`]:{borderColor:e.colorPrimary}},[`${l}${l}-picture-card, ${l}${l}-picture-circle`]:{display:"flex",flexWrap:"wrap","@supports not (gap: 1px)":{"& > *":{marginBlockEnd:e.marginXS,marginInlineEnd:e.marginXS}},"@supports (gap: 1px)":{gap:e.marginXS},[`${l}-item-container`]:{display:"inline-block",width:s,height:s,verticalAlign:"top"},"&::after":{display:"none"},"&::before":{display:"none"},[a]:{height:"100%",margin:0,"&::before":{position:"absolute",zIndex:1,width:`calc(100% - ${(0,V.zA)(i(e.paddingXS).mul(2).equal())})`,height:`calc(100% - ${(0,V.zA)(i(e.paddingXS).mul(2).equal())})`,backgroundColor:e.colorBgMask,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'" "'}},[`${a}:hover`]:{[`&::before, ${a}-actions`]:{opacity:1}},[`${a}-actions`]:{position:"absolute",insetInlineStart:0,zIndex:10,width:"100%",whiteSpace:"nowrap",textAlign:"center",opacity:0,transition:`all ${e.motionDurationSlow}`,[`\n            ${n}-eye,\n            ${n}-download,\n            ${n}-delete\n          `]:{zIndex:10,width:r,margin:`0 ${(0,V.zA)(e.marginXXS)}`,fontSize:r,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,color:o,"&:hover":{color:o},svg:{verticalAlign:"baseline"}}},[`${a}-thumbnail, ${a}-thumbnail img`]:{position:"static",display:"block",width:"100%",height:"100%",objectFit:"contain"},[`${a}-name`]:{display:"none",textAlign:"center"},[`${a}-file + ${a}-name`]:{position:"absolute",bottom:e.margin,display:"block",width:`calc(100% - ${(0,V.zA)(i(e.paddingXS).mul(2).equal())})`},[`${a}-uploading`]:{[`&${a}`]:{backgroundColor:e.colorFillAlter},[`&::before, ${n}-eye, ${n}-download, ${n}-delete`]:{display:"none"}},[`${a}-progress`]:{bottom:e.marginXL,width:`calc(100% - ${(0,V.zA)(i(e.paddingXS).mul(2).equal())})`,paddingInlineStart:0}}}),[`${t}-wrapper${t}-picture-circle-wrapper`]:{[`${t}${t}-select`]:{borderRadius:"50%"}}}};var J=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}};const ee=e=>{const{componentCls:t,colorTextDisabled:n}=e;return{[`${t}-wrapper`]:Object.assign(Object.assign({},(0,_.dF)(e)),{[t]:{outline:0,"input[type='file']":{cursor:"pointer"}},[`${t}-select`]:{display:"inline-block"},[`${t}-hidden`]:{display:"none"},[`${t}-disabled`]:{color:n,cursor:"not-allowed"}})}};var te=(0,U.OF)("Upload",(e=>{const{fontSizeHeading3:t,fontHeight:n,lineWidth:r,controlHeightLG:o,calc:i}=e,l=(0,q.oX)(e,{uploadThumbnailSize:i(t).mul(2).equal(),uploadProgressOffset:i(i(n).div(2)).add(r).equal(),uploadPicCardSize:i(o).mul(2.55).equal()});return[ee(l),W(l),Y(l),K(l),X(l),G(l),J(l),(0,H.A)(l)]}),(e=>({actionsColor:e.colorTextDescription}))),ne={icon:function(e,t){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M534 352V136H232v752h560V394H576a42 42 0 01-42-42z",fill:t}},{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z",fill:e}}]}},name:"file",theme:"twotone"},re=n(7064),oe=function(e,t){return r.createElement(re.A,(0,s.A)({},e,{ref:t,icon:ne}))};var ie=r.forwardRef(oe),le=n(3567),ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"}}]},name:"paper-clip",theme:"outlined"},se=function(e,t){return r.createElement(re.A,(0,s.A)({},e,{ref:t,icon:ae}))};var ce=r.forwardRef(se),ue={icon:function(e,t){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z",fill:e}},{tag:"path",attrs:{d:"M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z",fill:t}},{tag:"path",attrs:{d:"M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z",fill:t}},{tag:"path",attrs:{d:"M276 368a28 28 0 1056 0 28 28 0 10-56 0z",fill:t}},{tag:"path",attrs:{d:"M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z",fill:e}}]}},name:"picture",theme:"twotone"},fe=function(e,t){return r.createElement(re.A,(0,s.A)({},e,{ref:t,icon:ue}))};var de=r.forwardRef(fe),pe=n(754),he=n(9853),me=n(7447),ge=n(3723),ye=n(682),ve=n(2941);function be(e){return Object.assign(Object.assign({},e),{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:e.name,size:e.size,type:e.type,uid:e.uid,percent:0,originFileObj:e})}function xe(e,t){const n=(0,o.A)(t),r=n.findIndex((t=>{let{uid:n}=t;return n===e.uid}));return-1===r?n.push(e):n[r]=e,n}function ke(e,t){const n=void 0!==e.uid?"uid":"name";return t.filter((t=>t[n]===e[n]))[0]}const we=e=>0===e.indexOf("image/"),Ce=e=>{if(e.type&&!e.thumbUrl)return we(e.type);const t=e.thumbUrl||e.url||"",n=function(){const e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split("/"),t=e[e.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(t)||[""])[0]}(t);return!(!/^data:image\//.test(t)&&!/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(n))||!/^data:/.test(t)&&!n},Se=200;function Ae(e){return new Promise((t=>{if(!e.type||!we(e.type))return void t("");const n=document.createElement("canvas");n.width=Se,n.height=Se,n.style.cssText="position: fixed; left: 0; top: 0; width: 200px; height: 200px; z-index: 9999; display: none;",document.body.appendChild(n);const r=n.getContext("2d"),o=new Image;if(o.onload=()=>{const{width:e,height:i}=o;let l=Se,a=Se,s=0,c=0;e>i?(a=i*(Se/e),c=-(a-l)/2):(l=e*(Se/i),s=-(l-a)/2),r.drawImage(o,s,c,l,a);const u=n.toDataURL();document.body.removeChild(n),window.URL.revokeObjectURL(o.src),t(u)},o.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const t=new FileReader;t.onload=()=>{t.result&&"string"==typeof t.result&&(o.src=t.result)},t.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const n=new FileReader;n.onload=()=>{n.result&&t(n.result)},n.readAsDataURL(e)}else o.src=window.URL.createObjectURL(e)}))}var Ee={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},$e=function(e,t){return r.createElement(re.A,(0,s.A)({},e,{ref:t,icon:Ee}))};var Oe=r.forwardRef($e),Ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},Pe=function(e,t){return r.createElement(re.A,(0,s.A)({},e,{ref:t,icon:Ie}))};var Te=r.forwardRef(Pe),ze=n(234),De=n(2616),je=n(8811),Fe=n(6067),Ne=n(6029),Le=n(7852),Me={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},Re=function(){var e=(0,r.useRef)([]),t=(0,r.useRef)(null);return(0,r.useEffect)((function(){var n=Date.now(),r=!1;e.current.forEach((function(e){if(e){r=!0;var o=e.style;o.transitionDuration=".3s, .3s, .3s, .06s",t.current&&n-t.current<100&&(o.transitionDuration="0s, 0s")}})),r&&(t.current=Date.now())})),e.current};var Be=n(5544),_e=n(998),He=0,Ue=(0,_e.A)();var qe=function(e){var t=r.useState(),n=(0,Be.A)(t,2),o=n[0],i=n[1];return r.useEffect((function(){var e;i("rc_progress_".concat((Ue?(e=He,He+=1):e="TEST_OR_SSR",e)))}),[]),e||o},Ve=function(e){var t=e.bg,n=e.children;return r.createElement("div",{style:{width:"100%",height:"100%",background:t}},n)};function We(e,t){return Object.keys(e).map((function(n){var r=parseFloat(n),o="".concat(Math.floor(r*t),"%");return"".concat(e[n]," ").concat(o)}))}var Xe=r.forwardRef((function(e,t){var n=e.prefixCls,o=e.color,i=e.gradientId,l=e.radius,a=e.style,s=e.ptg,c=e.strokeLinecap,u=e.strokeWidth,f=e.size,d=e.gapDegree,p=o&&"object"===(0,y.A)(o),h=p?"#FFF":void 0,m=f/2,g=r.createElement("circle",{className:"".concat(n,"-circle-path"),r:l,cx:m,cy:m,stroke:h,strokeLinecap:c,strokeWidth:u,opacity:0===s?0:1,style:a,ref:t});if(!p)return g;var v="".concat(i,"-conic"),b=d?"".concat(180+d/2,"deg"):"0deg",x=We(o,(360-d)/360),k=We(o,1),w="conic-gradient(from ".concat(b,", ").concat(x.join(", "),")"),C="linear-gradient(to ".concat(d?"bottom":"top",", ").concat(k.join(", "),")");return r.createElement(r.Fragment,null,r.createElement("mask",{id:v},g),r.createElement("foreignObject",{x:0,y:0,width:f,height:f,mask:"url(#".concat(v,")")},r.createElement(Ve,{bg:C},r.createElement(Ve,{bg:w}))))})),Ze=100,Ge=function(e,t,n,r,o,i,l,a,s,c){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,f=n/100*360*((360-i)/360),d=0===i?0:{bottom:0,top:180,left:90,right:-90}[l],p=(100-r)/100*t;"round"===s&&100!==r&&(p+=c/2)>=t&&(p=t-.01);return{stroke:"string"==typeof a?a:void 0,strokeDasharray:"".concat(t,"px ").concat(e),strokeDashoffset:p+u,transform:"rotate(".concat(o+f+d,"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},Qe=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function Ye(e){var t=null!=e?e:[];return Array.isArray(t)?t:[t]}var Ke=function(e){var t,n,o,i=(0,m.A)((0,m.A)({},Me),e),l=i.id,c=i.prefixCls,u=i.steps,f=i.strokeWidth,d=i.trailWidth,p=i.gapDegree,h=void 0===p?0:p,v=i.gapPosition,b=i.trailColor,x=i.strokeLinecap,k=i.style,w=i.className,C=i.strokeColor,S=i.percent,A=(0,g.A)(i,Qe),E=qe(l),$="".concat(E,"-gradient"),O=50-f/2,I=2*Math.PI*O,P=h>0?90+h/2:-90,T=I*((360-h)/360),z="object"===(0,y.A)(u)?u:{count:u,gap:2},D=z.count,j=z.gap,F=Ye(S),N=Ye(C),L=N.find((function(e){return e&&"object"===(0,y.A)(e)})),M=L&&"object"===(0,y.A)(L)?"butt":x,R=Ge(I,T,0,100,P,h,v,b,M,f),B=Re();return r.createElement("svg",(0,s.A)({className:a()("".concat(c,"-circle"),w),viewBox:"0 0 ".concat(Ze," ").concat(Ze),style:k,id:l,role:"presentation"},A),!D&&r.createElement("circle",{className:"".concat(c,"-circle-trail"),r:O,cx:50,cy:50,stroke:b,strokeLinecap:M,strokeWidth:d||f,style:R}),D?(t=Math.round(D*(F[0]/100)),n=100/D,o=0,new Array(D).fill(null).map((function(e,i){var l=i<=t-1?N[0]:b,a=l&&"object"===(0,y.A)(l)?"url(#".concat($,")"):void 0,s=Ge(I,T,o,n,P,h,v,l,"butt",f,j);return o+=100*(T-s.strokeDashoffset+j)/T,r.createElement("circle",{key:i,className:"".concat(c,"-circle-path"),r:O,cx:50,cy:50,stroke:a,strokeWidth:f,opacity:1,style:s,ref:function(e){B[i]=e}})}))):function(){var e=0;return F.map((function(t,n){var o=N[n]||N[N.length-1],i=Ge(I,T,e,t,P,h,v,o,M,f);return e+=t,r.createElement(Xe,{key:n,color:o,ptg:t,radius:O,prefixCls:c,gradientId:$,style:i,strokeLinecap:M,strokeWidth:f,gapDegree:h,ref:function(e){B[n]=e},size:Ze})})).reverse()}())},Je=n(955);function et(e){return!e||e<0?0:e>100?100:e}function tt(e){let{success:t,successPercent:n}=e,r=n;return t&&"progress"in t&&(r=t.progress),t&&"percent"in t&&(r=t.percent),r}const nt=(e,t,n)=>{var r,o,i,l;let a=-1,s=-1;if("step"===t){const t=n.steps,r=n.strokeWidth;"string"==typeof e||void 0===e?(a="small"===e?2:14,s=null!=r?r:8):"number"==typeof e?[a,s]=[e,e]:[a=14,s=8]=Array.isArray(e)?e:[e.width,e.height],a*=t}else if("line"===t){const t=null==n?void 0:n.strokeWidth;"string"==typeof e||void 0===e?s=t||("small"===e?6:8):"number"==typeof e?[a,s]=[e,e]:[a=-1,s=8]=Array.isArray(e)?e:[e.width,e.height]}else"circle"!==t&&"dashboard"!==t||("string"==typeof e||void 0===e?[a,s]="small"===e?[60,60]:[120,120]:"number"==typeof e?[a,s]=[e,e]:Array.isArray(e)&&(a=null!==(o=null!==(r=e[0])&&void 0!==r?r:e[1])&&void 0!==o?o:120,s=null!==(l=null!==(i=e[0])&&void 0!==i?i:e[1])&&void 0!==l?l:120));return[a,s]};var rt=e=>{const{prefixCls:t,trailColor:n=null,strokeLinecap:o="round",gapPosition:i,gapDegree:l,width:s=120,type:c,children:u,success:f,size:d=s,steps:p}=e,[h,m]=nt(d,"circle");let{strokeWidth:g}=e;void 0===g&&(g=Math.max((e=>3/e*100)(h),6));const y={width:h,height:m,fontSize:.15*h+6},v=r.useMemo((()=>l||0===l?l:"dashboard"===c?75:void 0),[l,c]),b=(e=>{let{percent:t,success:n,successPercent:r}=e;const o=et(tt({success:n,successPercent:r}));return[o,et(et(t)-o)]})(e),x=i||"dashboard"===c&&"bottom"||void 0,k="[object Object]"===Object.prototype.toString.call(e.strokeColor),w=(e=>{let{success:t={},strokeColor:n}=e;const{strokeColor:r}=t;return[r||Q.uy.green,n||null]})({success:f,strokeColor:e.strokeColor}),C=a()(`${t}-inner`,{[`${t}-circle-gradient`]:k}),S=r.createElement(Ke,{steps:p,percent:p?b[1]:b,strokeWidth:g,trailWidth:g,strokeColor:p?w[1]:w,strokeLinecap:o,trailColor:n,prefixCls:t,gapDegree:v,gapPosition:x}),A=h<=20,E=r.createElement("div",{className:C,style:y},S,!A&&u);return A?r.createElement(Je.A,{title:u},E):E};const ot="--progress-line-stroke-color",it="--progress-percent",lt=e=>{const t=e?"100%":"-100%";return new V.Mo(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},at=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:Object.assign(Object.assign({},(0,_.dF)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${ot})`]},height:"100%",width:`calc(1 / var(${it}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[n]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,V.zA)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:lt(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:lt(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},st=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[n]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},ct=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},ut=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${n}`]:{fontSize:e.fontSizeSM}}}};var ft=(0,U.OF)("Progress",(e=>{const t=e.calc(e.marginXXS).div(2).equal(),n=(0,q.oX)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[at(n),st(n),ct(n),ut(n)]}),(e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:e.fontSize/e.fontSizeSM+"em"}))),dt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const pt=(e,t)=>{const{from:n=Q.uy.blue,to:r=Q.uy.blue,direction:o=("rtl"===t?"to left":"to right")}=e,i=dt(e,["from","to","direction"]);if(0!==Object.keys(i).length){const e=`linear-gradient(${o}, ${(e=>{let t=[];return Object.keys(e).forEach((n=>{const r=parseFloat(n.replace(/%/g,""));Number.isNaN(r)||t.push({key:r,value:e[n]})})),t=t.sort(((e,t)=>e.key-t.key)),t.map((e=>{let{key:t,value:n}=e;return`${n} ${t}%`})).join(", ")})(i)})`;return{background:e,[ot]:e}}const l=`linear-gradient(${o}, ${n}, ${r})`;return{background:l,[ot]:l}};var ht=e=>{const{prefixCls:t,direction:n,percent:o,size:i,strokeWidth:l,strokeColor:s,strokeLinecap:c="round",children:u,trailColor:f=null,percentPosition:d,success:p}=e,{align:h,type:m}=d,g=s&&"string"!=typeof s?pt(s,n):{[ot]:s,background:s},y="square"===c||"butt"===c?0:void 0,v=null!=i?i:[-1,l||("small"===i?6:8)],[b,x]=nt(v,"line",{strokeWidth:l});const k={backgroundColor:f||void 0,borderRadius:y},w=Object.assign(Object.assign({width:`${et(o)}%`,height:x,borderRadius:y},g),{[it]:et(o)/100}),C=tt(e),S={width:`${et(C)}%`,height:x,borderRadius:y,backgroundColor:null==p?void 0:p.strokeColor},A={width:b<0?"100%":b},E=r.createElement("div",{className:`${t}-inner`,style:k},r.createElement("div",{className:a()(`${t}-bg`,`${t}-bg-${m}`),style:w},"inner"===m&&u),void 0!==C&&r.createElement("div",{className:`${t}-success-bg`,style:S})),$="outer"===m&&"start"===h,O="outer"===m&&"end"===h;return"outer"===m&&"center"===h?r.createElement("div",{className:`${t}-layout-bottom`},E,u):r.createElement("div",{className:`${t}-outer`,style:A},$&&u,E,O&&u)};var mt=e=>{const{size:t,steps:n,rounding:o=Math.round,percent:i=0,strokeWidth:l=8,strokeColor:s,trailColor:c=null,prefixCls:u,children:f}=e,d=o(n*(i/100)),p=null!=t?t:["small"===t?2:14,l],[h,m]=nt(p,"step",{steps:n,strokeWidth:l}),g=h/n,y=Array.from({length:n});for(let v=0;v<n;v++){const e=Array.isArray(s)?s[v]:s;y[v]=r.createElement("div",{key:v,className:a()(`${u}-steps-item`,{[`${u}-steps-item-active`]:v<=d-1}),style:{backgroundColor:v<=d-1?e:c,width:g,height:m}})}return r.createElement("div",{className:`${u}-steps-outer`},y,f)},gt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const yt=["normal","exception","active","success"],vt=r.forwardRef(((e,t)=>{const{prefixCls:n,className:o,rootClassName:i,steps:l,strokeColor:s,percent:c=0,size:u="default",showInfo:f=!0,type:d="line",status:p,format:h,style:m,percentPosition:g={}}=e,y=gt(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:v="end",type:b="outer"}=g,x=Array.isArray(s)?s[0]:s,k="string"==typeof s||Array.isArray(s)?s:void 0,w=r.useMemo((()=>{if(x){const e="string"==typeof x?x:Object.values(x)[0];return new De.Y(e).isLight()}return!1}),[s]),C=r.useMemo((()=>{var t,n;const r=tt(e);return parseInt(void 0!==r?null===(t=null!=r?r:0)||void 0===t?void 0:t.toString():null===(n=null!=c?c:0)||void 0===n?void 0:n.toString(),10)}),[c,e.success,e.successPercent]),S=r.useMemo((()=>!yt.includes(p)&&C>=100?"success":p||"normal"),[p,C]),{getPrefixCls:A,direction:E,progress:$}=r.useContext(L.QO),O=A("progress",n),[I,P,T]=ft(O),z="line"===d,D=z&&!l,j=r.useMemo((()=>{if(!f)return null;const t=tt(e);let n;const o=z&&w&&"inner"===b;return"inner"===b||h||"exception"!==S&&"success"!==S?n=(h||(e=>`${e}%`))(et(c),et(t)):"exception"===S?n=z?r.createElement(Ne.A,null):r.createElement(Le.A,null):"success"===S&&(n=z?r.createElement(je.A,null):r.createElement(Fe.A,null)),r.createElement("span",{className:a()(`${O}-text`,{[`${O}-text-bright`]:o,[`${O}-text-${v}`]:D,[`${O}-text-${b}`]:D}),title:"string"==typeof n?n:void 0},n)}),[f,c,C,S,d,O,h]);let F;"line"===d?F=l?r.createElement(mt,Object.assign({},e,{strokeColor:k,prefixCls:O,steps:"object"==typeof l?l.count:l}),j):r.createElement(ht,Object.assign({},e,{strokeColor:x,prefixCls:O,direction:E,percentPosition:{align:v,type:b}}),j):"circle"!==d&&"dashboard"!==d||(F=r.createElement(rt,Object.assign({},e,{strokeColor:x,prefixCls:O,progressStatus:S}),j));const N=a()(O,`${O}-status-${S}`,{[`${O}-${"dashboard"===d?"circle":d}`]:"line"!==d,[`${O}-inline-circle`]:"circle"===d&&nt(u,"circle")[0]<=20,[`${O}-line`]:D,[`${O}-line-align-${v}`]:D,[`${O}-line-position-${b}`]:D,[`${O}-steps`]:l,[`${O}-show-info`]:f,[`${O}-${u}`]:"string"==typeof u,[`${O}-rtl`]:"rtl"===E},null==$?void 0:$.className,o,i,P,T);return I(r.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},null==$?void 0:$.style),m),className:N,role:"progressbar","aria-valuenow":C,"aria-valuemin":0,"aria-valuemax":100},(0,he.A)(y,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),F))}));var bt=vt;const xt=r.forwardRef(((e,t)=>{let{prefixCls:n,className:o,style:i,locale:l,listType:s,file:c,items:u,progress:f,iconRender:d,actionIconRender:p,itemRender:h,isImgUrl:m,showPreviewIcon:g,showRemoveIcon:y,showDownloadIcon:v,previewIcon:b,removeIcon:x,downloadIcon:k,extra:w,onPreview:C,onDownload:S,onClose:A}=e;var E,$;const{status:O}=c,[I,P]=r.useState(O);r.useEffect((()=>{"removed"!==O&&P(O)}),[O]);const[T,z]=r.useState(!1);r.useEffect((()=>{const e=setTimeout((()=>{z(!0)}),300);return()=>{clearTimeout(e)}}),[]);const D=d(c);let j=r.createElement("div",{className:`${n}-icon`},D);if("picture"===s||"picture-card"===s||"picture-circle"===s)if("uploading"===I||!c.thumbUrl&&!c.url){const e=a()(`${n}-list-item-thumbnail`,{[`${n}-list-item-file`]:"uploading"!==I});j=r.createElement("div",{className:e},D)}else{const e=(null==m?void 0:m(c))?r.createElement("img",{src:c.thumbUrl||c.url,alt:c.name,className:`${n}-list-item-image`,crossOrigin:c.crossOrigin}):D,t=a()(`${n}-list-item-thumbnail`,{[`${n}-list-item-file`]:m&&!m(c)});j=r.createElement("a",{className:t,onClick:e=>C(c,e),href:c.url||c.thumbUrl,target:"_blank",rel:"noopener noreferrer"},e)}const F=a()(`${n}-list-item`,`${n}-list-item-${I}`),N="string"==typeof c.linkProps?JSON.parse(c.linkProps):c.linkProps,M=("function"==typeof y?y(c):y)?p(("function"==typeof x?x(c):x)||r.createElement(Oe,null),(()=>A(c)),n,l.removeFile,!0):null,R=("function"==typeof v?v(c):v)&&"done"===I?p(("function"==typeof k?k(c):k)||r.createElement(Te,null),(()=>S(c)),n,l.downloadFile):null,B="picture-card"!==s&&"picture-circle"!==s&&r.createElement("span",{key:"download-delete",className:a()(`${n}-list-item-actions`,{picture:"picture"===s})},R,M),_="function"==typeof w?w(c):w,H=_&&r.createElement("span",{className:`${n}-list-item-extra`},_),U=a()(`${n}-list-item-name`),q=c.url?r.createElement("a",Object.assign({key:"view",target:"_blank",rel:"noopener noreferrer",className:U,title:c.name},N,{href:c.url,onClick:e=>C(c,e)}),c.name,H):r.createElement("span",{key:"view",className:U,onClick:e=>C(c,e),title:c.name},c.name,H),V=("function"==typeof g?g(c):g)&&(c.url||c.thumbUrl)?r.createElement("a",{href:c.url||c.thumbUrl,target:"_blank",rel:"noopener noreferrer",onClick:e=>C(c,e),title:l.previewFile},"function"==typeof b?b(c):b||r.createElement(ze.A,null)):null,W=("picture-card"===s||"picture-circle"===s)&&"uploading"!==I&&r.createElement("span",{className:`${n}-list-item-actions`},V,"done"===I&&R,M),{getPrefixCls:X}=r.useContext(L.QO),Z=X(),G=r.createElement("div",{className:F},j,q,B,W,T&&r.createElement(pe.Ay,{motionName:`${Z}-fade`,visible:"uploading"===I,motionDeadline:2e3},(e=>{let{className:t}=e;const o="percent"in c?r.createElement(bt,Object.assign({},f,{type:"line",percent:c.percent,"aria-label":c["aria-label"],"aria-labelledby":c["aria-labelledby"]})):null;return r.createElement("div",{className:a()(`${n}-list-item-progress`,t)},o)}))),Q=c.response&&"string"==typeof c.response?c.response:(null===(E=c.error)||void 0===E?void 0:E.statusText)||(null===($=c.error)||void 0===$?void 0:$.message)||l.uploadError,Y="error"===I?r.createElement(Je.A,{title:Q,getPopupContainer:e=>e.parentNode},G):G;return r.createElement("div",{className:a()(`${n}-list-item-container`,o),style:i,ref:t},h?h(Y,c,u,{download:S.bind(null,c),preview:C.bind(null,c),remove:A.bind(null,c)}):Y)}));var kt=xt;const wt=(e,t)=>{const{listType:n="text",previewFile:i=Ae,onPreview:l,onDownload:s,onRemove:c,locale:u,iconRender:f,isImageUrl:d=Ce,prefixCls:p,items:h=[],showPreviewIcon:m=!0,showRemoveIcon:g=!0,showDownloadIcon:y=!1,removeIcon:v,previewIcon:b,downloadIcon:x,extra:k,progress:w={size:[-1,2],showInfo:!1},appendAction:C,appendActionVisible:S=!0,itemRender:A,disabled:E}=e,$=(0,me.A)(),[O,I]=r.useState(!1),P=["picture-card","picture-circle"].includes(n);r.useEffect((()=>{n.startsWith("picture")&&(h||[]).forEach((e=>{(e.originFileObj instanceof File||e.originFileObj instanceof Blob)&&void 0===e.thumbUrl&&(e.thumbUrl="",null==i||i(e.originFileObj).then((t=>{e.thumbUrl=t||"",$()})))}))}),[n,h,i]),r.useEffect((()=>{I(!0)}),[]);const T=(e,t)=>{if(l)return null==t||t.preventDefault(),l(e)},z=e=>{"function"==typeof s?s(e):e.url&&window.open(e.url)},D=e=>{null==c||c(e)},j=e=>{if(f)return f(e,n);const t="uploading"===e.status;if(n.startsWith("picture")){const o="picture"===n?r.createElement(le.A,null):u.uploading,i=(null==d?void 0:d(e))?r.createElement(de,null):r.createElement(ie,null);return t?o:i}return t?r.createElement(le.A,null):r.createElement(ce,null)},F=(e,t,n,o,i)=>{const l={type:"text",size:"small",title:o,onClick:n=>{var o,i;t(),r.isValidElement(e)&&(null===(i=(o=e.props).onClick)||void 0===i||i.call(o,n))},className:`${n}-list-item-action`};return i&&(l.disabled=E),r.isValidElement(e)?r.createElement(ve.Ay,Object.assign({},l,{icon:(0,ye.Ob)(e,Object.assign(Object.assign({},e.props),{onClick:()=>{}}))})):r.createElement(ve.Ay,Object.assign({},l),r.createElement("span",null,e))};r.useImperativeHandle(t,(()=>({handlePreview:T,handleDownload:z})));const{getPrefixCls:N}=r.useContext(L.QO),M=N("upload",p),R=N(),B=a()(`${M}-list`,`${M}-list-${n}`),_=r.useMemo((()=>(0,he.A)((0,ge.A)(R),["onAppearEnd","onEnterEnd","onLeaveEnd"])),[R]),H=Object.assign(Object.assign({},P?{}:_),{motionDeadline:2e3,motionName:`${M}-${P?"animate-inline":"animate"}`,keys:(0,o.A)(h.map((e=>({key:e.uid,file:e})))),motionAppear:O});return r.createElement("div",{className:B},r.createElement(pe.aF,Object.assign({},H,{component:!1}),(e=>{let{key:t,file:o,className:i,style:l}=e;return r.createElement(kt,{key:t,locale:u,prefixCls:M,className:i,style:l,file:o,items:h,progress:w,listType:n,isImgUrl:d,showPreviewIcon:m,showRemoveIcon:g,showDownloadIcon:y,removeIcon:v,previewIcon:b,downloadIcon:x,extra:k,iconRender:j,actionIconRender:F,itemRender:A,onPreview:T,onDownload:z,onClose:D})})),C&&r.createElement(pe.Ay,Object.assign({},H,{visible:S,forceRender:!0}),(e=>{let{className:t,style:n}=e;return(0,ye.Ob)(C,(e=>({className:a()(e.className,t),style:Object.assign(Object.assign(Object.assign({},n),{pointerEvents:t?"none":void 0}),e.style)})))})))};var Ct=r.forwardRef(wt),St=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function l(e){try{s(r.next(e))}catch(t){i(t)}}function a(e){try{s(r.throw(e))}catch(t){i(t)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,a)}s((r=r.apply(e,t||[])).next())}))};const At=`__LIST_IGNORE_${Date.now()}__`,Et=(e,t)=>{const{fileList:n,defaultFileList:l,onRemove:s,showUploadList:c=!0,listType:u="text",onPreview:f,onDownload:d,onChange:p,onDrop:h,previewFile:m,disabled:g,locale:y,iconRender:v,isImageUrl:b,progress:x,prefixCls:k,className:w,type:C="select",children:S,style:A,itemRender:E,maxCount:$,data:O={},multiple:I=!1,hasControlInside:P=!0,action:T="",accept:z="",supportServerRender:D=!0,rootClassName:j}=e,_=r.useContext(M.A),H=null!=g?g:_,[U,q]=(0,N.A)(l||[],{value:n,postState:e=>null!=e?e:[]}),[V,W]=r.useState("drop"),X=r.useRef(null),Z=r.useRef(null);r.useMemo((()=>{const e=Date.now();(n||[]).forEach(((t,n)=>{t.uid||Object.isFrozen(t)||(t.uid=`__AUTO__${e}_${n}__`)}))}),[n]);const G=(e,t,n)=>{let r=(0,o.A)(t),l=!1;1===$?r=r.slice(-1):$&&(l=r.length>$,r=r.slice(0,$)),(0,i.flushSync)((()=>{q(r)}));const a={file:e,fileList:r};n&&(a.event=n),l&&"removed"!==e.status&&!r.some((t=>t.uid===e.uid))||(0,i.flushSync)((()=>{null==p||p(a)}))},Q=e=>{const t=e.filter((e=>!e.file[At]));if(!t.length)return;const n=t.map((e=>be(e.file)));let r=(0,o.A)(U);n.forEach((e=>{r=xe(e,r)})),n.forEach(((e,n)=>{let o=e;if(t[n].parsedFile)e.status="uploading";else{const{originFileObj:t}=e;let n;try{n=new File([t],t.name,{type:t.type})}catch(i){n=new Blob([t],{type:t.type}),n.name=t.name,n.lastModifiedDate=new Date,n.lastModified=(new Date).getTime()}n.uid=e.uid,o=n}G(o,r)}))},Y=(e,t,n)=>{try{"string"==typeof e&&(e=JSON.parse(e))}catch(i){}if(!ke(t,U))return;const r=be(t);r.status="done",r.percent=100,r.response=e,r.xhr=n;const o=xe(r,U);G(r,o)},K=(e,t)=>{if(!ke(t,U))return;const n=be(t);n.status="uploading",n.percent=e.percent;const r=xe(n,U);G(n,r,e)},J=(e,t,n)=>{if(!ke(n,U))return;const r=be(n);r.error=e,r.response=t,r.status="error";const o=xe(r,U);G(r,o)},ee=e=>{let t;Promise.resolve("function"==typeof s?s(e):s).then((n=>{var r;if(!1===n)return;const o=function(e,t){const n=void 0!==e.uid?"uid":"name",r=t.filter((t=>t[n]!==e[n]));return r.length===t.length?null:r}(e,U);o&&(t=Object.assign(Object.assign({},e),{status:"removed"}),null==U||U.forEach((e=>{const n=void 0!==t.uid?"uid":"name";e[n]!==t[n]||Object.isFrozen(e)||(e.status="removed")})),null===(r=X.current)||void 0===r||r.abort(t),G(t,o))}))},ne=e=>{W(e.type),"drop"===e.type&&(null==h||h(e))};r.useImperativeHandle(t,(()=>({onBatchStart:Q,onSuccess:Y,onProgress:K,onError:J,fileList:U,upload:X.current,nativeElement:Z.current})));const{getPrefixCls:re,direction:oe,upload:ie}=r.useContext(L.QO),le=re("upload",k),ae=Object.assign(Object.assign({onBatchStart:Q,onError:J,onProgress:K,onSuccess:Y},e),{data:O,multiple:I,action:T,accept:z,supportServerRender:D,prefixCls:le,disabled:H,beforeUpload:(t,n)=>St(void 0,void 0,void 0,(function*(){const{beforeUpload:r,transformFile:o}=e;let i=t;if(r){const e=yield r(t,n);if(!1===e)return!1;if(delete t[At],e===At)return Object.defineProperty(t,At,{value:!0,configurable:!0}),!1;"object"==typeof e&&e&&(i=e)}return o&&(i=yield o(i)),i})),onChange:void 0,hasControlInside:P});delete ae.className,delete ae.style,S&&!H||delete ae.id;const se=`${le}-wrapper`,[ce,ue,fe]=te(le,se),[de]=(0,R.A)("Upload",B.A.Upload),{showRemoveIcon:pe,showPreviewIcon:he,showDownloadIcon:me,removeIcon:ge,previewIcon:ye,downloadIcon:ve,extra:we}="boolean"==typeof c?{}:c,Ce=void 0===pe?!H:pe,Se=(e,t)=>c?r.createElement(Ct,{prefixCls:le,listType:u,items:U,previewFile:m,onPreview:f,onDownload:d,onRemove:ee,showRemoveIcon:Ce,showPreviewIcon:he,showDownloadIcon:me,removeIcon:ge,previewIcon:ye,downloadIcon:ve,iconRender:v,extra:we,locale:Object.assign(Object.assign({},de),y),isImageUrl:b,progress:x,appendAction:e,appendActionVisible:t,itemRender:E,disabled:H}):e,Ae=a()(se,w,j,ue,fe,null==ie?void 0:ie.className,{[`${le}-rtl`]:"rtl"===oe,[`${le}-picture-card-wrapper`]:"picture-card"===u,[`${le}-picture-circle-wrapper`]:"picture-circle"===u}),Ee=Object.assign(Object.assign({},null==ie?void 0:ie.style),A);if("drag"===C){const e=a()(ue,le,`${le}-drag`,{[`${le}-drag-uploading`]:U.some((e=>"uploading"===e.status)),[`${le}-drag-hover`]:"dragover"===V,[`${le}-disabled`]:H,[`${le}-rtl`]:"rtl"===oe});return ce(r.createElement("span",{className:Ae,ref:Z},r.createElement("div",{className:e,style:Ee,onDrop:ne,onDragOver:ne,onDragLeave:ne},r.createElement(F,Object.assign({},ae,{ref:X,className:`${le}-btn`}),r.createElement("div",{className:`${le}-drag-container`},S))),Se()))}const $e=a()(le,`${le}-select`,{[`${le}-disabled`]:H,[`${le}-hidden`]:!S}),Oe=r.createElement("div",{className:$e},r.createElement(F,Object.assign({},ae,{ref:X})));return ce("picture-card"===u||"picture-circle"===u?r.createElement("span",{className:Ae,ref:Z},Se(Oe,!!S)):r.createElement("span",{className:Ae,ref:Z},Oe,Se()))};var $t=r.forwardRef(Et),Ot=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const It=r.forwardRef(((e,t)=>{var{style:n,height:o,hasControlInside:i=!1}=e,l=Ot(e,["style","height","hasControlInside"]);return r.createElement($t,Object.assign({ref:t,hasControlInside:i},l,{type:"drag",style:Object.assign(Object.assign({},n),{height:o})}))}));var Pt=It;const Tt=$t;Tt.Dragger=Pt,Tt.LIST_IGNORE=At;var zt=Tt},8458:function(e,t,n){"use strict";n.d(t,{A:function(){return it}});var r=n(436),o=n(6540),i=n(2279),l=n(867),a=n(4642),s=n(8811),c=n(6029),u=n(7541),f=n(7850),d=n(6942),p=n.n(d),h=n(275),m=n(3723),g=n(9155),y=n(1320),v=n(1233),b=n(2941),x=n(9449);function k(e){return!!(null==e?void 0:e.then)}var w=e=>{const{type:t,children:n,prefixCls:r,buttonProps:i,close:l,autoFocus:a,emitEvent:s,isSilent:c,quitOnNullishReturnValue:u,actionFn:f}=e,d=o.useRef(!1),p=o.useRef(null),[h,m]=(0,v.A)(!1),g=function(){null==l||l.apply(void 0,arguments)};o.useEffect((()=>{let e=null;return a&&(e=setTimeout((()=>{var e;null===(e=p.current)||void 0===e||e.focus({preventScroll:!0})}))),()=>{e&&clearTimeout(e)}}),[]);return o.createElement(b.Ay,Object.assign({},(0,x.DU)(t),{onClick:e=>{if(d.current)return;if(d.current=!0,!f)return void g();let t;if(s){if(t=f(e),u&&!k(t))return d.current=!1,void g(e)}else if(f.length)t=f(l),d.current=!1;else if(t=f(),!k(t))return void g();(e=>{k(e)&&(m(!0),e.then((function(){m(!1,!0),g.apply(void 0,arguments),d.current=!1}),(e=>{if(m(!1,!0),d.current=!1,!(null==c?void 0:c()))return Promise.reject(e)})))})(t)},loading:h,prefixCls:r},i,{ref:p}),n)};const C=o.createContext({}),{Provider:S}=C;var A=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:r,mergedOkCancel:i,rootPrefixCls:l,close:a,onCancel:s,onConfirm:c}=(0,o.useContext)(C);return i?o.createElement(w,{isSilent:r,actionFn:s,close:function(){null==a||a.apply(void 0,arguments),null==c||c(!1)},autoFocus:"cancel"===e,buttonProps:t,prefixCls:`${l}-btn`},n):null};var E=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:r,rootPrefixCls:i,okTextLocale:l,okType:a,onConfirm:s,onOk:c}=(0,o.useContext)(C);return o.createElement(w,{isSilent:n,type:a||"primary",actionFn:c,close:function(){null==t||t.apply(void 0,arguments),null==s||s(!0)},autoFocus:"ok"===e,buttonProps:r,prefixCls:`${i}-btn`},l)},$=n(7852),O=n(8168),I=n(5544),P=n(5062),T=o.createContext({}),z=n(9379),D=n(4808),j=n(6855),F=n(6928),N=n(2065);function L(e,t,n){var r=t;return!r&&n&&(r="".concat(e,"-").concat(n)),r}function M(e,t){var n=e["page".concat(t?"Y":"X","Offset")],r="scroll".concat(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}var R=n(754),B=n(2284),_=n(8719),H=o.memo((function(e){return e.children}),(function(e,t){return!t.shouldUpdate})),U={width:0,height:0,overflow:"hidden",outline:"none"},q={outline:"none"},V=o.forwardRef((function(e,t){var n=e.prefixCls,r=e.className,i=e.style,l=e.title,a=e.ariaId,s=e.footer,c=e.closable,u=e.closeIcon,f=e.onClose,d=e.children,h=e.bodyStyle,m=e.bodyProps,g=e.modalRender,y=e.onMouseDown,v=e.onMouseUp,b=e.holderRef,x=e.visible,k=e.forceRender,w=e.width,C=e.height,S=e.classNames,A=e.styles,E=o.useContext(T).panel,$=(0,_.xK)(b,E),I=(0,o.useRef)(),P=(0,o.useRef)();o.useImperativeHandle(t,(function(){return{focus:function(){var e;null===(e=I.current)||void 0===e||e.focus({preventScroll:!0})},changeActive:function(e){var t=document.activeElement;e&&t===P.current?I.current.focus({preventScroll:!0}):e||t!==I.current||P.current.focus({preventScroll:!0})}}}));var D={};void 0!==w&&(D.width=w),void 0!==C&&(D.height=C);var j=s?o.createElement("div",{className:p()("".concat(n,"-footer"),null==S?void 0:S.footer),style:(0,z.A)({},null==A?void 0:A.footer)},s):null,F=l?o.createElement("div",{className:p()("".concat(n,"-header"),null==S?void 0:S.header),style:(0,z.A)({},null==A?void 0:A.header)},o.createElement("div",{className:"".concat(n,"-title"),id:a},l)):null,L=(0,o.useMemo)((function(){return"object"===(0,B.A)(c)&&null!==c?c:c?{closeIcon:null!=u?u:o.createElement("span",{className:"".concat(n,"-close-x")})}:{}}),[c,u,n]),M=(0,N.A)(L,!0),R="object"===(0,B.A)(c)&&c.disabled,V=c?o.createElement("button",(0,O.A)({type:"button",onClick:f,"aria-label":"Close"},M,{className:"".concat(n,"-close"),disabled:R}),L.closeIcon):null,W=o.createElement("div",{className:p()("".concat(n,"-content"),null==S?void 0:S.content),style:null==A?void 0:A.content},V,F,o.createElement("div",(0,O.A)({className:p()("".concat(n,"-body"),null==S?void 0:S.body),style:(0,z.A)((0,z.A)({},h),null==A?void 0:A.body)},m),d),j);return o.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":l?a:null,"aria-modal":"true",ref:$,style:(0,z.A)((0,z.A)({},i),D),className:p()(n,r),onMouseDown:y,onMouseUp:v},o.createElement("div",{ref:I,tabIndex:0,style:q},o.createElement(H,{shouldUpdate:x||k},g?g(W):W)),o.createElement("div",{tabIndex:0,ref:P,style:U}))}));var W=V,X=o.forwardRef((function(e,t){var n=e.prefixCls,r=e.title,i=e.style,l=e.className,a=e.visible,s=e.forceRender,c=e.destroyOnClose,u=e.motionName,f=e.ariaId,d=e.onVisibleChanged,h=e.mousePosition,m=(0,o.useRef)(),g=o.useState(),y=(0,I.A)(g,2),v=y[0],b=y[1],x={};function k(){var e,t,n,r,o,i=(e=m.current,t=e.getBoundingClientRect(),n={left:t.left,top:t.top},r=e.ownerDocument,o=r.defaultView||r.parentWindow,n.left+=M(o),n.top+=M(o,!0),n);b(h&&(h.x||h.y)?"".concat(h.x-i.left,"px ").concat(h.y-i.top,"px"):"")}return v&&(x.transformOrigin=v),o.createElement(R.Ay,{visible:a,onVisibleChanged:d,onAppearPrepare:k,onEnterPrepare:k,forceRender:s,motionName:u,removeOnLeave:c,ref:m},(function(a,s){var c=a.className,u=a.style;return o.createElement(W,(0,O.A)({},e,{ref:t,title:r,ariaId:f,prefixCls:n,holderRef:s,style:(0,z.A)((0,z.A)((0,z.A)({},u),i),x),className:p()(l,c)}))}))}));X.displayName="Content";var Z=X,G=function(e){var t=e.prefixCls,n=e.style,r=e.visible,i=e.maskProps,l=e.motionName,a=e.className;return o.createElement(R.Ay,{key:"mask",visible:r,motionName:l,leavedClassName:"".concat(t,"-mask-hidden")},(function(e,r){var l=e.className,s=e.style;return o.createElement("div",(0,O.A)({ref:r,style:(0,z.A)((0,z.A)({},s),n),className:p()("".concat(t,"-mask"),l,a)},i))}))},Q=(n(8210),function(e){var t=e.prefixCls,n=void 0===t?"rc-dialog":t,r=e.zIndex,i=e.visible,l=void 0!==i&&i,a=e.keyboard,s=void 0===a||a,c=e.focusTriggerAfterClose,u=void 0===c||c,f=e.wrapStyle,d=e.wrapClassName,h=e.wrapProps,m=e.onClose,g=e.afterOpenChange,y=e.afterClose,v=e.transitionName,b=e.animation,x=e.closable,k=void 0===x||x,w=e.mask,C=void 0===w||w,S=e.maskTransitionName,A=e.maskAnimation,E=e.maskClosable,$=void 0===E||E,P=e.maskStyle,T=e.maskProps,M=e.rootClassName,R=e.classNames,B=e.styles;var _=(0,o.useRef)(),H=(0,o.useRef)(),U=(0,o.useRef)(),q=o.useState(l),V=(0,I.A)(q,2),W=V[0],X=V[1],Q=(0,j.A)();function Y(e){null==m||m(e)}var K=(0,o.useRef)(!1),J=(0,o.useRef)(),ee=null;$&&(ee=function(e){K.current?K.current=!1:H.current===e.target&&Y(e)}),(0,o.useEffect)((function(){l&&(X(!0),(0,D.A)(H.current,document.activeElement)||(_.current=document.activeElement))}),[l]),(0,o.useEffect)((function(){return function(){clearTimeout(J.current)}}),[]);var te=(0,z.A)((0,z.A)((0,z.A)({zIndex:r},f),null==B?void 0:B.wrapper),{},{display:W?null:"none"});return o.createElement("div",(0,O.A)({className:p()("".concat(n,"-root"),M)},(0,N.A)(e,{data:!0})),o.createElement(G,{prefixCls:n,visible:C&&l,motionName:L(n,S,A),style:(0,z.A)((0,z.A)({zIndex:r},P),null==B?void 0:B.mask),maskProps:T,className:null==R?void 0:R.mask}),o.createElement("div",(0,O.A)({tabIndex:-1,onKeyDown:function(e){if(s&&e.keyCode===F.A.ESC)return e.stopPropagation(),void Y(e);l&&e.keyCode===F.A.TAB&&U.current.changeActive(!e.shiftKey)},className:p()("".concat(n,"-wrap"),d,null==R?void 0:R.wrapper),ref:H,onClick:ee,style:te},h),o.createElement(Z,(0,O.A)({},e,{onMouseDown:function(){clearTimeout(J.current),K.current=!0},onMouseUp:function(){J.current=setTimeout((function(){K.current=!1}))},ref:U,closable:k,ariaId:Q,prefixCls:n,visible:l&&W,onClose:Y,onVisibleChanged:function(e){if(e)(0,D.A)(H.current,document.activeElement)||null===(t=U.current)||void 0===t||t.focus();else{if(X(!1),C&&_.current&&u){try{_.current.focus({preventScroll:!0})}catch(n){}_.current=null}W&&(null==y||y())}var t;null==g||g(e)},motionName:L(n,v,b)}))))}),Y=function(e){var t=e.visible,n=e.getContainer,r=e.forceRender,i=e.destroyOnClose,l=void 0!==i&&i,a=e.afterClose,s=e.panelRef,c=o.useState(t),u=(0,I.A)(c,2),f=u[0],d=u[1],p=o.useMemo((function(){return{panel:s}}),[s]);return o.useEffect((function(){t&&d(!0)}),[t]),r||!l||f?o.createElement(T.Provider,{value:p},o.createElement(P.A,{open:t||r||f,autoDestroy:!1,getContainer:n,autoLock:t||f},o.createElement(Q,(0,O.A)({},e,{destroyOnClose:l,afterClose:function(){null==a||a(),d(!1)}})))):null};Y.displayName="Dialog";var K=Y,J=n(2897),ee=n(64),te=n(998);var ne=n(235),re=n(934),oe=n(7072),ie=n(8557),le=n(8119);var ae=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=(0,o.useContext)(C);return o.createElement(b.Ay,Object.assign({onClick:n},e),t)};var se=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:r,onOk:i}=(0,o.useContext)(C);return o.createElement(b.Ay,Object.assign({},(0,x.DU)(n),{loading:e,onClick:i},t),r)},ce=n(1815);function ue(e,t){return o.createElement("span",{className:`${e}-close-x`},t||o.createElement($.A,{className:`${e}-close-icon`}))}const fe=e=>{const{okText:t,okType:n="primary",cancelText:i,confirmLoading:l,onOk:a,onCancel:s,okButtonProps:c,cancelButtonProps:u,footer:f}=e,[d]=(0,g.A)("Modal",(0,ce.l)()),p={confirmLoading:l,okButtonProps:c,cancelButtonProps:u,okTextLocale:t||(null==d?void 0:d.okText),cancelTextLocale:i||(null==d?void 0:d.cancelText),okType:n,onOk:a,onCancel:s},h=o.useMemo((()=>p),(0,r.A)(Object.values(p)));let m;return"function"==typeof f||void 0===f?(m=o.createElement(o.Fragment,null,o.createElement(ae,null),o.createElement(se,null)),"function"==typeof f&&(m=f(m,{OkBtn:se,CancelBtn:ae})),m=o.createElement(S,{value:h},m)):m=f,o.createElement(le.X,{disabled:!1},m)};var de=n(2187),pe=n(5006),he=n(5905),me=n(8680),ge=n(9077),ye=n(4277),ve=n(7358);function be(e){return{position:e,inset:0}}const xe=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},be("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},be("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:(0,me.p9)(e)}]},ke=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${(0,de.zA)(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},(0,he.dF)(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${(0,de.zA)(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:(0,de.zA)(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},(0,he.K8)(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${(0,de.zA)(e.borderRadiusLG)} ${(0,de.zA)(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${(0,de.zA)(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,\n          ${t}-body,\n          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},we=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},Ce=e=>{const{componentCls:t}=e,n=(0,pe.i4)(e);delete n.xs;const o=Object.keys(n).map((e=>({[`@media (min-width: ${(0,de.zA)(n[e])})`]:{width:`var(--${t.replace(".","")}-${e}-width)`}})));return{[`${t}-root`]:{[t]:[{width:`var(--${t.replace(".","")}-xs-width)`}].concat((0,r.A)(o))}}},Se=e=>{const t=e.padding,n=e.fontSizeHeading5,r=e.lineHeightHeading5;return(0,ye.oX)(e,{modalHeaderHeight:e.calc(e.calc(r).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},Ae=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${(0,de.zA)(e.paddingMD)} ${(0,de.zA)(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${(0,de.zA)(e.padding)} ${(0,de.zA)(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${(0,de.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${(0,de.zA)(e.paddingXS)} ${(0,de.zA)(e.padding)}`:0,footerBorderTop:e.wireframe?`${(0,de.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${(0,de.zA)(e.borderRadiusLG)} ${(0,de.zA)(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${(0,de.zA)(2*e.padding)} ${(0,de.zA)(2*e.padding)} ${(0,de.zA)(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM});var Ee=(0,ve.OF)("Modal",(e=>{const t=Se(e);return[ke(t),we(t),xe(t),(0,ge.aB)(t,"zoom"),Ce(t)]}),Ae,{unitless:{titleLineHeight:!0}}),$e=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};let Oe;const Ie=e=>{Oe={x:e.pageX,y:e.pageY},setTimeout((()=>{Oe=null}),100)};(0,te.A)()&&window.document.documentElement&&document.documentElement.addEventListener("click",Ie,!0);var Pe=e=>{var t;const{getPopupContainer:n,getPrefixCls:r,direction:l,modal:a}=o.useContext(i.QO),s=t=>{const{onCancel:n}=e;null==n||n(t)};const{prefixCls:c,className:u,rootClassName:f,open:d,wrapClassName:g,centered:y,getContainer:v,focusTriggerAfterClose:b=!0,style:x,visible:k,width:w=520,footer:C,classNames:S,styles:A,children:E,loading:O}=e,I=$e(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading"]),P=r("modal",c),T=r(),z=(0,re.A)(P),[D,j,F]=Ee(P,z),N=p()(g,{[`${P}-centered`]:null!=y?y:null==a?void 0:a.centered,[`${P}-wrap-rtl`]:"rtl"===l}),L=null===C||O?null:o.createElement(fe,Object.assign({},e,{onOk:t=>{const{onOk:n}=e;null==n||n(t)},onCancel:s})),[M,R,B]=(0,ee.A)((0,ee.d)(e),(0,ee.d)(a),{closable:!0,closeIcon:o.createElement($.A,{className:`${P}-close-icon`}),closeIconRender:e=>ue(P,e)}),_=(0,ie.f)(`.${P}-content`),[H,U]=(0,h.YK)("Modal",I.zIndex),[q,V]=o.useMemo((()=>w&&"object"==typeof w?[void 0,w]:[w,void 0]),[w]),W=o.useMemo((()=>{const e={};return V&&Object.keys(V).forEach((t=>{const n=V[t];void 0!==n&&(e[`--${P}-${t}-width`]="number"==typeof n?`${n}px`:n)})),e}),[V]);return D(o.createElement(J.A,{form:!0,space:!0},o.createElement(ne.A.Provider,{value:U},o.createElement(K,Object.assign({width:q},I,{zIndex:H,getContainer:void 0===v?n:v,prefixCls:P,rootClassName:p()(j,f,F,z),footer:L,visible:null!=d?d:k,mousePosition:null!==(t=I.mousePosition)&&void 0!==t?t:Oe,onClose:s,closable:M?{disabled:B,closeIcon:R}:M,closeIcon:R,focusTriggerAfterClose:b,transitionName:(0,m.b)(T,"zoom",e.transitionName),maskTransitionName:(0,m.b)(T,"fade",e.maskTransitionName),className:p()(j,u,null==a?void 0:a.className),style:Object.assign(Object.assign(Object.assign({},null==a?void 0:a.style),x),W),classNames:Object.assign(Object.assign(Object.assign({},null==a?void 0:a.classNames),S),{wrapper:p()(N,null==S?void 0:S.wrapper)}),styles:Object.assign(Object.assign({},null==a?void 0:a.styles),A),panelRef:_}),O?o.createElement(oe.A,{active:!0,title:!1,paragraph:{rows:4},className:`${P}-body-skeleton`}):E))))};const Te=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:r,modalConfirmIconSize:o,fontSize:i,lineHeight:l,modalTitleHeight:a,fontHeight:s,confirmBodyPadding:c}=e,u=`${t}-confirm`;return{[u]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${u}-body-wrapper`]:Object.assign({},(0,he.t6)()),[`&${t} ${t}-body`]:{padding:c},[`${u}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:o,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(s).sub(o).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(a).sub(o).equal()).div(2).equal()}},[`${u}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${(0,de.zA)(e.marginSM)})`},[`${e.iconCls} + ${u}-paragraph`]:{maxWidth:`calc(100% - ${(0,de.zA)(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${u}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:r},[`${u}-content`]:{color:e.colorText,fontSize:i,lineHeight:l},[`${u}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${u}-error ${u}-body > ${e.iconCls}`]:{color:e.colorError},[`${u}-warning ${u}-body > ${e.iconCls},\n        ${u}-confirm ${u}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${u}-info ${u}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${u}-success ${u}-body > ${e.iconCls}`]:{color:e.colorSuccess}}};var ze=(0,ve.bf)(["Modal","confirm"],(e=>{const t=Se(e);return[Te(t)]}),Ae,{order:-1e3}),De=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function je(e){const{prefixCls:t,icon:n,okText:i,cancelText:l,confirmPrefixCls:a,type:d,okCancel:h,footer:m,locale:y}=e,v=De(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let b=n;if(!n&&null!==n)switch(d){case"info":b=o.createElement(f.A,null);break;case"success":b=o.createElement(s.A,null);break;case"error":b=o.createElement(c.A,null);break;default:b=o.createElement(u.A,null)}const x=null!=h?h:"confirm"===d,k=null!==e.autoFocusButton&&(e.autoFocusButton||"ok"),[w]=(0,g.A)("Modal"),C=y||w,$=i||(x?null==C?void 0:C.okText:null==C?void 0:C.justOkText),O=l||(null==C?void 0:C.cancelText),I=Object.assign({autoFocusButton:k,cancelTextLocale:O,okTextLocale:$,mergedOkCancel:x},v),P=o.useMemo((()=>I),(0,r.A)(Object.values(I))),T=o.createElement(o.Fragment,null,o.createElement(A,null),o.createElement(E,null)),z=void 0!==e.title&&null!==e.title,D=`${a}-body`;return o.createElement("div",{className:`${a}-body-wrapper`},o.createElement("div",{className:p()(D,{[`${D}-has-title`]:z})},b,o.createElement("div",{className:`${a}-paragraph`},z&&o.createElement("span",{className:`${a}-title`},e.title),o.createElement("div",{className:`${a}-content`},e.content))),void 0===m||"function"==typeof m?o.createElement(S,{value:P},o.createElement("div",{className:`${a}-btns`},"function"==typeof m?m(T,{OkBtn:E,CancelBtn:A}):T)):m,o.createElement(ze,{prefixCls:t}))}const Fe=e=>{const{close:t,zIndex:n,maskStyle:r,direction:i,prefixCls:l,wrapClassName:a,rootPrefixCls:s,bodyStyle:c,closable:u=!1,onConfirm:f,styles:d}=e;const g=`${l}-confirm`,v=e.width||416,b=e.style||{},x=void 0===e.mask||e.mask,k=void 0!==e.maskClosable&&e.maskClosable,w=p()(g,`${g}-${e.type}`,{[`${g}-rtl`]:"rtl"===i},e.className),[,C]=(0,y.Ay)(),S=o.useMemo((()=>void 0!==n?n:C.zIndexPopupBase+h.jH),[n,C]);return o.createElement(Pe,Object.assign({},e,{className:w,wrapClassName:p()({[`${g}-centered`]:!!e.centered},a),onCancel:()=>{null==t||t({triggerCancel:!0}),null==f||f(!1)},title:"",footer:null,transitionName:(0,m.b)(s||"","zoom",e.transitionName),maskTransitionName:(0,m.b)(s||"","fade",e.maskTransitionName),mask:x,maskClosable:k,style:b,styles:Object.assign({body:c,mask:r},d),width:v,zIndex:S,closable:u}),o.createElement(je,Object.assign({},e,{confirmPrefixCls:g})))};var Ne=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:r,theme:i}=e;return o.createElement(l.Ay,{prefixCls:t,iconPrefixCls:n,direction:r,theme:i},o.createElement(Fe,Object.assign({},e)))};var Le=[];let Me="";function Re(){return Me}const Be=e=>{var t,n;const{prefixCls:r,getContainer:l,direction:a}=e,s=(0,ce.l)(),c=(0,o.useContext)(i.QO),u=Re()||c.getPrefixCls(),f=r||`${u}-modal`;let d=l;return!1===d&&(d=void 0),o.createElement(Ne,Object.assign({},e,{rootPrefixCls:u,prefixCls:f,iconPrefixCls:c.iconPrefixCls,theme:c.theme,direction:null!=a?a:c.direction,locale:null!==(n=null===(t=c.locale)||void 0===t?void 0:t.Modal)&&void 0!==n?n:s,getContainer:d}))};function _e(e){const t=(0,l.cr)();const n=document.createDocumentFragment();let i,s,c=Object.assign(Object.assign({},e),{close:d,open:!0});function u(){for(var t,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var l;o.some((e=>null==e?void 0:e.triggerCancel))&&(null===(t=e.onCancel)||void 0===t||(l=t).call.apply(l,[e,()=>{}].concat((0,r.A)(o.slice(1)))));for(let e=0;e<Le.length;e++){if(Le[e]===d){Le.splice(e,1);break}}s()}function f(e){clearTimeout(i),i=setTimeout((()=>{const r=t.getPrefixCls(void 0,Re()),i=t.getIconPrefixCls(),c=t.getTheme(),u=o.createElement(Be,Object.assign({},e)),f=(0,a.K)();s=f(o.createElement(l.Ay,{prefixCls:r,iconPrefixCls:i,theme:c},t.holderRender?t.holderRender(u):u),n)}))}function d(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];c=Object.assign(Object.assign({},c),{open:!1,afterClose:()=>{"function"==typeof e.afterClose&&e.afterClose(),u.apply(this,n)}}),c.visible&&delete c.visible,f(c)}return f(c),Le.push(d),{destroy:d,update:function(e){c="function"==typeof e?e(c):Object.assign(Object.assign({},c),e),f(c)}}}function He(e){return Object.assign(Object.assign({},e),{type:"warning"})}function Ue(e){return Object.assign(Object.assign({},e),{type:"info"})}function qe(e){return Object.assign(Object.assign({},e),{type:"success"})}function Ve(e){return Object.assign(Object.assign({},e),{type:"error"})}function We(e){return Object.assign(Object.assign({},e),{type:"confirm"})}var Xe=n(3425),Ze=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};var Ge=(0,Xe.U)((e=>{const{prefixCls:t,className:n,closeIcon:r,closable:l,type:a,title:s,children:c,footer:u}=e,f=Ze(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:d}=o.useContext(i.QO),h=d(),m=t||d("modal"),g=(0,re.A)(h),[y,v,b]=Ee(m,g),x=`${m}-confirm`;let k={};return k=a?{closable:null!=l&&l,title:"",footer:"",children:o.createElement(je,Object.assign({},e,{prefixCls:m,confirmPrefixCls:x,rootPrefixCls:h,content:c}))}:{closable:null==l||l,title:s,footer:null!==u&&o.createElement(fe,Object.assign({},e)),children:c},y(o.createElement(W,Object.assign({prefixCls:m,className:p()(v,`${m}-pure-panel`,a&&x,a&&`${x}-${a}`,n,b,g)},f,{closeIcon:ue(m,r),closable:l},k)))}));var Qe=n(8055),Ye=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const Ke=(e,t)=>{var n,{afterClose:l,config:a}=e,s=Ye(e,["afterClose","config"]);const[c,u]=o.useState(!0),[f,d]=o.useState(a),{direction:p,getPrefixCls:h}=o.useContext(i.QO),m=h("modal"),y=h(),v=function(){var e;u(!1);for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i;n.some((e=>null==e?void 0:e.triggerCancel))&&(null===(e=f.onCancel)||void 0===e||(i=e).call.apply(i,[f,()=>{}].concat((0,r.A)(n.slice(1)))))};o.useImperativeHandle(t,(()=>({destroy:v,update:e=>{d((t=>Object.assign(Object.assign({},t),e)))}})));const b=null!==(n=f.okCancel)&&void 0!==n?n:"confirm"===f.type,[x]=(0,g.A)("Modal",Qe.A.Modal);return o.createElement(Ne,Object.assign({prefixCls:m,rootPrefixCls:y},f,{close:v,open:c,afterClose:()=>{var e;l(),null===(e=f.afterClose)||void 0===e||e.call(f)},okText:f.okText||(b?null==x?void 0:x.okText:null==x?void 0:x.justOkText),direction:f.direction||p,cancelText:f.cancelText||(null==x?void 0:x.cancelText)},s))};var Je=o.forwardRef(Ke);let et=0;const tt=o.memo(o.forwardRef(((e,t)=>{const[n,i]=function(){const[e,t]=o.useState([]);return[e,o.useCallback((e=>(t((t=>[].concat((0,r.A)(t),[e]))),()=>{t((t=>t.filter((t=>t!==e))))})),[])]}();return o.useImperativeHandle(t,(()=>({patchElement:i})),[]),o.createElement(o.Fragment,null,n)})));var nt=function(){const e=o.useRef(null),[t,n]=o.useState([]);o.useEffect((()=>{if(t.length){(0,r.A)(t).forEach((e=>{e()})),n([])}}),[t]);const i=o.useCallback((t=>function(i){var l;et+=1;const a=o.createRef();let s;const c=new Promise((e=>{s=e}));let u,f=!1;const d=o.createElement(Je,{key:`modal-${et}`,config:t(i),ref:a,afterClose:()=>{null==u||u()},isSilent:()=>f,onConfirm:e=>{s(e)}});u=null===(l=e.current)||void 0===l?void 0:l.patchElement(d),u&&Le.push(u);const p={destroy:()=>{function e(){var e;null===(e=a.current)||void 0===e||e.destroy()}a.current?e():n((t=>[].concat((0,r.A)(t),[e])))},update:e=>{function t(){var t;null===(t=a.current)||void 0===t||t.update(e)}a.current?t():n((e=>[].concat((0,r.A)(e),[t])))},then:e=>(f=!0,c.then(e))};return p}),[]);return[o.useMemo((()=>({info:i(Ue),success:i(qe),error:i(Ve),warning:i(He),confirm:i(We)})),[]),o.createElement(tt,{key:"modal-holder",ref:e})]};function rt(e){return _e(He(e))}const ot=Pe;ot.useModal=nt,ot.info=function(e){return _e(Ue(e))},ot.success=function(e){return _e(qe(e))},ot.error=function(e){return _e(Ve(e))},ot.warning=rt,ot.warn=rt,ot.confirm=function(e){return _e(We(e))},ot.destroyAll=function(){for(;Le.length;){const e=Le.pop();e&&e()}},ot.config=function(e){let{rootPrefixCls:t}=e;Me=t},ot._InternalPanelDoNotUseOrYouWillBeFired=Ge;var it=ot},8680:function(e,t,n){"use strict";n.d(t,{p9:function(){return a}});var r=n(2187),o=n(4980);const i=new r.Mo("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),l=new r.Mo("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),a=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{antCls:n}=e,r=`${n}-fade`,a=t?"&":"";return[(0,o.b)(r,i,l,e.motionDurationMid,t),{[`\n        ${a}${r}-enter,\n        ${a}${r}-appear\n      `]:{opacity:0,animationTimingFunction:"linear"},[`${a}${r}-leave`]:{animationTimingFunction:"linear"}}]}},8917:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,o=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,l=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||o.test(e)||n.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(l,s):e.replace(i,s)).replace(r,a))}}}]);
//# sourceMappingURL=febf2f12c27657ec738fa287d5ba21fddfc3e3e8-b6f43e7b7630a9ac173a.js.map