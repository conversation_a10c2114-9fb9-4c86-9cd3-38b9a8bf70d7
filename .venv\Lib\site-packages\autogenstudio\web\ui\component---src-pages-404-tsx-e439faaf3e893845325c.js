"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[453],{731:function(e,t,n){n.r(t),n.d(t,{Head:function(){return i}});var o=n(6540),l=n(4810);const a={color:"#232129",padding:"96px",fontFamily:"-apple-system, Roboto, sans-serif, serif"},r={marginTop:0,marginBottom:64,maxWidth:320},u={marginBottom:48};t.default=()=>o.createElement("main",{style:a},o.createElement("h1",{style:r},"Page not found"),o.createElement("p",{style:u},"Sorry 😔, we couldn’t find what you were looking for.",o.createElement("br",null),null,o.createElement("br",null),o.createElement(l.<PERSON>,{to:"/"},"Go home"),"."));const i=()=>o.createElement("title",null,"Not found")}}]);
//# sourceMappingURL=component---src-pages-404-tsx-e439faaf3e893845325c.js.map