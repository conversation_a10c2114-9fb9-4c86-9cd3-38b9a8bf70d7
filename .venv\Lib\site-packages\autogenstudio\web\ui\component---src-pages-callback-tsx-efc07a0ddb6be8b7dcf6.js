"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[606],{5422:function(e,o,t){t.r(o);var n=t(6540),r=t(226),i=t(3041),a=t(4716),c=t(7260),s=t(5312);const{Title:l}=i.A;o.default=e=>{let{data:o,location:t}=e;const{handleAuthCallback:i}=(0,r.A)(),{0:d,1:m}=(0,n.useState)(null),{0:u,1:p}=(0,n.useState)(!0);return(0,n.useEffect)((()=>{(async()=>{try{const e=new URLSearchParams(t.search),o=e.get("code"),n=e.get("state"),r=e.get("error");if(r)return m(`Authentication error: ${r}`),void p(!1);if(!o)return m("No authorization code found in the URL"),void p(!1);await i(o,n||void 0),p(!1)}catch(e){console.error("Error during auth callback:",e),m("Failed to complete authentication"),p(!1)}})()}),[t.search,i]),n.createElement(s.A,{meta:o.site.siteMetadata,title:"Authenticating",link:"/callback",showHeader:!1},n.createElement("div",{className:"flex flex-col items-center justify-center h-screen"},u?n.createElement(n.Fragment,null,n.createElement(a.A,{size:"large"}),n.createElement(l,{level:4,className:"mt-4"},"Completing Authentication...")):d?n.createElement(c.A,{message:"Authentication Error",description:d,type:"error",showIcon:!0,className:"max-w-md"}):n.createElement(c.A,{message:"Authentication Successful",description:"You have been successfully authenticated. You can close this window now.",type:"success",showIcon:!0,className:"max-w-md"})))}},7260:function(e,o,t){t.d(o,{A:function(){return R}});var n=t(6540),r=t(8811),i=t(6029),a=t(7852),c=t(7541),s=t(7850),l=t(6942),d=t.n(l),m=t(754),u=t(2065),p=t(8719),g=t(682),f=t(2279),h=t(2187),b=t(5905),v=t(7358);const $=(e,o,t,n,r)=>({background:e,border:`${(0,h.zA)(n.lineWidth)} ${n.lineType} ${o}`,[`${r}-icon`]:{color:t}}),y=e=>{const{componentCls:o,motionDurationSlow:t,marginXS:n,marginSM:r,fontSize:i,fontSizeLG:a,lineHeight:c,borderRadiusLG:s,motionEaseInOutCirc:l,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:g}=e;return{[o]:Object.assign(Object.assign({},(0,b.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:s,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:c},"&-message":{color:u},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${t} ${l}, opacity ${t} ${l},\n        padding-top ${t} ${l}, padding-bottom ${t} ${l},\n        margin-bottom ${t} ${l}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",padding:p,[`${o}-icon`]:{marginInlineEnd:r,fontSize:d,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:n,color:u,fontSize:a},[`${o}-description`]:{display:"block",color:m}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},E=e=>{const{componentCls:o,colorSuccess:t,colorSuccessBorder:n,colorSuccessBg:r,colorWarning:i,colorWarningBorder:a,colorWarningBg:c,colorError:s,colorErrorBorder:l,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[o]:{"&-success":$(r,n,t,e,o),"&-info":$(p,u,m,e,o),"&-warning":$(c,a,i,e,o),"&-error":Object.assign(Object.assign({},$(d,l,s,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},w=e=>{const{componentCls:o,iconCls:t,motionDurationMid:n,marginXS:r,fontSizeIcon:i,colorIcon:a,colorIconHover:c}=e;return{[o]:{"&-action":{marginInlineStart:r},[`${o}-close-icon`]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,h.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${t}-close`]:{color:a,transition:`color ${n}`,"&:hover":{color:c}}},"&-close-text":{color:a,transition:`color ${n}`,"&:hover":{color:c}}}}};var C=(0,v.OF)("Alert",(e=>[y(e),E(e),w(e)]),(e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}))),I=function(e,o){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)o.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(t[n[r]]=e[n[r]])}return t};const S={success:r.A,info:s.A,error:i.A,warning:c.A},A=e=>{const{icon:o,prefixCls:t,type:r}=e,i=S[r]||null;return o?(0,g.fx)(o,n.createElement("span",{className:`${t}-icon`},o),(()=>({className:d()(`${t}-icon`,o.props.className)}))):n.createElement(i,{className:`${t}-icon`})},x=e=>{const{isClosable:o,prefixCls:t,closeIcon:r,handleClose:i,ariaProps:c}=e,s=!0===r||void 0===r?n.createElement(a.A,null):r;return o?n.createElement("button",Object.assign({type:"button",onClick:i,className:`${t}-close-icon`,tabIndex:0},c),s):null},N=n.forwardRef(((e,o)=>{const{description:t,prefixCls:r,message:i,banner:a,className:c,rootClassName:s,style:l,onMouseEnter:g,onMouseLeave:h,onClick:b,afterClose:v,showIcon:$,closable:y,closeText:E,closeIcon:w,action:S,id:N}=e,k=I(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,j]=n.useState(!1);const z=n.useRef(null);n.useImperativeHandle(o,(()=>({nativeElement:z.current})));const{getPrefixCls:M,direction:H,closable:B,closeIcon:P,className:L,style:D}=(0,f.TP)("alert"),R=M("alert",r),[T,W,F]=C(R),G=o=>{var t;j(!0),null===(t=e.onClose)||void 0===t||t.call(e,o)},X=n.useMemo((()=>void 0!==e.type?e.type:a?"warning":"info"),[e.type,a]),U=n.useMemo((()=>!("object"!=typeof y||!y.closeIcon)||(!!E||("boolean"==typeof y?y:!1!==w&&null!=w||!!B))),[E,w,y,B]),Y=!(!a||void 0!==$)||$,K=d()(R,`${R}-${X}`,{[`${R}-with-description`]:!!t,[`${R}-no-icon`]:!Y,[`${R}-banner`]:!!a,[`${R}-rtl`]:"rtl"===H},L,c,s,F,W),V=(0,u.A)(k,{aria:!0,data:!0}),q=n.useMemo((()=>"object"==typeof y&&y.closeIcon?y.closeIcon:E||(void 0!==w?w:"object"==typeof B&&B.closeIcon?B.closeIcon:P)),[w,y,E,P]),J=n.useMemo((()=>{const e=null!=y?y:B;if("object"==typeof e){const{closeIcon:o}=e;return I(e,["closeIcon"])}return{}}),[y,B]);return T(n.createElement(m.Ay,{visible:!O,motionName:`${R}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},((o,r)=>{let{className:a,style:c}=o;return n.createElement("div",Object.assign({id:N,ref:(0,p.K4)(z,r),"data-show":!O,className:d()(K,a),style:Object.assign(Object.assign(Object.assign({},D),l),c),onMouseEnter:g,onMouseLeave:h,onClick:b,role:"alert"},V),Y?n.createElement(A,{description:t,icon:e.icon,prefixCls:R,type:X}):null,n.createElement("div",{className:`${R}-content`},i?n.createElement("div",{className:`${R}-message`},i):null,t?n.createElement("div",{className:`${R}-description`},t):null),S?n.createElement("div",{className:`${R}-action`},S):null,n.createElement(x,{isClosable:U,prefixCls:R,closeIcon:q,handleClose:G,ariaProps:J}))})))}));var k=N,O=t(3029),j=t(2901),z=t(3954),M=t(2176),H=t(6822);var B=t(5501);let P=function(e){function o(){var e,t,n,r;return(0,O.A)(this,o),t=this,n=o,r=arguments,n=(0,z.A)(n),(e=(0,H.A)(t,(0,M.A)()?Reflect.construct(n,r||[],(0,z.A)(t).constructor):n.apply(t,r))).state={error:void 0,info:{componentStack:""}},e}return(0,B.A)(o,e),(0,j.A)(o,[{key:"componentDidCatch",value:function(e,o){this.setState({error:e,info:o})}},{key:"render",value:function(){const{message:e,description:o,id:t,children:r}=this.props,{error:i,info:a}=this.state,c=(null==a?void 0:a.componentStack)||null,s=void 0===e?(i||"").toString():e,l=void 0===o?c:o;return i?n.createElement(k,{id:t,type:"error",message:s,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},l)}):r}}])}(n.Component);var L=P;const D=k;D.ErrorBoundary=L;var R=D}}]);
//# sourceMappingURL=component---src-pages-callback-tsx-efc07a0ddb6be8b7dcf6.js.map