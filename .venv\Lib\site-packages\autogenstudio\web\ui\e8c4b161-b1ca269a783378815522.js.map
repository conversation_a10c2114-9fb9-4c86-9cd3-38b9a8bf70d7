{"version": 3, "file": "e8c4b161-b1ca269a783378815522.js", "mappings": "ydAUA,MAAMA,GAAe,IAAAC,eAAc,MAC7BC,EAAaF,EAAaG,SAE1BC,EAAsB,KAAwB,WAoBpD,SAASC,EAASC,EAAUC,GACxB,MAAMC,GAAQ,IAAAC,YAAWT,GACzB,GAAc,OAAVQ,EACA,MAAM,IAAIE,MAAMN,GAEpB,OAAO,OAAuBI,EAAOF,EAAUC,EACnD,CAeA,SAASI,IACL,MAAMH,GAAQ,IAAAC,YAAWT,GACzB,GAAc,OAAVQ,EACA,MAAM,IAAIE,MAAMN,GAEpB,OAAO,IAAAQ,UAAQ,KAAM,CACjBC,SAAUL,EAAMK,SAChBC,SAAUN,EAAMM,SAChBC,UAAWP,EAAMO,aACjB,CAACP,GACT,CAEA,MAAMQ,EAAQ,CAAEC,QAAS,QACnBC,EAAgB,CAClBC,SAAU,WACVC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,OAAQ,EACRC,QAAS,EACTC,SAAU,SACVC,KAAM,2BACNC,SAAU,eAERC,EAAqB,wBACrBC,EAAqB,wBAErBC,EAAcC,GAAMA,EAAEC,gBAC5B,SAASC,GAAgB,KAAEC,IACvB,MAAMF,EAAkB3B,EAASyB,GACjC,OAAQ,IAAAK,KAAI,MAAO,CAAEC,GAAI,yBAAwBF,IAAQ,YAAa,YAAa,cAAe,OAAQlB,MAAOE,EAAemB,SAAUL,GAC9I,CACA,SAASM,GAAiB,KAAEJ,EAAI,oBAAEK,IAC9B,OAAQ,IAAAC,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAG,MAAK,MAAO,CAAEJ,GAAI,GAAGR,KAAsBM,IAAQlB,MAAOA,EAAOqB,SAAU,CAAC,0CAA2CE,GAAuB,2DAA4D,mDAAoD,QAAS,IAAAJ,KAAI,MAAO,CAAEC,GAAI,GAAGP,KAAsBK,IAAQlB,MAAOA,EAAOqB,SAAU,yGAA2GE,IAAuB,IAAAJ,KAAIF,EAAiB,CAAEC,KAAMA,MACxhB,CAEA,MAAMQ,EAAcX,GAAOA,EAAEY,oBAAsB,OAAS,MA0BtDC,GAAQ,IAAAC,aAAW,EAAG1B,WAAW,WAAYkB,WAAUS,YAAW9B,WAAU+B,GAAQC,KACtF,MAAMC,EAAgB5C,EAASqC,GACzBQ,EAAkB,GAAG/B,IAAWgC,MAAM,KAC5C,OAAQ,IAAAhB,KAAI,MAAO,CAAEW,WAAW,OAAG,CAAC,oBAAqBA,KAAcI,IAAmBlC,MAAO,IAAKA,EAAOiC,iBAAiBD,IAAKA,KAAQD,EAAMV,SAAUA,GAAY,IAI3K,SAASe,GAAY,WAAEC,EAAU,SAAElC,EAAW,iBAC1C,OAAIkC,GAAYC,gBACL,MAEH,IAAAnB,KAAIS,EAAO,CAAEzB,SAAUA,EAAU2B,UAAW,0BAA2B,eAAgB,yGAA0GT,UAAU,IAAAF,KAAI,IAAK,CAAEoB,KAAM,wBAAyBC,OAAQ,SAAUC,IAAK,sBAAuB,aAAc,yBAA0BpB,SAAU,gBACjW,CAPAO,EAAMc,YAAc,QASpB,MAAMC,EAAc5B,IAChB,MAAM6B,EAAgB,GAChBC,EAAgB,GACtB,IAAK,MAAO,CAAEC,KAAS/B,EAAEgC,WACjBD,EAAKE,UACLJ,EAAcK,KAAKH,EAAKI,UAAUC,UAG1C,IAAK,MAAO,CAAEC,KAASrC,EAAEsC,WACjBD,EAAKJ,UACLH,EAAcI,KAAKG,GAG3B,MAAO,CAAER,gBAAeC,gBAAe,EAErCS,EAAYC,GAAQA,EAAInC,GAC9B,SAASoC,EAASC,EAAGC,GACjB,OAAQ,OAAQD,EAAEb,cAAce,IAAIL,GAAWI,EAAEd,cAAce,IAAIL,MAC/D,OAAQG,EAAEZ,cAAcc,IAAIL,GAAWI,EAAEb,cAAcc,IAAIL,GACnE,CACA,SAASM,GAAuB,kBAAEC,IAC9B,MAAMrE,EAAQG,KACR,cAAEiD,EAAa,cAAEC,GAAkBxD,EAASsD,EAAYa,GAM9D,OALA,IAAAM,YAAU,KACN,MAAMC,EAAS,CAAEC,MAAOpB,EAAeqB,MAAOpB,GAC9CgB,IAAoBE,GACpBvE,EAAMK,WAAWqE,0BAA0BC,SAASC,GAAOA,EAAGL,IAAQ,GACvE,CAACnB,EAAeC,EAAegB,IAC3B,IACX,CACA,MAAMQ,EAAkBtD,KAAQA,EAAEmD,0BAClC,SAASI,GAAkB,kBAAET,IACzB,MAAMU,EAAkClF,EAASgF,GACjD,OAAIR,GAAqBU,GACd,IAAApD,KAAIyC,EAAwB,CAAEC,kBAAmBA,IAErD,IACX,CAEA,MAAMW,EAAoB,CAAC,EAAG,GACxBC,EAAkB,CAAEC,EAAG,EAAGC,EAAG,EAAGC,KAAM,GAkEtCC,EAAgB,CAzDlB,QACA,QACA,eACA,eACA,YACA,iBACA,eACA,sBACA,oBACA,iBACA,mBACA,iBACA,iBACA,qBACA,uBACA,uBACA,UACA,UACA,aACA,gBACA,gBACA,qBACA,iBACA,WACA,aACA,kBACA,iBACA,qBACA,UACA,iBACA,gBACA,gBACA,WACA,aACA,kBACA,iBACA,kBACA,uBACA,sBACA,cACA,SACA,YACA,iBACA,aACA,mBACA,oBACA,UACA,mBACA,oBACA,oBACA,oBACA,iBACA,QACA,eACA,oBAG8C,QAC5CC,EAAc/D,IAAM,CACtBgE,SAAUhE,EAAEgE,SACZC,SAAUjE,EAAEiE,SACZC,WAAYlE,EAAEkE,WACdC,WAAYnE,EAAEmE,WACdC,mBAAoBpE,EAAEoE,mBACtBC,cAAerE,EAAEqE,cACjBC,MAAOtE,EAAEsE,MACTC,wBAAyBvE,EAAEuE,wBAC3BC,qBAAsBxE,EAAEwE,uBAEtBC,EAAiB,CAMnBC,gBAAiB,KACjBC,WAAYlB,EACZmB,QAAS,GACTC,QAAS,EACTC,oBAAoB,EACpBC,eAAgB,QAChB5E,KAAM,IACN6E,kBAAmB,GAEvB,SAASC,EAAaC,GAClB,MAAM,SAAElB,EAAQ,SAAEC,EAAQ,WAAEC,EAAU,WAAEC,EAAU,mBAAEC,EAAkB,cAAEC,EAAa,MAAEC,EAAK,wBAAEC,EAAuB,qBAAEC,GAA0BlG,EAASyF,EAAY,KAChKtF,EAAQG,KACd,IAAAmE,YAAU,KACNwB,EAAwBW,EAAMC,aAAcD,EAAME,cAC3C,KAEHC,EAAeC,QAAUb,EACzBH,GAAO,IAEZ,IACH,MAAMe,GAAiB,IAAAE,QAAOd,GAqC9B,OApCA,IAAA1B,YAAU,KACN,IAAK,MAAMyC,KAAa1B,EAAe,CACnC,MAAM2B,EAAaP,EAAMM,GAErBC,IADuBJ,EAAeC,QAAQE,UAGlB,IAArBN,EAAMM,KAGC,UAAdA,EACAxB,EAASyB,GACU,UAAdD,EACLvB,EAASwB,GACU,YAAdD,EACLtB,EAAWuB,GACQ,YAAdD,EACLrB,EAAWsB,GACQ,oBAAdD,EACLpB,EAAmBqB,GACA,eAAdD,EACLnB,EAAcoB,GACK,sBAAdD,EACLhB,EAAqBiB,GAEF,YAAdD,EACL/G,EAAMM,SAAS,CAAE2G,cAAeD,IACb,mBAAdD,EACL/G,EAAMM,SAAS,CAAE4G,qBAAsBF,IAGvChH,EAAMM,SAAS,CAAE,CAACyG,GAAYC,KACtC,CACAJ,EAAeC,QAAUJ,CAAK,GAGlCpB,EAAclB,KAAK4C,GAAcN,EAAMM,MAChC,IACX,CAEA,SAASI,IACL,MAAsB,oBAAXC,QAA2BA,OAAOC,WAGtCD,OAAOC,WAAW,gCAFd,IAGf,CAyBA,MAAMC,EAAiC,oBAAbC,SAA2BA,SAAW,KA2BhE,SAASC,EAOTC,EAAU,KAAMC,EAAU,CAAE1E,OAAQsE,EAAYK,4BAA4B,IACxE,MAAOC,EAAYC,IAAiB,IAAAC,WAAS,GAEvCC,GAAkB,IAAAjB,SAAO,GAEzBkB,GAAc,IAAAlB,QAAO,IAAImB,IAAI,MAS5BC,EAAUC,IAAe,IAAA/H,UAAQ,KACpC,GAAgB,OAAZqH,EAAkB,CAClB,MACMW,GADaC,MAAMC,QAAQb,GAAWA,EAAU,CAACA,IAElDc,QAAQC,GAAqB,iBAAPA,IAMtBrE,KAAKqE,GAAOA,EAAGC,QAAQ,IAAK,MAAMA,QAAQ,OAAQ,OAAO9F,MAAM,QAC9D+F,EAAWN,EAAKO,QAAO,CAACC,EAAKC,IAASD,EAAIE,UAAUD,IAAO,IACjE,MAAO,CAACT,EAAMM,EAClB,CACA,MAAO,CAAC,GAAI,GAAG,GAChB,CAACjB,IAsDJ,OArDA,IAAAnD,YAAU,KACN,MAAMtB,EAAS0E,GAAS1E,QAAUsE,EAClC,GAAgB,OAAZG,EAAkB,CAClB,MAAMsB,EAAeC,IACjBjB,EAAgBlB,QAAUmC,EAAMC,SAAWD,EAAME,SAAWF,EAAMG,SAGlE,KAFwBpB,EAAgBlB,SAAYkB,EAAgBlB,UAAYa,EAAQC,8BACpF,QAAeqB,GAEf,OAAO,EAEX,MAAMI,EAAYC,EAAaL,EAAMM,KAAMnB,GAC3CH,EAAYnB,QAAQ0C,IAAIP,EAAMI,IAC1BI,EAActB,EAAUF,EAAYnB,SAAS,KAC7CmC,EAAMS,iBACN5B,GAAc,GAClB,EAEE6B,EAAaV,IAGf,KAFwBjB,EAAgBlB,SAAYkB,EAAgBlB,UAAYa,EAAQC,8BACpF,QAAeqB,GAEf,OAAO,EAEX,MAAMI,EAAYC,EAAaL,EAAMM,KAAMnB,GACvCqB,EAActB,EAAUF,EAAYnB,SAAS,IAC7CgB,GAAc,GACdG,EAAYnB,QAAQ8C,SAGpB3B,EAAYnB,QAAQ+C,OAAOZ,EAAMI,IAGnB,SAAdJ,EAAMa,KACN7B,EAAYnB,QAAQ8C,QAExB5B,EAAgBlB,SAAU,CAAK,EAE7BiD,EAAe,KACjB9B,EAAYnB,QAAQ8C,QACpB9B,GAAc,EAAM,EAMxB,OAJA7E,GAAQ+G,iBAAiB,UAAWhB,GACpC/F,GAAQ+G,iBAAiB,QAASL,GAClCtC,OAAO2C,iBAAiB,OAAQD,GAChC1C,OAAO2C,iBAAiB,cAAeD,GAChC,KACH9G,GAAQgH,oBAAoB,UAAWjB,GACvC/F,GAAQgH,oBAAoB,QAASN,GACrCtC,OAAO4C,oBAAoB,OAAQF,GACnC1C,OAAO4C,oBAAoB,cAAeF,EAAa,CAE/D,IACD,CAACrC,EAASI,IACND,CACX,CAEA,SAAS4B,EAActB,EAAUF,EAAaiC,GAC1C,OAAQ/B,EAMHK,QAAQH,GAAS6B,GAAQ7B,EAAK8B,SAAWlC,EAAYmC,OAKrDC,MAAMhC,GAASA,EAAKiC,OAAOC,GAAMtC,EAAYuC,IAAID,MAC1D,CACA,SAASjB,EAAamB,EAAWrC,GAC7B,OAAOA,EAAYsC,SAASD,GAAa,OAAS,KACtD,CAQA,MAAME,EAAoB,KACtB,MAAM1K,EAAQG,IACd,OAAO,IAAAC,UAAQ,KACJ,CACHuK,OAASjD,IACL,MAAM,QAAEkD,GAAY5K,EAAMK,WAC1B,OAAOuK,EAAUA,EAAQC,QAAQ,IAAK,CAAEC,SAAUpD,GAASoD,WAAcC,QAAQC,SAAQ,EAAM,EAEnGC,QAAUvD,IACN,MAAM,QAAEkD,GAAY5K,EAAMK,WAC1B,OAAOuK,EAAUA,EAAQC,QAAQ,EAAI,IAAK,CAAEC,SAAUpD,GAASoD,WAAcC,QAAQC,SAAQ,EAAM,EAEvGE,OAAQ,CAACC,EAAWzD,KAChB,MAAM,QAAEkD,GAAY5K,EAAMK,WAC1B,OAAOuK,EAAUA,EAAQQ,QAAQD,EAAW,CAAEL,SAAUpD,GAASoD,WAAcC,QAAQC,SAAQ,EAAM,EAEzGK,QAAS,IAAMrL,EAAMK,WAAWiL,UAAU,GAC1CC,YAAaC,MAAOC,EAAU/D,KAC1B,MAAQ4D,WAAYI,EAAIC,EAAIC,GAAM,QAAEhB,GAAa5K,EAAMK,WACvD,OAAKuK,SAGCA,EAAQW,YAAY,CACtBrG,EAAGuG,EAASvG,GAAKwG,EACjBvG,EAAGsG,EAAStG,GAAKwG,EACjBvG,KAAMqG,EAASrG,MAAQwG,GACxB,CAAEd,SAAUpD,GAASoD,WACjBC,QAAQC,SAAQ,IAPZD,QAAQC,SAAQ,EAOC,EAEhCa,YAAa,KACT,MAAO3G,EAAGC,EAAGC,GAAQpF,EAAMK,WAAWiL,UACtC,MAAO,CAAEpG,IAAGC,IAAGC,OAAM,EAEzB0G,QAAUpE,IACN,MAAM,WAAEnE,EAAU,QAAE4C,EAAO,QAAEC,EAAO,QAAEwE,EAAO,QAAEmB,GAAY/L,EAAMK,WACjE,IAAKuK,IAAYmB,EACb,OAAOhB,QAAQC,SAAQ,GAE3B,MAAMgB,GAAe,QAAgBzI,EAAYmE,IAC3C,MAAE9G,EAAK,OAAEC,IAAW,QAAckL,GACxC,OAAO,QAAQ,CACXvH,MAAOwH,EACPpL,QACAC,SACAsF,UACAC,UACAwE,WACDlD,EAAQ,EAEfuE,UAAWT,MAAOtG,EAAGC,EAAGuC,KACpB,MAAM,MAAE9G,EAAK,OAAEC,EAAM,QAAEuF,EAAO,QAAEwE,GAAY5K,EAAMK,WAC5C6L,OAAoC,IAAlBxE,GAAStC,KAAuBsC,EAAQtC,KAAOgB,EACjE+F,EAAUvL,EAAQ,EAAIsE,EAAIgH,EAC1BE,EAAUvL,EAAS,EAAIsE,EAAI+G,EACjC,OAAKtB,SAGCA,EAAQW,YAAY,CACtBrG,EAAGiH,EACHhH,EAAGiH,EACHhH,KAAM8G,GACP,CAAEpB,SAAUpD,GAASoD,WACjBC,QAAQC,SAAQ,IAPZD,QAAQC,SAAQ,EAOC,EAEhCqB,UAAWb,MAAOc,EAAQ5E,KACtB,MAAM,MAAE9G,EAAK,OAAEC,EAAM,QAAEsF,EAAO,QAAEC,EAAO,QAAEwE,GAAY5K,EAAMK,WACrDoL,GAAW,QAAqBa,EAAQ1L,EAAOC,EAAQsF,EAASC,EAASsB,GAAS1G,SAAW,IACnG,OAAK4J,SAGCA,EAAQW,YAAYE,EAAU,CAAEX,SAAUpD,GAASoD,WAClDC,QAAQC,SAAQ,IAHZD,QAAQC,SAAQ,EAGC,EAEhCuB,qBAAsB,CAACC,EAAgB9E,EAAU,CAAC,KAC9C,MAAM,UAAE4D,EAAS,SAAEmB,EAAQ,WAAEC,EAAU,QAAEX,GAAY/L,EAAMK,WAC3D,IAAK0L,EACD,OAAOS,EAEX,MAAQtH,EAAGyH,EAAMxH,EAAGyH,GAASb,EAAQc,wBAC/BC,EAAoB,CACtB5H,EAAGsH,EAAetH,EAAIyH,EACtBxH,EAAGqH,EAAerH,EAAIyH,GAEpBG,EAAYrF,EAAQ+E,UAAYA,EAChCO,EAActF,EAAQgF,YAAcA,EAC1C,OAAO,QAAqBI,EAAmBxB,EAAW0B,EAAaD,EAAU,EAErFE,qBAAuBC,IACnB,MAAM,UAAE5B,EAAS,QAAES,GAAY/L,EAAMK,WACrC,IAAK0L,EACD,OAAOmB,EAEX,MAAQhI,EAAGyH,EAAMxH,EAAGyH,GAASb,EAAQc,wBAC/BM,GAAmB,QAAqBD,EAAc5B,GAC5D,MAAO,CACHpG,EAAGiI,EAAiBjI,EAAIyH,EACxBxH,EAAGgI,EAAiBhI,EAAIyH,EAC3B,KAGV,GAAG,EAQV,SAASQ,EAAaC,EAASC,GAC3B,MAAMC,EAAkB,GAKlBC,EAAa,IAAIC,IACjBC,EAAiB,GACvB,IAAK,MAAMC,KAAUN,EACjB,GAAoB,QAAhBM,EAAOC,KAIN,GAAoB,WAAhBD,EAAOC,MAAqC,YAAhBD,EAAOC,KAKxCJ,EAAWK,IAAIF,EAAO/L,GAAI,CAAC+L,QAE1B,CACD,MAAMG,EAAiBN,EAAWO,IAAIJ,EAAO/L,IACzCkM,EAKAA,EAAerK,KAAKkK,GAGpBH,EAAWK,IAAIF,EAAO/L,GAAI,CAAC+L,GAEnC,MAtBID,EAAejK,KAAKkK,GAwB5B,IAAK,MAAMK,KAAWV,EAAU,CAC5B,MAAMD,EAAUG,EAAWO,IAAIC,EAAQpM,IAKvC,IAAKyL,EAAS,CACVE,EAAgB9J,KAAKuK,GACrB,QACJ,CAEA,GAAwB,WAApBX,EAAQ,GAAGO,KACX,SAEJ,GAAwB,YAApBP,EAAQ,GAAGO,KAAoB,CAC/BL,EAAgB9J,KAAK,IAAK4J,EAAQ,GAAGxE,OACrC,QACJ,CAMA,MAAMoF,EAAiB,IAAKD,GAC5B,IAAK,MAAML,KAAUN,EACjBa,EAAYP,EAAQM,GAExBV,EAAgB9J,KAAKwK,EACzB,CAeA,OAVIP,EAAexD,QACfwD,EAAe/I,SAASgJ,SACCQ,IAAjBR,EAAOS,MACPb,EAAgBc,OAAOV,EAAOS,MAAO,EAAG,IAAKT,EAAO9E,OAGpD0E,EAAgB9J,KAAK,IAAKkK,EAAO9E,MACrC,IAGD0E,CACX,CAEA,SAASW,EAAYP,EAAQK,GACzB,OAAQL,EAAOC,MACX,IAAK,SACDI,EAAQxK,SAAWmK,EAAOnK,SAC1B,MAEJ,IAAK,gBAC8B,IAApBmK,EAAOhN,WACdqN,EAAQrN,SAAWgN,EAAOhN,eAEC,IAApBgN,EAAOW,WACdN,EAAQM,SAAWX,EAAOW,UAE9B,MAEJ,IAAK,kBACgC,IAAtBX,EAAOY,aACdP,EAAQQ,WAAa,CAAC,EACtBR,EAAQQ,SAAS5N,MAAQ+M,EAAOY,WAAW3N,MAC3CoN,EAAQQ,SAAS3N,OAAS8M,EAAOY,WAAW1N,OACxC8M,EAAOc,gBACPT,EAAQpN,MAAQ+M,EAAOY,WAAW3N,MAClCoN,EAAQnN,OAAS8M,EAAOY,WAAW1N,SAGZ,kBAApB8M,EAAOe,WACdV,EAAQU,SAAWf,EAAOe,UAK1C,CAgCA,SAASC,EAAiBtB,EAAS7I,GAC/B,OAAO4I,EAAaC,EAAS7I,EACjC,CAgCA,SAASoK,EAAiBvB,EAAS5I,GAC/B,OAAO2I,EAAaC,EAAS5I,EACjC,CACA,SAASoK,EAAsBjN,EAAI4B,GAC/B,MAAO,CACH5B,KACAgM,KAAM,SACNpK,WAER,CACA,SAASsL,EAAoBC,EAAOC,EAAc,IAAI/G,IAAOgH,GAAa,GACtE,MAAM5B,EAAU,GAChB,IAAK,MAAOzL,EAAIiH,KAASkG,EAAO,CAC5B,MAAMG,EAAiBF,EAAYzE,IAAI3I,QAEfuM,IAAlBtF,EAAKrF,WAA2B0L,GAAmBrG,EAAKrF,WAAa0L,IACnED,IAMApG,EAAKrF,SAAW0L,GAEpB7B,EAAQ5J,KAAKoL,EAAsBhG,EAAKjH,GAAIsN,IAEpD,CACA,OAAO7B,CACX,CACA,SAAS8B,GAAuB,MAAEJ,EAAQ,GAAE,OAAEK,IAC1C,MAAM/B,EAAU,GACVgC,EAAc,IAAI5B,IAAIsB,EAAM5K,KAAK0E,GAAS,CAACA,EAAKjH,GAAIiH,MAC1D,IAAK,MAAOuF,EAAOvF,KAASkG,EAAMO,UAAW,CACzC,MAAMC,EAAaH,EAAOrB,IAAIlF,EAAKjH,IAC7B4N,EAAYD,GAAY7L,WAAWC,UAAY4L,OACnCpB,IAAdqB,GAA2BA,IAAc3G,GACzCwE,EAAQ5J,KAAK,CAAE7B,GAAIiH,EAAKjH,GAAIiH,KAAMA,EAAM+E,KAAM,iBAEhCO,IAAdqB,GACAnC,EAAQ5J,KAAK,CAAEoF,KAAMA,EAAM+E,KAAM,MAAOQ,SAEhD,CACA,IAAK,MAAOxM,KAAOwN,EAAQ,MAENjB,IADAkB,EAAYtB,IAAInM,IAE7ByL,EAAQ5J,KAAK,CAAE7B,KAAIgM,KAAM,UAEjC,CACA,OAAOP,CACX,CACA,SAASoC,EAAsB5G,GAC3B,MAAO,CACHjH,GAAIiH,EAAKjH,GACTgM,KAAM,SAEd,CAqBA,MAAM8B,EAAU1B,IAAY,QAAWA,GAoBjC2B,EAAU3B,IAAY,QAAWA,GAEvC,SAAS4B,EAAgBC,GAErB,OAAO,IAAAxN,YAAWwN,EACtB,CAGA,MAAMC,GAA8C,oBAAX1I,OAAyB,EAAA2I,gBAAkB,EAAAzL,UAUpF,SAAS0L,GAASC,GAQd,MAAOC,EAAQC,IAAa,IAAArI,UAASsI,OAAO,KAMrCC,IAAS,IAAAvI,WAAS,IAe7B,SAAqBwI,GACjB,IAAID,EAAQ,GACZ,MAAO,CACHtC,IAAK,IAAMsC,EACXxK,MAAO,KACHwK,EAAQ,EAAE,EAEd5M,KAAOoF,IACHwH,EAAM5M,KAAKoF,GACXyH,GAAI,EAGhB,CA3BmCC,EAAY,IAAMJ,GAAUK,GAAKA,EAAIJ,OAAO,SAa3E,OAPAN,IAA0B,KACtB,MAAMW,EAAaJ,EAAMtC,MACrB0C,EAAWvG,SACX+F,EAASQ,GACTJ,EAAMxK,QACV,GACD,CAACqK,IACGG,CACX,CAeA,MAAMK,IAAe,IAAAjR,eAAc,MAOnC,SAASkR,IAAc,SAAE9O,IACrB,MAAM7B,EAAQG,IAsBRyQ,EAAYZ,IArBO,IAAAa,cAAaJ,IAClC,MAAM,MAAEjM,EAAQ,GAAE,SAAEe,EAAQ,gBAAEuL,EAAe,cAAEC,EAAa,WAAExN,GAAevD,EAAMK,WAMnF,IAAI2Q,EAAOxM,EACX,IAAK,MAAMyM,KAAWR,EAClBO,EAA0B,mBAAZC,EAAyBA,EAAQD,GAAQC,EAEvDH,EACAvL,EAASyL,GAEJD,GACLA,EAAc5B,EAAuB,CACjCJ,MAAOiC,EACP5B,OAAQ7L,IAEhB,GACD,KAkBG2N,EAAYlB,IAhBO,IAAAa,cAAaJ,IAClC,MAAM,MAAEhM,EAAQ,GAAE,SAAEe,EAAQ,gBAAE2L,EAAe,cAAEC,EAAa,WAAEvN,GAAe7D,EAAMK,WACnF,IAAI2Q,EAAOvM,EACX,IAAK,MAAMwM,KAAWR,EAClBO,EAA0B,mBAAZC,EAAyBA,EAAQD,GAAQC,EAEvDE,EACA3L,EAASwL,GAEJI,GACLA,EAAcjC,EAAuB,CACjCJ,MAAOiC,EACP5B,OAAQvL,IAEhB,GACD,KAEGwN,GAAQ,IAAAjR,UAAQ,KAAM,CAAGwQ,YAAWM,eAAc,IACxD,OAAO,IAAAvP,KAAI+O,GAAa/Q,SAAU,CAAE0R,MAAOA,EAAOxP,SAAUA,GAChE,CASA,MAAMyP,GAAc/P,KAAQA,EAAEqJ,QA8B9B,SAAS2G,KACL,MAAMC,EAAiB9G,IACjB1K,EAAQG,IACRsR,EAzCV,WACI,MAAMA,GAAe,IAAAxR,YAAWyQ,IAChC,IAAKe,EACD,MAAM,IAAIvR,MAAM,uDAEpB,OAAOuR,CACX,CAmCyBC,GACfC,EAAsB9R,EAASyR,IAC/BM,GAAgB,IAAAxR,UAAQ,KAC1B,MAAMyR,EAAmBjQ,GAAO5B,EAAMK,WAAWkD,WAAWwK,IAAInM,GAC1D2D,EAAY0L,IACdQ,EAAab,UAAUnN,KAAKwN,EAAQ,EAElCzL,EAAYyL,IACdQ,EAAaP,UAAUzN,KAAKwN,EAAQ,EAElCa,EAAexO,IACjB,MAAM,WAAEC,EAAU,WAAE2C,GAAelG,EAAMK,WACnC0R,EAAYrC,EAAOpM,GAAQA,EAAOC,EAAWwK,IAAIzK,EAAK1B,IACtDjB,EAAWoR,EAAUC,UACrB,QAAyBD,EAAUpR,SAAUoR,EAAUvD,SAAUuD,EAAUC,SAAUzO,EAAY2C,GACjG6L,EAAUpR,SACVsR,EAAmB,IAClBF,EACHpR,WACAC,MAAOmR,EAAUvD,UAAU5N,OAASmR,EAAUnR,MAC9CC,OAAQkR,EAAUvD,UAAU3N,QAAUkR,EAAUlR,QAEpD,OAAO,QAAWoR,EAAiB,EAEjCC,EAAa,CAACtQ,EAAIuQ,EAAYzK,EAAU,CAAEe,SAAS,MACrDlD,GAAU6M,GAAcA,EAAUjO,KAAKb,IACnC,GAAIA,EAAK1B,KAAOA,EAAI,CAChB,MAAMyQ,EAAiC,mBAAfF,EAA4BA,EAAW7O,GAAQ6O,EACvE,OAAOzK,EAAQe,SAAWiH,EAAO2C,GAAYA,EAAW,IAAK/O,KAAS+O,EAC1E,CACA,OAAO/O,CAAI,KACZ,EAEDgP,EAAa,CAAC1Q,EAAI2Q,EAAY7K,EAAU,CAAEe,SAAS,MACrDjD,GAAUgN,GAAcA,EAAUrO,KAAKP,IACnC,GAAIA,EAAKhC,KAAOA,EAAI,CAChB,MAAM6Q,EAAiC,mBAAfF,EAA4BA,EAAW3O,GAAQ2O,EACvE,OAAO7K,EAAQe,SAAWkH,EAAO8C,GAAYA,EAAW,IAAK7O,KAAS6O,EAC1E,CACA,OAAO7O,CAAI,KACZ,EAEP,MAAO,CACH8O,SAAU,IAAM1S,EAAMK,WAAWmE,MAAML,KAAKqM,IAAM,IAAMA,MACxDmC,QAAU/Q,GAAOiQ,EAAgBjQ,IAAK8B,UAAUC,SAChDkO,kBACAe,SAAU,KACN,MAAM,MAAEnO,EAAQ,IAAOzE,EAAMK,WAC7B,OAAOoE,EAAMN,KAAK0O,IAAM,IAAMA,KAAK,EAEvCC,QAAUlR,GAAO5B,EAAMK,WAAWwD,WAAWkK,IAAInM,GACjD2D,WACAC,WACAuN,SAAW9B,IACP,MAAM+B,EAAW3K,MAAMC,QAAQ2I,GAAWA,EAAU,CAACA,GACrDQ,EAAab,UAAUnN,MAAMe,GAAU,IAAIA,KAAUwO,IAAU,EAEnEC,SAAWhC,IACP,MAAMiC,EAAW7K,MAAMC,QAAQ2I,GAAWA,EAAU,CAACA,GACrDQ,EAAaP,UAAUzN,MAAMgB,GAAU,IAAIA,KAAUyO,IAAU,EAEnEC,SAAU,KACN,MAAM,MAAE3O,EAAQ,GAAE,MAAEC,EAAQ,GAAE,UAAE6G,GAActL,EAAMK,YAC7C6E,EAAGC,EAAGC,GAAQkG,EACrB,MAAO,CACH9G,MAAOA,EAAML,KAAKqM,IAAM,IAAMA,MAC9B/L,MAAOA,EAAMN,KAAK0O,IAAM,IAAMA,MAC9BpH,SAAU,CACNvG,IACAC,IACAC,QAEP,EAELgO,eAAgB5H,OAAShH,MAAO6O,EAAgB,GAAI5O,MAAO6O,EAAgB,OACvE,MAAM,MAAE9O,EAAK,MAAEC,EAAK,cAAE8O,EAAa,cAAEC,EAAa,mBAAEC,EAAkB,mBAAEC,EAAkB,SAAEC,EAAQ,eAAEC,GAAoB5T,EAAMK,YACxHmE,MAAOqP,EAAepP,MAAOqP,SAAwB,QAAoB,CAC7ET,gBACAC,gBACA9O,QACAC,QACAmP,mBAEEG,EAAmBD,EAAc5J,OAAS,EAC1C8J,EAAmBH,EAAc3J,OAAS,EAChD,GAAI6J,EAAkB,CAClB,MAAME,EAAcH,EAAc3P,IAAIsL,GACtC+D,IAAgBM,GAChBJ,EAAmBO,EACvB,CACA,GAAID,EAAkB,CAClB,MAAME,EAAcL,EAAc1P,IAAIsL,GACtC8D,IAAgBM,GAChBJ,EAAmBS,EACvB,CAIA,OAHIF,GAAoBD,IACpBJ,IAAW,CAAEnP,MAAOqP,EAAepP,MAAOqP,IAEvC,CAAEK,aAAcN,EAAeO,aAAcN,EAAe,EAEvEO,qBAAsB,CAACC,EAAYC,GAAY,EAAM/P,KACjD,MAAMgQ,GAAS,QAAaF,GACtBG,EAAWD,EAASF,EAAaxC,EAAYwC,GAC7CI,OAA2BvG,IAAV3J,EACvB,OAAKiQ,GAGGjQ,GAASxE,EAAMK,WAAWmE,OAAO+D,QAAQiI,IAC7C,MAAMmE,EAAe3U,EAAMK,WAAWkD,WAAWwK,IAAIyC,EAAE5O,IACvD,GAAI+S,IAAiBH,IAAWhE,EAAE5O,KAAO0S,EAAW1S,KAAO+S,EAAajR,UAAUkR,kBAC9E,OAAO,EAEX,MAAMC,GAAe,QAAWH,EAAiBlE,EAAImE,GAC/CG,GAAkB,QAAmBD,EAAcJ,GAEzD,OADyBF,GAAaO,EAAkB,GAC7BA,GAAmBL,EAAS7T,MAAQ6T,EAAS5T,MAAM,IAVvE,EAWT,EAENkU,mBAAoB,CAACT,EAAYU,EAAMT,GAAY,KAC/C,MACME,GADS,QAAaH,GACFA,EAAaxC,EAAYwC,GACnD,IAAKG,EACD,OAAO,EAEX,MAAMK,GAAkB,QAAmBL,EAAUO,GAErD,OADyBT,GAAaO,EAAkB,GAC7BA,GAAmBL,EAAS7T,MAAQ6T,EAAS5T,MAAM,EAElFqR,aACA+C,eAAgB,CAACrT,EAAIsT,EAAYxN,EAAU,CAAEe,SAAS,MAClDyJ,EAAWtQ,GAAK0B,IACZ,MAAM6R,EAAiC,mBAAfD,EAA4BA,EAAW5R,GAAQ4R,EACvE,OAAOxN,EAAQe,QAAU,IAAKnF,EAAM8R,KAAMD,GAAa,IAAK7R,EAAM8R,KAAM,IAAK9R,EAAK8R,QAASD,GAAY,GACxGzN,EAAQ,EAEf4K,aACA+C,eAAgB,CAACzT,EAAIsT,EAAYxN,EAAU,CAAEe,SAAS,MAClD6J,EAAW1Q,GAAKgC,IACZ,MAAMuR,EAAiC,mBAAfD,EAA4BA,EAAWtR,GAAQsR,EACvE,OAAOxN,EAAQe,QAAU,IAAK7E,EAAMwR,KAAMD,GAAa,IAAKvR,EAAMwR,KAAM,IAAKxR,EAAKwR,QAASD,GAAY,GACxGzN,EAAQ,EAEf4N,eAAiB9Q,IACb,MAAM,WAAEjB,EAAU,WAAE2C,GAAelG,EAAMK,WACzC,OAAO,QAAemE,EAAO,CAAEjB,aAAY2C,cAAa,EAE5DqP,qBAAsB,EAAG3H,OAAMhM,KAAI4T,YAAanN,MAAMoN,KAAKzV,EACtDK,WACAqV,iBAAiB3H,IAAI,GAAGyH,KAAU5H,IAAOhM,EAAK,IAAIA,IAAO,OACxD+T,UAAY,IAClBC,mBAAoB,EAAGhI,OAAMiI,WAAUL,YAAanN,MAAMoN,KAAKzV,EAC1DK,WACAqV,iBAAiB3H,IAAI,GAAGyH,IAAS5H,EAAQiI,EAAW,IAAIjI,KAAQiI,IAAa,IAAIjI,IAAU,OAC1F+H,UAAY,IACrB,GACF,IACH,OAAO,IAAAvV,UAAQ,KACJ,IACAwR,KACAJ,EACHG,yBAEL,CAACA,GACR,CAEA,MAAMnO,GAAYqF,GAASA,EAAKrF,SAC1BsS,GAAmB,CAAEnO,4BAA4B,GACjDoO,GAA0B,oBAAX3O,OAAyBA,YAAS+G,EAwDvD,MAAM6H,GAAiB,CACnBrV,SAAU,WACVC,MAAO,OACPC,OAAQ,OACRoV,IAAK,EACLC,KAAM,GAGJC,GAAc5U,IAAM,CACtBY,oBAAqBZ,EAAEY,oBACvBiU,IAAK7U,EAAE6U,MAEX,SAASC,IAAS,kBAAEC,EAAiB,aAAEC,GAAe,EAAI,YAAEC,GAAc,EAAI,YAAEC,GAAc,EAAK,iBAAEC,EAAmB,GAAG,gBAAEC,EAAkB,KAAgBC,KAAI,kBAAEC,GAAoB,EAAI,UAAEC,GAAY,EAAI,gBAAE7R,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,sBAAE2Q,EAAqB,iBAAEC,GAAmB,EAAI,SAAEnV,EAAQ,iBAAEoV,EAAgB,eAAE3Q,EAAc,iBAAE4Q,EAAgB,qBAAEC,EAAoB,kBAAE5Q,IACrY,MAAMvG,EAAQG,IACRiX,GAAW,IAAAtQ,QAAO,OAClB,oBAAE3E,EAAmB,IAAEiU,GAAQvW,EAASsW,GAAY,KACpDkB,EAA2B7P,EAAYuP,GACvCnM,GAAU,IAAA9D,WA7CpB,SAA0BiF,GACtB,MAAM/L,EAAQG,KACd,IAAAmE,YAAU,KACN,MAAMgT,EAAmB,KACrB,IAAKvL,EAAQlF,QACT,OAAO,EAEX,MAAMsD,GAAO,QAAc4B,EAAQlF,SACf,IAAhBsD,EAAKtJ,QAA+B,IAAfsJ,EAAKvJ,OAC1BZ,EAAMK,WAAWkX,UAAU,MAAO,KAAwB,YAE9DvX,EAAMM,SAAS,CAAEM,MAAOuJ,EAAKvJ,OAAS,IAAKC,OAAQsJ,EAAKtJ,QAAU,KAAM,EAE5E,GAAIkL,EAAQlF,QAAS,CACjByQ,IACAlQ,OAAO2C,iBAAiB,SAAUuN,GAClC,MAAME,EAAiB,IAAIC,gBAAe,IAAMH,MAEhD,OADAE,EAAeE,QAAQ3L,EAAQlF,SACxB,KACHO,OAAO4C,oBAAoB,SAAUsN,GACjCE,GAAkBzL,EAAQlF,SAC1B2Q,EAAeG,UAAU5L,EAAQlF,QACrC,CAER,IACD,GACP,CAoBI+Q,CAAiBR,GACjB,MAAMS,GAAoB,IAAAhH,cAAavF,IACnC4L,IAAmB,CAAEhS,EAAGoG,EAAU,GAAInG,EAAGmG,EAAU,GAAIlG,KAAMkG,EAAU,KAClE6L,GACDnX,EAAMM,SAAS,CAAEgL,aACrB,GACD,CAAC4L,EAAkBC,IAyEtB,OAxEA,IAAA7S,YAAU,KACN,GAAI8S,EAASvQ,QAAS,CAClB+D,EAAQ/D,SAAU,QAAU,CACxBkF,QAASqL,EAASvQ,QAClBV,UACAC,UACAH,kBACAwF,SAAUxG,EACVsB,oBACAuR,iBAAmBC,GAAiB/X,EAAMM,SAAS,CAAEyX,iBACrDC,eAAgB,CAAChP,EAAOiP,KACpB,MAAM,sBAAEC,EAAqB,YAAEC,GAAgBnY,EAAMK,WACrD8X,IAAcnP,EAAOiP,GACrBC,IAAwBD,EAAG,EAE/BG,UAAW,CAACpP,EAAOiP,KACf,MAAM,iBAAEf,EAAgB,OAAEmB,GAAWrY,EAAMK,WAC3CgY,IAASrP,EAAOiP,GAChBf,IAAmBe,EAAG,EAE1BK,aAAc,CAACtP,EAAOiP,KAClB,MAAM,oBAAEM,EAAmB,UAAEC,GAAcxY,EAAMK,WACjDmY,IAAYxP,EAAOiP,GACnBM,IAAsBN,EAAG,IAGjC,MAAM,EAAE/S,EAAC,EAAEC,EAAC,KAAEC,GAASwF,EAAQ/D,QAAQgF,cAMvC,OALA7L,EAAMM,SAAS,CACXsK,QAASA,EAAQ/D,QACjByE,UAAW,CAACpG,EAAGC,EAAGC,GAClB2G,QAASqL,EAASvQ,QAAQ4R,QAAQ,iBAE/B,KACH7N,EAAQ/D,SAAS6R,SAAS,CAElC,IACD,KACH,IAAApU,YAAU,KACNsG,EAAQ/D,SAAS8R,OAAO,CACpBrC,oBACAC,eACAC,cACAC,cACAC,mBACAC,kBACAE,oBACAC,YACAO,2BACAL,mBACA1Q,iBACAnE,sBACA8U,mBACAb,MACAyB,qBACF,GACH,CACCvB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAE,EACAC,EACAO,EACAL,EACA1Q,EACAnE,EACA8U,EACAb,EACAyB,KAEI,IAAAlW,KAAI,MAAO,CAAEW,UAAW,uBAAwBE,IAAK4U,EAAU5W,MAAOwV,GAAgBnU,SAAUA,GAC5G,CAEA,MAAM+W,GAAcrX,IAAM,CACtBY,oBAAqBZ,EAAEY,oBACvB0W,kBAAmBtX,EAAEsX,oBAEzB,SAASC,KACL,MAAM,oBAAE3W,EAAmB,kBAAE0W,GAAsBhZ,EAAS+Y,GAAY,KAExE,OADiBzW,GAAuB0W,GAIhC,IAAAlX,KAAI,MAAO,CAAEW,UAAW,8CAA+C9B,MAAO,CAC9EI,MAAOiY,EAAkBjY,MACzBC,OAAQgY,EAAkBhY,OAC1ByK,UAAW,aAAauN,EAAkB3T,QAAQ2T,EAAkB1T,UALjE,IAOf,CAEA,MAAM4T,GAAc,CAACC,EAASC,IAClBjQ,IACAA,EAAMhG,SAAWiW,EAAapS,SAGlCmS,IAAUhQ,EAAM,EAGlBkQ,GAAc3X,IAAM,CACtBY,oBAAqBZ,EAAEY,oBACvBkE,mBAAoB9E,EAAE8E,mBACtBiI,SAAU/M,EAAEwW,eAEhB,SAASoB,IAAK,YAAEC,EAAW,oBAAEC,EAAmB,cAAEC,EAAgB,KAAcC,KAAI,UAAEzC,EAAS,gBAAE0C,EAAe,iBAAEC,EAAgB,eAAEC,EAAc,YAAEC,EAAW,kBAAErD,EAAiB,aAAEsD,EAAY,iBAAEC,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,SAAElY,IACnP,MAAM7B,EAAQG,KACR,oBAAEgC,EAAmB,mBAAEkE,EAAkB,SAAEiI,GAAazO,EAASqZ,GAAY,KAC7Ec,EAAqB3T,IAAuB+S,GAAejX,GAC3D8X,GAAY,IAAAnT,QAAO,MACnBoT,GAAkB,IAAApT,UAClBqT,GAAkB,IAAArT,QAAO,IAAImB,KAC7BmS,GAAkB,IAAAtT,QAAO,IAAImB,KAE7BoS,GAAsB,IAAAvT,SAAO,GAC7BwT,GAAmB,IAAAxT,SAAO,GAC1ByT,EAAWvR,IAETqR,EAAoBxT,QACpBwT,EAAoBxT,SAAU,GAGlC8S,IAAc3Q,GACdhJ,EAAMK,WAAWma,wBACjBxa,EAAMM,SAAS,CAAEma,sBAAsB,IAAQ,EAS7CC,EAAUd,EAAgB5Q,GAAU4Q,EAAa5Q,QAASmF,EAuG1DwM,GAA0B,IAAd7D,GAAuBzO,MAAMC,QAAQwO,IAAcA,EAAUrM,SAAS,GACxF,OAAQ,IAAAzI,MAAK,MAAO,CAAEM,WAAW,OAAG,CAAC,mBAAoB,CAAEqY,YAAWrM,WAAUsM,UAAWxB,KAAiBmB,QAASP,OAAqB7L,EAAY4K,GAAYwB,EAASN,GAAYY,cAAe9B,IA/G/K/P,IACfX,MAAMC,QAAQwO,IAAcA,GAAWrM,SAAS,GAChDzB,EAAMS,iBAGV6M,IAAoBtN,EAAM,GA0GmMiR,GAAYS,QAAS3B,GAAY2B,EAAST,GAAYa,eAAgBd,OAAqB7L,EAAY0L,EAAkBkB,cAAef,EAvGlVhR,IACnB,MAAM,sBAAEwR,EAAqB,QAAEzO,GAAY/L,EAAMK,WAEjD,GADA6Z,EAAgBrT,QAAUkF,GAASc,yBAC9BxG,IACA+S,GACgB,IAAjBpQ,EAAMgS,QACNhS,EAAMhG,SAAWiX,EAAUpT,UAC1BqT,EAAgBrT,QACjB,OAEJmC,EAAMhG,QAAQiY,oBAAoBjS,EAAMkS,WACxCZ,EAAiBzT,SAAU,EAC3BwT,EAAoBxT,SAAU,EAC9B,MAAM,EAAE3B,EAAC,EAAEC,IAAM,QAAiB6D,EAAMmS,YAAajB,EAAgBrT,SACrE2T,IACAxa,EAAMM,SAAS,CACXuY,kBAAmB,CACfjY,MAAO,EACPC,OAAQ,EACRua,OAAQlW,EACRmW,OAAQlW,EACRD,IACAC,OAGRsU,IAAmBzQ,EAAM,EA8EiX8Q,EAAiBwB,cAAetB,EA5EvZhR,IACnB,MAAM,kBAAE6P,EAAiB,UAAEvN,EAAS,WAAE/H,EAAU,WAAEM,EAAU,iBAAE6R,EAAgB,mBAAEjC,EAAkB,mBAAEC,EAAkB,mBAAE6H,GAAwBvb,EAAMK,WACtJ,IAAK6Z,EAAgBrT,UAAYgS,EAC7B,OAEJwB,EAAoBxT,SAAU,EAC9B,MAAQ3B,EAAGsW,EAAQrW,EAAGsW,IAAW,QAAiBzS,EAAMmS,YAAajB,EAAgBrT,UAC/E,OAAEuU,EAAM,OAAEC,GAAWxC,EACrB6C,EAAqB,CACvBN,SACAC,SACAnW,EAAGsW,EAASJ,EAASI,EAASJ,EAC9BjW,EAAGsW,EAASJ,EAASI,EAASJ,EAC9Bza,MAAO+a,KAAKC,IAAIJ,EAASJ,GACzBva,OAAQ8a,KAAKC,IAAIH,EAASJ,IAExBQ,EAAsB1B,EAAgBtT,QACtCiV,EAAsB1B,EAAgBvT,QAC5CsT,EAAgBtT,QAAU,IAAIoB,KAAI,QAAe1E,EAAYmY,EAAoBpQ,EAAWgO,IAAkB,KAAcyC,SAAS,GAAM5X,KAAKb,GAASA,EAAK1B,MAC9JwY,EAAgBvT,QAAU,IAAIoB,IAC9B,MAAM+T,EAAkBT,GAAoBU,aAAc,EAE1D,IAAK,MAAMzG,KAAU2E,EAAgBtT,QAAS,CAC1C,MAAMqV,EAAcxG,EAAiB3H,IAAIyH,GACzC,GAAK0G,EAEL,IAAK,MAAM,OAAEC,KAAYD,EAAYvG,SAAU,CAC3C,MAAM/R,EAAOC,EAAWkK,IAAIoO,GACxBvY,IAASA,EAAKqY,YAAcD,IAC5B5B,EAAgBvT,QAAQ0C,IAAI4S,EAEpC,CACJ,CACA,KAAK,QAAaN,EAAqB1B,EAAgBtT,SAAU,CAE7D4M,EADgB3E,EAAoBvL,EAAY4W,EAAgBtT,SAAS,GAE7E,CACA,KAAK,QAAaiV,EAAqB1B,EAAgBvT,SAAU,CAE7D6M,EADgB5E,EAAoBjL,EAAYuW,EAAgBvT,SAEpE,CACA7G,EAAMM,SAAS,CACXuY,kBAAmB6C,EACnBvZ,qBAAqB,EACrBsY,sBAAsB,GACxB,EA+B6cX,EAAiBsC,YAAapC,EA7B5dhR,IACjB,GAAqB,IAAjBA,EAAMgS,SAAiBV,EAAiBzT,QACxC,OAEJmC,EAAMhG,QAAQqZ,wBAAwBrT,EAAMkS,WAC5C,MAAM,kBAAErC,GAAsB7Y,EAAMK,YAK/B8B,GAAuB0W,GAAqB7P,EAAMhG,SAAWiX,EAAUpT,SACxE0T,IAAUvR,GAEdhJ,EAAMM,SAAS,CACX6B,qBAAqB,EACrB0W,kBAAmB,KACnB4B,qBAAsBN,EAAgBtT,QAAQsD,KAAO,IAEzDuP,IAAiB1Q,IAKbqQ,GAAuBG,KACvBa,EAAoBxT,SAAU,GAElCyT,EAAiBzT,SAAU,CAAK,OAGgfsH,EAAWmO,eAAgBvC,EAAkBvX,IAAKyX,EAAWzZ,MAAOwV,GAAgBnU,SAAU,CAACA,GAAU,IAAAF,KAAImX,GAAe,CAAC,KACrpB,CAQA,SAASyD,IAAgB,GAAE3a,EAAE,MAAE5B,EAAK,SAAEwc,GAAW,EAAK,QAAEC,IACpD,MAAM,iBAAEC,EAAgB,sBAAEC,EAAqB,qBAAEC,EAAoB,WAAErZ,EAAU,QAAEgU,GAAYvX,EAAMK,WAC/FiD,EAAOC,EAAWwK,IAAInM,GACvB0B,GAILtD,EAAMM,SAAS,CAAEma,sBAAsB,IAClCnX,EAAKE,UAGDgZ,GAAalZ,EAAKE,UAAYoZ,KACnCD,EAAsB,CAAEnY,MAAO,CAAClB,GAAOmB,MAAO,KAC9CoY,uBAAsB,IAAMJ,GAAS5V,SAASiW,UAJ9CJ,EAAiB,CAAC9a,KALlB2V,IAAU,MAAO,KAAwB,SAAE3V,GAWnD,CAOA,SAASmb,IAAQ,QAAEN,EAAO,SAAEO,GAAW,EAAK,gBAAEC,EAAe,eAAEC,EAAc,OAAE1H,EAAM,aAAE2H,EAAY,kBAAEC,IACjG,MAAMpd,EAAQG,KACPmO,EAAU+O,IAAe,IAAAvV,WAAS,GACnCwV,GAAS,IAAAxW,UAqCf,OApCA,IAAAxC,YAAU,KACNgZ,EAAOzW,SAAU,QAAO,CACpB0W,cAAe,IAAMvd,EAAMK,WAC3Bmd,gBAAkB5b,IACd2a,GAAgB,CACZ3a,KACA5B,QACAyc,WACF,EAENgB,YAAa,KACTJ,GAAY,EAAK,EAErBK,WAAY,KACRL,GAAY,EAAM,GAExB,GACH,KACH,IAAA/Y,YAAU,KACN,GAAI0Y,EACAM,EAAOzW,SAAS6R,eAEf,GAAI+D,EAAQ5V,QASb,OARAyW,EAAOzW,SAAS8R,OAAO,CACnBsE,kBACAC,iBACAnR,QAAS0Q,EAAQ5V,QACjBsW,eACA3H,SACA4H,sBAEG,KACHE,EAAOzW,SAAS6R,SAAS,CAEjC,GACD,CAACuE,EAAiBC,EAAgBF,EAAUG,EAAcV,EAASjH,IAC/DlH,CACX,CASA,SAASqP,KACL,MAAM3d,EAAQG,IAsCd,OArC0B,IAAA0Q,cAAatM,IACnC,MAAM,WAAEqZ,EAAU,WAAElR,EAAU,SAAED,EAAQ,eAAEoR,EAAc,QAAEtG,EAAO,oBAAEuG,EAAmB,WAAEva,EAAU,WAAE2C,GAAelG,EAAMK,WACnH0d,EAAc,IAAItQ,IAClBuQ,EAZe,CAACH,GAAoBrN,GAAMA,EAAEhN,WAAagN,EAAEmK,WAAckD,QAAyC,IAAhBrN,EAAEmK,WAYvFsD,CAAqBJ,GAKlCK,EAAQxR,EAAaD,EAAS,GAAK,EACnC0R,EAAQzR,EAAaD,EAAS,GAAK,EACnC2R,EAAQ7Z,EAAO8Z,UAAUnZ,EAAIgZ,EAAQ3Z,EAAO+Z,OAC5CC,EAAQha,EAAO8Z,UAAUlZ,EAAIgZ,EAAQ5Z,EAAO+Z,OAClD,IAAK,MAAO,CAAEhb,KAASC,EAAY,CAC/B,IAAKya,EAAW1a,GACZ,SAEJ,IAAIkb,EAAe,CACftZ,EAAG5B,EAAKI,UAAUkR,iBAAiB1P,EAAIkZ,EACvCjZ,EAAG7B,EAAKI,UAAUkR,iBAAiBzP,EAAIoZ,GAEvC7R,IACA8R,GAAe,QAAaA,EAAc/R,IAE9C,MAAM,SAAE9L,EAAQ,iBAAEiU,IAAqB,QAAsB,CACzDY,OAAQlS,EAAK1B,GACb4c,eACAjb,aACAqa,aACA1X,aACAqR,YAEJjU,EAAK3C,SAAWA,EAChB2C,EAAKI,UAAUkR,iBAAmBA,EAClCmJ,EAAYlQ,IAAIvK,EAAK1B,GAAI0B,EAC7B,CACAwa,EAAoBC,EAAY,GACjC,GAEP,CAEA,MAAMU,IAAgB,IAAAhf,eAAc,MAC9BE,GAAW8e,GAAc9e,SAC/B8e,GAAcC,SA6Bd,MAAMC,GAAY,KACC,IAAA1e,YAAWwe,IAIxBG,GAAcrd,IAAM,CACtBsd,eAAgBtd,EAAEsd,eAClBvY,eAAgB/E,EAAE+E,eAClB5E,KAAMH,EAAEG,OAuKZ,MAAMod,IAAS,IAAAC,MAAKnP,GArJpB,UAAyB,KAAEhC,EAAO,SAAQ,SAAEjN,EAAW,KAASqe,IAAG,kBAAEC,EAAiB,cAAEC,GAAgB,EAAI,mBAAEC,GAAqB,EAAI,iBAAEC,GAAmB,EAAI,GAAExd,EAAE,UAAEyd,EAAS,SAAExd,EAAQ,UAAES,EAAS,YAAEgd,EAAW,aAAEC,KAAiBhd,GAAQC,GACxO,MAAMqT,EAAWjU,GAAM,KACjB4d,EAAoB,WAAT5R,EACX5N,EAAQG,IACRqV,EAASmJ,MACT,eAAEE,EAAc,eAAEvY,EAAc,KAAE5E,GAAS7B,EAAS+e,GAAY,MAChE,eAAEa,EAAc,aAAEC,EAAY,gBAAEC,EAAe,oBAAEC,EAAmB,oBAAEC,EAAmB,yBAAEC,EAAwB,MAAEC,GAAWlgB,EAtB/G,EAAC2V,EAAQK,EAAUjI,IAAUoS,IACpD,MAAQC,2BAA4BC,EAAW,eAAEC,EAAc,WAAEC,GAAeJ,GAC1E,WAAEK,EAAU,SAAEC,EAAQ,QAAEC,GAAYH,EACpCV,EAAeY,GAAU9K,SAAWA,GAAU8K,GAAU1e,KAAOiU,GAAYyK,GAAU1S,OAASA,EACpG,MAAO,CACH6R,eAAgBY,GAAY7K,SAAWA,GAAU6K,GAAYze,KAAOiU,GAAYwK,GAAYzS,OAASA,EACrG8R,eACAC,gBAAiBO,GAAa1K,SAAWA,GAAU0K,GAAate,KAAOiU,GAAYqK,GAAatS,OAASA,EACzGgS,oBAAqBO,IAAmB,KAAeK,OACjDH,GAAYzS,OAASA,EACrB4H,IAAW6K,GAAY7K,QAAUK,IAAawK,GAAYze,GAChEie,sBAAuBQ,EACvBP,2BAA4BI,EAC5BH,MAAOL,GAAgBa,EAC1B,EAQ8IE,CAAmBjL,EAAQK,EAAUjI,GAAO,KACtL4H,GACDxV,EAAMK,WAAWkX,UAAU,MAAO,KAAwB,YAE9D,MAAMmJ,EAAqBnc,IACvB,MAAM,mBAAEgX,EAAoB8D,UAAWsB,EAAe,gBAAExP,GAAoBnR,EAAMK,WAC5EugB,EAAa,IACZrF,KACAhX,GAEP,GAAI4M,EAAiB,CACjB,MAAM,MAAE1M,EAAK,SAAEe,GAAaxF,EAAMK,WAClCmF,GAAS,QAAQob,EAAYnc,GACjC,CACAkc,IAAkBC,GAClBvB,IAAYuB,EAAW,EAErB7F,EAAiB/R,IACnB,IAAKwM,EACD,OAEJ,MAAMqL,GAAmB,QAAa7X,EAAMmS,aAC5C,GAAIgE,IACE0B,GAAqC,IAAjB7X,EAAMgS,SAAkB6F,GAAmB,CACjE,MAAMC,EAAe9gB,EAAMK,WAC3B,KAAS0a,cAAc/R,EAAMmS,YAAa,CACtC4F,iBAAkBD,EAAaC,iBAC/BZ,eAAgBW,EAAaX,eAC7Ba,iBAAkBF,EAAaE,iBAC/BjV,QAAS+U,EAAa/U,QACtBxI,WAAYud,EAAavd,WACzB6S,IAAK0K,EAAa1K,IAClBoJ,WACA3J,WACAL,SACAyL,OAAQH,EAAapf,KACrBwf,MAAOJ,EAAaI,MACpBC,iBAAkBL,EAAaK,iBAC/BC,eAAgBN,EAAaM,eAC7BC,aAAcP,EAAaO,aAC3BC,iBAAkBR,EAAaQ,iBAC/BjC,UAAWqB,EACXzB,kBAAmBA,GAAqB6B,EAAa7B,kBACrDsC,aAAc,IAAMvhB,EAAMK,WAAWiL,UACrCkW,cAAe,IAAMxhB,EAAMK,WAAW+f,WAAWC,WACjDoB,aAAcX,EAAaW,cAEnC,CACIZ,EACAvB,IAActW,GAGduW,IAAevW,EACnB,EAuCJ,OAAQ,IAAArH,KAAI,MAAO,CAAE,gBAAiBkU,EAAU,cAAeL,EAAQ,iBAAkB7U,EAAU,UAAW,GAAGe,KAAQ8T,KAAUK,KAAYjI,IAAQtL,WAAW,OAAG,CAC7J,qBACA,sBAAsB3B,IACtB,SACA2F,EACAhE,EACA,CACIof,QAASlC,EACTxc,OAAQwc,EACRmC,YAAazC,EACb0C,iBAAkBzC,EAClB0C,eAAgBzC,EAChB0C,gBAAiBnC,EACjBoC,eAAgBtC,EAChBuC,aAActC,EACdK,QAKAkC,oBAAqB/C,KACfW,GAAuBD,KACxBC,GAAuBC,EAA2BV,EAAmBD,MAE9EG,YAAavE,EAAewE,aAAcxE,EAAeR,QAASsE,EA7DzD7V,IACb,MAAM,oBAAEkZ,EAAmB,kBAAEC,EAAiB,2BAAElC,EAA0B,eAAEE,EAAgBlB,kBAAmBmD,EAAsB,IAAEhM,EAAK1U,KAAMuf,EAAM,WAAE1d,EAAY6c,WAAYiC,GAAqBriB,EAAMK,WAC7M,IAAKmV,IAAYyK,IAA+Bd,EAC5C,OAEJ,IAAKc,EAGD,OAFAiC,IAAsBlZ,EAAMmS,YAAa,CAAE3F,SAAQK,WAAUyM,WAAY1U,SACzE5N,EAAMM,SAAS,CAAE2f,2BAA4B,CAAEzK,SAAQ5H,OAAMhM,GAAIiU,KAGrE,MAAM0M,GAAM,QAAkBvZ,EAAMhG,QAC9Bwf,EAA2BvD,GAAqBmD,GAChD,WAAEhC,EAAU,QAAEG,GAAY,KAASA,QAAQvX,EAAMmS,YAAa,CAChEsH,OAAQ,CACJjN,SACA5T,GAAIiU,EACJjI,QAEJuS,iBACAuC,WAAYzC,EAA2BzK,OACvCmN,aAAc1C,EAA2Bre,IAAM,KAC/CghB,SAAU3C,EAA2BrS,KACrCqR,kBAAmBuD,EACnBvB,SACAsB,MACAnM,MACA7S,eAEAgd,GAAWH,GACXM,EAAkBN,GAEtB,MAAMyC,EAAkBC,gBAAgBT,UACjCQ,EAAgBE,WACvBF,EAAgBG,WAAaH,EAAgBvC,SAAWuC,EAAgBvC,SAAS3f,SAAW,KAC5FwhB,IAAoBnZ,EAAO6Z,GAC3B7iB,EAAMM,SAAS,CAAE2f,2BAA4B,MAAO,OA0B6C9R,EAAW3L,IAAKA,KAAQD,EAAMV,SAAUA,GACjJ,KA4CA,MAAMohB,GAAgB,CAClBC,QAAS,CAAEhe,EAAG,EAAGC,GAAI,GACrBge,UAAW,CAAEje,EAAG,EAAGC,EAAG,GACtBie,UAAW,CAAEle,GAAI,EAAGC,EAAG,GACvBke,WAAY,CAAEne,EAAG,EAAGC,EAAG,IAErBme,GAAmB,CACrBC,MAvBJ,UAAmB,KAAEnO,EAAI,cAAE8J,EAAa,eAAEsE,EAAiB,KAASC,SAChE,OAAQ,IAAAzhB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,CAACuT,GAAMsO,OAAO,IAAA/hB,KAAImd,GAAQ,CAAElR,KAAM,SAAUjN,SAAU6iB,EAAgBtE,cAAeA,MAC5H,EAsBIyE,QApBJ,UAAqB,KAAEvO,EAAI,cAAE8J,EAAa,eAAE0E,EAAiB,KAAS5E,IAAG,eAAEwE,EAAiB,KAASC,SACjG,OAAQ,IAAAzhB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAImd,GAAQ,CAAElR,KAAM,SAAUjN,SAAUijB,EAAgB1E,cAAeA,IAAkB9J,GAAMsO,OAAO,IAAA/hB,KAAImd,GAAQ,CAAElR,KAAM,SAAUjN,SAAU6iB,EAAgBtE,cAAeA,MACrN,EAmBI2E,OAbJ,UAAoB,KAAEzO,EAAI,cAAE8J,EAAa,eAAE0E,EAAiB,KAAS5E,MACjE,OAAQ,IAAAhd,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAImd,GAAQ,CAAElR,KAAM,SAAUjN,SAAUijB,EAAgB1E,cAAeA,IAAkB9J,GAAMsO,QACvI,EAYII,MAlBJ,WACI,OAAO,IACX,GA+BA,MAAMC,GAAcxiB,IAChB,MAAM,MAAEX,EAAK,OAAEC,EAAM,EAAEqE,EAAC,EAAEC,IAAM,QAAuB5D,EAAEgC,WAAY,CACjEgF,OAASjF,KAAWA,EAAKE,WAE7B,MAAO,CACH5C,OAAO,QAAUA,GAASA,EAAQ,KAClCC,QAAQ,QAAUA,GAAUA,EAAS,KACrCsB,oBAAqBZ,EAAEY,oBACvB6hB,gBAAiB,aAAaziB,EAAE+J,UAAU,QAAQ/J,EAAE+J,UAAU,eAAe/J,EAAE+J,UAAU,iBAAiBpG,OAAOC,OACpH,EAEL,SAAS8e,IAAe,uBAAEC,EAAsB,eAAE5d,EAAc,oBAAEvE,IAC9D,MAAM/B,EAAQG,KACR,MAAES,EAAK,OAAEC,EAAM,gBAAEmjB,EAAe,oBAAE7hB,GAAwBtC,EAASkkB,GAAY,KAC/EI,EAAoBxG,KACpBlB,GAAU,IAAA3V,QAAO,MAWvB,IAVA,IAAAxC,YAAU,KACDvC,GACD0a,EAAQ5V,SAASud,MAAM,CACnBC,eAAe,GAEvB,GACD,CAACtiB,IACJgb,GAAQ,CACJN,YAEAta,IAAwBvB,IAAUC,EAClC,OAAO,KAEX,MAAMga,EAAgBqJ,EACflb,IACC,MAAM5F,EAAgBpD,EAAMK,WAAWmE,MAAM+D,QAAQiI,GAAMA,EAAEhN,WAC7D0gB,EAAuBlb,EAAO5F,EAAc,OAE9C+K,EAUN,OAAQ,IAAAxM,KAAI,MAAO,CAAEW,WAAW,OAAG,CAAC,6BAA8B,wBAAyBgE,IAAkB9F,MAAO,CAC5G8K,UAAW0Y,GACZniB,UAAU,IAAAF,KAAI,MAAO,CAAEa,IAAKia,EAASna,UAAW,kCAAmCuY,cAAeA,EAAeyJ,SAAUviB,OAAsBoM,GAAa,EAAGoW,UAAWxiB,OAAsBoM,EAXtLnF,IACXwb,OAAOC,UAAUC,eAAeC,KAAK1B,GAAeja,EAAMa,OAC1Db,EAAMS,iBACN0a,EAAkB,CACd9F,UAAW4E,GAAcja,EAAMa,KAC/ByU,OAAQtV,EAAMG,SAAW,EAAI,IAErC,EAI4N3I,MAAO,CAC3NI,QACAC,aAEhB,CAEA,MAAM+jB,GAAwB,oBAAXxd,OAAyBA,YAAS+G,EAC/C0W,GAActjB,IACT,CAAEkZ,qBAAsBlZ,EAAEkZ,qBAAsBtY,oBAAqBZ,EAAEY,sBAElF,SAAS2iB,IAAsB,SAAEjjB,EAAQ,YAAE8X,EAAW,iBAAEE,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAEzD,EAAiB,aAAEsD,EAAY,kBAAErT,EAAiB,cAAEwe,EAAa,iBAAEC,EAAgB,gBAAExL,EAAe,cAAEF,EAAa,iBAAEG,EAAgB,eAAEC,EAAc,sBAAEuL,EAAqB,qBAAEC,EAAoB,sBAAEnO,EAAqB,mBAAE1Q,EAAkB,aAAEkQ,EAAY,YAAEC,EAAaC,YAAa0O,EAAY,iBAAEzO,EAAgB,gBAAEC,EAAe,kBAAEE,EAAmBC,UAAWsO,EAAU,gBAAEngB,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,iBAAE4Q,EAAgB,uBAAEkN,EAAsB,iBAAEjN,EAAgB,eAAE3Q,EAAc,oBAAEvE,EAAmB,iBAAEmV,EAAgB,qBAAEC,IACloB,MAAM,qBAAEsD,EAAoB,oBAAEtY,GAAwBtC,EAASglB,IACzDxL,EAAsB7R,EAAYwd,EAAkB,CAAEhiB,OAAQ4hB,KAC9DS,EAA0B7d,EAAY0d,EAAsB,CAAEliB,OAAQ4hB,KACtE9N,EAAYuO,GAA2BD,EACvC3O,EAAc4O,GAA2BF,EACzCG,EAAmB9L,IAAiC,IAAd1C,EACtCsC,EAAcC,GAAuBlX,GAAuBmjB,EAElE,OA7uBJ,UAA6B,cAAEP,EAAa,sBAAEE,IAC1C,MAAMjlB,EAAQG,KACR,eAAEiT,GAAmB7B,KACrBgU,EAAmB/d,EAAYud,EAAejP,IAC9C0P,EAA2Bhe,EAAYyd,EAAuB,CAAEjiB,OAAQ+S,MAC9E,IAAAzR,YAAU,KACN,GAAIihB,EAAkB,CAClB,MAAM,MAAE9gB,EAAK,MAAED,GAAUxE,EAAMK,WAC/B+S,EAAe,CAAE5O,MAAOA,EAAM+D,OAAO/E,IAAWiB,MAAOA,EAAM8D,OAAO/E,MACpExD,EAAMM,SAAS,CAAEma,sBAAsB,GAC3C,IACD,CAAC8K,KACJ,IAAAjhB,YAAU,KACNtE,EAAMM,SAAS,CAAEsc,qBAAsB4I,GAA2B,GACnE,CAACA,GACR,CA6tBIC,CAAoB,CAAEV,gBAAeE,2BAC7B,IAAAtjB,KAAI0U,GAAU,CAAEC,kBAAmBA,EAAmBjQ,mBAAoBA,EAAoBkQ,aAAcA,EAAcC,YAAaA,EAAaC,YAAaA,EAAaC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBE,kBAAmBA,EAAmBC,WAAYuC,GAAuBvC,EAAW7R,gBAAiBA,EAAiBgB,gBAAiBA,EAAiBE,QAASA,EAASC,QAASA,EAAS2Q,sBAAuBA,EAAuBC,iBAAkBA,EAAkBC,iBAAkBA,EAAkB3Q,eAAgBA,EAAgB4Q,iBAAkBA,EAAkBC,qBAAsBA,EAAsB5Q,kBAAmBA,EAAmB1E,UAAU,IAAAG,MAAKmX,GAAM,CAAEM,iBAAkBA,EAAkBC,eAAgBA,EAAgBC,YAAaA,EAAaE,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBzD,kBAAmBA,EAAmBsD,aAAcA,EAAc9C,UAAWA,EAAWsC,cAAeA,EAAaE,cAAeA,EAAeD,oBAAqBA,EAAqBG,gBAAiB8L,EAAkBzjB,SAAU,CAACA,EAAU4Y,IAAyB,IAAA9Y,KAAIsiB,GAAgB,CAAEC,uBAAwBA,EAAwB5d,eAAgBA,EAAgBvE,oBAAqBA,QAC/yC,CACA+iB,GAAsB5hB,YAAc,eACpC,MAAMwiB,IAAe,IAAA3G,MAAK+F,IAc1B,SAASa,GAAkBC,GAEvB,OADgB/lB,GAAS,IAAAgR,aAbV,CAAC+U,GAAuBrkB,GAChCqkB,GACD,QAAerkB,EAAEgC,WAAY,CAAE2B,EAAG,EAAGC,EAAG,EAAGvE,MAAOW,EAAEX,MAAOC,OAAQU,EAAEV,QAAUU,EAAE+J,WAAW,GAAMnH,KAAKb,GAASA,EAAK1B,KACrHyG,MAAMoN,KAAKlU,EAAEgC,WAAW6E,QAUOyd,CAAWD,GAAoB,CAACA,IAAqB,IAE9F,CAEA,MAAME,GAAcvkB,GAAMA,EAAEwkB,oBAiF5B,SAASC,IAAY,GAAEpkB,EAAE,QAAE2Y,EAAO,aAAE0L,EAAY,YAAEC,EAAW,aAAEC,EAAY,cAAEtL,EAAa,cAAEuL,EAAa,eAAEvI,EAAc,mBAAExX,EAAkB,iBAAEggB,EAAgB,eAAEC,EAAc,eAAE9O,EAAc,gBAAEyF,EAAe,eAAE3W,EAAc,oBAAEvE,EAAmB,KAAEL,EAAI,UAAE6kB,EAAS,kBAAEnJ,EAAiB,QAAE7F,IACvR,MAAM,KAAEjU,EAAI,UAAEI,EAAS,SAAE8iB,GAAa3mB,GAAU0B,IAC5C,MAAM+B,EAAO/B,EAAEgC,WAAWwK,IAAInM,GACxB4kB,EAAWjlB,EAAEklB,aAAalc,IAAI3I,GACpC,MAAO,CACH0B,OACAI,UAAWJ,EAAKI,UAChB8iB,WACH,GACF,KACH,IAAIE,EAAWpjB,EAAKsK,MAAQ,UACxB+Y,EAAgBJ,IAAYG,IAAapD,GAAiBoD,QACxCvY,IAAlBwY,IACApP,IAAU,MAAO,KAAwB,SAAEmP,IAC3CA,EAAW,UACXC,EAAgBrD,GAAiBK,SAErC,MAAMiD,KAAiBtjB,EAAKqX,WAAckD,QAA4C,IAAnBva,EAAKqX,WAClEwC,KAAkB7Z,EAAK2Y,YAAe5V,QAAiD,IAApB/C,EAAK2Y,YACxEiD,KAAmB5b,EAAKqe,aAAgB0E,QAAgD,IAArB/iB,EAAKqe,aACxEkF,KAAiBvjB,EAAKwjB,WAAcR,QAA4C,IAAnBhjB,EAAKwjB,WAClE9mB,EAAQG,IACR4mB,GAAgB,QAAkBzjB,GAClCmZ,EAtEV,UAAyB,KAAEnZ,EAAI,SAAEojB,EAAQ,cAAEK,EAAa,eAAEvP,IACtD,MAAMxX,EAAQG,IACRsc,GAAU,IAAA3V,QAAO,MACjBkgB,GAAe,IAAAlgB,QAAO,MACtBmgB,GAAqB,IAAAngB,QAAOxD,EAAKkgB,gBACjC0D,GAAqB,IAAApgB,QAAOxD,EAAKsgB,gBACjCuD,GAAW,IAAArgB,QAAO4f,GAClBU,EAAgBL,KAAmBzjB,EAAKI,UAAU2jB,aAqCxD,OApCA,IAAA/iB,YAAU,MACFmY,EAAQ5V,SAAYvD,EAAKgkB,QAAYF,GAAiBJ,EAAangB,UAAY4V,EAAQ5V,UACnFmgB,EAAangB,SACb2Q,GAAgBG,UAAUqP,EAAangB,SAE3C2Q,GAAgBE,QAAQ+E,EAAQ5V,SAChCmgB,EAAangB,QAAU4V,EAAQ5V,QACnC,GACD,CAACugB,EAAe9jB,EAAKgkB,UACxB,IAAAhjB,YAAU,IACC,KACC0iB,EAAangB,UACb2Q,GAAgBG,UAAUqP,EAAangB,SACvCmgB,EAAangB,QAAU,KAC3B,GAEL,KACH,IAAAvC,YAAU,KACN,GAAImY,EAAQ5V,QAAS,CAKjB,MAAM0gB,EAAcJ,EAAStgB,UAAY6f,EACnCc,EAAmBP,EAAmBpgB,UAAYvD,EAAKkgB,eACvDiE,EAAmBP,EAAmBrgB,UAAYvD,EAAKsgB,gBACzD2D,GAAeC,GAAoBC,KACnCN,EAAStgB,QAAU6f,EACnBO,EAAmBpgB,QAAUvD,EAAKkgB,eAClC0D,EAAmBrgB,QAAUvD,EAAKsgB,eAClC5jB,EACKK,WACA0lB,oBAAoB,IAAItY,IAAI,CAAC,CAACnK,EAAK1B,GAAI,CAAEA,GAAI0B,EAAK1B,GAAI8lB,YAAajL,EAAQ5V,QAAS8gB,OAAO,OAExG,IACD,CAACrkB,EAAK1B,GAAI8kB,EAAUpjB,EAAKkgB,eAAgBlgB,EAAKsgB,iBAC1CnH,CACX,CAyBoBmL,CAAgB,CAAEtkB,OAAMojB,WAAUK,gBAAevP,mBAC3DlJ,EAAWyO,GAAQ,CACrBN,UACAO,SAAU1Z,EAAKgkB,SAAWV,EAC1B3J,kBACAC,eAAgB5Z,EAAKukB,WACrBrS,OAAQ5T,EACRub,eACAC,sBAEE+G,EAAoBxG,KAC1B,GAAIra,EAAKgkB,OACL,OAAO,KAEX,MAAMQ,GAAiB,QAAkBxkB,GACnCykB,EA3NV,SAAsCzkB,GAClC,YAAoC6K,IAAhC7K,EAAKI,UAAU2jB,aACR,CACHzmB,MAAO0C,EAAK1C,OAAS0C,EAAK0kB,cAAgB1kB,EAAK9C,OAAOI,MACtDC,OAAQyC,EAAKzC,QAAUyC,EAAK2kB,eAAiB3kB,EAAK9C,OAAOK,QAG1D,CACHD,MAAO0C,EAAK1C,OAAS0C,EAAK9C,OAAOI,MACjCC,OAAQyC,EAAKzC,QAAUyC,EAAK9C,OAAOK,OAE3C,CAgN6BqnB,CAA6B5kB,GAChD6kB,EAAmBhL,GAAgByJ,GAAerM,GAAW0L,GAAgBC,GAAeC,EAC5FiC,EAAsBnC,EACrBjd,GAAUid,EAAajd,EAAO,IAAKtF,EAAUC,gBAC9CwK,EACAka,EAAqBnC,EACpBld,GAAUkd,EAAYld,EAAO,IAAKtF,EAAUC,gBAC7CwK,EACAma,EAAsBnC,EACrBnd,GAAUmd,EAAand,EAAO,IAAKtF,EAAUC,gBAC9CwK,EACAoa,EAAuB1N,EACtB7R,GAAU6R,EAAc7R,EAAO,IAAKtF,EAAUC,gBAC/CwK,EACAqa,EAAuBpC,EACtBpd,GAAUod,EAAcpd,EAAO,IAAKtF,EAAUC,gBAC/CwK,EA6CN,OAAQ,IAAAxM,KAAI,MAAO,CAAEW,WAAW,OAAG,CAC3B,mBACA,oBAAoBokB,IACpB,CAEI,CAACpgB,GAAiBsgB,GAEtBtjB,EAAKhB,UACL,CACIkB,SAAUF,EAAKE,SACfyY,WAAYkB,EACZsL,OAAQjC,EACR7L,UAAWiM,EACXtY,cAEJ9L,IAAKia,EAASjc,MAAO,CACrBkoB,OAAQhlB,EAAUilB,EAClBrd,UAAW,aAAa5H,EAAUkR,iBAAiB1P,OAAOxB,EAAUkR,iBAAiBzP,OACrF1C,cAAe0lB,EAAmB,MAAQ,OAC1CS,WAAY7B,EAAgB,UAAY,YACrCzjB,EAAK9C,SACLunB,GACJ,UAAWnmB,EAAI,cAAe,YAAYA,IAAMqkB,aAAcmC,EAAqBlC,YAAamC,EAAoBlC,aAAcmC,EAAqBzN,cAAe0N,EAAsBhO,QAlEtKvR,IACzB,MAAM,kBAAE6f,EAAiB,kBAAEC,GAAsB9oB,EAAMK,WACnD8c,KAAkB0L,IAAsBjC,GAAekC,EAAoB,IAK3EvM,GAAgB,CACZ3a,KACA5B,QACAyc,YAGJlC,GACAA,EAAQvR,EAAO,IAAKtF,EAAUC,UAClC,EAmD6NyiB,cAAeoC,EAAsBjE,UAAWsC,EAjD9P7d,IACf,KAAI,QAAeA,EAAMmS,eAAgBpZ,EAGzC,GAAI,KAAqB0I,SAASzB,EAAMa,MAAQsT,EAAc,CAC1D,MAAMX,EAAyB,WAAdxT,EAAMa,IACvB0S,GAAgB,CACZ3a,KACA5B,QACAwc,WACAC,WAER,MACSmK,GAAetjB,EAAKE,UAAYghB,OAAOC,UAAUC,eAAeC,KAAK1B,GAAeja,EAAMa,OAE/Fb,EAAMS,iBACNzJ,EAAMM,SAAS,CACXkB,gBAAiB,uBAAuBwH,EAAMa,IACzCpB,QAAQ,QAAS,IACjBsgB,qCAAqCrlB,EAAUkR,iBAAiB1P,WAAWxB,EAAUkR,iBAAiBzP,MAE/Ggf,EAAkB,CACd9F,UAAW4E,GAAcja,EAAMa,KAC/ByU,OAAQtV,EAAMG,SAAW,EAAI,IAErC,OAwBuSgF,EAAWmW,SAAUuC,EAAc,OAAI1Y,EAAW6a,KAAMnC,EAAc,cAAW1Y,EAAW,mBAAoBpM,OAAsBoM,EAAY,GAAG/M,KAAsBM,IAAQ,aAAc4B,EAAK2lB,UAAWpnB,UAAU,IAAAF,KAAIhC,GAAU,CAAE0R,MAAOzP,EAAIC,UAAU,IAAAF,KAAIglB,EAAe,CAAE/kB,GAAIA,EAAIwT,KAAM9R,EAAK8R,KAAMxH,KAAM8Y,EAAUwC,kBAAmBxlB,EAAUkR,iBAAiB1P,EAAGikB,kBAAmBzlB,EAAUkR,iBAAiBzP,EAAG3B,SAAUF,EAAKE,WAAY,EAAOyY,WAAYkB,EAAcxC,UAAWiM,EAAawC,UAAW9lB,EAAK8lB,YAAa,EAAMlK,cAAeA,EAAesE,eAAgBlgB,EAAKkgB,eAAgBI,eAAgBtgB,EAAKsgB,eAAgBtV,SAAUA,EAAUuZ,WAAYvkB,EAAKukB,WAAYa,OAAQhlB,EAAUilB,EAAG3W,SAAU1O,EAAK0O,YAAa8V,OAChhC,CAEA,MAAMuB,GAAc9nB,IAAM,CACtBsc,eAAgBtc,EAAEsc,eAClBwI,iBAAkB9kB,EAAE8kB,iBACpBC,eAAgB/kB,EAAE+kB,eAClBjgB,mBAAoB9E,EAAE8E,mBACtBkR,QAAShW,EAAEgW,UAEf,SAAS+R,GAAsB7iB,GAC3B,MAAM,eAAEoX,EAAc,iBAAEwI,EAAgB,eAAEC,EAAc,mBAAEjgB,EAAkB,QAAEkR,GAAY1X,EAASwpB,GAAY,KACzGE,EAAU5D,GAAkBlf,EAAM+iB,2BAClChS,EAtNV,WACI,MAAMuO,EAAsBlmB,EAASimB,KAC9BtO,IAAkB,IAAA1P,WAAS,IACA,oBAAnB2P,eACA,KAEJ,IAAIA,gBAAgBnI,IACvB,MAAMma,EAAU,IAAIhc,IACpB6B,EAAQ3K,SAAS+kB,IACb,MAAM9nB,EAAK8nB,EAAM1mB,OAAO2mB,aAAa,WACrCF,EAAQ5b,IAAIjM,EAAI,CACZA,KACA8lB,YAAagC,EAAM1mB,OACnB2kB,OAAO,GACT,IAEN5B,EAAoB0D,EAAQ,MAQpC,OALA,IAAAnlB,YAAU,IACC,KACHkT,GAAgBoS,YAAY,GAEjC,CAACpS,IACGA,CACX,CA6L2BqS,GACvB,OAAQ,IAAAloB,KAAI,MAAO,CAAEW,UAAW,oBAAqB9B,MAAOwV,GAAgBnU,SAAU0nB,EAAQplB,KAAKqR,IA2B3F,IAAA7T,KAAIqkB,GAAa,CAAEpkB,GAAI4T,EAAQ+Q,UAAW9f,EAAM8f,UAAW3I,WAAYnX,EAAMmX,WAAYrD,QAAS9T,EAAMqjB,YAAa7D,aAAcxf,EAAMsjB,iBAAkB7D,YAAazf,EAAMujB,gBAAiB7D,aAAc1f,EAAMwjB,iBAAkBpP,cAAepU,EAAMyjB,kBAAmB9D,cAAe3f,EAAM0jB,kBAAmBlN,gBAAiBxW,EAAMwW,gBAAiB3W,eAAgBG,EAAMH,eAAgB5E,KAAM+E,EAAM/E,KAAMK,oBAAqB0E,EAAM1E,oBAAqByV,eAAgBA,EAAgBqG,eAAgBA,EAAgBwI,iBAAkBA,EAAkBC,eAAgBA,EAAgBjgB,mBAAoBA,EAAoB+W,kBAAmB3W,EAAM2W,kBAAmB7F,QAASA,GAAW/B,MAE7rB,CACA8T,GAAsBpmB,YAAc,eACpC,MAAMknB,IAAe,IAAArL,MAAKuK,IAqC1B,MAaMe,GAAgB,CAClB,CAAC,KAAWC,OAdI,EAAGC,QAAQ,OAAQC,cAAc,MACzC,IAAA7oB,KAAI,WAAY,CAAEnB,MAAO,CACzBiqB,OAAQF,EACRC,eACDE,cAAe,QAASC,eAAgB,QAASC,KAAM,OAAQC,OAAQ,mBAW9E,CAAC,KAAWC,aATU,EAAGP,QAAQ,OAAQC,cAAc,MAC/C,IAAA7oB,KAAI,WAAY,CAAEnB,MAAO,CACzBiqB,OAAQF,EACRK,KAAML,EACNC,eACDE,cAAe,QAASC,eAAgB,QAASE,OAAQ,0BAmBpE,MAAME,GAAS,EAAGnpB,KAAIgM,OAAM2c,QAAO3pB,QAAQ,KAAMC,SAAS,KAAMmqB,cAAc,cAAeR,cAAaS,SAAS,yBAC/G,MAAMC,EAdV,SAAyBtd,GACrB,MAAM5N,EAAQG,IASd,OARe,IAAAC,UAAQ,IACEokB,OAAOC,UAAUC,eAAeC,KAAK0F,GAAezc,GAKlEyc,GAAczc,IAHjB5N,EAAMK,WAAWkX,UAAU,MAAO,KAAwB,SAAE3J,IACrD,OAGZ,CAACA,GAER,CAGmBud,CAAgBvd,GAC/B,OAAKsd,GAGG,IAAAvpB,KAAI,SAAU,CAAEW,UAAW,wBAAyBV,GAAIA,EAAIwpB,YAAa,GAAGxqB,IAASyqB,aAAc,GAAGxqB,IAAUyqB,QAAS,gBAAiBN,YAAaA,EAAaC,OAAQA,EAAQM,KAAM,IAAKC,KAAM,IAAK3pB,UAAU,IAAAF,KAAIupB,EAAQ,CAAEX,MAAOA,EAAOC,YAAaA,MAF1P,IAE4Q,EAOrRiB,GAAoB,EAAGC,eAAchqB,WACvC,MAAM+C,EAAQ5E,GAAU0B,GAAMA,EAAEkD,QAC1B8W,EAAqB1b,GAAU0B,GAAMA,EAAEga,qBACvCoQ,GAAU,IAAAvrB,UAAQ,KACJ,QAAgBqE,EAAO,CACnC7C,GAAIF,EACJgqB,eACAE,mBAAoBrQ,GAAoBsQ,YACxCC,iBAAkBvQ,GAAoBwQ,aAG3C,CAACtnB,EAAO8W,EAAoB7Z,EAAMgqB,IACrC,OAAKC,EAAQzhB,QAGL,IAAAvI,KAAI,MAAO,CAAEW,UAAW,qBAAsBT,UAAU,IAAAF,KAAI,OAAQ,CAAEE,SAAU8pB,EAAQxnB,KAAK6nB,IAAY,IAAArqB,KAAIopB,GAAQ,CAAEnpB,GAAIoqB,EAAOpqB,GAAIgM,KAAMoe,EAAOpe,KAAM2c,MAAOyB,EAAOzB,MAAO3pB,MAAOorB,EAAOprB,MAAOC,OAAQmrB,EAAOnrB,OAAQmqB,YAAagB,EAAOhB,YAAaR,YAAawB,EAAOxB,YAAaS,OAAQe,EAAOf,QAAUe,EAAOpqB,UAFzT,IAEqU,EAEpV6pB,GAAkBvoB,YAAc,oBAChC,IAAI+oB,IAAsB,IAAAlN,MAAK0M,IAE/B,SAASS,IAAkB,EAAEhnB,EAAC,EAAEC,EAAC,MAAEue,EAAK,WAAEyI,EAAa,CAAC,EAAC,YAAEC,GAAc,EAAI,aAAEC,EAAe,CAAC,EAAC,eAAEC,EAAiB,CAAC,EAAG,GAAE,oBAAEC,EAAsB,EAAC,SAAE1qB,EAAQ,UAAES,KAAcC,IACxK,MAAOiqB,EAAcC,IAAmB,IAAA3kB,UAAS,CAAE5C,EAAG,EAAGC,EAAG,EAAGvE,MAAO,EAAGC,OAAQ,IAC3E6rB,GAAkB,OAAG,CAAC,+BAAgCpqB,IACtDqqB,GAAc,IAAA7lB,QAAO,MAY3B,OAXA,IAAAxC,YAAU,KACN,GAAIqoB,EAAY9lB,QAAS,CACrB,MAAM+lB,EAAWD,EAAY9lB,QAAQgmB,UACrCJ,EAAgB,CACZvnB,EAAG0nB,EAAS1nB,EACZC,EAAGynB,EAASznB,EACZvE,MAAOgsB,EAAShsB,MAChBC,OAAQ+rB,EAAS/rB,QAEzB,IACD,CAAC6iB,SACiB,IAAVA,GAA0BA,GAG7B,IAAA1hB,MAAK,IAAK,CAAEsJ,UAAW,aAAapG,EAAIsnB,EAAa5rB,MAAQ,KAAKuE,EAAIqnB,EAAa3rB,OAAS,KAAMyB,UAAWoqB,EAAiB9D,WAAY4D,EAAa5rB,MAAQ,UAAY,YAAa2B,EAAMV,SAAU,CAACuqB,IAAgB,IAAAzqB,KAAI,OAAQ,CAAEf,MAAO4rB,EAAa5rB,MAAQ,EAAI0rB,EAAe,GAAIpnB,GAAIonB,EAAe,GAAInnB,GAAImnB,EAAe,GAAIzrB,OAAQ2rB,EAAa3rB,OAAS,EAAIyrB,EAAe,GAAIhqB,UAAW,0BAA2B9B,MAAO6rB,EAAcS,GAAIP,EAAqBQ,GAAIR,KAAyB,IAAA5qB,KAAI,OAAQ,CAAEW,UAAW,wBAAyB6C,EAAGqnB,EAAa3rB,OAAS,EAAGmsB,GAAI,QAASxqB,IAAKmqB,EAAansB,MAAO2rB,EAAYtqB,SAAU6hB,IAAU7hB,KAF/nB,IAGf,CACAqqB,GAAkBhpB,YAAc,WA2BhC,MAAM+pB,IAAW,IAAAlO,MAAKmN,IA6BtB,SAASgB,IAAS,KAAEC,EAAI,OAAEC,EAAM,OAAEC,EAAM,MAAE3J,EAAK,WAAEyI,EAAU,YAAEC,EAAW,aAAEC,EAAY,eAAEC,EAAc,oBAAEC,EAAmB,iBAAEe,EAAmB,MAAO7mB,IACnJ,OAAQ,IAAAzE,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAI,OAAQ,IAAK8E,EAAO8mB,EAAGJ,EAAMvC,KAAM,OAAQtoB,WAAW,OAAG,CAAC,wBAAyBmE,EAAMnE,cAAgBgrB,IAAqB,IAAA3rB,KAAI,OAAQ,CAAE4rB,EAAGJ,EAAMvC,KAAM,OAAQ4C,cAAe,EAAGhD,YAAa8C,EAAkBhrB,UAAW,iCAAoCohB,IAAS,QAAU0J,KAAW,QAAUC,IAAW,IAAA1rB,KAAIsrB,GAAU,CAAE/nB,EAAGkoB,EAAQjoB,EAAGkoB,EAAQ3J,MAAOA,EAAOyI,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,IAA0B,OACnjB,CAEA,SAASkB,IAAW,IAAEC,EAAG,GAAEC,EAAE,GAAEC,EAAE,GAAEC,EAAE,GAAEC,IACnC,OAAIJ,IAAQ,KAASK,MAAQL,IAAQ,KAASM,MACnC,CAAC,IAAOL,EAAKE,GAAKD,GAEtB,CAACD,EAAI,IAAOC,EAAKE,GAC5B,CAMA,SAASG,IAAoB,QAAEC,EAAO,QAAEC,EAAO,eAAE3K,EAAiB,KAASC,OAAM,QAAE2K,EAAO,QAAEC,EAAO,eAAEzK,EAAiB,KAAS5E,MAC3H,MAAOsP,EAAgBC,GAAkBd,GAAW,CAChDC,IAAKlK,EACLmK,GAAIO,EACJN,GAAIO,EACJN,GAAIO,EACJN,GAAIO,KAEDG,EAAgBC,GAAkBhB,GAAW,CAChDC,IAAK9J,EACL+J,GAAIS,EACJR,GAAIS,EACJR,GAAIK,EACJJ,GAAIK,KAEDf,EAAQC,EAAQqB,EAASC,IAAW,QAAoB,CAC3DT,UACAC,UACAC,UACAC,UACAC,iBACAC,iBACAC,iBACAC,mBAEJ,MAAO,CACH,IAAIP,KAAWC,MAAYG,KAAkBC,KAAkBC,KAAkBC,KAAkBL,KAAWC,IAC9GjB,EACAC,EACAqB,EACAC,EAER,CACA,SAASC,GAAuBrqB,GAE5B,OAAO,IAAAwa,OAAK,EAAGnd,KAAIssB,UAASC,UAASC,UAASC,UAAS7K,iBAAiB,KAASC,OAAQG,iBAAiB,KAAS5E,IAAK0E,QAAOyI,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB/rB,QAAOurB,YAAWF,cAAayB,uBACtO,MAAOH,EAAMC,EAAQC,GAAUY,GAAoB,CAC/CC,UACAC,UACA3K,iBACA4K,UACAC,UACAzK,mBAEEiL,EAAMtqB,EAAOuqB,gBAAa3gB,EAAYvM,EAC5C,OAAQ,IAAAD,KAAIurB,GAAU,CAAEtrB,GAAIitB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQ3J,MAAOA,EAAOyI,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB/rB,MAAOA,EAAOurB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,GAAoB,GAE/V,CACA,MAAMyB,GAAmBH,GAAuB,CAAEE,YAAY,IACxDE,GAA2BJ,GAAuB,CAAEE,YAAY,IAItE,SAASG,GAAqB1qB,GAE1B,OAAO,IAAAwa,OAAK,EAAGnd,KAAIssB,UAASC,UAASC,UAASC,UAAS3K,QAAOyI,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB/rB,QAAOgjB,iBAAiB,KAASC,OAAQG,iBAAiB,KAAS5E,IAAK+M,YAAWF,cAAaqD,cAAa5B,uBACnP,MAAOH,EAAMC,EAAQC,IAAU,QAAkB,CAC7Ca,UACAC,UACA3K,iBACA4K,UACAC,UACAzK,iBACAuL,aAAcD,GAAaC,aAC3BC,OAAQF,GAAaE,SAEnBP,EAAMtqB,EAAOuqB,gBAAa3gB,EAAYvM,EAC5C,OAAQ,IAAAD,KAAIurB,GAAU,CAAEtrB,GAAIitB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQ3J,MAAOA,EAAOyI,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB/rB,MAAOA,EAAOurB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,GAAoB,GAE/V,CAnBAyB,GAAiB7rB,YAAc,mBAC/B8rB,GAAyB9rB,YAAc,2BAmBvC,MAAMmsB,GAAiBJ,GAAqB,CAAEH,YAAY,IACpDQ,GAAyBL,GAAqB,CAAEH,YAAY,IAIlE,SAASS,GAAehrB,GAEpB,OAAO,IAAAwa,OAAK,EAAGnd,QAAO6E,MAClB,MAAMooB,EAAMtqB,EAAOuqB,gBAAa3gB,EAAYvM,EAC5C,OAAQ,IAAAD,KAAI0tB,GAAgB,IAAK5oB,EAAO7E,GAAIitB,EAAKK,aAAa,IAAA9uB,UAAQ,KAAM,CAAG+uB,aAAc,EAAGC,OAAQ3oB,EAAMyoB,aAAaE,UAAW,CAAC3oB,EAAMyoB,aAAaE,UAAY,GAE9K,CATAC,GAAensB,YAAc,iBAC7BosB,GAAuBpsB,YAAc,yBASrC,MAAMssB,GAAWD,GAAe,CAAET,YAAY,IACxCW,GAAmBF,GAAe,CAAET,YAAY,IAItD,SAASY,GAAmBnrB,GAExB,OAAO,IAAAwa,OAAK,EAAGnd,KAAIssB,UAASC,UAASC,UAASC,UAAS3K,QAAOyI,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB/rB,QAAOurB,YAAWF,cAAayB,uBACrK,MAAOH,EAAMC,EAAQC,IAAU,QAAgB,CAAEa,UAASC,UAASC,UAASC,YACtEQ,EAAMtqB,EAAOuqB,gBAAa3gB,EAAYvM,EAC5C,OAAQ,IAAAD,KAAIurB,GAAU,CAAEtrB,GAAIitB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQ3J,MAAOA,EAAOyI,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB/rB,MAAOA,EAAOurB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,GAAoB,GAE/V,CAVAkC,GAAStsB,YAAc,WACvBusB,GAAiBvsB,YAAc,mBAU/B,MAAMysB,GAAeD,GAAmB,CAAEZ,YAAY,IAChDc,GAAuBF,GAAmB,CAAEZ,YAAY,IAI9D,SAASe,GAAiBtrB,GAEtB,OAAO,IAAAwa,OAAK,EAAGnd,KAAIssB,UAASC,UAASC,UAASC,UAAS7K,iBAAiB,KAASC,OAAQG,iBAAiB,KAAS5E,IAAK0E,QAAOyI,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB/rB,QAAOurB,YAAWF,cAAaqD,cAAa5B,uBACnP,MAAOH,EAAMC,EAAQC,IAAU,QAAc,CACzCa,UACAC,UACA3K,iBACA4K,UACAC,UACAzK,iBACAkM,UAAWZ,GAAaY,YAEtBjB,EAAMtqB,EAAOuqB,gBAAa3gB,EAAYvM,EAC5C,OAAQ,IAAAD,KAAIurB,GAAU,CAAEtrB,GAAIitB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQ3J,MAAOA,EAAOyI,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB/rB,MAAOA,EAAOurB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,GAAoB,GAE/V,CAlBAqC,GAAazsB,YAAc,eAC3B0sB,GAAqB1sB,YAAc,uBAkBnC,MAAM6sB,GAAaF,GAAiB,CAAEf,YAAY,IAC5CkB,GAAqBH,GAAiB,CAAEf,YAAY,IAC1DiB,GAAW7sB,YAAc,aACzB8sB,GAAmB9sB,YAAc,qBAEjC,MAAM+sB,GAAmB,CACrBtM,QAASqM,GACTE,SAAUN,GACVO,KAAMV,GACNW,WAAYd,GACZe,aAAcrB,IAEZsB,GAAe,CACjBpC,QAAS,KACTC,QAAS,KACTC,QAAS,KACTC,QAAS,KACT7K,eAAgB,KAChBI,eAAgB,MAGd2M,GAAS,CAACrrB,EAAGsrB,EAAO7vB,IAClBA,IAAa,KAASotB,KACf7oB,EAAIsrB,EACX7vB,IAAa,KAASqtB,MACf9oB,EAAIsrB,EACRtrB,EAELurB,GAAS,CAACtrB,EAAGqrB,EAAO7vB,IAClBA,IAAa,KAASqe,IACf7Z,EAAIqrB,EACX7vB,IAAa,KAAS8iB,OACfte,EAAIqrB,EACRrrB,EAELurB,GAAuB,0BAC7B,SAASC,IAAW,SAAEhwB,EAAQ,QAAEwL,EAAO,QAAEC,EAAO,OAAEwkB,EAAS,GAAE,YAAEtR,EAAW,aAAE2G,EAAY,WAAE4K,EAAU,KAAEjjB,IAClG,OAAQ,IAAAjM,KAAI,SAAU,CAAE2d,YAAaA,EAAa2G,aAAcA,EAAc4K,WAAYA,EAAYvuB,WAAW,OAAG,CAACouB,GAAsB,GAAGA,MAAwB9iB,MAAUkjB,GAAIP,GAAOpkB,EAASykB,EAAQjwB,GAAWowB,GAAIN,GAAOrkB,EAASwkB,EAAQjwB,GAAWqwB,EAAGJ,EAAQnG,OAAQ,cAAeG,KAAM,eAC1S,CAEA,SAASqG,IAAkB,gBAAEC,EAAe,gBAAEC,EAAe,KAAEvtB,EAAI,QAAEsqB,EAAO,QAAEC,EAAO,QAAEC,EAAO,QAAEC,EAAO,eAAE7K,EAAc,eAAEI,EAAc,YAAEwN,EAAW,iBAAEC,EAAgB,eAAEC,EAAc,gBAAEC,EAAe,eAAEC,IACrM,MAAMxxB,EAAQG,IACRsxB,EAAoB,CAACzoB,EAAO0oB,KAE9B,GAAqB,IAAjB1oB,EAAMgS,OACN,OAEJ,MAAM,iBAAE+F,EAAgB,QAAEhV,EAAO,kBAAEkT,EAAiB,eAAEkB,EAAc,iBAAEa,EAAgB,IAAE5K,EAAG,eAAEgL,EAAc,aAAEC,EAAY,iBAAEF,EAAgB,WAAE5d,EAAY7B,KAAMuf,EAAM,MAAEC,EAAK,iBAAEI,GAAsBthB,EAAMK,WACpMmf,EAAmC,WAAxBkS,EAAe9jB,KAChC2jB,GAAgB,GAChBF,IAAmBroB,EAAOpF,EAAM8tB,EAAe9jB,MAM/C,KAASmN,cAAc/R,EAAMmS,YAAa,CACtC4F,mBACAZ,iBACAa,mBACAjV,UACA8J,SAAU6b,EAAe9vB,GACzB4T,OAAQkc,EAAelc,OACvBjS,aACAic,WACAmS,gBAAiBD,EAAe9jB,KAChCwI,MACA6K,SACAE,mBACAD,QACAjC,oBACAI,UAhBmBe,GAAegR,IAAcxtB,EAAMwc,GAiBtDgB,iBACAC,eACAiQ,eAvBoB,CAACM,EAAKvP,KAC1BkP,GAAgB,GAChBD,IAAiBM,EAAKhuB,EAAM8tB,EAAe9jB,KAAMyU,EAAgB,EAsBjEf,mBACAC,aAAc,IAAMvhB,EAAMK,WAAWiL,UACrCkW,cAAe,IAAMxhB,EAAMK,WAAW+f,WAAWC,YACnD,EAIAwR,EAAwB,IAAML,GAAe,GAC7CM,EAAsB,IAAMN,GAAe,GACjD,OAAQ,IAAAxvB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,GAAsB,IAApBqvB,GAAgD,WAApBA,KAAkC,IAAAvvB,KAAIgvB,GAAY,CAAEhwB,SAAU6iB,EAAgBrX,QAAS+hB,EAAS9hB,QAAS+hB,EAASyC,OAAQO,EAAiB7R,YAJxKtW,GAAUyoB,EAAkBzoB,EAAO,CAAEwM,OAAQ5R,EAAKZ,OAAQpB,GAAIgC,EAAKmuB,cAAgB,KAAMnkB,KAAM,WAIkHqY,aAAc4L,EAAuBhB,WAAYiB,EAAqBlkB,KAAM,aAAmC,IAApBsjB,GAAgD,WAApBA,KAAkC,IAAAvvB,KAAIgvB,GAAY,CAAEhwB,SAAUijB,EAAgBzX,QAASiiB,EAAShiB,QAASiiB,EAASuC,OAAQO,EAAiB7R,YAHndtW,GAAUyoB,EAAkBzoB,EAAO,CAAEwM,OAAQ5R,EAAK8d,OAAQ9f,GAAIgC,EAAKouB,cAAgB,KAAMpkB,KAAM,WAG6ZqY,aAAc4L,EAAuBhB,WAAYiB,EAAqBlkB,KAAM,aAChnB,CAEA,SAASqkB,IAAY,GAAErwB,EAAE,eAAEswB,EAAc,mBAAEC,EAAkB,mBAAE9rB,EAAkB,QAAEkU,EAAO,cAAE6L,EAAa,cAAEvL,EAAa,aAAEoL,EAAY,YAAEC,EAAW,aAAEC,EAAY,gBAAEgL,EAAe,YAAEC,EAAW,iBAAEC,EAAgB,eAAEC,EAAc,KAAE5vB,EAAI,UAAE0wB,EAAS,eAAE9rB,EAAc,QAAEiR,EAAO,oBAAExV,IACzQ,IAAI6B,EAAO/D,GAAU0B,GAAMA,EAAEsC,WAAWkK,IAAInM,KAC5C,MAAM2Z,EAAqB1b,GAAU0B,GAAMA,EAAEga,qBAC7C3X,EAAO2X,EAAqB,IAAKA,KAAuB3X,GAASA,EACjE,IAAIyuB,EAAWzuB,EAAKgK,MAAQ,UACxB0kB,EAAgBF,IAAYC,IAAapC,GAAiBoC,QACxClkB,IAAlBmkB,IACA/a,IAAU,MAAO,KAAwB,SAAE8a,IAC3CA,EAAW,UACXC,EAAgBrC,GAAiBtM,SAErC,MAAMkD,KAAiBjjB,EAAKkjB,WAAcoL,QAA4C,IAAnBtuB,EAAKkjB,WAClEoK,OAAyC,IAAhBE,IAC1BxtB,EAAK2uB,eAAkBJ,QAAoD,IAAvBvuB,EAAK2uB,eACxDpV,KAAkBvZ,EAAKqY,YAAe5V,QAAiD,IAApBzC,EAAKqY,YACxEuW,GAAU,IAAA1rB,QAAO,OAChB2rB,EAAajB,IAAkB,IAAA1pB,WAAS,IACxC4qB,EAAcnB,IAAmB,IAAAzpB,WAAS,GAC3C9H,EAAQG,KACR,OAAEuoB,EAAM,QAAEwF,EAAO,QAAEC,EAAO,QAAEC,EAAO,QAAEC,EAAO,eAAE7K,EAAc,eAAEI,GAAmB/jB,GAAS,IAAAgR,cAAa7Q,IACzG,MAAM2yB,EAAa3yB,EAAMuD,WAAWwK,IAAInK,EAAK8d,QACvCkR,EAAa5yB,EAAMuD,WAAWwK,IAAInK,EAAKZ,QAC7C,IAAK2vB,IAAeC,EAChB,MAAO,CACHlK,OAAQ9kB,EAAK8kB,UACV4H,IAGX,MAAMuC,GAAe,QAAgB,CACjCjxB,KACA+wB,aACAC,aACAZ,aAAcpuB,EAAKouB,cAAgB,KACnCD,aAAcnuB,EAAKmuB,cAAgB,KACnC5R,eAAgBngB,EAAMmgB,eACtB5I,YASJ,MAAO,CACHmR,QARW,QAAsB,CACjCllB,SAAUI,EAAKJ,SACfklB,OAAQ9kB,EAAK8kB,OACbiK,aACAC,aACAE,gBAAiB9yB,EAAM+yB,0BAInBF,GAAgBvC,GACvB,GACF,CAAC1sB,EAAK8d,OAAQ9d,EAAKZ,OAAQY,EAAKouB,aAAcpuB,EAAKmuB,aAAcnuB,EAAKJ,SAAUI,EAAK8kB,SAAU,KAC5FsK,GAAiB,IAAA5yB,UAAQ,IAAOwD,EAAKioB,YAAc,UAAS,QAAYjoB,EAAKioB,YAAanqB,YAAYyM,GAAY,CAACvK,EAAKioB,YAAanqB,IACrIuxB,GAAe,IAAA7yB,UAAQ,IAAOwD,EAAKmoB,UAAY,UAAS,QAAYnoB,EAAKmoB,UAAWrqB,YAAYyM,GAAY,CAACvK,EAAKmoB,UAAWrqB,IACnI,GAAIkC,EAAK0jB,QAAsB,OAAZ4G,GAAgC,OAAZC,GAAgC,OAAZC,GAAgC,OAAZC,EAC3E,OAAO,KAEX,MAgBM6E,EAAoB9M,EACnBpd,IACCod,EAAcpd,EAAO,IAAKpF,GAAO,OAEnCuK,EACAglB,EAAoBtY,EACnB7R,IACC6R,EAAc7R,EAAO,IAAKpF,GAAO,OAEnCuK,EACAilB,EAAmBnN,EAClBjd,IACCid,EAAajd,EAAO,IAAKpF,GAAO,OAElCuK,EACAklB,EAAkBnN,EACjBld,IACCkd,EAAYld,EAAO,IAAKpF,GAAO,OAEjCuK,EACAmlB,EAAmBnN,EAClBnd,IACCmd,EAAand,EAAO,IAAKpF,GAAO,OAElCuK,EAcN,OAAQ,IAAAxM,KAAI,MAAO,CAAEnB,MAAO,CAAEkoB,UAAU7mB,UAAU,IAAAG,MAAK,IAAK,CAAEM,WAAW,OAAG,CAChE,mBACA,oBAAoB+vB,IACpBzuB,EAAKtB,UACLgE,EACA,CACI9C,SAAUI,EAAKJ,SACf+vB,SAAU3vB,EAAK2vB,SACfC,UAAWrW,IAAiB5C,EAC5BkZ,SAAUhB,EACVxW,WAAYkB,KAEhB5C,QAlESvR,IACjB,MAAM,iBAAE0qB,EAAgB,sBAAE/W,EAAqB,qBAAEC,GAAyB5c,EAAMK,WAC5E8c,IACAnd,EAAMM,SAAS,CAAEma,sBAAsB,IACnC7W,EAAKJ,UAAYoZ,GACjBD,EAAsB,CAAEnY,MAAO,GAAIC,MAAO,CAACb,KAC3C4uB,EAAQ3rB,SAASiW,QAGjB4W,EAAiB,CAAC9xB,KAGtB2Y,GACAA,EAAQvR,EAAOpF,EACnB,EAoD8BwiB,cAAe8M,EAAmBrY,cAAesY,EAAmBlN,aAAcmN,EAAkBlN,YAAamN,EAAiBlN,aAAcmN,EAAkB/O,UAAWsC,EAzB5L7d,IACf,IAAKjH,GAAuB,KAAqB0I,SAASzB,EAAMa,MAAQsT,EAAc,CAClF,MAAM,sBAAER,EAAqB,iBAAE+W,GAAqB1zB,EAAMK,WAC3B,WAAd2I,EAAMa,KAEnB2oB,EAAQ3rB,SAASiW,OACjBH,EAAsB,CAAElY,MAAO,CAACb,MAGhC8vB,EAAiB,CAAC9xB,GAE1B,QAcqOuM,EAAWmW,SAAUuC,EAAc,OAAI1Y,EAAW6a,KAAMnC,EAAc,SAAW,MAAO,UAAWjlB,EAAI,cAAe,YAAYA,IAAM,aAAiC,OAAnBgC,EAAKqlB,eAAqB9a,EAAYvK,EAAKqlB,WAAa,aAAarlB,EAAK8d,aAAa9d,EAAKZ,SAAU,mBAAoB6jB,EAAc,GAAGxlB,KAAsBK,SAASyM,EAAW3L,IAAKgwB,EAAS3wB,SAAU,EAAE6wB,IAAiB,IAAA/wB,KAAI2wB,EAAe,CAAE1wB,GAAIA,EAAI8f,OAAQ9d,EAAK8d,OAAQ1e,OAAQY,EAAKZ,OAAQ4K,KAAMhK,EAAKgK,KAAMpK,SAAUI,EAAKJ,SAAU+vB,SAAU3vB,EAAK2vB,SAAUtX,WAAYkB,EAAciM,UAAWxlB,EAAKwlB,YAAa,EAAM1F,MAAO9f,EAAK8f,MAAOyI,WAAYvoB,EAAKuoB,WAAYC,YAAaxoB,EAAKwoB,YAAaC,aAAczoB,EAAKyoB,aAAcC,eAAgB1oB,EAAK0oB,eAAgBC,oBAAqB3oB,EAAK2oB,oBAAqB2B,QAASA,EAASC,QAASA,EAASC,QAASA,EAASC,QAASA,EAAS7K,eAAgBA,EAAgBI,eAAgBA,EAAgBxO,KAAMxR,EAAKwR,KAAM5U,MAAOoD,EAAKpD,MAAOmzB,eAAgB/vB,EAAKouB,aAAc4B,eAAgBhwB,EAAKmuB,aAAclG,YAAamH,EAAgBjH,UAAWkH,EAAc/D,YAAa,gBAAiBtrB,EAAOA,EAAKsrB,iBAAc/gB,EAAWmf,iBAAkB1pB,EAAK0pB,mBAAsB4D,IAAoB,IAAAvvB,KAAIsvB,GAAmB,CAAErtB,KAAMA,EAAMstB,gBAAiBA,EAAiBC,gBAAiBA,EAAiBC,YAAaA,EAAaC,iBAAkBA,EAAkBC,eAAgBA,EAAgBpD,QAASA,EAASC,QAASA,EAASC,QAASA,EAASC,QAASA,EAAS7K,eAAgBA,EAAgBI,eAAgBA,EAAgB4N,eAAgBA,EAAgBD,gBAAiBA,QAC1xD,CAEA,MAAMsC,GAActyB,IAAM,CACtB2wB,eAAgB3wB,EAAE2wB,eAClBC,mBAAoB5wB,EAAE4wB,mBACtB9rB,mBAAoB9E,EAAE8E,mBACtB8Z,eAAgB5e,EAAE4e,eAClB5I,QAAShW,EAAEgW,UAEf,SAASuc,IAAsB,mBAAEC,EAAkB,0BAAEvK,EAAyB,KAAE9nB,EAAI,UAAE0wB,EAAS,eAAE9rB,EAAc,YAAE8qB,EAAW,kBAAE+B,EAAiB,iBAAEC,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,YAAEU,EAAW,gBAAE7C,EAAe,kBAAE+B,EAAiB,iBAAE7B,EAAgB,eAAEC,EAAc,oBAAEvvB,IACrR,MAAM,eAAEmwB,EAAc,mBAAEC,EAAkB,mBAAE9rB,EAAkB,QAAEkR,GAAY1X,EAASg0B,GAAY,KAC3FI,GApgBiBrO,EAogBW4D,EAngBlB3pB,GAAS,IAAAgR,cAAatP,IAClC,IAAKqkB,EACD,OAAOrkB,EAAEkD,MAAMN,KAAKP,GAASA,EAAKhC,KAEtC,MAAMsyB,EAAiB,GACvB,GAAI3yB,EAAEX,OAASW,EAAEV,OACb,IAAK,MAAM+C,KAAQrC,EAAEkD,MAAO,CACxB,MAAMkuB,EAAapxB,EAAEgC,WAAWwK,IAAInK,EAAK8d,QACnCkR,EAAarxB,EAAEgC,WAAWwK,IAAInK,EAAKZ,QACrC2vB,GACAC,IACA,QAAc,CACVD,aACAC,aACAhyB,MAAOW,EAAEX,MACTC,OAAQU,EAAEV,OACVyK,UAAW/J,EAAE+J,aAEjB4oB,EAAezwB,KAAKG,EAAKhC,GAEjC,CAEJ,OAAOsyB,CAAc,GACtB,CAACtO,IAAqB,MAxB7B,IAA2BA,EAqgBvB,OAAQ,IAAA5jB,MAAK,MAAO,CAAEM,UAAW,oBAAqBT,SAAU,EAAC,IAAAF,KAAIsqB,GAAqB,CAAEP,aAAcqI,EAAoBryB,KAAMA,IAASuyB,EAAQ9vB,KAAKvC,IACtI,IAAAD,KAAIswB,GAAa,CAAErwB,GAAIA,EAAIswB,eAAgBA,EAAgBC,mBAAoBA,EAAoB9rB,mBAAoBA,EAAoBC,eAAgBA,EAAgB8qB,YAAaA,EAAavW,cAAesY,EAAmBlN,aAAcmN,EAAkBlN,YAAamN,EAAiBlN,aAAcmN,EAAkB/Y,QAASyZ,EAAa7C,gBAAiBA,EAAiB/K,cAAe8M,EAAmB7B,iBAAkBA,EAAkBC,eAAgBA,EAAgB5vB,KAAMA,EAAM6V,QAASA,EAAS6a,UAAWA,EAAWrwB,oBAAqBA,GAAuBH,OAE3lB,CACAkyB,GAAsB5wB,YAAc,eACpC,MAAMixB,IAAe,IAAApV,MAAK+U,IAEpBM,GAAc7yB,GAAM,aAAaA,EAAE+J,UAAU,QAAQ/J,EAAE+J,UAAU,eAAe/J,EAAE+J,UAAU,MAClG,SAAS+oB,IAAS,SAAExyB,IAChB,MAAMyJ,EAAYzL,EAASu0B,IAC3B,OAAQ,IAAAzyB,KAAI,MAAO,CAAEW,UAAW,8DAA+D9B,MAAO,CAAE8K,aAAazJ,SAAUA,GACnI,CAkBA,MAAMyyB,GAActU,GAAUA,EAAMpV,SAAS2pB,aAmB7C,SAASC,GAAgBjzB,GACrB,OAAOA,EAAE6e,WAAW2C,WACd,IAAKxhB,EAAE6e,WAAYqU,IAAI,QAAqBlzB,EAAE6e,WAAWqU,GAAIlzB,EAAE+J,YAC/D,IAAK/J,EAAE6e,WACjB,CAoCA,SAASsU,GAAcC,GACnB,MAAMC,EApCV,SAAqBD,GACjB,GAAIA,EAKA,OAJ0BpzB,IACtB,MAAM6e,EAAaoU,GAAgBjzB,GACnC,OAAOozB,EAAmBvU,EAAW,EAI7C,OAAOoU,EACX,CA2B6BK,CAAYF,GACrC,OAAO90B,EAAS+0B,EAAkB,IACtC,CAEA,MAAME,GAAcvzB,IAAM,CACtB8kB,iBAAkB9kB,EAAE8kB,iBACpB9F,QAAShf,EAAE6e,WAAWG,QACtBwC,WAAYxhB,EAAE6e,WAAW2C,WACzBniB,MAAOW,EAAEX,MACTC,OAAQU,EAAEV,SAEd,SAASk0B,IAAsB,eAAE/e,EAAc,MAAExV,EAAK,KAAEoN,EAAI,UAAEonB,IAC1D,MAAM,iBAAE3O,EAAgB,MAAEzlB,EAAK,OAAEC,EAAM,QAAE0f,EAAO,WAAEwC,GAAeljB,EAASi1B,GAAY,KAEtF,SAD4Bl0B,GAASylB,GAAoBtD,IAIjD,IAAAphB,KAAI,MAAO,CAAEnB,MAAOwV,EAAgBpV,MAAOA,EAAOC,OAAQA,EAAQyB,UAAW,mDAAoDT,UAAU,IAAAF,KAAI,IAAK,CAAEW,WAAW,OAAG,CAAC,0BAA0B,QAAoBie,KAAY1e,UAAU,IAAAF,KAAIszB,GAAgB,CAAEz0B,MAAOA,EAAOoN,KAAMA,EAAMsnB,gBAAiBF,EAAWzU,QAASA,QAF3T,IAGf,CACA,MAAM0U,GAAiB,EAAGz0B,QAAOoN,OAAO,KAAmBunB,OAAQD,kBAAiB3U,cAChF,MAAM,WAAEwC,EAAU,KAAEtN,EAAI,SAAE2f,EAAQ,WAAE/U,EAAU,aAAEgV,EAAY,GAAEZ,EAAE,OAAEa,EAAM,SAAEhV,EAAQ,WAAE0C,GAAe0R,KACnG,IAAK3R,EACD,OAEJ,GAAImS,EACA,OAAQ,IAAAvzB,KAAIuzB,EAAiB,CAAEK,mBAAoB3nB,EAAM4nB,oBAAqBh1B,EAAO40B,SAAUA,EAAU/U,WAAYA,EAAYoV,MAAOhgB,EAAKvQ,EAAGwwB,MAAOjgB,EAAKtQ,EAAGwwB,IAAKlB,EAAGvvB,EAAG0wB,IAAKnB,EAAGtvB,EAAGkwB,aAAcA,EAAcrS,WAAYA,EAAY6S,kBAAkB,QAAoBtV,GAAU+U,OAAQA,EAAQhV,SAAUA,IAEvT,IAAI6M,EAAO,GACX,MAAM2I,EAAa,CACf5H,QAASzY,EAAKvQ,EACdipB,QAAS1Y,EAAKtQ,EACdqe,eAAgB6R,EAChBjH,QAASqG,EAAGvvB,EACZmpB,QAASoG,EAAGtvB,EACZye,eAAgBZ,GAEpB,OAAQpV,GACJ,KAAK,KAAmBunB,QACnBhI,IAAQ,QAAc2I,GACvB,MACJ,KAAK,KAAmBC,cACnB5I,GAAQc,GAAoB6H,GAC7B,MACJ,KAAK,KAAmBE,MACnB7I,IAAQ,QAAkB,IACpB2I,EACH3G,aAAc,IAElB,MACJ,KAAK,KAAmB8G,YACnB9I,IAAQ,QAAkB2I,GAC3B,MACJ,SACK3I,IAAQ,QAAgB2I,GAEjC,OAAO,IAAAn0B,KAAI,OAAQ,CAAE4rB,EAAGJ,EAAMvC,KAAM,OAAQtoB,UAAW,8BAA+B9B,MAAOA,GAAQ,EAEzGy0B,GAAe/xB,YAAc,iBAE7B,MAAMgzB,GAAa,CAAC,EAEpB,SAASC,GAA0BC,EAAkBF,KAChC,IAAApvB,QAAOsvB,GACVj2B,KACd,IAAAmE,YAAU,KACF,CAAyC,GAU9C,CAAC8xB,GACR,CAkBA,SAASC,IAAmB,UAAE9P,EAAS,UAAE6L,EAAS,OAAEkE,EAAM,YAAExM,EAAW,YAAEkK,EAAW,kBAAE7J,EAAiB,kBAAE+I,EAAiB,iBAAEnJ,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAEC,EAAiB,uBAAEhG,EAAsB,iBAAEzK,EAAgB,eAAEC,EAAc,mBAAE6b,EAAkB,oBAAEC,EAAmB,wBAAEe,EAAuB,6BAAEC,EAA4B,iBAAExR,EAAgB,gBAAExL,EAAe,cAAEF,EAAa,sBAAE2L,EAAqB,qBAAEC,EAAoB,sBAAEnO,EAAqB,cAAEgO,EAAa,0BAAEyE,EAAyB,mBAAEnjB,EAAkB,gBAAEpB,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,iBAAE4Q,EAAgB,mBAAE+c,EAAkB,aAAExd,EAAY,YAAEC,EAAW,YAAEC,EAAW,iBAAEC,EAAgB,gBAAEC,EAAe,kBAAEE,EAAiB,UAAEC,EAAS,YAAE6C,EAAW,iBAAEE,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,aAAEH,EAAY,kBAAEtD,EAAiB,kBAAE/P,EAAiB,kBAAE6W,EAAiB,kBAAE+V,EAAiB,iBAAEC,EAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,gBAAEnC,GAAe,YAAEC,GAAW,iBAAEC,GAAgB,eAAEC,GAAc,gBAAErU,GAAe,iBAAEhG,GAAgB,eAAE3Q,GAAc,oBAAEvE,GAAmB,WAAE6b,GAAU,KAAElc,GAAI,SAAE+J,GAAQ,iBAAEyL,KAMtkC,OALAif,GAA0B5P,GAC1B4P,GAA0B/D,GAjBZjyB,KACE,IAAA2G,SAAO,IACvB,IAAAxC,YAAU,KACuC,GAS9C,IAlKP,SAA0BgyB,GACtB,MAAMG,EAAallB,KACb6V,GAAgB,IAAAtgB,SAAO,IAC7B,IAAAxC,YAAU,MACD8iB,EAAcvgB,SAAW4vB,EAAW9kB,qBAAuB2kB,IAC5DI,YAAW,IAAMJ,EAAOG,IAAa,GACrCrP,EAAcvgB,SAAU,EAC5B,GACD,CAACyvB,EAAQG,EAAW9kB,qBAC3B,CAgKIglB,CAAiBL,GAvJrB,SAAyB7qB,GACrB,MAAM8oB,EAAe10B,EAASy0B,IACxBt0B,EAAQG,KACd,IAAAmE,YAAU,KACFmH,IACA8oB,IAAe9oB,GACfzL,EAAMM,SAAS,CAAEgL,UAAW,CAACG,EAASvG,EAAGuG,EAAStG,EAAGsG,EAASrG,QAClE,GACD,CAACqG,EAAU8oB,GAElB,CA8IIqC,CAAgBnrB,KACR,IAAA9J,KAAI+jB,GAAc,CAAE/L,YAAaA,EAAaE,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBzD,kBAAmBA,EAAmBsD,aAAcA,EAAcrT,kBAAmBA,EAAmBwe,cAAeA,EAAeC,iBAAkBA,EAAkBxL,gBAAiBA,EAAiBF,cAAeA,EAAeG,iBAAkBA,EAAkBC,eAAgBA,EAAgBuL,sBAAuBA,EAAuBC,qBAAsBA,EAAsBnO,sBAAuBA,EAAuB1Q,mBAAoBA,EAAoBkQ,aAAcA,EAAcC,YAAaA,EAAaK,kBAAmBA,EAAmBJ,YAAaA,EAAaC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBG,UAAWA,EAAW7R,gBAAiBA,EAAiBgB,gBAAiBA,EAAiBE,QAASA,EAASC,QAASA,EAAS8d,uBAAwBA,EAAwBlN,iBAAkBA,EAAkBiG,gBAAiBA,GAAiBhG,iBAAkBA,GAAkB3Q,eAAgBA,GAAgBvE,oBAAqBA,GAAqBmV,iBAAkBA,GAAkBC,uBAAwB1L,GAAU5J,UAAU,IAAAG,MAAKqyB,GAAU,CAAExyB,SAAU,EAAC,IAAAF,KAAIwyB,GAAc,CAAE/B,UAAWA,EAAW4B,YAAaA,EAAad,kBAAmBA,EAAmB9B,YAAaA,GAAaC,iBAAkBA,GAAkBC,eAAgBA,GAAgB9H,0BAA2BA,EAA2B2J,kBAAmBA,EAAmBC,iBAAkBA,EAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBnC,gBAAiBA,GAAiB4C,mBAAoBA,EAAoBztB,eAAgBA,GAAgBvE,oBAAqBA,GAAqBL,KAAMA,MAAS,IAAAC,KAAIozB,GAAuB,CAAEv0B,MAAOg1B,EAAqB5nB,KAAM2nB,EAAoBP,UAAWuB,EAAyBvgB,eAAgBwgB,KAAiC,IAAA70B,KAAI,MAAO,CAAEW,UAAW,oCAAqC,IAAAX,KAAIyoB,GAAc,CAAE7D,UAAWA,EAAWuD,YAAaA,EAAaK,kBAAmBA,EAAmBJ,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBC,kBAAmBA,EAAmB9M,kBAAmBA,EAAmBoM,0BAA2BA,EAA2BljB,eAAgBA,GAAgB2W,gBAAiBA,GAAiBlb,oBAAqBA,GAAqB6b,WAAYA,GAAYlc,KAAMA,MAAS,IAAAC,KAAI,MAAO,CAAEW,UAAW,oCACniF,CACA+zB,GAAmBnzB,YAAc,YACjC,MAAM2zB,IAAY,IAAA9X,MAAKsX,IAEjBS,GAAkB,EAAGtyB,QAAOC,QAAOiC,eAAcC,eAAc/F,QAAOC,SAAQiL,UAAS5F,aAAY0X,cAAgB,CAAC,KACtH,MAAMra,EAAa,IAAIkK,IACjBgZ,EAAe,IAAIhZ,IACnBiI,EAAmB,IAAIjI,IACvB5J,EAAa,IAAI4J,IACjBspB,EAAapwB,GAAgBlC,GAAS,GACtCuyB,EAAatwB,GAAgBlC,GAAS,GACtCyyB,EAAkB/wB,GAAc,CAAC,EAAG,GACpCgxB,EAAkBtZ,GAAc,MACtC,QAAuBlI,EAAkB7R,EAAYkzB,IACrD,QAAeC,EAAYzzB,EAAYkjB,EAAc,CACjDvgB,WAAY+wB,EACZrZ,WAAYsZ,EACZC,sBAAsB,IAE1B,IAAI7rB,EAAY,CAAC,EAAG,EAAG,GACvB,GAAIQ,GAAWlL,GAASC,EAAQ,CAC5B,MAAMyL,GAAS,QAAuB/I,EAAY,CAC9CgF,OAASjF,MAAaA,EAAK1C,QAAS0C,EAAK0kB,eAAkB1kB,EAAKzC,SAAUyC,EAAK2kB,kBAE7E,EAAE/iB,EAAC,EAAEC,EAAC,KAAEC,IAAS,QAAqBkH,EAAQ1L,EAAOC,EAAQ,GAAK,EAAG,IAC3EyK,EAAY,CAACpG,EAAGC,EAAGC,EACvB,CACA,MAAO,CACH1D,KAAM,IACNd,MAAO,EACPC,OAAQ,EACRyK,YACA9G,MAAOwyB,EACPzzB,aACAkjB,eACAhiB,MAAOsyB,EACPlzB,aACA6R,mBACA3E,cAAe,KACfK,cAAe,KACfN,qBAAkC3C,IAAjBzH,EACjByK,qBAAkChD,IAAjBxH,EACjBiE,QAAS,KACTzE,QAAS,GACTC,QAAS,EACTH,gBAAiB,KACjB2X,WAAYsZ,EACZzc,sBAAsB,EACtBtY,qBAAqB,EACrB0W,kBAAmB,KACnBsH,eAAgB,KAAeK,OAC/BzU,QAAS,KACTgM,cAAc,EACdzR,eAAgB,QAChBJ,WAAY+wB,EACZnO,kBAAmB,EACnBrc,SAAU,CAAC,GAAI,IACfC,YAAY,EACZmR,gBAAgB,EAChBwI,kBAAkB,EAClBC,gBAAgB,EAChB4L,gBAAgB,EAChBC,oBAAoB,EACpB9rB,oBAAoB,EACpB8wB,sBAAsB,EACtBpE,sBAAsB,EACtB9rB,eAAe,EACfmwB,aAAa,EACblwB,0BAAsBiH,EACtB0a,mBAAmB,EACnBjM,sBAAsB,EACtBwD,WAAY,IAAK,MACjBH,2BAA4B,KAC5BpB,gBAAgB,EAChBrd,gBAAiB,GACjBuf,kBAAkB,EAClBsW,mBAAmB,EACnB5V,aAAc,GACdT,iBAAkB,GAClBzJ,QAAS,KACT0H,uBAAmB9Q,EACnBzJ,0BAA2B,GAC3B0R,IAAK,QACLkhB,OAAO,EACV,EAGCC,GAAc,EAAG/yB,QAAOC,QAAOiC,eAAcC,eAAc/F,QAAOC,SAAQiL,QAAS0rB,EAAWtxB,aAAY0X,iBAAkB,QAAqB,CAAC/P,EAAKE,KAAQ,IAC9J+oB,GAAgB,CAAEtyB,QAAOC,QAAO7D,QAAOC,SAAQiL,QAAS0rB,EAAWtxB,aAAY0X,aAAYlX,eAAcC,iBAC5GpB,SAAWf,IACP,MAAM,WAAEjB,EAAU,aAAEkjB,EAAY,WAAEvgB,EAAU,qBAAEixB,GAAyBppB,KASvE,QAAevJ,EAAOjB,EAAYkjB,EAAc,CAC5CvgB,aACA0X,aACAuZ,uBACAM,eAAe,IAEnB5pB,EAAI,CAAErJ,SAAQ,EAElBgB,SAAWf,IACP,MAAM,iBAAEiR,EAAgB,WAAE7R,GAAekK,KACzC,QAAuB2H,EAAkB7R,EAAYY,GACrDoJ,EAAI,CAAEpJ,SAAQ,EAElBqB,wBAAyB,CAACtB,EAAOC,KAC7B,GAAID,EAAO,CACP,MAAM,SAAEe,GAAawI,IACrBxI,EAASf,GACTqJ,EAAI,CAAEiD,iBAAiB,GAC3B,CACA,GAAIrM,EAAO,CACP,MAAM,SAAEe,GAAauI,IACrBvI,EAASf,GACToJ,EAAI,CAAEsD,iBAAiB,GAC3B,GAOJ4U,oBAAqB,CAAC0D,EAASllB,EAAS,CAAEmzB,gBAAgB,MACtD,MAAM,mBAAEjkB,EAAkB,WAAElQ,EAAU,aAAEkjB,EAAY,cAAExf,EAAa,YAAEmwB,EAAW,qBAAElwB,EAAoB,QAAE6E,EAAO,WAAE7F,EAAU,WAAE0X,EAAU,MAAE0Z,EAAK,YAAEK,GAAiB5pB,KAC3J,QAAEV,EAAO,iBAAEuqB,IAAqB,QAAoBnO,EAASlmB,EAAYkjB,EAAc1a,EAAS7F,EAAY0X,GAClH,GAAKga,EAAL,CAIA,IADA,QAAwBr0B,EAAYkjB,EAAc,CAAEvgB,aAAY0X,eAC5DrZ,EAAOmzB,eAAgB,CAEvB,IAAIG,EAAkBT,GACjBA,GAAenwB,IAChB4wB,EAAkBF,EAAY,IACvBzwB,EACH1C,MAAO0C,GAAsB1C,SAUrCqJ,EAAI,CAAEupB,YAAaS,GACvB,MAGIhqB,EAAI,CAAC,GAELR,GAASnD,OAAS,IACdotB,GACAQ,QAAQC,IAAI,mCAAoC1qB,GAEpDoG,IAAqBpG,GA5BzB,CA6BA,EAEJyQ,oBAAqB,CAACka,EAAe1pB,GAAW,KAC5C,MAAM2pB,EAAuB,GACvB5qB,EAAU,IACV,WAAE9J,EAAU,mBAAEkQ,GAAuB1F,IAC3C,IAAK,MAAOnM,EAAIs2B,KAAaF,EAAe,CAExC,MAAM10B,EAAOC,EAAWwK,IAAInM,GACtBu2B,KAAkB70B,GAAM60B,cAAgB70B,GAAM0O,UAAYkmB,GAAUv3B,UACpEgN,EAAS,CACX/L,KACAgM,KAAM,WACNjN,SAAUw3B,EACJ,CACEjzB,EAAGyW,KAAKyc,IAAI,EAAGF,EAASv3B,SAASuE,GACjCC,EAAGwW,KAAKyc,IAAI,EAAGF,EAASv3B,SAASwE,IAEnC+yB,EAASv3B,SACf2N,YAEA6pB,GAAgB70B,EAAK0O,UACrBimB,EAAqBx0B,KAAK,CACtB7B,KACAoQ,SAAU1O,EAAK0O,SACfqmB,KAAM,IACCH,EAASx0B,UAAUkR,iBACtBhU,MAAOs3B,EAAS1pB,SAAS5N,OAAS,EAClCC,OAAQq3B,EAAS1pB,SAAS3N,QAAU,KAIhDwM,EAAQ5J,KAAKkK,EACjB,CACA,GAAIsqB,EAAqB/tB,OAAS,EAAG,CACjC,MAAM,aAAEuc,EAAY,WAAEvgB,GAAe6H,IAC/BuqB,GAAsB,QAAmBL,EAAsB10B,EAAYkjB,EAAcvgB,GAC/FmH,EAAQ5J,QAAQ60B,EACpB,CACA7kB,EAAmBpG,EAAQ,EAE/BoG,mBAAqBpG,IACjB,MAAM,cAAE0D,EAAa,SAAExL,EAAQ,MAAEf,EAAK,gBAAEsM,EAAe,MAAEwmB,GAAUvpB,IACnE,GAAIV,GAASnD,OAAQ,CACjB,GAAI4G,EAAiB,CAEjBvL,EADqBoJ,EAAiBtB,EAAS7I,GAEnD,CACI8yB,GACAQ,QAAQC,IAAI,mCAAoC1qB,GAEpD0D,IAAgB1D,EACpB,GAEJqG,mBAAqBrG,IACjB,MAAM,cAAE+D,EAAa,SAAE5L,EAAQ,MAAEf,EAAK,gBAAE0M,EAAe,MAAEmmB,GAAUvpB,IACnE,GAAIV,GAASnD,OAAQ,CACjB,GAAIiH,EAAiB,CAEjB3L,EADqBoJ,EAAiBvB,EAAS5I,GAEnD,CACI6yB,GACAQ,QAAQC,IAAI,mCAAoC1qB,GAEpD+D,IAAgB/D,EACpB,GAEJqP,iBAAmBvC,IACf,MAAM,qBAAEyC,EAAoB,WAAE/Y,EAAU,WAAEN,EAAU,mBAAEkQ,EAAkB,mBAAEC,GAAuB3F,IACjG,GAAI6O,EAAJ,CAEInJ,EADoB0G,EAAgBhW,KAAKqR,GAAW3G,EAAsB2G,GAAQ,KAGtF,MACA/B,EAAmB3E,EAAoBvL,EAAY,IAAI0E,IAAI,IAAIkS,KAAmB,IAClFzG,EAAmB5E,EAAoBjL,GAAY,EAEvD6vB,iBAAmBtZ,IACf,MAAM,qBAAEwC,EAAoB,WAAE/Y,EAAU,WAAEN,EAAU,mBAAEkQ,EAAkB,mBAAEC,GAAuB3F,IACjG,GAAI6O,EAAJ,CAEIlJ,EADqB0G,EAAgBjW,KAAKgY,GAAWtN,EAAsBsN,GAAQ,KAGvF,MACAzI,EAAmB5E,EAAoBjL,EAAY,IAAIoE,IAAI,IAAImS,MAC/D3G,EAAmB3E,EAAoBvL,EAAY,IAAI0E,KAAO,GAAM,EAExE0U,sBAAuB,EAAGnY,QAAOC,SAAU,CAAC,KACxC,MAAQA,MAAOsyB,EAAYvyB,MAAOwyB,EAAU,WAAEzzB,EAAU,mBAAEkQ,EAAkB,mBAAEC,GAAuB3F,IAE/FwqB,EAAkB9zB,GAAgBsyB,EAClC7iB,GAFkB1P,GAAgBwyB,GAEJ7yB,KAAKqM,IACrC,MAAMmE,EAAepR,EAAWwK,IAAIyC,EAAE5O,IAQtC,OAPI+S,IAKAA,EAAanR,UAAW,GAErBqL,EAAsB2B,EAAE5O,IAAI,EAAM,IAEvCqS,EAAcskB,EAAgBp0B,KAAKP,GAASiL,EAAsBjL,EAAKhC,IAAI,KACjF6R,EAAmBS,GACnBR,EAAmBO,EAAY,EAEnCxO,WAAaU,IACT,MAAM,QAAEyE,EAAO,QAAExE,GAAY2H,IAC7BnD,GAAS4tB,eAAe,CAACryB,EAASC,IAClCyH,EAAI,CAAE1H,WAAU,EAEpBT,WAAaU,IACT,MAAM,QAAEwE,EAAO,QAAEzE,GAAY4H,IAC7BnD,GAAS4tB,eAAe,CAACryB,EAASC,IAClCyH,EAAI,CAAEzH,WAAU,EAEpBT,mBAAqBM,IACjB8H,IAAMnD,SAASjF,mBAAmBM,GAClC4H,EAAI,CAAE5H,mBAAkB,EAE5BF,qBAAuB0yB,IACnB1qB,IAAMnD,SAAS8tB,iBAAiBD,EAAc,EAElDje,sBAAuB,KACnB,MAAM,MAAE/V,EAAK,MAAED,EAAK,mBAAEiP,EAAkB,mBAAEC,GAAuB3F,IAC3DmG,EAAc1P,EAAMmE,QAAO,CAACC,EAAKtF,IAAUA,EAAKE,SAAW,IAAIoF,EAAKiG,EAAsBvL,EAAK1B,IAAI,IAAUgH,GAAM,IACnHqL,EAAcxP,EAAMkE,QAAO,CAACC,EAAKhF,IAAUA,EAAKJ,SAAW,IAAIoF,EAAKiG,EAAsBjL,EAAKhC,IAAI,IAAUgH,GAAM,IACzH6K,EAAmBS,GACnBR,EAAmBO,EAAY,EAEnCrO,cAAgB+yB,IACZ,MAAM,MAAEn0B,EAAK,WAAEjB,EAAU,aAAEkjB,EAAY,WAAEvgB,EAAU,qBAAEixB,EAAoB,WAAEvZ,GAAe7P,IACtF4qB,EAAe,GAAG,KAAO/a,EAAW,GAAG,IACvC+a,EAAe,GAAG,KAAO/a,EAAW,GAAG,IACvC+a,EAAe,GAAG,KAAO/a,EAAW,GAAG,IACvC+a,EAAe,GAAG,KAAO/a,EAAW,GAAG,MAG3C,QAAepZ,EAAOjB,EAAYkjB,EAAc,CAC5CvgB,aACA0X,WAAY+a,EACZxB,uBACAM,eAAe,IAEnB5pB,EAAI,CAAE+P,WAAY+a,IAAiB,EAEvCzX,MAAQ0X,IACJ,MAAM,UAAEttB,EAAS,MAAE1K,EAAK,OAAEC,EAAM,QAAE+J,EAAO,gBAAE3E,GAAoB8H,IAC/D,OAAO,QAAM,CAAE6qB,QAAOhuB,UAASU,YAAWrF,kBAAiBrF,QAAOC,UAAS,EAE/EiL,QAAUpE,IACN,MAAM,QAAEkD,EAAO,MAAEhK,EAAK,OAAEC,EAAM,QAAEsF,EAAO,QAAEC,EAAO,WAAE7C,GAAewK,IACjE,IAAKnD,EACD,OAAOG,QAAQC,SAAQ,GAE3B,MAAMgB,GAAe,QAAgBzI,EAAYmE,GACjD,OAAO,QAAQ,CACXlD,MAAOwH,EACPpL,QACAC,SACA+J,UACAzE,UACAC,WACDsB,EAAQ,EAMfiwB,YAAcjwB,IACV,MAAM,QAAEkD,EAAO,MAAEhK,EAAK,OAAEC,EAAM,QAAEsF,EAAO,QAAEC,EAAO,WAAE7C,GAAewK,IACjE,IAAKnD,EACD,OAAO,EAEX,MAAMoB,GAAe,QAAgBzI,EAAYmE,GASjD,OARA,QAAQ,CACJlD,MAAOwH,EACPpL,QACAC,SACA+J,UACAzE,UACAC,WACDsB,GACIsE,EAAa7B,KAAO,CAAC,EAEhCgX,iBAAkB,KACdtT,EAAI,CACAuS,WAAY,IAAK,OACnB,EAENkB,iBAAmBlB,IACfvS,EAAI,CAAEuS,cAAa,EAEvBva,MAAO,IAAMgI,EAAI,IAAKipB,UACtBtS,OAAOqU,IAoCX,SAASC,IAAoBC,aAAcv0B,EAAOw0B,aAAcv0B,EAAK,aAAEiC,EAAY,aAAEC,EAAcqhB,aAAcpnB,EAAOqnB,cAAepnB,EAAM,QAAEiL,EAAO,WAAE5F,EAAU,WAAE0X,EAAU,SAAE/b,IAC5K,MAAO7B,IAAS,IAAA8H,WAAS,IAAMyvB,GAAY,CACvC/yB,QACAC,QACAiC,eACAC,eACA/F,QACAC,SACAiL,UACA5F,aACA0X,iBAEJ,OAAQ,IAAAjc,KAAIjC,EAAY,CAAE2R,MAAOrR,EAAO6B,UAAU,IAAAF,KAAIgP,GAAe,CAAE9O,SAAUA,KACrF,CAEA,SAASo3B,IAAQ,SAAEp3B,EAAQ,MAAE2C,EAAK,MAAEC,EAAK,aAAEiC,EAAY,aAAEC,EAAY,MAAE/F,EAAK,OAAEC,EAAM,QAAEiL,EAAO,WAAE5F,EAAU,WAAE0X,IAEvG,OADkB,IAAA3d,YAAWT,IAMlB,IAAAmC,KAAI,EAAAM,SAAU,CAAEJ,SAAUA,KAE7B,IAAAF,KAAIm3B,GAAmB,CAAEC,aAAcv0B,EAAOw0B,aAAcv0B,EAAOiC,aAAcA,EAAcC,aAAcA,EAAcqhB,aAAcpnB,EAAOqnB,cAAepnB,EAAQiL,QAASA,EAAS5F,WAAYA,EAAY0X,WAAYA,EAAY/b,SAAUA,GAC/P,CAEA,MAAMq3B,GAAe,CACjBt4B,MAAO,OACPC,OAAQ,OACRI,SAAU,SACVN,SAAU,WACV+nB,OAAQ,GAgCZ,IAAIta,GAAQwB,GA9BZ,UAAmB,MAAEpL,EAAK,MAAEC,EAAK,aAAEiC,EAAY,aAAEC,EAAY,UAAErE,EAAS,UAAEikB,EAAS,UAAE6L,EAAS,YAAEtI,EAAW,YAAEkK,EAAW,OAAEsC,EAAM,OAAEje,EAAM,YAAEF,EAAW,UAAEK,EAAS,UAAE6G,EAAS,eAAE+B,EAAc,aAAEC,EAAY,oBAAEa,EAAmB,kBAAEC,EAAiB,iBAAE4H,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAEC,EAAiB,kBAAEC,EAAiB,gBAAEgP,EAAe,WAAEC,EAAU,eAAEC,EAAc,cAAE9lB,EAAa,cAAEC,EAAa,SAAEG,EAAQ,kBAAEtP,EAAiB,qBAAEi1B,EAAoB,gBAAEC,EAAe,oBAAEC,EAAmB,uBAAEtV,EAAsB,iBAAEzK,EAAgB,eAAEC,EAAc,eAAE9F,EAAc,eAAEuM,EAAc,mBAAEoV,EAAqB,KAAmBJ,OAAM,oBAAEK,EAAmB,wBAAEe,EAAuB,6BAAEC,EAA4B,cAAEzR,EAAgB,YAAW,iBAAEC,GAAmB,QAAO,gBAAExL,IAAkB,EAAK,cAAEF,GAAgB,KAAcC,KAAI,qBAAE2L,GAAuB,QAAO,sBAAED,KAAwB,UAAY,OAAS,WAAS,sBAAElO,KAAwB,UAAY,OAAS,WAAS,WAAErK,GAAU,SAAED,GAAQ,0BAAE+c,IAA4B,EAAK,kBAAEX,GAAiB,eAAEhL,GAAc,iBAAEwI,GAAgB,eAAEC,GAAc,WAAEpgB,GAAalB,EAAiB,eAAEktB,GAAc,mBAAEC,GAAkB,mBAAE9rB,IAAqB,EAAMpB,gBAAiBw0B,GAAoBx0B,EAAe,QAAEkB,GAAU,GAAG,QAAEC,GAAU,EAAC,gBAAEH,GAAkB,KAAc,iBAAE+Q,IAAmB,EAAI,WAAE4G,GAAU,mBAAEmW,GAAqB,UAAS,aAAExd,IAAe,EAAI,YAAEC,IAAc,EAAI,YAAEC,IAAc,EAAK,iBAAEC,GAAmB,GAAG,gBAAEC,GAAkB,KAAgBC,KAAI,kBAAEC,IAAoB,EAAI,UAAEC,IAAY,EAAI,YAAE6C,GAAW,iBAAEE,GAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,aAAEH,GAAY,kBAAEtD,GAAiB,kBAAE/P,GAAoB,EAAC,kBAAE6W,GAAoB,EAAC,SAAEvb,GAAQ,YAAEuvB,GAAW,iBAAEC,GAAgB,eAAEC,GAAc,kBAAE6B,GAAiB,kBAAED,GAAiB,iBAAEE,GAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,gBAAEnC,GAAkB,GAAE,cAAEpgB,GAAa,cAAEK,GAAa,gBAAE6L,GAAkB,SAAQ,iBAAEhG,GAAmB,UAAS,eAAE3Q,GAAiB,QAAO,QAAEwF,GAAO,eAAE4tB,GAAc,eAAE7a,GAAc,oBAAE8a,GAAmB,WAAE92B,GAAU,mBAAE0Y,GAAkB,qBAAE4b,GAAoB,qBAAEpE,GAAoB,oBAAEhxB,IAAsB,EAAK,iBAAEgf,GAAgB,kBAAEsW,GAAiB,aAAE5V,GAAY,iBAAET,GAAgB,kBAAE/B,GAAiB,QAAE1H,GAAO,MAAE/W,GAAK,GAAEoB,GAAE,kBAAEknB,GAAiB,SAAErd,GAAQ,iBAAEyL,GAAgB,MAAEtW,GAAK,OAAEC,GAAM,UAAE+4B,GAAY,QAAO,MAAEtC,GAAK,SAAEuC,MAAat3B,IAAQC,IACzzE,MAAMd,GAAOE,IAAM,IACbk4B,GAx9FV,SAA2BF,GACvB,MAAOG,EAAgBC,IAAqB,IAAAlyB,UAAuB,WAAd8xB,EAAyB,KAAOA,GAcrF,OAbA,IAAAt1B,YAAU,KACN,GAAkB,WAAds1B,EAEA,YADAI,EAAkBJ,GAGtB,MAAMK,EAAa9yB,IACb+yB,EAAuB,IAAMF,EAAkBC,GAAYE,QAAU,OAAS,SAGpF,OAFAD,IACAD,GAAYlwB,iBAAiB,SAAUmwB,GAChC,KACHD,GAAYjwB,oBAAoB,SAAUkwB,EAAqB,CAClE,GACF,CAACN,IACsB,OAAnBG,EAA0BA,EAAiB5yB,KAAiBgzB,QAAU,OAAS,OAC1F,CAw8F+BC,CAAkBR,IAEvCS,IAAkB,IAAAxpB,cAAagC,IACjCA,EAAEynB,cAAcC,SAAS,CAAEtkB,IAAK,EAAGC,KAAM,EAAGskB,SAAU,YACtDX,KAAWhnB,EAAE,GACd,CAACgnB,KACJ,OAAQ,IAAAl4B,KAAI,MAAO,CAAE,cAAe,iBAAkBY,GAAMs3B,SAAUQ,GAAiB75B,MAAO,IAAKA,MAAU04B,IAAgB12B,IAAKA,GAAKF,WAAW,OAAG,CAAC,aAAcA,EAAWw3B,KAAsBl4B,GAAIA,GAAIC,UAAU,IAAAG,MAAKi3B,GAAS,CAAEz0B,MAAOA,EAAOC,MAAOA,EAAO7D,MAAOA,GAAOC,OAAQA,GAAQiL,QAASA,GAAS5F,WAAYA,GAAY0X,WAAYA,GAAY/b,SAAU,EAAC,IAAAF,KAAIk1B,GAAW,CAAEP,OAAQA,EAAQxM,YAAaA,EAAakK,YAAaA,EAAajK,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBC,kBAAmBA,EAAmBC,kBAAmBA,EAAmB5D,UAAWA,EAAW6L,UAAWA,EAAWmD,mBAAoBA,EAAoBC,oBAAqBA,EAAqBe,wBAAyBA,EAAyBC,6BAA8BA,EAA8BxR,iBAAkBA,GAAkBxL,gBAAiBA,GAAiBF,cAAeA,GAAeyL,cAAeA,EAAeE,sBAAuBA,GAAuBC,qBAAsBA,GAAsBnO,sBAAuBA,GAAuByS,0BAA2BA,GAA2BvkB,gBAAiBw0B,GAAmBxzB,gBAAiBA,GAAiBE,QAASA,GAASC,QAASA,GAAS4Q,iBAAkBA,GAAkBT,aAAcA,GAAcC,YAAaA,GAAaK,kBAAmBA,GAAmBJ,YAAaA,GAAaC,iBAAkBA,GAAkBC,gBAAiBA,GAAiBG,UAAWA,GAAW6C,YAAaA,GAAaE,iBAAkBA,GAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBH,aAAcA,GAActD,kBAAmBA,GAAmB/P,kBAAmBA,GAAmB6W,kBAAmBA,GAAmB8G,uBAAwBA,EAAwBzK,iBAAkBA,EAAkBC,eAAgBA,EAAgB0X,YAAaA,GAAaC,iBAAkBA,GAAkBC,eAAgBA,GAAgB6B,kBAAmBA,GAAmBD,kBAAmBA,GAAmBE,iBAAkBA,GAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBnC,gBAAiBA,GAAiB4C,mBAAoBA,GAAoB9W,gBAAiBA,GAAiBhG,iBAAkBA,GAAkB3Q,eAAgBA,GAAgB5E,KAAMA,GAAMK,oBAAqBA,GAAqB6b,WAAYA,GAAYnS,SAAUA,GAAUyL,iBAAkBA,MAAqB,IAAAvV,KAAI6E,EAAc,CAAEhC,MAAOA,EAAOC,MAAOA,EAAOiC,aAAcA,EAAcC,aAAcA,EAAc0Y,UAAWA,EAAW+B,eAAgBA,EAAgBC,aAAcA,EAAca,oBAAqBA,EAAqBC,kBAAmBA,EAAmBtE,eAAgBA,GAAgBwI,iBAAkBA,GAAkBC,eAAgBA,GAAgB4L,eAAgBA,GAAgBC,mBAAoBA,GAAoB9rB,mBAAoBA,GAAoB8wB,qBAAsBA,GAAsBpE,qBAAsBA,GAAsB5sB,QAASA,GAASC,QAASA,GAASwX,WAAYA,GAAY7M,cAAeA,GAAeK,cAAeA,GAAe1E,WAAYA,GAAYD,SAAUA,GAAU0T,eAAgBA,EAAgBla,gBAAiBA,GAAiB4Y,eAAgBA,GAAgBtD,mBAAoBA,GAAoBzP,QAASA,GAAS4tB,eAAgBA,GAAgBnmB,cAAeA,EAAeC,cAAeA,EAAeG,SAAUA,EAAUwlB,gBAAiBA,EAAiBC,WAAYA,EAAYC,eAAgBA,EAAgBE,gBAAiBA,EAAiBD,qBAAsBA,EAAsBE,oBAAqBA,EAAqBnhB,OAAQA,EAAQF,YAAaA,EAAaK,UAAWA,EAAWlS,eAAgBA,GAAgBJ,WAAYA,GAAYxE,KAAMA,GAAMqf,iBAAkBA,GAAkBsW,kBAAmBA,GAAmB5V,aAAcA,GAAclK,QAASA,GAASyJ,iBAAkBA,GAAkB/B,kBAAmBA,GAAmB4J,kBAAmBA,GAAmBC,kBAAmBA,GAAmBlV,eAAgBA,EAAgBrN,kBAAmBA,GAAmB+wB,MAAOA,MAAU,IAAA31B,KAAImD,EAAmB,CAAET,kBAAmBA,IAAsBxC,IAAU,IAAAF,KAAIiB,EAAa,CAAEC,WAAYA,GAAYlC,SAAUg5B,MAAwB,IAAAh4B,KAAIG,EAAkB,CAAEJ,KAAMA,GAAMK,oBAAqBA,SAC/0I,IAuBA,MAAM04B,GAAcl5B,GAAMA,EAAEwK,SAAS2uB,cAAc,mCAyCnD,SAASC,IAAkB,SAAE94B,IACzB,MAAM+4B,EAAoB/6B,EAAS46B,IACnC,OAAKG,GAGE,IAAAC,cAAah5B,EAAU+4B,GAFnB,IAGf,CA6NA,SAASE,GAAc/B,GACnB,MAAOv0B,EAAOe,IAAY,IAAAuC,UAASixB,GAC7BhoB,GAAgB,IAAAF,cAAaxD,GAAY9H,GAAUw1B,GAAQpsB,EAAiBtB,EAAS0tB,MAAO,IAClG,MAAO,CAACv2B,EAAOe,EAAUwL,EAC7B,CAsCA,SAASiqB,GAAchC,GACnB,MAAOv0B,EAAOe,IAAY,IAAAsC,UAASkxB,GAC7B5nB,GAAgB,IAAAP,cAAaxD,GAAY7H,GAAUy1B,GAAQrsB,EAAiBvB,EAAS4tB,MAAO,IAClG,MAAO,CAACx2B,EAAOe,EAAU4L,EAC7B,CA8KiB,KAAwB,WAqGzC,SAAS8pB,IAAY,WAAE3sB,EAAU,UAAE4sB,EAAS,QAAEC,EAAO,UAAE94B,IACnD,OAAQ,IAAAX,KAAI,OAAQ,CAAE6oB,YAAa2Q,EAAW5N,EAAG,IAAIhf,EAAW,GAAK,QAAQA,EAAW,SAASA,EAAW,GAAK,MAAMA,EAAW,KAAMjM,WAAW,OAAG,CAAC,iCAAkC84B,EAAS94B,KACtM,CACA,SAAS+4B,IAAW,OAAEzK,EAAM,UAAEtuB,IAC1B,OAAQ,IAAAX,KAAI,SAAU,CAAEmvB,GAAIF,EAAQG,GAAIH,EAAQI,EAAGJ,EAAQtuB,WAAW,OAAG,CAAC,iCAAkC,OAAQA,KACxH,CAQA,IAAIg5B,IACJ,SAAWA,GACPA,EAAyB,MAAI,QAC7BA,EAAwB,KAAI,OAC5BA,EAAyB,MAAI,OAChC,CAJD,CAIGA,KAAsBA,GAAoB,CAAC,IAE9C,MAAMC,GAAc,CAChB,CAACD,GAAkBE,MAAO,EAC1B,CAACF,GAAkBG,OAAQ,EAC3B,CAACH,GAAkBI,OAAQ,GAEzBC,GAAcp6B,IAAM,CAAG+J,UAAW/J,EAAE+J,UAAWswB,UAAW,WAAWr6B,EAAEG,SAC7E,SAASm6B,IAAoB,GAAEj6B,EAAE,QAAEw5B,EAAUE,GAAkBE,KAAI,IAEnEM,EAAM,GAAE,KAER3xB,EAAI,UAAEgxB,EAAY,EAAC,OAAE/L,EAAS,EAAC,MAAE7E,EAAK,QAAEwR,EAAO,MAAEv7B,EAAK,UAAE8B,EAAS,iBAAE05B,IAC/D,MAAMx5B,GAAM,IAAAsE,QAAO,OACb,UAAEwE,EAAS,UAAEswB,GAAc/7B,EAAS87B,GAAY,KAChDM,EAAc9xB,GAAQoxB,GAAYH,GAClCc,EAASd,IAAYE,GAAkBE,KACvCW,EAAUf,IAAYE,GAAkBI,MACxCU,EAAQ/zB,MAAMC,QAAQwzB,GAAOA,EAAM,CAACA,EAAKA,GACzCO,EAAY,CAACD,EAAM,GAAK9wB,EAAU,IAAM,EAAG8wB,EAAM,GAAK9wB,EAAU,IAAM,GACtEgxB,EAAaL,EAAc3wB,EAAU,GACrCixB,EAAWl0B,MAAMC,QAAQ8mB,GAAUA,EAAS,CAACA,EAAQA,GACrDoN,EAAoBL,EAAU,CAACG,EAAYA,GAAcD,EACzDI,EAAe,CACjBF,EAAS,GAAKjxB,EAAU,IAAM,EAAIkxB,EAAkB,GAAK,EACzDD,EAAS,GAAKjxB,EAAU,IAAM,EAAIkxB,EAAkB,GAAK,GAEvDE,EAAa,GAAGd,IAAYh6B,GAAU,KAC5C,OAAQ,IAAAI,MAAK,MAAO,CAAEM,WAAW,OAAG,CAAC,yBAA0BA,IAAa9B,MAAO,IACxEA,KACAwV,GACH,8BAA+B+lB,EAC/B,sCAAuCxR,GACxC/nB,IAAKA,EAAK,cAAe,iBAAkBX,SAAU,EAAC,IAAAF,KAAI,UAAW,CAAEC,GAAI86B,EAAYx3B,EAAGoG,EAAU,GAAK+wB,EAAU,GAAIl3B,EAAGmG,EAAU,GAAK+wB,EAAU,GAAIz7B,MAAOy7B,EAAU,GAAIx7B,OAAQw7B,EAAU,GAAIM,aAAc,iBAAkBC,iBAAkB,cAAcH,EAAa,OAAOA,EAAa,MAAO56B,SAAUq6B,GAAU,IAAAv6B,KAAI05B,GAAY,CAAEzK,OAAQ0L,EAAa,EAAGh6B,UAAW05B,KAAwB,IAAAr6B,KAAIu5B,GAAa,CAAE3sB,WAAYiuB,EAAmBrB,UAAWA,EAAWC,QAASA,EAAS94B,UAAW05B,OAAyB,IAAAr6B,KAAI,OAAQ,CAAEuD,EAAG,IAAKC,EAAG,IAAKvE,MAAO,OAAQC,OAAQ,OAAQ+pB,KAAM,QAAQ8R,SAC/lB,CACAb,GAAoB34B,YAAc,aAsDlC,MAAM25B,IAAa,IAAA9d,MAAK8c,IAExB,SAASiB,KACL,OAAQ,IAAAn7B,KAAI,MAAO,CAAEo7B,MAAO,6BAA8BzR,QAAS,YAAazpB,UAAU,IAAAF,KAAI,OAAQ,CAAE4rB,EAAG,2EAC/G,CAEA,SAASyP,KACL,OAAQ,IAAAr7B,KAAI,MAAO,CAAEo7B,MAAO,6BAA8BzR,QAAS,WAAYzpB,UAAU,IAAAF,KAAI,OAAQ,CAAE4rB,EAAG,oBAC9G,CAEA,SAAS0P,KACL,OAAQ,IAAAt7B,KAAI,MAAO,CAAEo7B,MAAO,6BAA8BzR,QAAS,YAAazpB,UAAU,IAAAF,KAAI,OAAQ,CAAE4rB,EAAG,iYAC/G,CAEA,SAAS2P,KACL,OAAQ,IAAAv7B,KAAI,MAAO,CAAEo7B,MAAO,6BAA8BzR,QAAS,YAAazpB,UAAU,IAAAF,KAAI,OAAQ,CAAE4rB,EAAG,ocAC/G,CAEA,SAAS4P,KACL,OAAQ,IAAAx7B,KAAI,MAAO,CAAEo7B,MAAO,6BAA8BzR,QAAS,YAAazpB,UAAU,IAAAF,KAAI,OAAQ,CAAE4rB,EAAG,0YAC/G,CAyBA,SAAS6P,IAAc,SAAEv7B,EAAQ,UAAES,KAAcC,IAC7C,OAAQ,IAAAZ,KAAI,SAAU,CAAEiM,KAAM,SAAUtL,WAAW,OAAG,CAAC,8BAA+BA,OAAgBC,EAAMV,SAAUA,GAC1H,CAEA,MAAMw7B,GAAc97B,IAAM,CACtB+7B,cAAe/7B,EAAEsc,gBAAkBtc,EAAE8kB,kBAAoB9kB,EAAE8E,mBAC3Dk3B,eAAgBh8B,EAAE+J,UAAU,IAAM/J,EAAE4E,QACpCq3B,eAAgBj8B,EAAE+J,UAAU,IAAM/J,EAAE6E,UAExC,SAASq3B,IAAkB,MAAEj9B,EAAK,SAAEk9B,GAAW,EAAI,YAAEC,GAAc,EAAI,gBAAEC,GAAkB,EAAI,eAAElE,EAAc,SAAEmE,EAAQ,UAAEC,EAAS,UAAEC,EAAS,oBAAEC,EAAmB,UAAE17B,EAAS,SAAET,EAAQ,SAAElB,EAAW,cAAa,YAAEs9B,EAAc,WAAY,aAAchV,EAAY,wBACrQ,MAAMjpB,EAAQG,KACR,cAAEm9B,EAAa,eAAEC,EAAc,eAAEC,GAAmB39B,EAASw9B,GAAY,MACzE,OAAE1yB,EAAM,QAAEM,EAAO,QAAEa,GAAYyF,KAqB/B2sB,EAAmC,eAAhBD,EAA+B,aAAe,WACvE,OAAQ,IAAAj8B,MAAKI,EAAO,CAAEE,WAAW,OAAG,CAAC,uBAAwB47B,EAAkB57B,IAAa3B,SAAUA,EAAUH,MAAOA,EAAO,cAAe,eAAgB,aAAcyoB,EAAWpnB,SAAU,CAAC67B,IAAa,IAAA17B,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAIy7B,GAAe,CAAE7iB,QArBvO,KACpB5P,IACAkzB,KAAY,EAmByQv7B,UAAW,8BAA+B67B,MAAO,UAAW,aAAc,UAAWnhB,SAAUwgB,EAAgB37B,UAAU,IAAAF,KAAIm7B,GAAU,CAAC,MAAO,IAAAn7B,KAAIy7B,GAAe,CAAE7iB,QAjBpa,KACrBtP,IACA6yB,KAAa,EAeucx7B,UAAW,+BAAgC67B,MAAO,WAAY,aAAc,WAAYnhB,SAAUugB,EAAgB17B,UAAU,IAAAF,KAAIq7B,GAAW,CAAC,QAAYW,IAAgB,IAAAh8B,KAAIy7B,GAAe,CAAE96B,UAAW,+BAAgCiY,QAbvqB,KACrBzO,EAAQ4tB,GACRqE,KAAa,EAW0sBI,MAAO,WAAY,aAAc,WAAYt8B,UAAU,IAAAF,KAAIs7B,GAAa,CAAC,KAAQW,IAAoB,IAAAj8B,KAAIy7B,GAAe,CAAE96B,UAAW,mCAAoCiY,QATt2B,KAC1Bva,EAAMM,SAAS,CACXud,gBAAiByf,EACjBjX,kBAAmBiX,EACnBj3B,oBAAqBi3B,IAEzBU,KAAuBV,EAAc,EAG23Ba,MAAO,uBAAwB,aAAc,uBAAwBt8B,SAAUy7B,GAAgB,IAAA37B,KAAIw7B,GAAY,CAAC,IAAK,IAAAx7B,KAAIu7B,GAAU,CAAC,KAAQr7B,IACpjC,CACA47B,GAAkBv6B,YAAc,YAsBf,IAAA6b,MAAK0e,IAWtB,MAAMW,IAAc,IAAArf,OATpB,UAA8B,GAAEnd,EAAE,EAAEsD,EAAC,EAAEC,EAAC,MAAEvE,EAAK,OAAEC,EAAM,MAAEL,EAAK,MAAE+pB,EAAK,YAAE8T,EAAW,YAAE7T,EAAW,UAAEloB,EAAS,aAAE6sB,EAAY,eAAEmP,EAAc,SAAE96B,EAAQ,QAAE+W,IAChJ,MAAM,WAAEgkB,EAAU,gBAAEC,GAAoBh+B,GAAS,CAAC,EAC5CoqB,EAAQL,GAASgU,GAAcC,EACrC,OAAQ,IAAA78B,KAAI,OAAQ,CAAEW,WAAW,OAAG,CAAC,2BAA4B,CAAEkB,YAAYlB,IAAa4C,EAAGA,EAAGC,EAAGA,EAAG2nB,GAAIqC,EAAcpC,GAAIoC,EAAcvuB,MAAOA,EAAOC,OAAQA,EAAQL,MAAO,CACzKoqB,OACAH,OAAQ4T,EACR7T,eACD8T,eAAgBA,EAAgB/jB,QAASA,EAAWvR,GAAUuR,EAAQvR,EAAOpH,QAAMuM,GAC9F,IAGMswB,GAAmBl9B,GAAMA,EAAEiD,MAAML,KAAKb,GAASA,EAAK1B,KACpD88B,GAAmBC,GAASA,aAAgBC,SAAWD,EAAO,IAAMA,EAwC1E,MAAME,IAAuB,IAAA9f,OAlB7B,UAAmC,GAAEnd,EAAE,cAAEk9B,EAAa,oBAAEC,EAAmB,kBAAEC,EAAiB,iBAAEC,EAAgB,gBAAEC,EAAe,eAAEZ,EAAc,cAAE3X,EAAa,QAAEpM,IAC9J,MAAM,KAAEjX,EAAI,EAAE4B,EAAC,EAAEC,EAAC,MAAEvE,EAAK,OAAEC,GAAWhB,GAAU0B,IAC5C,MAAM+B,EAAO/B,EAAEgC,WAAWwK,IAAInM,IACxB,EAAEsD,EAAC,EAAEC,GAAM7B,EAAKI,UAAUkR,kBAC1B,MAAEhU,EAAK,OAAEC,IAAW,QAAkByC,GAC5C,MAAO,CACHA,OACA4B,IACAC,IACAvE,QACAC,SACH,GACF,KACH,OAAKyC,IAAQA,EAAKgkB,SAAW,QAAkBhkB,IAGvC,IAAA3B,KAAIglB,EAAe,CAAEzhB,EAAGA,EAAGC,EAAGA,EAAGvE,MAAOA,EAAOC,OAAQA,EAAQL,MAAO8C,EAAK9C,MAAOgD,WAAYF,EAAKE,SAAUlB,UAAW08B,EAAkB17B,GAAOinB,MAAOuU,EAAcx7B,GAAO6rB,aAAc8P,EAAkBZ,YAAaU,EAAoBz7B,GAAOknB,YAAa0U,EAAiBZ,eAAgBA,EAAgB/jB,QAASA,EAAS3Y,GAAI0B,EAAK1B,KAF3U,IAGf,IAEA,IAAIu9B,IAAiB,IAAApgB,OAxCrB,UAAsB,gBAAEqgB,EAAe,UAAEC,EAAS,cAAEC,EAAgB,GAAE,iBAAEL,EAAmB,EAAC,gBAAEC,EAK9FK,cAAe5Y,EAAgByX,GAAW,QAAE7jB,IACxC,MAAMgP,EAAU1pB,EAAS4+B,GAAiB,KACpCK,EAAgBJ,GAAgBW,GAChCN,EAAsBL,GAAgBU,GACtCJ,EAAoBN,GAAgBY,GACpChB,EAAmC,oBAAXl3B,QAA4BA,OAAOo4B,OAAS,aAAe,qBACzF,OAAQ,IAAA79B,KAAI,EAAAM,SAAU,CAAEJ,SAAU0nB,EAAQplB,KAAKqR,IAQ3C,IAAA7T,KAAIk9B,GAAsB,CAAEj9B,GAAI4T,EAAQspB,cAAeA,EAAeC,oBAAqBA,EAAqBC,kBAAmBA,EAAmBC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBvY,cAAeA,EAAepM,QAASA,EAAS+jB,eAAgBA,GAAkB9oB,MACtT,IAsBA,MAEMiqB,GAAcl+B,IAChB,MAAMm+B,EAAS,CACXx6B,GAAI3D,EAAE+J,UAAU,GAAK/J,EAAE+J,UAAU,GACjCnG,GAAI5D,EAAE+J,UAAU,GAAK/J,EAAE+J,UAAU,GACjC1K,MAAOW,EAAEX,MAAQW,EAAE+J,UAAU,GAC7BzK,OAAQU,EAAEV,OAASU,EAAE+J,UAAU,IAEnC,MAAO,CACHo0B,SACAC,aAAcp+B,EAAEgC,WAAW4G,KAAO,GAAI,SAAiB,QAAuB5I,EAAEgC,YAAam8B,GAAUA,EACvGh+B,KAAMH,EAAEG,KACRkJ,QAASrJ,EAAEqJ,QACX3E,gBAAiB1E,EAAE0E,gBACnB25B,UAAWr+B,EAAEX,MACbi/B,WAAYt+B,EAAEV,OACjB,EAGL,SAASi/B,IAAiB,MAAEt/B,EAAK,UAAE8B,EAAS,gBAAE88B,EAAe,UAAEC,EAAS,cAAEC,EAAgB,GAAE,iBAAEL,EAAmB,EAAC,gBAAEC,EAAe,cAKnIK,EAAa,QAAExD,EAAO,UAAEgE,EAAS,gBAAEC,EAAe,gBAAEC,EAAe,SAAEt/B,EAAW,eAAc,QAAE4Z,EAAO,YAAEuP,EAAW,SAAEoW,GAAW,EAAK,SAAEC,GAAW,EAAK,UAAElX,EAAY,sBAAqB,WAAEmX,EAAU,SAAEC,EAAW,GAAE,YAAEC,EAAc,IAClO,MAAMtgC,EAAQG,IACRogC,GAAM,IAAAz5B,QAAO,OACb,aAAE64B,EAAY,OAAED,EAAM,KAAEh+B,EAAI,QAAEkJ,EAAO,gBAAE3E,EAAe,UAAE25B,EAAS,WAAEC,GAAehgC,EAAS4/B,GAAY,KACvGe,EAAehgC,GAAOI,OA7BX,IA8BX6/B,EAAgBjgC,GAAOK,QA7BX,IA8BZ6/B,EAAcf,EAAa/+B,MAAQ4/B,EACnCG,EAAehB,EAAa9+B,OAAS4/B,EACrCG,EAAYjlB,KAAKyc,IAAIsI,EAAaC,GAClCE,EAAYD,EAAYJ,EACxBM,EAAaF,EAAYH,EACzBrR,EAASkR,EAAcM,EACvB17B,EAAIy6B,EAAaz6B,GAAK27B,EAAYlB,EAAa/+B,OAAS,EAAIwuB,EAC5DjqB,EAAIw6B,EAAax6B,GAAK27B,EAAanB,EAAa9+B,QAAU,EAAIuuB,EAC9DxuB,EAAQigC,EAAqB,EAATzR,EACpBvuB,EAASigC,EAAsB,EAAT1R,EACtB2R,EAAa,4BAAqBr/B,IAClCs/B,GAAe,IAAAl6B,QAAO,GACtBm6B,GAAkB,IAAAn6B,UACxBk6B,EAAan6B,QAAU+5B,GACvB,IAAAt8B,YAAU,KACN,GAAIi8B,EAAI15B,SAAW+D,EAOf,OANAq2B,EAAgBp6B,SAAU,QAAU,CAChCkF,QAASw0B,EAAI15B,QACb+D,UACA2W,aAAc,IAAMvhB,EAAMK,WAAWiL,UACrC41B,aAAc,IAAMF,EAAan6B,UAE9B,KACHo6B,EAAgBp6B,SAAS6R,SAAS,CAE1C,GACD,CAAC9N,KACJ,IAAAtG,YAAU,KACN28B,EAAgBp6B,SAAS8R,OAAO,CAC5B1S,kBACArF,MAAOg/B,EACP/+B,OAAQg/B,EACRO,aACAF,WACAG,WACAF,YACF,GACH,CAACD,EAAUC,EAAUC,EAAYC,EAAUp6B,EAAiB25B,EAAWC,IAC1E,MAAMsB,EAAa5mB,EACZvR,IACC,MAAO9D,EAAGC,GAAK87B,EAAgBp6B,SAASu6B,QAAQp4B,IAAU,CAAC,EAAG,GAC9DuR,EAAQvR,EAAO,CAAE9D,IAAGC,KAAI,OAE1BgJ,EACAkzB,GAAiBvX,GACjB,IAAAjZ,cAAY,CAAC7H,EAAOwM,KAClB,MAAMlS,EAAOtD,EAAMK,WAAWkD,WAAWwK,IAAIyH,GAC7CsU,EAAY9gB,EAAO1F,EAAK,GACzB,SACD6K,EACN,OAAQ,IAAAxM,KAAIS,EAAO,CAAEzB,SAAUA,EAAUH,MAAO,IACrCA,EACH,sCAA0D,iBAAZu7B,EAAuBA,OAAU5tB,EAC/E,2CAAiE,iBAAd4xB,EAAyBA,OAAY5xB,EACxF,uCAAmE,iBAApB6xB,EAA+BA,OAAkB7xB,EAChG,uCAAmE,iBAApB8xB,EAA+BA,EAAkBW,OAAYzyB,EAC5G,2CAAiE,iBAAdkxB,EAAyBA,OAAYlxB,EACxF,uCAAmE,iBAApBixB,EAA+BA,OAAkBjxB,EAChG,uCAAmE,iBAApB+wB,EAA+BA,OAAkB/wB,GACjG7L,WAAW,OAAG,CAAC,sBAAuBA,IAAa,cAAe,cAAeT,UAAU,IAAAG,MAAK,MAAO,CAAEpB,MAAO4/B,EAAc3/B,OAAQ4/B,EAAenV,QAAS,GAAGpmB,KAAKC,KAAKvE,KAASC,IAAUyB,UAAW,0BAA2B0mB,KAAM,MAAO,kBAAmB+X,EAAYv+B,IAAK+9B,EAAKhmB,QAAS4mB,EAAYt/B,SAAU,CAAConB,IAAa,IAAAtnB,KAAI,QAAS,CAAEC,GAAIm/B,EAAYl/B,SAAUonB,KAAc,IAAAtnB,KAAIw9B,GAAgB,CAAE5kB,QAAS8mB,GAAgBhC,UAAWA,EAAWD,gBAAiBA,EAAiBH,iBAAkBA,EAAkBK,cAAeA,EAAeJ,gBAAiBA,EAAiBK,cAAeA,KAAkB,IAAA59B,KAAI,OAAQ,CAAEW,UAAW,2BAA4BirB,EAAG,IAAIroB,EAAIkqB,KAAUjqB,EAAIiqB,KAAUxuB,EAAiB,EAATwuB,KAAcvuB,EAAkB,EAATuuB,MAAexuB,EAAiB,EAATwuB,gBACxvBsQ,EAAOx6B,KAAKw6B,EAAOv6B,KAAKu6B,EAAO9+B,SAAS8+B,EAAO7+B,WAAW6+B,EAAO9+B,SAAU0gC,SAAU,UAAW7+B,cAAe,aAC1H,CACAq9B,GAAiB58B,YAAc,UAqB/B,MAAMq+B,IAAU,IAAAxiB,MAAK+gB,KA0IK,IAAA/gB,OAxI1B,UAAuB,OAAEvJ,EAAM,SAAE7U,EAAQ,QAAEy6B,EAAU,KAAqBtc,OAAM,UAAExc,EAAS,MAAE9B,EAAQ,CAAC,EAAC,SAAEqB,EAAQ,MAAE0oB,EAAK,SAAEiX,EAAW,GAAE,UAAEC,EAAY,GAAE,SAAEC,EAAWC,OAAOC,UAAS,UAAEC,EAAYF,OAAOC,UAAS,gBAAEE,GAAkB,EAAK,aAAEC,EAAY,cAAEC,EAAa,SAAEC,EAAQ,YAAEC,IAChR,MAAMC,EAAgBxjB,KAChB/c,EAAuB,iBAAX4T,EAAsBA,EAAS2sB,EAC3CniC,EAAQG,IACRiiC,GAAmB,IAAAt7B,QAAO,MAC1Bu7B,EAAkBjH,IAAY,KAAqBkH,KAAO,QAAU,eACpEC,EAAkB5hC,GAAY0hC,EAC9BG,GAAU,IAAA17B,QAAO,OACvB,IAAAxC,YAAU,KACN,GAAK89B,EAAiBv7B,SAAYjF,EAsGlC,OAnGK4gC,EAAQ37B,UACT27B,EAAQ37B,SAAU,QAAU,CACxBkF,QAASq2B,EAAiBv7B,QAC1B2O,OAAQ5T,EACR2b,cAAe,KACX,MAAM,WAAEha,EAAU,UAAE+H,EAAS,SAAEmB,EAAQ,WAAEC,EAAU,WAAExG,EAAU,QAAE6F,GAAY/L,EAAMK,WACnF,MAAO,CACHkD,aACA+H,YACAmB,WACAC,aACAxG,aACAu8B,YAAa12B,EAChB,EAEL22B,SAAU,CAAC/0B,EAAQg1B,KACf,MAAM,mBAAElvB,EAAkB,WAAElQ,EAAU,aAAEkjB,EAAY,WAAEvgB,GAAelG,EAAMK,WACrEgN,EAAU,GACVmR,EAAe,CAAEtZ,EAAGyI,EAAOzI,EAAGC,EAAGwI,EAAOxI,GACxC7B,EAAOC,EAAWwK,IAAInM,GAC5B,GAAI0B,GAAQA,EAAK60B,cAAgB70B,EAAK0O,SAAU,CAC5C,MAAM4wB,EAASt/B,EAAKs/B,QAAU18B,EACxBtF,EAAQ+M,EAAO/M,OAAS0C,EAAKkL,SAAS5N,OAAS,EAC/CC,EAAS8M,EAAO9M,QAAUyC,EAAKkL,SAAS3N,QAAU,EAClDgiC,EAAQ,CACVjhC,GAAI0B,EAAK1B,GACToQ,SAAU1O,EAAK0O,SACfqmB,KAAM,CACFz3B,QACAC,aACG,QAAyB,CACxBqE,EAAGyI,EAAOzI,GAAK5B,EAAK3C,SAASuE,EAC7BC,EAAGwI,EAAOxI,GAAK7B,EAAK3C,SAASwE,GAC9B,CAAEvE,QAAOC,UAAUyC,EAAK0O,SAAUzO,EAAYq/B,KAGnDtK,GAAsB,QAAmB,CAACuK,GAAQt/B,EAAYkjB,EAAcvgB,GAClFmH,EAAQ5J,QAAQ60B,GAKhB9Z,EAAatZ,EAAIyI,EAAOzI,EAAIyW,KAAKyc,IAAIwK,EAAO,GAAKhiC,EAAO+M,EAAOzI,QAAKiJ,EACpEqQ,EAAarZ,EAAIwI,EAAOxI,EAAIwW,KAAKyc,IAAIwK,EAAO,GAAK/hC,EAAQ8M,EAAOxI,QAAKgJ,CACzE,CACA,QAAuBA,IAAnBqQ,EAAatZ,QAAsCiJ,IAAnBqQ,EAAarZ,EAAiB,CAC9D,MAAM29B,EAAiB,CACnBlhC,KACAgM,KAAM,WACNjN,SAAU,IAAK6d,IAEnBnR,EAAQ5J,KAAKq/B,EACjB,CACA,QAAqB30B,IAAjBR,EAAO/M,YAAyCuN,IAAlBR,EAAO9M,OAAsB,CAC3D,MAAMkiC,EAAkB,CACpBnhC,KACAgM,KAAM,aACNc,UAAU,EACVD,eAAe,EACfF,WAAY,CACR3N,MAAO+M,EAAO/M,MACdC,OAAQ8M,EAAO9M,SAGvBwM,EAAQ5J,KAAKs/B,EACjB,CACA,IAAK,MAAMC,KAAeL,EAAc,CACpC,MAAMG,EAAiB,IAChBE,EACHp1B,KAAM,YAEVP,EAAQ5J,KAAKq/B,EACjB,CACArvB,EAAmBpG,EAAQ,EAE/B41B,MAAO,KACH,MAAMF,EAAkB,CACpBnhC,GAAIA,EACJgM,KAAM,aACNc,UAAU,GAEd1O,EAAMK,WAAWoT,mBAAmB,CAACsvB,GAAiB,KAIlEP,EAAQ37B,QAAQ8R,OAAO,CACnB4pB,kBACAW,WAAY,CACR1B,WACAC,YACAC,WACAG,aAEJC,kBACAE,gBACAC,WACAC,cACAH,iBAEG,KACHS,EAAQ37B,SAAS6R,SAAS,CAC7B,GACF,CACC6pB,EACAf,EACAC,EACAC,EACAG,EACAC,EACAE,EACAC,EACAC,EACAH,IAEJ,MAAMoB,EAAqBZ,EAAgB5/B,MAAM,KAC3CygC,EAAiBhI,IAAY,KAAqBkH,KAAO,cAAgB,kBACzEe,EAAe9Y,EAAQ,IAAK/pB,EAAO,CAAC4iC,GAAiB7Y,GAAU/pB,EACrE,OAAQ,IAAAmB,KAAI,MAAO,CAAEW,WAAW,OAAG,CAAC,6BAA8B,YAAa6gC,EAAoB/H,EAAS94B,IAAaE,IAAK4/B,EAAkB5hC,MAAO6iC,EAAcxhC,SAAUA,GACnL,G", "sources": ["webpack://autogentstudio/./node_modules/@xyflow/react/dist/esm/index.js"], "sourcesContent": ["\"use client\"\nimport { jsxs, Fragment, jsx } from 'react/jsx-runtime';\nimport { createContext, useContext, useMemo, forwardRef, useEffect, useRef, useState, useLayoutEffect, useCallback, memo } from 'react';\nimport cc from 'classcat';\nimport { errorMessages, infiniteExtent, isInputDOMNode, getFitViewNodes, getDimensions, fitView, getViewportForBounds, pointToRendererPoint, rendererPointToPoint, isNodeBase, isEdgeBase, getElementsToRemove, isRectObject, nodeToRect, getOverlappingArea, getNodesBounds, evaluateAbsolutePosition, XYPanZoom, PanOnScrollMode, SelectionMode, getEventPosition, getNodesInside, areSetsEqual, XYDrag, snapPosition, calculateNodePosition, Position, ConnectionMode, isMouseEvent, XYHandle, getHostForElement, addEdge, getInternalNodesBounds, isNumeric, nodeHasDimensions, getNodeDimensions, elementSelectionKeys, isEdgeVisible, MarkerType, createMarkerIds, getBezierEdgeCenter, getSmoothStepPath, getStraightPath, getBezierPath, getEdgePosition, getElevatedEdgeZIndex, getMarkerId, getConnectionStatus, ConnectionLineType, updateConnectionLookup, adoptUserNodes, initialConnection, devWarn, updateNodeInternals, updateAbsolutePositions, handleExpandParent, panBy, isMacOs, areConnectionMapsEqual, handleConnectionChange, shallowNodeData, XYMinimap, getBoundsOfRects, ResizeControlVariant, XYResizer, XY_RESIZER_LINE_POSITIONS, XY_RESIZER_HANDLE_POSITIONS, getNodeToolbarTransform } from '@xyflow/system';\nexport { ConnectionLineType, ConnectionMode, MarkerType, PanOnScrollMode, Position, ResizeControlVariant, SelectionMode, addEdge, getBezierEdgeCenter, getBezierPath, getConnectedEdges, getEdgeCenter, getIncomers, getNodesBounds, getOutgoers, getSmoothStepPath, getStraightPath, getViewportForBounds, reconnectEdge } from '@xyflow/system';\nimport { useStoreWithEqualityFn, createWithEqualityFn } from 'zustand/traditional';\nimport { shallow } from 'zustand/shallow';\nimport { createPortal } from 'react-dom';\n\nconst StoreContext = createContext(null);\nconst Provider$1 = StoreContext.Provider;\n\nconst zustandErrorMessage = errorMessages['error001']();\n/**\n * This hook can be used to subscribe to internal state changes of the React Flow\n * component. The `useStore` hook is re-exported from the [Zustand](https://github.com/pmndrs/zustand)\n * state management library, so you should check out their docs for more details.\n *\n * @public\n * @param selector\n * @param equalityFn\n * @returns The selected state slice\n *\n * @example\n * ```ts\n * const nodes = useStore((state) => state.nodes);\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStore(selector, equalityFn) {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useStoreWithEqualityFn(store, selector, equalityFn);\n}\n/**\n * In some cases, you might need to access the store directly. This hook returns the store object which can be used on demand to access the state or dispatch actions.\n *\n * @returns The store object\n *\n * @example\n * ```ts\n * const store = useStoreApi();\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStoreApi() {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useMemo(() => ({\n        getState: store.getState,\n        setState: store.setState,\n        subscribe: store.subscribe,\n    }), [store]);\n}\n\nconst style = { display: 'none' };\nconst ariaLiveStyle = {\n    position: 'absolute',\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0px, 0px, 0px, 0px)',\n    clipPath: 'inset(100%)',\n};\nconst ARIA_NODE_DESC_KEY = 'react-flow__node-desc';\nconst ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';\nconst ARIA_LIVE_MESSAGE = 'react-flow__aria-live';\nconst selector$o = (s) => s.ariaLiveMessage;\nfunction AriaLiveMessage({ rfId }) {\n    const ariaLiveMessage = useStore(selector$o);\n    return (jsx(\"div\", { id: `${ARIA_LIVE_MESSAGE}-${rfId}`, \"aria-live\": \"assertive\", \"aria-atomic\": \"true\", style: ariaLiveStyle, children: ariaLiveMessage }));\n}\nfunction A11yDescriptions({ rfId, disableKeyboardA11y }) {\n    return (jsxs(Fragment, { children: [jsxs(\"div\", { id: `${ARIA_NODE_DESC_KEY}-${rfId}`, style: style, children: [\"Press enter or space to select a node.\", !disableKeyboardA11y && 'You can then use the arrow keys to move the node around.', \" Press delete to remove it and escape to cancel.\", ' '] }), jsx(\"div\", { id: `${ARIA_EDGE_DESC_KEY}-${rfId}`, style: style, children: \"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.\" }), !disableKeyboardA11y && jsx(AriaLiveMessage, { rfId: rfId })] }));\n}\n\nconst selector$n = (s) => (s.userSelectionActive ? 'none' : 'all');\n/**\n * The `<Panel />` component helps you position content above the viewport.\n * It is used internally by the [`<MiniMap />`](/api-reference/components/minimap)\n * and [`<Controls />`](/api-reference/components/controls) components.\n *\n * @public\n *\n * @example\n * ```jsx\n *import { ReactFlow, Background, Panel } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[]} fitView>\n *      <Panel position=\"top-left\">top-left</Panel>\n *      <Panel position=\"top-center\">top-center</Panel>\n *      <Panel position=\"top-right\">top-right</Panel>\n *      <Panel position=\"bottom-left\">bottom-left</Panel>\n *      <Panel position=\"bottom-center\">bottom-center</Panel>\n *      <Panel position=\"bottom-right\">bottom-right</Panel>\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst Panel = forwardRef(({ position = 'top-left', children, className, style, ...rest }, ref) => {\n    const pointerEvents = useStore(selector$n);\n    const positionClasses = `${position}`.split('-');\n    return (jsx(\"div\", { className: cc(['react-flow__panel', className, ...positionClasses]), style: { ...style, pointerEvents }, ref: ref, ...rest, children: children }));\n});\nPanel.displayName = 'Panel';\n\nfunction Attribution({ proOptions, position = 'bottom-right' }) {\n    if (proOptions?.hideAttribution) {\n        return null;\n    }\n    return (jsx(Panel, { position: position, className: \"react-flow__attribution\", \"data-message\": \"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev\", children: jsx(\"a\", { href: \"https://reactflow.dev\", target: \"_blank\", rel: \"noopener noreferrer\", \"aria-label\": \"React Flow attribution\", children: \"React Flow\" }) }));\n}\n\nconst selector$m = (s) => {\n    const selectedNodes = [];\n    const selectedEdges = [];\n    for (const [, node] of s.nodeLookup) {\n        if (node.selected) {\n            selectedNodes.push(node.internals.userNode);\n        }\n    }\n    for (const [, edge] of s.edgeLookup) {\n        if (edge.selected) {\n            selectedEdges.push(edge);\n        }\n    }\n    return { selectedNodes, selectedEdges };\n};\nconst selectId = (obj) => obj.id;\nfunction areEqual(a, b) {\n    return (shallow(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) &&\n        shallow(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId)));\n}\nfunction SelectionListenerInner({ onSelectionChange, }) {\n    const store = useStoreApi();\n    const { selectedNodes, selectedEdges } = useStore(selector$m, areEqual);\n    useEffect(() => {\n        const params = { nodes: selectedNodes, edges: selectedEdges };\n        onSelectionChange?.(params);\n        store.getState().onSelectionChangeHandlers.forEach((fn) => fn(params));\n    }, [selectedNodes, selectedEdges, onSelectionChange]);\n    return null;\n}\nconst changeSelector = (s) => !!s.onSelectionChangeHandlers;\nfunction SelectionListener({ onSelectionChange, }) {\n    const storeHasSelectionChangeHandlers = useStore(changeSelector);\n    if (onSelectionChange || storeHasSelectionChangeHandlers) {\n        return jsx(SelectionListenerInner, { onSelectionChange: onSelectionChange });\n    }\n    return null;\n}\n\nconst defaultNodeOrigin = [0, 0];\nconst defaultViewport = { x: 0, y: 0, zoom: 1 };\n\n/*\n * This component helps us to update the store with the values coming from the user.\n * We distinguish between values we can update directly with `useDirectStoreUpdater` (like `snapGrid`)\n * and values that have a dedicated setter function in the store (like `setNodes`).\n */\n// these fields exist in the global store and we need to keep them up to date\nconst reactFlowFieldsToTrack = [\n    'nodes',\n    'edges',\n    'defaultNodes',\n    'defaultEdges',\n    'onConnect',\n    'onConnectStart',\n    'onConnectEnd',\n    'onClickConnectStart',\n    'onClickConnectEnd',\n    'nodesDraggable',\n    'nodesConnectable',\n    'nodesFocusable',\n    'edgesFocusable',\n    'edgesReconnectable',\n    'elevateNodesOnSelect',\n    'elevateEdgesOnSelect',\n    'minZoom',\n    'maxZoom',\n    'nodeExtent',\n    'onNodesChange',\n    'onEdgesChange',\n    'elementsSelectable',\n    'connectionMode',\n    'snapGrid',\n    'snapToGrid',\n    'translateExtent',\n    'connectOnClick',\n    'defaultEdgeOptions',\n    'fitView',\n    'fitViewOptions',\n    'onNodesDelete',\n    'onEdgesDelete',\n    'onDelete',\n    'onNodeDrag',\n    'onNodeDragStart',\n    'onNodeDragStop',\n    'onSelectionDrag',\n    'onSelectionDragStart',\n    'onSelectionDragStop',\n    'onMoveStart',\n    'onMove',\n    'onMoveEnd',\n    'noPanClassName',\n    'nodeOrigin',\n    'autoPanOnConnect',\n    'autoPanOnNodeDrag',\n    'onError',\n    'connectionRadius',\n    'isValidConnection',\n    'selectNodesOnDrag',\n    'nodeDragThreshold',\n    'onBeforeDelete',\n    'debug',\n    'autoPanSpeed',\n    'paneClickDistance',\n];\n// rfId doesn't exist in ReactFlowProps, but it's one of the fields we want to update\nconst fieldsToTrack = [...reactFlowFieldsToTrack, 'rfId'];\nconst selector$l = (s) => ({\n    setNodes: s.setNodes,\n    setEdges: s.setEdges,\n    setMinZoom: s.setMinZoom,\n    setMaxZoom: s.setMaxZoom,\n    setTranslateExtent: s.setTranslateExtent,\n    setNodeExtent: s.setNodeExtent,\n    reset: s.reset,\n    setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,\n    setPaneClickDistance: s.setPaneClickDistance,\n});\nconst initPrevValues = {\n    /*\n     * these are values that are also passed directly to other components\n     * than the StoreUpdater. We can reduce the number of setStore calls\n     * by setting the same values here as prev fields.\n     */\n    translateExtent: infiniteExtent,\n    nodeOrigin: defaultNodeOrigin,\n    minZoom: 0.5,\n    maxZoom: 2,\n    elementsSelectable: true,\n    noPanClassName: 'nopan',\n    rfId: '1',\n    paneClickDistance: 0,\n};\nfunction StoreUpdater(props) {\n    const { setNodes, setEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset, setDefaultNodesAndEdges, setPaneClickDistance, } = useStore(selector$l, shallow);\n    const store = useStoreApi();\n    useEffect(() => {\n        setDefaultNodesAndEdges(props.defaultNodes, props.defaultEdges);\n        return () => {\n            // when we reset the store we also need to reset the previous fields\n            previousFields.current = initPrevValues;\n            reset();\n        };\n    }, []);\n    const previousFields = useRef(initPrevValues);\n    useEffect(() => {\n        for (const fieldName of fieldsToTrack) {\n            const fieldValue = props[fieldName];\n            const previousFieldValue = previousFields.current[fieldName];\n            if (fieldValue === previousFieldValue)\n                continue;\n            if (typeof props[fieldName] === 'undefined')\n                continue;\n            // Custom handling with dedicated setters for some fields\n            if (fieldName === 'nodes')\n                setNodes(fieldValue);\n            else if (fieldName === 'edges')\n                setEdges(fieldValue);\n            else if (fieldName === 'minZoom')\n                setMinZoom(fieldValue);\n            else if (fieldName === 'maxZoom')\n                setMaxZoom(fieldValue);\n            else if (fieldName === 'translateExtent')\n                setTranslateExtent(fieldValue);\n            else if (fieldName === 'nodeExtent')\n                setNodeExtent(fieldValue);\n            else if (fieldName === 'paneClickDistance')\n                setPaneClickDistance(fieldValue);\n            // Renamed fields\n            else if (fieldName === 'fitView')\n                store.setState({ fitViewOnInit: fieldValue });\n            else if (fieldName === 'fitViewOptions')\n                store.setState({ fitViewOnInitOptions: fieldValue });\n            // General case\n            else\n                store.setState({ [fieldName]: fieldValue });\n        }\n        previousFields.current = props;\n    }, \n    // Only re-run the effect if one of the fields we track changes\n    fieldsToTrack.map((fieldName) => props[fieldName]));\n    return null;\n}\n\nfunction getMediaQuery() {\n    if (typeof window === 'undefined' || !window.matchMedia) {\n        return null;\n    }\n    return window.matchMedia('(prefers-color-scheme: dark)');\n}\n/**\n * Hook for receiving the current color mode class 'dark' or 'light'.\n *\n * @internal\n * @param colorMode - The color mode to use ('dark', 'light' or 'system')\n */\nfunction useColorModeClass(colorMode) {\n    const [colorModeClass, setColorModeClass] = useState(colorMode === 'system' ? null : colorMode);\n    useEffect(() => {\n        if (colorMode !== 'system') {\n            setColorModeClass(colorMode);\n            return;\n        }\n        const mediaQuery = getMediaQuery();\n        const updateColorModeClass = () => setColorModeClass(mediaQuery?.matches ? 'dark' : 'light');\n        updateColorModeClass();\n        mediaQuery?.addEventListener('change', updateColorModeClass);\n        return () => {\n            mediaQuery?.removeEventListener('change', updateColorModeClass);\n        };\n    }, [colorMode]);\n    return colorModeClass !== null ? colorModeClass : getMediaQuery()?.matches ? 'dark' : 'light';\n}\n\nconst defaultDoc = typeof document !== 'undefined' ? document : null;\n/**\n * This hook lets you listen for specific key codes and tells you whether they are\n * currently pressed or not.\n *\n * @public\n * @param param.keyCode - The key code (string or array of strings) to use\n * @param param.options - Options\n * @returns boolean\n *\n * @example\n * ```tsx\n *import { useKeyPress } from '@xyflow/react';\n *\n *export default function () {\n *  const spacePressed = useKeyPress('Space');\n *  const cmdAndSPressed = useKeyPress(['Meta+s', 'Strg+s']);\n *\n *  return (\n *    <div>\n *     {spacePressed && <p>Space pressed!</p>}\n *     {cmdAndSPressed && <p>Cmd + S pressed!</p>}\n *    </div>\n *  );\n *}\n *```\n */\nfunction useKeyPress(\n/*\n * the keycode can be a string 'a' or an array of strings ['a', 'a+d']\n * a string means a single key 'a' or a combination when '+' is used 'a+d'\n * an array means different possibilites. Explainer: ['a', 'd+s'] here the\n * user can use the single key 'a' or the combination 'd' + 's'\n */\nkeyCode = null, options = { target: defaultDoc, actInsideInputWithModifier: true }) {\n    const [keyPressed, setKeyPressed] = useState(false);\n    // we need to remember if a modifier key is pressed in order to track it\n    const modifierPressed = useRef(false);\n    // we need to remember the pressed keys in order to support combinations\n    const pressedKeys = useRef(new Set([]));\n    /*\n     * keyCodes = array with single keys [['a']] or key combinations [['a', 's']]\n     * keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']\n     * used to check if we store event.code or event.key. When the code is in the list of keysToWatch\n     * we use the code otherwise the key. Explainer: When you press the left \"command\" key, the code is \"MetaLeft\"\n     * and the key is \"Meta\". We want users to be able to pass keys and codes so we assume that the key is meant when\n     * we can't find it in the list of keysToWatch.\n     */\n    const [keyCodes, keysToWatch] = useMemo(() => {\n        if (keyCode !== null) {\n            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [keyCode];\n            const keys = keyCodeArr\n                .filter((kc) => typeof kc === 'string')\n                /*\n                 * we first replace all '+' with '\\n'  which we will use to split the keys on\n                 * then we replace '\\n\\n' with '\\n+', this way we can also support the combination 'key++'\n                 * in the end we simply split on '\\n' to get the key array\n                 */\n                .map((kc) => kc.replace('+', '\\n').replace('\\n\\n', '\\n+').split('\\n'));\n            const keysFlat = keys.reduce((res, item) => res.concat(...item), []);\n            return [keys, keysFlat];\n        }\n        return [[], []];\n    }, [keyCode]);\n    useEffect(() => {\n        const target = options?.target || defaultDoc;\n        if (keyCode !== null) {\n            const downHandler = (event) => {\n                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey;\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                pressedKeys.current.add(event[keyOrCode]);\n                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {\n                    event.preventDefault();\n                    setKeyPressed(true);\n                }\n            };\n            const upHandler = (event) => {\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {\n                    setKeyPressed(false);\n                    pressedKeys.current.clear();\n                }\n                else {\n                    pressedKeys.current.delete(event[keyOrCode]);\n                }\n                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key\n                if (event.key === 'Meta') {\n                    pressedKeys.current.clear();\n                }\n                modifierPressed.current = false;\n            };\n            const resetHandler = () => {\n                pressedKeys.current.clear();\n                setKeyPressed(false);\n            };\n            target?.addEventListener('keydown', downHandler);\n            target?.addEventListener('keyup', upHandler);\n            window.addEventListener('blur', resetHandler);\n            window.addEventListener('contextmenu', resetHandler);\n            return () => {\n                target?.removeEventListener('keydown', downHandler);\n                target?.removeEventListener('keyup', upHandler);\n                window.removeEventListener('blur', resetHandler);\n                window.removeEventListener('contextmenu', resetHandler);\n            };\n        }\n    }, [keyCode, setKeyPressed]);\n    return keyPressed;\n}\n// utils\nfunction isMatchingKey(keyCodes, pressedKeys, isUp) {\n    return (keyCodes\n        /*\n         * we only want to compare same sizes of keyCode definitions\n         * and pressed keys. When the user specified 'Meta' as a key somewhere\n         * this would also be truthy without this filter when user presses 'Meta' + 'r'\n         */\n        .filter((keys) => isUp || keys.length === pressedKeys.size)\n        /*\n         * since we want to support multiple possibilities only one of the\n         * combinations need to be part of the pressed keys\n         */\n        .some((keys) => keys.every((k) => pressedKeys.has(k))));\n}\nfunction useKeyOrCode(eventCode, keysToWatch) {\n    return keysToWatch.includes(eventCode) ? 'code' : 'key';\n}\n\n/**\n * Hook for getting viewport helper functions.\n *\n * @internal\n * @returns viewport helper functions\n */\nconst useViewportHelper = () => {\n    const store = useStoreApi();\n    return useMemo(() => {\n        return {\n            zoomIn: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomOut: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1 / 1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomTo: (zoomLevel, options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleTo(zoomLevel, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            getZoom: () => store.getState().transform[2],\n            setViewport: async (viewport, options) => {\n                const { transform: [tX, tY, tZoom], panZoom, } = store.getState();\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport({\n                    x: viewport.x ?? tX,\n                    y: viewport.y ?? tY,\n                    zoom: viewport.zoom ?? tZoom,\n                }, { duration: options?.duration });\n                return Promise.resolve(true);\n            },\n            getViewport: () => {\n                const [x, y, zoom] = store.getState().transform;\n                return { x, y, zoom };\n            },\n            fitView: (options) => {\n                const { nodeLookup, minZoom, maxZoom, panZoom, domNode } = store.getState();\n                if (!panZoom || !domNode) {\n                    return Promise.resolve(false);\n                }\n                const fitViewNodes = getFitViewNodes(nodeLookup, options);\n                const { width, height } = getDimensions(domNode);\n                return fitView({\n                    nodes: fitViewNodes,\n                    width,\n                    height,\n                    minZoom,\n                    maxZoom,\n                    panZoom,\n                }, options);\n            },\n            setCenter: async (x, y, options) => {\n                const { width, height, maxZoom, panZoom } = store.getState();\n                const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;\n                const centerX = width / 2 - x * nextZoom;\n                const centerY = height / 2 - y * nextZoom;\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport({\n                    x: centerX,\n                    y: centerY,\n                    zoom: nextZoom,\n                }, { duration: options?.duration });\n                return Promise.resolve(true);\n            },\n            fitBounds: async (bounds, options) => {\n                const { width, height, minZoom, maxZoom, panZoom } = store.getState();\n                const viewport = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport(viewport, { duration: options?.duration });\n                return Promise.resolve(true);\n            },\n            screenToFlowPosition: (clientPosition, options = {}) => {\n                const { transform, snapGrid, snapToGrid, domNode } = store.getState();\n                if (!domNode) {\n                    return clientPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const correctedPosition = {\n                    x: clientPosition.x - domX,\n                    y: clientPosition.y - domY,\n                };\n                const _snapGrid = options.snapGrid ?? snapGrid;\n                const _snapToGrid = options.snapToGrid ?? snapToGrid;\n                return pointToRendererPoint(correctedPosition, transform, _snapToGrid, _snapGrid);\n            },\n            flowToScreenPosition: (flowPosition) => {\n                const { transform, domNode } = store.getState();\n                if (!domNode) {\n                    return flowPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const rendererPosition = rendererPointToPoint(flowPosition, transform);\n                return {\n                    x: rendererPosition.x + domX,\n                    y: rendererPosition.y + domY,\n                };\n            },\n        };\n    }, []);\n};\n\n/*\n * This function applies changes to nodes or edges that are triggered by React Flow internally.\n * When you drag a node for example, React Flow will send a position change update.\n * This function then applies the changes and returns the updated elements.\n */\nfunction applyChanges(changes, elements) {\n    const updatedElements = [];\n    /*\n     * By storing a map of changes for each element, we can a quick lookup as we\n     * iterate over the elements array!\n     */\n    const changesMap = new Map();\n    const addItemChanges = [];\n    for (const change of changes) {\n        if (change.type === 'add') {\n            addItemChanges.push(change);\n            continue;\n        }\n        else if (change.type === 'remove' || change.type === 'replace') {\n            /*\n             * For a 'remove' change we can safely ignore any other changes queued for\n             * the same element, it's going to be removed anyway!\n             */\n            changesMap.set(change.id, [change]);\n        }\n        else {\n            const elementChanges = changesMap.get(change.id);\n            if (elementChanges) {\n                /*\n                 * If we have some changes queued already, we can do a mutable update of\n                 * that array and save ourselves some copying.\n                 */\n                elementChanges.push(change);\n            }\n            else {\n                changesMap.set(change.id, [change]);\n            }\n        }\n    }\n    for (const element of elements) {\n        const changes = changesMap.get(element.id);\n        /*\n         * When there are no changes for an element we can just push it unmodified,\n         * no need to copy it.\n         */\n        if (!changes) {\n            updatedElements.push(element);\n            continue;\n        }\n        // If we have a 'remove' change queued, it'll be the only change in the array\n        if (changes[0].type === 'remove') {\n            continue;\n        }\n        if (changes[0].type === 'replace') {\n            updatedElements.push({ ...changes[0].item });\n            continue;\n        }\n        /**\n         * For other types of changes, we want to start with a shallow copy of the\n         * object so React knows this element has changed. Sequential changes will\n         * each _mutate_ this object, so there's only ever one copy.\n         */\n        const updatedElement = { ...element };\n        for (const change of changes) {\n            applyChange(change, updatedElement);\n        }\n        updatedElements.push(updatedElement);\n    }\n    /*\n     * we need to wait for all changes to be applied before adding new items\n     * to be able to add them at the correct index\n     */\n    if (addItemChanges.length) {\n        addItemChanges.forEach((change) => {\n            if (change.index !== undefined) {\n                updatedElements.splice(change.index, 0, { ...change.item });\n            }\n            else {\n                updatedElements.push({ ...change.item });\n            }\n        });\n    }\n    return updatedElements;\n}\n// Applies a single change to an element. This is a *mutable* update.\nfunction applyChange(change, element) {\n    switch (change.type) {\n        case 'select': {\n            element.selected = change.selected;\n            break;\n        }\n        case 'position': {\n            if (typeof change.position !== 'undefined') {\n                element.position = change.position;\n            }\n            if (typeof change.dragging !== 'undefined') {\n                element.dragging = change.dragging;\n            }\n            break;\n        }\n        case 'dimensions': {\n            if (typeof change.dimensions !== 'undefined') {\n                element.measured ??= {};\n                element.measured.width = change.dimensions.width;\n                element.measured.height = change.dimensions.height;\n                if (change.setAttributes) {\n                    element.width = change.dimensions.width;\n                    element.height = change.dimensions.height;\n                }\n            }\n            if (typeof change.resizing === 'boolean') {\n                element.resizing = change.resizing;\n            }\n            break;\n        }\n    }\n}\n/**\n * Drop in function that applies node changes to an array of nodes.\n * @public\n * @param changes - Array of changes to apply\n * @param nodes - Array of nodes to apply the changes to\n * @returns Array of updated nodes\n * @example\n *```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyNodeChanges, type Node, type Edge, type OnNodesChange } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState<Node[]>([]);\n *  const [edges, setEdges] = useState<Edge[]>([]);\n *  const onNodesChange: OnNodesChange = useCallback(\n *    (changes) => {\n *      setNodes((oldNodes) => applyNodeChanges(changes, oldNodes));\n *    },\n *    [setNodes],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onNodesChange={onNodesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link NodeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyNodeChanges(changes, nodes) {\n    return applyChanges(changes, nodes);\n}\n/**\n * Drop in function that applies edge changes to an array of edges.\n * @public\n * @param changes - Array of changes to apply\n * @param edges - Array of edge to apply the changes to\n * @returns Array of updated edges\n * @example\n * ```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyEdgeChanges } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState([]);\n *  const [edges, setEdges] = useState([]);\n *  const onEdgesChange = useCallback(\n *    (changes) => {\n *      setEdges((oldEdges) => applyEdgeChanges(changes, oldEdges));\n *    },\n *    [setEdges],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onEdgesChange={onEdgesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link EdgeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyEdgeChanges(changes, edges) {\n    return applyChanges(changes, edges);\n}\nfunction createSelectionChange(id, selected) {\n    return {\n        id,\n        type: 'select',\n        selected,\n    };\n}\nfunction getSelectionChanges(items, selectedIds = new Set(), mutateItem = false) {\n    const changes = [];\n    for (const [id, item] of items) {\n        const willBeSelected = selectedIds.has(id);\n        // we don't want to set all items to selected=false on the first selection\n        if (!(item.selected === undefined && !willBeSelected) && item.selected !== willBeSelected) {\n            if (mutateItem) {\n                /*\n                 * this hack is needed for nodes. When the user dragged a node, it's selected.\n                 * When another node gets dragged, we need to deselect the previous one,\n                 * in order to have only one selected node at a time - the onNodesChange callback comes too late here :/\n                 */\n                item.selected = willBeSelected;\n            }\n            changes.push(createSelectionChange(item.id, willBeSelected));\n        }\n    }\n    return changes;\n}\nfunction getElementsDiffChanges({ items = [], lookup, }) {\n    const changes = [];\n    const itemsLookup = new Map(items.map((item) => [item.id, item]));\n    for (const [index, item] of items.entries()) {\n        const lookupItem = lookup.get(item.id);\n        const storeItem = lookupItem?.internals?.userNode ?? lookupItem;\n        if (storeItem !== undefined && storeItem !== item) {\n            changes.push({ id: item.id, item: item, type: 'replace' });\n        }\n        if (storeItem === undefined) {\n            changes.push({ item: item, type: 'add', index });\n        }\n    }\n    for (const [id] of lookup) {\n        const nextNode = itemsLookup.get(id);\n        if (nextNode === undefined) {\n            changes.push({ id, type: 'remove' });\n        }\n    }\n    return changes;\n}\nfunction elementToRemoveChange(item) {\n    return {\n        id: item.id,\n        type: 'remove',\n    };\n}\n\n/**\n * Test whether an object is useable as an [`Node`](/api-reference/types/node).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Node`](/api-reference/types/node) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Node if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Node\n *\n * @example\n * ```js\n *import { isNode } from '@xyflow/react';\n *\n *if (isNode(node)) {\n * // ..\n *}\n *```\n */\nconst isNode = (element) => isNodeBase(element);\n/**\n * Test whether an object is useable as an [`Edge`](/api-reference/types/edge).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Edge`](/api-reference/types/edge) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Edge if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Edge\n *\n * @example\n * ```js\n *import { isEdge } from '@xyflow/react';\n *\n *if (isEdge(edge)) {\n * // ..\n *}\n *```\n */\nconst isEdge = (element) => isEdgeBase(element);\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nfunction fixedForwardRef(render) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return forwardRef(render);\n}\n\n// we need this hook to prevent a warning when using react-flow in SSR\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n\n/**\n * This hook returns a queue that can be used to batch updates.\n *\n * @param runQueue - a function that gets called when the queue is flushed\n * @internal\n *\n * @returns a Queue object\n */\nfunction useQueue(runQueue) {\n    /*\n     * Because we're using a ref above, we need some way to let React know when to\n     * actually process the queue. We increment this number any time we mutate the\n     * queue, creating a new state to trigger the layout effect below.\n     * Using a boolean dirty flag here instead would lead to issues related to\n     * automatic batching. (https://github.com/xyflow/xyflow/issues/4779)\n     */\n    const [serial, setSerial] = useState(BigInt(0));\n    /*\n     * A reference of all the batched updates to process before the next render. We\n     * want a reference here so multiple synchronous calls to `setNodes` etc can be\n     * batched together.\n     */\n    const [queue] = useState(() => createQueue(() => setSerial(n => n + BigInt(1))));\n    /*\n     * Layout effects are guaranteed to run before the next render which means we\n     * shouldn't run into any issues with stale state or weird issues that come from\n     * rendering things one frame later than expected (we used to use `setTimeout`).\n     */\n    useIsomorphicLayoutEffect(() => {\n        const queueItems = queue.get();\n        if (queueItems.length) {\n            runQueue(queueItems);\n            queue.reset();\n        }\n    }, [serial]);\n    return queue;\n}\nfunction createQueue(cb) {\n    let queue = [];\n    return {\n        get: () => queue,\n        reset: () => {\n            queue = [];\n        },\n        push: (item) => {\n            queue.push(item);\n            cb();\n        },\n    };\n}\n\nconst BatchContext = createContext(null);\n/**\n * This is a context provider that holds and processes the node and edge update queues\n * that are needed to handle setNodes, addNodes, setEdges and addEdges.\n *\n * @internal\n */\nfunction BatchProvider({ children, }) {\n    const store = useStoreApi();\n    const nodeQueueHandler = useCallback((queueItems) => {\n        const { nodes = [], setNodes, hasDefaultNodes, onNodesChange, nodeLookup } = store.getState();\n        /*\n         * This is essentially an `Array.reduce` in imperative clothing. Processing\n         * this queue is a relatively hot path so we'd like to avoid the overhead of\n         * array methods where we can.\n         */\n        let next = nodes;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        if (hasDefaultNodes) {\n            setNodes(next);\n        }\n        else if (onNodesChange) {\n            onNodesChange(getElementsDiffChanges({\n                items: next,\n                lookup: nodeLookup,\n            }));\n        }\n    }, []);\n    const nodeQueue = useQueue(nodeQueueHandler);\n    const edgeQueueHandler = useCallback((queueItems) => {\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange, edgeLookup } = store.getState();\n        let next = edges;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        if (hasDefaultEdges) {\n            setEdges(next);\n        }\n        else if (onEdgesChange) {\n            onEdgesChange(getElementsDiffChanges({\n                items: next,\n                lookup: edgeLookup,\n            }));\n        }\n    }, []);\n    const edgeQueue = useQueue(edgeQueueHandler);\n    const value = useMemo(() => ({ nodeQueue, edgeQueue }), []);\n    return jsx(BatchContext.Provider, { value: value, children: children });\n}\nfunction useBatchContext() {\n    const batchContext = useContext(BatchContext);\n    if (!batchContext) {\n        throw new Error('useBatchContext must be used within a BatchProvider');\n    }\n    return batchContext;\n}\n\nconst selector$k = (s) => !!s.panZoom;\n/**\n * This hook returns a ReactFlowInstance that can be used to update nodes and edges, manipulate the viewport, or query the current state of the flow.\n *\n * @public\n * @returns ReactFlowInstance\n *\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { useReactFlow } from '@xyflow/react';\n *\n *export function NodeCounter() {\n *  const reactFlow = useReactFlow();\n *  const [count, setCount] = useState(0);\n *  const countNodes = useCallback(() => {\n *    setCount(reactFlow.getNodes().length);\n *    // you need to pass it as a dependency if you are using it with useEffect or useCallback\n *    // because at the first render, it's not initialized yet and some functions might not work.\n *  }, [reactFlow]);\n *\n *  return (\n *    <div>\n *      <button onClick={countNodes}>Update count</button>\n *      <p>There are {count} nodes in the flow.</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useReactFlow() {\n    const viewportHelper = useViewportHelper();\n    const store = useStoreApi();\n    const batchContext = useBatchContext();\n    const viewportInitialized = useStore(selector$k);\n    const generalHelper = useMemo(() => {\n        const getInternalNode = (id) => store.getState().nodeLookup.get(id);\n        const setNodes = (payload) => {\n            batchContext.nodeQueue.push(payload);\n        };\n        const setEdges = (payload) => {\n            batchContext.edgeQueue.push(payload);\n        };\n        const getNodeRect = (node) => {\n            const { nodeLookup, nodeOrigin } = store.getState();\n            const nodeToUse = isNode(node) ? node : nodeLookup.get(node.id);\n            const position = nodeToUse.parentId\n                ? evaluateAbsolutePosition(nodeToUse.position, nodeToUse.measured, nodeToUse.parentId, nodeLookup, nodeOrigin)\n                : nodeToUse.position;\n            const nodeWithPosition = {\n                ...nodeToUse,\n                position,\n                width: nodeToUse.measured?.width ?? nodeToUse.width,\n                height: nodeToUse.measured?.height ?? nodeToUse.height,\n            };\n            return nodeToRect(nodeWithPosition);\n        };\n        const updateNode = (id, nodeUpdate, options = { replace: false }) => {\n            setNodes((prevNodes) => prevNodes.map((node) => {\n                if (node.id === id) {\n                    const nextNode = typeof nodeUpdate === 'function' ? nodeUpdate(node) : nodeUpdate;\n                    return options.replace && isNode(nextNode) ? nextNode : { ...node, ...nextNode };\n                }\n                return node;\n            }));\n        };\n        const updateEdge = (id, edgeUpdate, options = { replace: false }) => {\n            setEdges((prevEdges) => prevEdges.map((edge) => {\n                if (edge.id === id) {\n                    const nextEdge = typeof edgeUpdate === 'function' ? edgeUpdate(edge) : edgeUpdate;\n                    return options.replace && isEdge(nextEdge) ? nextEdge : { ...edge, ...nextEdge };\n                }\n                return edge;\n            }));\n        };\n        return {\n            getNodes: () => store.getState().nodes.map((n) => ({ ...n })),\n            getNode: (id) => getInternalNode(id)?.internals.userNode,\n            getInternalNode,\n            getEdges: () => {\n                const { edges = [] } = store.getState();\n                return edges.map((e) => ({ ...e }));\n            },\n            getEdge: (id) => store.getState().edgeLookup.get(id),\n            setNodes,\n            setEdges,\n            addNodes: (payload) => {\n                const newNodes = Array.isArray(payload) ? payload : [payload];\n                batchContext.nodeQueue.push((nodes) => [...nodes, ...newNodes]);\n            },\n            addEdges: (payload) => {\n                const newEdges = Array.isArray(payload) ? payload : [payload];\n                batchContext.edgeQueue.push((edges) => [...edges, ...newEdges]);\n            },\n            toObject: () => {\n                const { nodes = [], edges = [], transform } = store.getState();\n                const [x, y, zoom] = transform;\n                return {\n                    nodes: nodes.map((n) => ({ ...n })),\n                    edges: edges.map((e) => ({ ...e })),\n                    viewport: {\n                        x,\n                        y,\n                        zoom,\n                    },\n                };\n            },\n            deleteElements: async ({ nodes: nodesToRemove = [], edges: edgesToRemove = [] }) => {\n                const { nodes, edges, onNodesDelete, onEdgesDelete, triggerNodeChanges, triggerEdgeChanges, onDelete, onBeforeDelete, } = store.getState();\n                const { nodes: matchingNodes, edges: matchingEdges } = await getElementsToRemove({\n                    nodesToRemove,\n                    edgesToRemove,\n                    nodes,\n                    edges,\n                    onBeforeDelete,\n                });\n                const hasMatchingEdges = matchingEdges.length > 0;\n                const hasMatchingNodes = matchingNodes.length > 0;\n                if (hasMatchingEdges) {\n                    const edgeChanges = matchingEdges.map(elementToRemoveChange);\n                    onEdgesDelete?.(matchingEdges);\n                    triggerEdgeChanges(edgeChanges);\n                }\n                if (hasMatchingNodes) {\n                    const nodeChanges = matchingNodes.map(elementToRemoveChange);\n                    onNodesDelete?.(matchingNodes);\n                    triggerNodeChanges(nodeChanges);\n                }\n                if (hasMatchingNodes || hasMatchingEdges) {\n                    onDelete?.({ nodes: matchingNodes, edges: matchingEdges });\n                }\n                return { deletedNodes: matchingNodes, deletedEdges: matchingEdges };\n            },\n            getIntersectingNodes: (nodeOrRect, partially = true, nodes) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                const hasNodesOption = nodes !== undefined;\n                if (!nodeRect) {\n                    return [];\n                }\n                return (nodes || store.getState().nodes).filter((n) => {\n                    const internalNode = store.getState().nodeLookup.get(n.id);\n                    if (internalNode && !isRect && (n.id === nodeOrRect.id || !internalNode.internals.positionAbsolute)) {\n                        return false;\n                    }\n                    const currNodeRect = nodeToRect(hasNodesOption ? n : internalNode);\n                    const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);\n                    const partiallyVisible = partially && overlappingArea > 0;\n                    return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n                });\n            },\n            isNodeIntersecting: (nodeOrRect, area, partially = true) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                if (!nodeRect) {\n                    return false;\n                }\n                const overlappingArea = getOverlappingArea(nodeRect, area);\n                const partiallyVisible = partially && overlappingArea > 0;\n                return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n            },\n            updateNode,\n            updateNodeData: (id, dataUpdate, options = { replace: false }) => {\n                updateNode(id, (node) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(node) : dataUpdate;\n                    return options.replace ? { ...node, data: nextData } : { ...node, data: { ...node.data, ...nextData } };\n                }, options);\n            },\n            updateEdge,\n            updateEdgeData: (id, dataUpdate, options = { replace: false }) => {\n                updateEdge(id, (edge) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(edge) : dataUpdate;\n                    return options.replace ? { ...edge, data: nextData } : { ...edge, data: { ...edge.data, ...nextData } };\n                }, options);\n            },\n            getNodesBounds: (nodes) => {\n                const { nodeLookup, nodeOrigin } = store.getState();\n                return getNodesBounds(nodes, { nodeLookup, nodeOrigin });\n            },\n            getHandleConnections: ({ type, id, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}-${type}${id ? `-${id}` : ''}`)\n                ?.values() ?? []),\n            getNodeConnections: ({ type, handleId, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}${type ? (handleId ? `-${type}-${handleId}` : `-${type}`) : ''}`)\n                ?.values() ?? []),\n        };\n    }, []);\n    return useMemo(() => {\n        return {\n            ...generalHelper,\n            ...viewportHelper,\n            viewportInitialized,\n        };\n    }, [viewportInitialized]);\n}\n\nconst selected = (item) => item.selected;\nconst deleteKeyOptions = { actInsideInputWithModifier: false };\nconst win$1 = typeof window !== 'undefined' ? window : undefined;\n/**\n * Hook for handling global key events.\n *\n * @internal\n */\nfunction useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode, }) {\n    const store = useStoreApi();\n    const { deleteElements } = useReactFlow();\n    const deleteKeyPressed = useKeyPress(deleteKeyCode, deleteKeyOptions);\n    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode, { target: win$1 });\n    useEffect(() => {\n        if (deleteKeyPressed) {\n            const { edges, nodes } = store.getState();\n            deleteElements({ nodes: nodes.filter(selected), edges: edges.filter(selected) });\n            store.setState({ nodesSelectionActive: false });\n        }\n    }, [deleteKeyPressed]);\n    useEffect(() => {\n        store.setState({ multiSelectionActive: multiSelectionKeyPressed });\n    }, [multiSelectionKeyPressed]);\n}\n\n/**\n * Hook for handling resize events.\n *\n * @internal\n */\nfunction useResizeHandler(domNode) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const updateDimensions = () => {\n            if (!domNode.current) {\n                return false;\n            }\n            const size = getDimensions(domNode.current);\n            if (size.height === 0 || size.width === 0) {\n                store.getState().onError?.('004', errorMessages['error004']());\n            }\n            store.setState({ width: size.width || 500, height: size.height || 500 });\n        };\n        if (domNode.current) {\n            updateDimensions();\n            window.addEventListener('resize', updateDimensions);\n            const resizeObserver = new ResizeObserver(() => updateDimensions());\n            resizeObserver.observe(domNode.current);\n            return () => {\n                window.removeEventListener('resize', updateDimensions);\n                if (resizeObserver && domNode.current) {\n                    resizeObserver.unobserve(domNode.current);\n                }\n            };\n        }\n    }, []);\n}\n\nconst containerStyle = {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    top: 0,\n    left: 0,\n};\n\nconst selector$j = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    lib: s.lib,\n});\nfunction ZoomPane({ onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName, onViewportChange, isControlledViewport, paneClickDistance, }) {\n    const store = useStoreApi();\n    const zoomPane = useRef(null);\n    const { userSelectionActive, lib } = useStore(selector$j, shallow);\n    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);\n    const panZoom = useRef();\n    useResizeHandler(zoomPane);\n    const onTransformChange = useCallback((transform) => {\n        onViewportChange?.({ x: transform[0], y: transform[1], zoom: transform[2] });\n        if (!isControlledViewport) {\n            store.setState({ transform });\n        }\n    }, [onViewportChange, isControlledViewport]);\n    useEffect(() => {\n        if (zoomPane.current) {\n            panZoom.current = XYPanZoom({\n                domNode: zoomPane.current,\n                minZoom,\n                maxZoom,\n                translateExtent,\n                viewport: defaultViewport,\n                paneClickDistance,\n                onDraggingChange: (paneDragging) => store.setState({ paneDragging }),\n                onPanZoomStart: (event, vp) => {\n                    const { onViewportChangeStart, onMoveStart } = store.getState();\n                    onMoveStart?.(event, vp);\n                    onViewportChangeStart?.(vp);\n                },\n                onPanZoom: (event, vp) => {\n                    const { onViewportChange, onMove } = store.getState();\n                    onMove?.(event, vp);\n                    onViewportChange?.(vp);\n                },\n                onPanZoomEnd: (event, vp) => {\n                    const { onViewportChangeEnd, onMoveEnd } = store.getState();\n                    onMoveEnd?.(event, vp);\n                    onViewportChangeEnd?.(vp);\n                },\n            });\n            const { x, y, zoom } = panZoom.current.getViewport();\n            store.setState({\n                panZoom: panZoom.current,\n                transform: [x, y, zoom],\n                domNode: zoomPane.current.closest('.react-flow'),\n            });\n            return () => {\n                panZoom.current?.destroy();\n            };\n        }\n    }, []);\n    useEffect(() => {\n        panZoom.current?.update({\n            onPaneContextMenu,\n            zoomOnScroll,\n            zoomOnPinch,\n            panOnScroll,\n            panOnScrollSpeed,\n            panOnScrollMode,\n            zoomOnDoubleClick,\n            panOnDrag,\n            zoomActivationKeyPressed,\n            preventScrolling,\n            noPanClassName,\n            userSelectionActive,\n            noWheelClassName,\n            lib,\n            onTransformChange,\n        });\n    }, [\n        onPaneContextMenu,\n        zoomOnScroll,\n        zoomOnPinch,\n        panOnScroll,\n        panOnScrollSpeed,\n        panOnScrollMode,\n        zoomOnDoubleClick,\n        panOnDrag,\n        zoomActivationKeyPressed,\n        preventScrolling,\n        noPanClassName,\n        userSelectionActive,\n        noWheelClassName,\n        lib,\n        onTransformChange,\n    ]);\n    return (jsx(\"div\", { className: \"react-flow__renderer\", ref: zoomPane, style: containerStyle, children: children }));\n}\n\nconst selector$i = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    userSelectionRect: s.userSelectionRect,\n});\nfunction UserSelection() {\n    const { userSelectionActive, userSelectionRect } = useStore(selector$i, shallow);\n    const isActive = userSelectionActive && userSelectionRect;\n    if (!isActive) {\n        return null;\n    }\n    return (jsx(\"div\", { className: \"react-flow__selection react-flow__container\", style: {\n            width: userSelectionRect.width,\n            height: userSelectionRect.height,\n            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`,\n        } }));\n}\n\nconst wrapHandler = (handler, containerRef) => {\n    return (event) => {\n        if (event.target !== containerRef.current) {\n            return;\n        }\n        handler?.(event);\n    };\n};\nconst selector$h = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    elementsSelectable: s.elementsSelectable,\n    dragging: s.paneDragging,\n});\nfunction Pane({ isSelecting, selectionKeyPressed, selectionMode = SelectionMode.Full, panOnDrag, selectionOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children, }) {\n    const store = useStoreApi();\n    const { userSelectionActive, elementsSelectable, dragging } = useStore(selector$h, shallow);\n    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);\n    const container = useRef(null);\n    const containerBounds = useRef();\n    const selectedNodeIds = useRef(new Set());\n    const selectedEdgeIds = useRef(new Set());\n    // Used to prevent click events when the user lets go of the selectionKey during a selection\n    const selectionInProgress = useRef(false);\n    const selectionStarted = useRef(false);\n    const onClick = (event) => {\n        // We prevent click events when the user let go of the selectionKey during a selection\n        if (selectionInProgress.current) {\n            selectionInProgress.current = false;\n            return;\n        }\n        onPaneClick?.(event);\n        store.getState().resetSelectedElements();\n        store.setState({ nodesSelectionActive: false });\n    };\n    const onContextMenu = (event) => {\n        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {\n            event.preventDefault();\n            return;\n        }\n        onPaneContextMenu?.(event);\n    };\n    const onWheel = onPaneScroll ? (event) => onPaneScroll(event) : undefined;\n    const onPointerDown = (event) => {\n        const { resetSelectedElements, domNode } = store.getState();\n        containerBounds.current = domNode?.getBoundingClientRect();\n        if (!elementsSelectable ||\n            !isSelecting ||\n            event.button !== 0 ||\n            event.target !== container.current ||\n            !containerBounds.current) {\n            return;\n        }\n        event.target?.setPointerCapture?.(event.pointerId);\n        selectionStarted.current = true;\n        selectionInProgress.current = false;\n        const { x, y } = getEventPosition(event.nativeEvent, containerBounds.current);\n        resetSelectedElements();\n        store.setState({\n            userSelectionRect: {\n                width: 0,\n                height: 0,\n                startX: x,\n                startY: y,\n                x,\n                y,\n            },\n        });\n        onSelectionStart?.(event);\n    };\n    const onPointerMove = (event) => {\n        const { userSelectionRect, transform, nodeLookup, edgeLookup, connectionLookup, triggerNodeChanges, triggerEdgeChanges, defaultEdgeOptions, } = store.getState();\n        if (!containerBounds.current || !userSelectionRect) {\n            return;\n        }\n        selectionInProgress.current = true;\n        const { x: mouseX, y: mouseY } = getEventPosition(event.nativeEvent, containerBounds.current);\n        const { startX, startY } = userSelectionRect;\n        const nextUserSelectRect = {\n            startX,\n            startY,\n            x: mouseX < startX ? mouseX : startX,\n            y: mouseY < startY ? mouseY : startY,\n            width: Math.abs(mouseX - startX),\n            height: Math.abs(mouseY - startY),\n        };\n        const prevSelectedNodeIds = selectedNodeIds.current;\n        const prevSelectedEdgeIds = selectedEdgeIds.current;\n        selectedNodeIds.current = new Set(getNodesInside(nodeLookup, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true).map((node) => node.id));\n        selectedEdgeIds.current = new Set();\n        const edgesSelectable = defaultEdgeOptions?.selectable ?? true;\n        // We look for all edges connected to the selected nodes\n        for (const nodeId of selectedNodeIds.current) {\n            const connections = connectionLookup.get(nodeId);\n            if (!connections)\n                continue;\n            for (const { edgeId } of connections.values()) {\n                const edge = edgeLookup.get(edgeId);\n                if (edge && (edge.selectable ?? edgesSelectable)) {\n                    selectedEdgeIds.current.add(edgeId);\n                }\n            }\n        }\n        if (!areSetsEqual(prevSelectedNodeIds, selectedNodeIds.current)) {\n            const changes = getSelectionChanges(nodeLookup, selectedNodeIds.current, true);\n            triggerNodeChanges(changes);\n        }\n        if (!areSetsEqual(prevSelectedEdgeIds, selectedEdgeIds.current)) {\n            const changes = getSelectionChanges(edgeLookup, selectedEdgeIds.current);\n            triggerEdgeChanges(changes);\n        }\n        store.setState({\n            userSelectionRect: nextUserSelectRect,\n            userSelectionActive: true,\n            nodesSelectionActive: false,\n        });\n    };\n    const onPointerUp = (event) => {\n        if (event.button !== 0 || !selectionStarted.current) {\n            return;\n        }\n        event.target?.releasePointerCapture?.(event.pointerId);\n        const { userSelectionRect } = store.getState();\n        /*\n         * We only want to trigger click functions when in selection mode if\n         * the user did not move the mouse.\n         */\n        if (!userSelectionActive && userSelectionRect && event.target === container.current) {\n            onClick?.(event);\n        }\n        store.setState({\n            userSelectionActive: false,\n            userSelectionRect: null,\n            nodesSelectionActive: selectedNodeIds.current.size > 0,\n        });\n        onSelectionEnd?.(event);\n        /*\n         * If the user kept holding the selectionKey during the selection,\n         * we need to reset the selectionInProgress, so the next click event is not prevented\n         */\n        if (selectionKeyPressed || selectionOnDrag) {\n            selectionInProgress.current = false;\n        }\n        selectionStarted.current = false;\n    };\n    const draggable = panOnDrag === true || (Array.isArray(panOnDrag) && panOnDrag.includes(0));\n    return (jsxs(\"div\", { className: cc(['react-flow__pane', { draggable, dragging, selection: isSelecting }]), onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container), onContextMenu: wrapHandler(onContextMenu, container), onWheel: wrapHandler(onWheel, container), onPointerEnter: hasActiveSelection ? undefined : onPaneMouseEnter, onPointerDown: hasActiveSelection ? onPointerDown : onPaneMouseMove, onPointerMove: hasActiveSelection ? onPointerMove : onPaneMouseMove, onPointerUp: hasActiveSelection ? onPointerUp : undefined, onPointerLeave: onPaneMouseLeave, ref: container, style: containerStyle, children: [children, jsx(UserSelection, {})] }));\n}\n\n/*\n * this handler is called by\n * 1. the click handler when node is not draggable or selectNodesOnDrag = false\n * or\n * 2. the on drag start handler when node is draggable and selectNodesOnDrag = true\n */\nfunction handleNodeClick({ id, store, unselect = false, nodeRef, }) {\n    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeLookup, onError } = store.getState();\n    const node = nodeLookup.get(id);\n    if (!node) {\n        onError?.('012', errorMessages['error012'](id));\n        return;\n    }\n    store.setState({ nodesSelectionActive: false });\n    if (!node.selected) {\n        addSelectedNodes([id]);\n    }\n    else if (unselect || (node.selected && multiSelectionActive)) {\n        unselectNodesAndEdges({ nodes: [node], edges: [] });\n        requestAnimationFrame(() => nodeRef?.current?.blur());\n    }\n}\n\n/**\n * Hook for calling XYDrag helper from @xyflow/system.\n *\n * @internal\n */\nfunction useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, nodeClickDistance, }) {\n    const store = useStoreApi();\n    const [dragging, setDragging] = useState(false);\n    const xyDrag = useRef();\n    useEffect(() => {\n        xyDrag.current = XYDrag({\n            getStoreItems: () => store.getState(),\n            onNodeMouseDown: (id) => {\n                handleNodeClick({\n                    id,\n                    store,\n                    nodeRef,\n                });\n            },\n            onDragStart: () => {\n                setDragging(true);\n            },\n            onDragStop: () => {\n                setDragging(false);\n            },\n        });\n    }, []);\n    useEffect(() => {\n        if (disabled) {\n            xyDrag.current?.destroy();\n        }\n        else if (nodeRef.current) {\n            xyDrag.current?.update({\n                noDragClassName,\n                handleSelector,\n                domNode: nodeRef.current,\n                isSelectable,\n                nodeId,\n                nodeClickDistance,\n            });\n            return () => {\n                xyDrag.current?.destroy();\n            };\n        }\n    }, [noDragClassName, handleSelector, disabled, isSelectable, nodeRef, nodeId]);\n    return dragging;\n}\n\nconst selectedAndDraggable = (nodesDraggable) => (n) => n.selected && (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined'));\n/**\n * Hook for updating node positions by passing a direction and factor\n *\n * @internal\n * @returns function for updating node positions\n */\nfunction useMoveSelectedNodes() {\n    const store = useStoreApi();\n    const moveSelectedNodes = useCallback((params) => {\n        const { nodeExtent, snapToGrid, snapGrid, nodesDraggable, onError, updateNodePositions, nodeLookup, nodeOrigin } = store.getState();\n        const nodeUpdates = new Map();\n        const isSelected = selectedAndDraggable(nodesDraggable);\n        /*\n         * by default a node moves 5px on each key press\n         * if snap grid is enabled, we use that for the velocity\n         */\n        const xVelo = snapToGrid ? snapGrid[0] : 5;\n        const yVelo = snapToGrid ? snapGrid[1] : 5;\n        const xDiff = params.direction.x * xVelo * params.factor;\n        const yDiff = params.direction.y * yVelo * params.factor;\n        for (const [, node] of nodeLookup) {\n            if (!isSelected(node)) {\n                continue;\n            }\n            let nextPosition = {\n                x: node.internals.positionAbsolute.x + xDiff,\n                y: node.internals.positionAbsolute.y + yDiff,\n            };\n            if (snapToGrid) {\n                nextPosition = snapPosition(nextPosition, snapGrid);\n            }\n            const { position, positionAbsolute } = calculateNodePosition({\n                nodeId: node.id,\n                nextPosition,\n                nodeLookup,\n                nodeExtent,\n                nodeOrigin,\n                onError,\n            });\n            node.position = position;\n            node.internals.positionAbsolute = positionAbsolute;\n            nodeUpdates.set(node.id, node);\n        }\n        updateNodePositions(nodeUpdates);\n    }, []);\n    return moveSelectedNodes;\n}\n\nconst NodeIdContext = createContext(null);\nconst Provider = NodeIdContext.Provider;\nNodeIdContext.Consumer;\n/**\n * You can use this hook to get the id of the node it is used inside. It is useful\n * if you need the node's id deeper in the render tree but don't want to manually\n * drill down the id as a prop.\n *\n * @public\n * @returns id of the node\n *\n * @example\n *```jsx\n *import { useNodeId } from '@xyflow/react';\n *\n *export default function CustomNode() {\n *  return (\n *    <div>\n *      <span>This node has an id of </span>\n *      <NodeIdDisplay />\n *    </div>\n *  );\n *}\n *\n *function NodeIdDisplay() {\n *  const nodeId = useNodeId();\n *\n *  return <span>{nodeId}</span>;\n *}\n *```\n */\nconst useNodeId = () => {\n    const nodeId = useContext(NodeIdContext);\n    return nodeId;\n};\n\nconst selector$g = (s) => ({\n    connectOnClick: s.connectOnClick,\n    noPanClassName: s.noPanClassName,\n    rfId: s.rfId,\n});\nconst connectingSelector = (nodeId, handleId, type) => (state) => {\n    const { connectionClickStartHandle: clickHandle, connectionMode, connection } = state;\n    const { fromHandle, toHandle, isValid } = connection;\n    const connectingTo = toHandle?.nodeId === nodeId && toHandle?.id === handleId && toHandle?.type === type;\n    return {\n        connectingFrom: fromHandle?.nodeId === nodeId && fromHandle?.id === handleId && fromHandle?.type === type,\n        connectingTo,\n        clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.id === handleId && clickHandle?.type === type,\n        isPossibleEndHandle: connectionMode === ConnectionMode.Strict\n            ? fromHandle?.type !== type\n            : nodeId !== fromHandle?.nodeId || handleId !== fromHandle?.id,\n        connectionInProcess: !!fromHandle,\n        clickConnectionInProcess: !!clickHandle,\n        valid: connectingTo && isValid,\n    };\n};\nfunction HandleComponent({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref) {\n    const handleId = id || null;\n    const isTarget = type === 'target';\n    const store = useStoreApi();\n    const nodeId = useNodeId();\n    const { connectOnClick, noPanClassName, rfId } = useStore(selector$g, shallow);\n    const { connectingFrom, connectingTo, clickConnecting, isPossibleEndHandle, connectionInProcess, clickConnectionInProcess, valid, } = useStore(connectingSelector(nodeId, handleId, type), shallow);\n    if (!nodeId) {\n        store.getState().onError?.('010', errorMessages['error010']());\n    }\n    const onConnectExtended = (params) => {\n        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();\n        const edgeParams = {\n            ...defaultEdgeOptions,\n            ...params,\n        };\n        if (hasDefaultEdges) {\n            const { edges, setEdges } = store.getState();\n            setEdges(addEdge(edgeParams, edges));\n        }\n        onConnectAction?.(edgeParams);\n        onConnect?.(edgeParams);\n    };\n    const onPointerDown = (event) => {\n        if (!nodeId) {\n            return;\n        }\n        const isMouseTriggered = isMouseEvent(event.nativeEvent);\n        if (isConnectableStart &&\n            ((isMouseTriggered && event.button === 0) || !isMouseTriggered)) {\n            const currentStore = store.getState();\n            XYHandle.onPointerDown(event.nativeEvent, {\n                autoPanOnConnect: currentStore.autoPanOnConnect,\n                connectionMode: currentStore.connectionMode,\n                connectionRadius: currentStore.connectionRadius,\n                domNode: currentStore.domNode,\n                nodeLookup: currentStore.nodeLookup,\n                lib: currentStore.lib,\n                isTarget,\n                handleId,\n                nodeId,\n                flowId: currentStore.rfId,\n                panBy: currentStore.panBy,\n                cancelConnection: currentStore.cancelConnection,\n                onConnectStart: currentStore.onConnectStart,\n                onConnectEnd: currentStore.onConnectEnd,\n                updateConnection: currentStore.updateConnection,\n                onConnect: onConnectExtended,\n                isValidConnection: isValidConnection || currentStore.isValidConnection,\n                getTransform: () => store.getState().transform,\n                getFromHandle: () => store.getState().connection.fromHandle,\n                autoPanSpeed: currentStore.autoPanSpeed,\n            });\n        }\n        if (isMouseTriggered) {\n            onMouseDown?.(event);\n        }\n        else {\n            onTouchStart?.(event);\n        }\n    };\n    const onClick = (event) => {\n        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore, lib, rfId: flowId, nodeLookup, connection: connectionState, } = store.getState();\n        if (!nodeId || (!connectionClickStartHandle && !isConnectableStart)) {\n            return;\n        }\n        if (!connectionClickStartHandle) {\n            onClickConnectStart?.(event.nativeEvent, { nodeId, handleId, handleType: type });\n            store.setState({ connectionClickStartHandle: { nodeId, type, id: handleId } });\n            return;\n        }\n        const doc = getHostForElement(event.target);\n        const isValidConnectionHandler = isValidConnection || isValidConnectionStore;\n        const { connection, isValid } = XYHandle.isValid(event.nativeEvent, {\n            handle: {\n                nodeId,\n                id: handleId,\n                type,\n            },\n            connectionMode,\n            fromNodeId: connectionClickStartHandle.nodeId,\n            fromHandleId: connectionClickStartHandle.id || null,\n            fromType: connectionClickStartHandle.type,\n            isValidConnection: isValidConnectionHandler,\n            flowId,\n            doc,\n            lib,\n            nodeLookup,\n        });\n        if (isValid && connection) {\n            onConnectExtended(connection);\n        }\n        const connectionClone = structuredClone(connectionState);\n        delete connectionClone.inProgress;\n        connectionClone.toPosition = connectionClone.toHandle ? connectionClone.toHandle.position : null;\n        onClickConnectEnd?.(event, connectionClone);\n        store.setState({ connectionClickStartHandle: null });\n    };\n    return (jsx(\"div\", { \"data-handleid\": handleId, \"data-nodeid\": nodeId, \"data-handlepos\": position, \"data-id\": `${rfId}-${nodeId}-${handleId}-${type}`, className: cc([\n            'react-flow__handle',\n            `react-flow__handle-${position}`,\n            'nodrag',\n            noPanClassName,\n            className,\n            {\n                source: !isTarget,\n                target: isTarget,\n                connectable: isConnectable,\n                connectablestart: isConnectableStart,\n                connectableend: isConnectableEnd,\n                clickconnecting: clickConnecting,\n                connectingfrom: connectingFrom,\n                connectingto: connectingTo,\n                valid,\n                /*\n                 * shows where you can start a connection from\n                 * and where you can end it while connecting\n                 */\n                connectionindicator: isConnectable &&\n                    (!connectionInProcess || isPossibleEndHandle) &&\n                    (connectionInProcess || clickConnectionInProcess ? isConnectableEnd : isConnectableStart),\n            },\n        ]), onMouseDown: onPointerDown, onTouchStart: onPointerDown, onClick: connectOnClick ? onClick : undefined, ref: ref, ...rest, children: children }));\n}\n/**\n * The `<Handle />` component is used in your [custom nodes](/learn/customization/custom-nodes)\n * to define connection points.\n *\n *@public\n *\n *@example\n *\n *```jsx\n *import { Handle, Position } from '@xyflow/react';\n *\n *export function CustomNode({ data }) {\n *  return (\n *    <>\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *```\n */\nconst Handle = memo(fixedForwardRef(HandleComponent));\n\nfunction InputNode({ data, isConnectable, sourcePosition = Position.Bottom }) {\n    return (jsxs(Fragment, { children: [data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction DefaultNode({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom, }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction GroupNode() {\n    return null;\n}\n\nfunction OutputNode({ data, isConnectable, targetPosition = Position.Top }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label] }));\n}\n\nconst arrowKeyDiffs = {\n    ArrowUp: { x: 0, y: -1 },\n    ArrowDown: { x: 0, y: 1 },\n    ArrowLeft: { x: -1, y: 0 },\n    ArrowRight: { x: 1, y: 0 },\n};\nconst builtinNodeTypes = {\n    input: InputNode,\n    default: DefaultNode,\n    output: OutputNode,\n    group: GroupNode,\n};\nfunction getNodeInlineStyleDimensions(node) {\n    if (node.internals.handleBounds === undefined) {\n        return {\n            width: node.width ?? node.initialWidth ?? node.style?.width,\n            height: node.height ?? node.initialHeight ?? node.style?.height,\n        };\n    }\n    return {\n        width: node.width ?? node.style?.width,\n        height: node.height ?? node.style?.height,\n    };\n}\n\nconst selector$f = (s) => {\n    const { width, height, x, y } = getInternalNodesBounds(s.nodeLookup, {\n        filter: (node) => !!node.selected,\n    });\n    return {\n        width: isNumeric(width) ? width : null,\n        height: isNumeric(height) ? height : null,\n        userSelectionActive: s.userSelectionActive,\n        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]}) translate(${x}px,${y}px)`,\n    };\n};\nfunction NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y, }) {\n    const store = useStoreApi();\n    const { width, height, transformString, userSelectionActive } = useStore(selector$f, shallow);\n    const moveSelectedNodes = useMoveSelectedNodes();\n    const nodeRef = useRef(null);\n    useEffect(() => {\n        if (!disableKeyboardA11y) {\n            nodeRef.current?.focus({\n                preventScroll: true,\n            });\n        }\n    }, [disableKeyboardA11y]);\n    useDrag({\n        nodeRef,\n    });\n    if (userSelectionActive || !width || !height) {\n        return null;\n    }\n    const onContextMenu = onSelectionContextMenu\n        ? (event) => {\n            const selectedNodes = store.getState().nodes.filter((n) => n.selected);\n            onSelectionContextMenu(event, selectedNodes);\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            event.preventDefault();\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc(['react-flow__nodesselection', 'react-flow__container', noPanClassName]), style: {\n            transform: transformString,\n        }, children: jsx(\"div\", { ref: nodeRef, className: \"react-flow__nodesselection-rect\", onContextMenu: onContextMenu, tabIndex: disableKeyboardA11y ? undefined : -1, onKeyDown: disableKeyboardA11y ? undefined : onKeyDown, style: {\n                width,\n                height,\n            } }) }));\n}\n\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst selector$e = (s) => {\n    return { nodesSelectionActive: s.nodesSelectionActive, userSelectionActive: s.userSelectionActive };\n};\nfunction FlowRendererComponent({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, paneClickDistance, deleteKeyCode, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y, onViewportChange, isControlledViewport, }) {\n    const { nodesSelectionActive, userSelectionActive } = useStore(selector$e);\n    const selectionKeyPressed = useKeyPress(selectionKeyCode, { target: win });\n    const panActivationKeyPressed = useKeyPress(panActivationKeyCode, { target: win });\n    const panOnDrag = panActivationKeyPressed || _panOnDrag;\n    const panOnScroll = panActivationKeyPressed || _panOnScroll;\n    const _selectionOnDrag = selectionOnDrag && panOnDrag !== true;\n    const isSelecting = selectionKeyPressed || userSelectionActive || _selectionOnDrag;\n    useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode });\n    return (jsx(ZoomPane, { onPaneContextMenu: onPaneContextMenu, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, zoomOnDoubleClick: zoomOnDoubleClick, panOnDrag: !selectionKeyPressed && panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, zoomActivationKeyCode: zoomActivationKeyCode, preventScrolling: preventScrolling, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, onViewportChange: onViewportChange, isControlledViewport: isControlledViewport, paneClickDistance: paneClickDistance, children: jsxs(Pane, { onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, panOnDrag: panOnDrag, isSelecting: !!isSelecting, selectionMode: selectionMode, selectionKeyPressed: selectionKeyPressed, selectionOnDrag: _selectionOnDrag, children: [children, nodesSelectionActive && (jsx(NodesSelection, { onSelectionContextMenu: onSelectionContextMenu, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y }))] }) }));\n}\nFlowRendererComponent.displayName = 'FlowRenderer';\nconst FlowRenderer = memo(FlowRendererComponent);\n\nconst selector$d = (onlyRenderVisible) => (s) => {\n    return onlyRenderVisible\n        ? getNodesInside(s.nodeLookup, { x: 0, y: 0, width: s.width, height: s.height }, s.transform, true).map((node) => node.id)\n        : Array.from(s.nodeLookup.keys());\n};\n/**\n * Hook for getting the visible node ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible node ids\n */\nfunction useVisibleNodeIds(onlyRenderVisible) {\n    const nodeIds = useStore(useCallback(selector$d(onlyRenderVisible), [onlyRenderVisible]), shallow);\n    return nodeIds;\n}\n\nconst selector$c = (s) => s.updateNodeInternals;\nfunction useResizeObserver() {\n    const updateNodeInternals = useStore(selector$c);\n    const [resizeObserver] = useState(() => {\n        if (typeof ResizeObserver === 'undefined') {\n            return null;\n        }\n        return new ResizeObserver((entries) => {\n            const updates = new Map();\n            entries.forEach((entry) => {\n                const id = entry.target.getAttribute('data-id');\n                updates.set(id, {\n                    id,\n                    nodeElement: entry.target,\n                    force: true,\n                });\n            });\n            updateNodeInternals(updates);\n        });\n    });\n    useEffect(() => {\n        return () => {\n            resizeObserver?.disconnect();\n        };\n    }, [resizeObserver]);\n    return resizeObserver;\n}\n\n/**\n * Hook to handle the resize observation + internal updates for the passed node.\n *\n * @internal\n * @returns nodeRef - reference to the node element\n */\nfunction useNodeObserver({ node, nodeType, hasDimensions, resizeObserver, }) {\n    const store = useStoreApi();\n    const nodeRef = useRef(null);\n    const observedNode = useRef(null);\n    const prevSourcePosition = useRef(node.sourcePosition);\n    const prevTargetPosition = useRef(node.targetPosition);\n    const prevType = useRef(nodeType);\n    const isInitialized = hasDimensions && !!node.internals.handleBounds;\n    useEffect(() => {\n        if (nodeRef.current && !node.hidden && (!isInitialized || observedNode.current !== nodeRef.current)) {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n            }\n            resizeObserver?.observe(nodeRef.current);\n            observedNode.current = nodeRef.current;\n        }\n    }, [isInitialized, node.hidden]);\n    useEffect(() => {\n        return () => {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n                observedNode.current = null;\n            }\n        };\n    }, []);\n    useEffect(() => {\n        if (nodeRef.current) {\n            /*\n             * when the user programmatically changes the source or handle position, we need to update the internals\n             * to make sure the edges are updated correctly\n             */\n            const typeChanged = prevType.current !== nodeType;\n            const sourcePosChanged = prevSourcePosition.current !== node.sourcePosition;\n            const targetPosChanged = prevTargetPosition.current !== node.targetPosition;\n            if (typeChanged || sourcePosChanged || targetPosChanged) {\n                prevType.current = nodeType;\n                prevSourcePosition.current = node.sourcePosition;\n                prevTargetPosition.current = node.targetPosition;\n                store\n                    .getState()\n                    .updateNodeInternals(new Map([[node.id, { id: node.id, nodeElement: nodeRef.current, force: true }]]));\n            }\n        }\n    }, [node.id, nodeType, node.sourcePosition, node.targetPosition]);\n    return nodeRef;\n}\n\nfunction NodeWrapper({ id, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, nodesDraggable, elementsSelectable, nodesConnectable, nodesFocusable, resizeObserver, noDragClassName, noPanClassName, disableKeyboardA11y, rfId, nodeTypes, nodeClickDistance, onError, }) {\n    const { node, internals, isParent } = useStore((s) => {\n        const node = s.nodeLookup.get(id);\n        const isParent = s.parentLookup.has(id);\n        return {\n            node,\n            internals: node.internals,\n            isParent,\n        };\n    }, shallow);\n    let nodeType = node.type || 'default';\n    let NodeComponent = nodeTypes?.[nodeType] || builtinNodeTypes[nodeType];\n    if (NodeComponent === undefined) {\n        onError?.('003', errorMessages['error003'](nodeType));\n        nodeType = 'default';\n        NodeComponent = builtinNodeTypes.default;\n    }\n    const isDraggable = !!(node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'));\n    const isSelectable = !!(node.selectable || (elementsSelectable && typeof node.selectable === 'undefined'));\n    const isConnectable = !!(node.connectable || (nodesConnectable && typeof node.connectable === 'undefined'));\n    const isFocusable = !!(node.focusable || (nodesFocusable && typeof node.focusable === 'undefined'));\n    const store = useStoreApi();\n    const hasDimensions = nodeHasDimensions(node);\n    const nodeRef = useNodeObserver({ node, nodeType, hasDimensions, resizeObserver });\n    const dragging = useDrag({\n        nodeRef,\n        disabled: node.hidden || !isDraggable,\n        noDragClassName,\n        handleSelector: node.dragHandle,\n        nodeId: id,\n        isSelectable,\n        nodeClickDistance,\n    });\n    const moveSelectedNodes = useMoveSelectedNodes();\n    if (node.hidden) {\n        return null;\n    }\n    const nodeDimensions = getNodeDimensions(node);\n    const inlineDimensions = getNodeInlineStyleDimensions(node);\n    const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;\n    const onMouseEnterHandler = onMouseEnter\n        ? (event) => onMouseEnter(event, { ...internals.userNode })\n        : undefined;\n    const onMouseMoveHandler = onMouseMove\n        ? (event) => onMouseMove(event, { ...internals.userNode })\n        : undefined;\n    const onMouseLeaveHandler = onMouseLeave\n        ? (event) => onMouseLeave(event, { ...internals.userNode })\n        : undefined;\n    const onContextMenuHandler = onContextMenu\n        ? (event) => onContextMenu(event, { ...internals.userNode })\n        : undefined;\n    const onDoubleClickHandler = onDoubleClick\n        ? (event) => onDoubleClick(event, { ...internals.userNode })\n        : undefined;\n    const onSelectNodeHandler = (event) => {\n        const { selectNodesOnDrag, nodeDragThreshold } = store.getState();\n        if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {\n            /*\n             * this handler gets called by XYDrag on drag start when selectNodesOnDrag=true\n             * here we only need to call it when selectNodesOnDrag=false\n             */\n            handleNodeClick({\n                id,\n                store,\n                nodeRef,\n            });\n        }\n        if (onClick) {\n            onClick(event, { ...internals.userNode });\n        }\n    };\n    const onKeyDown = (event) => {\n        if (isInputDOMNode(event.nativeEvent) || disableKeyboardA11y) {\n            return;\n        }\n        if (elementSelectionKeys.includes(event.key) && isSelectable) {\n            const unselect = event.key === 'Escape';\n            handleNodeClick({\n                id,\n                store,\n                unselect,\n                nodeRef,\n            });\n        }\n        else if (isDraggable && node.selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            // prevent default scrolling behavior on arrow key press when node is moved\n            event.preventDefault();\n            store.setState({\n                ariaLiveMessage: `Moved selected node ${event.key\n                    .replace('Arrow', '')\n                    .toLowerCase()}. New position, x: ${~~internals.positionAbsolute.x}, y: ${~~internals.positionAbsolute.y}`,\n            });\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc([\n            'react-flow__node',\n            `react-flow__node-${nodeType}`,\n            {\n                // this is overwritable by passing `nopan` as a class name\n                [noPanClassName]: isDraggable,\n            },\n            node.className,\n            {\n                selected: node.selected,\n                selectable: isSelectable,\n                parent: isParent,\n                draggable: isDraggable,\n                dragging,\n            },\n        ]), ref: nodeRef, style: {\n            zIndex: internals.z,\n            transform: `translate(${internals.positionAbsolute.x}px,${internals.positionAbsolute.y}px)`,\n            pointerEvents: hasPointerEvents ? 'all' : 'none',\n            visibility: hasDimensions ? 'visible' : 'hidden',\n            ...node.style,\n            ...inlineDimensions,\n        }, \"data-id\": id, \"data-testid\": `rf__node-${id}`, onMouseEnter: onMouseEnterHandler, onMouseMove: onMouseMoveHandler, onMouseLeave: onMouseLeaveHandler, onContextMenu: onContextMenuHandler, onClick: onSelectNodeHandler, onDoubleClick: onDoubleClickHandler, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : undefined, \"aria-describedby\": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`, \"aria-label\": node.ariaLabel, children: jsx(Provider, { value: id, children: jsx(NodeComponent, { id: id, data: node.data, type: nodeType, positionAbsoluteX: internals.positionAbsolute.x, positionAbsoluteY: internals.positionAbsolute.y, selected: node.selected ?? false, selectable: isSelectable, draggable: isDraggable, deletable: node.deletable ?? true, isConnectable: isConnectable, sourcePosition: node.sourcePosition, targetPosition: node.targetPosition, dragging: dragging, dragHandle: node.dragHandle, zIndex: internals.z, parentId: node.parentId, ...nodeDimensions }) }) }));\n}\n\nconst selector$b = (s) => ({\n    nodesDraggable: s.nodesDraggable,\n    nodesConnectable: s.nodesConnectable,\n    nodesFocusable: s.nodesFocusable,\n    elementsSelectable: s.elementsSelectable,\n    onError: s.onError,\n});\nfunction NodeRendererComponent(props) {\n    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, onError } = useStore(selector$b, shallow);\n    const nodeIds = useVisibleNodeIds(props.onlyRenderVisibleElements);\n    const resizeObserver = useResizeObserver();\n    return (jsx(\"div\", { className: \"react-flow__nodes\", style: containerStyle, children: nodeIds.map((nodeId) => {\n            return (\n            /*\n             * The split of responsibilities between NodeRenderer and\n             * NodeComponentWrapper may appear weird. However, it’s designed to\n             * minimize the cost of updates when individual nodes change.\n             *\n             * For example, when you’re dragging a single node, that node gets\n             * updated multiple times per second. If `NodeRenderer` were to update\n             * every time, it would have to re-run the `nodes.map()` loop every\n             * time. This gets pricey with hundreds of nodes, especially if every\n             * loop cycle does more than just rendering a JSX element!\n             *\n             * As a result of this choice, we took the following implementation\n             * decisions:\n             * - NodeRenderer subscribes *only* to node IDs – and therefore\n             *   rerender *only* when visible nodes are added or removed.\n             * - NodeRenderer performs all operations the result of which can be\n             *   shared between nodes (such as creating the `ResizeObserver`\n             *   instance, or subscribing to `selector`). This means extra prop\n             *   drilling into `NodeComponentWrapper`, but it means we need to run\n             *   these operations only once – instead of once per node.\n             * - Any operations that you’d normally write inside `nodes.map` are\n             *   moved into `NodeComponentWrapper`. This ensures they are\n             *   memorized – so if `NodeRenderer` *has* to rerender, it only\n             *   needs to regenerate the list of nodes, nothing else.\n             */\n            jsx(NodeWrapper, { id: nodeId, nodeTypes: props.nodeTypes, nodeExtent: props.nodeExtent, onClick: props.onNodeClick, onMouseEnter: props.onNodeMouseEnter, onMouseMove: props.onNodeMouseMove, onMouseLeave: props.onNodeMouseLeave, onContextMenu: props.onNodeContextMenu, onDoubleClick: props.onNodeDoubleClick, noDragClassName: props.noDragClassName, noPanClassName: props.noPanClassName, rfId: props.rfId, disableKeyboardA11y: props.disableKeyboardA11y, resizeObserver: resizeObserver, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, elementsSelectable: elementsSelectable, nodeClickDistance: props.nodeClickDistance, onError: onError }, nodeId));\n        }) }));\n}\nNodeRendererComponent.displayName = 'NodeRenderer';\nconst NodeRenderer = memo(NodeRendererComponent);\n\n/**\n * Hook for getting the visible edge ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible edge ids\n */\nfunction useVisibleEdgeIds(onlyRenderVisible) {\n    const edgeIds = useStore(useCallback((s) => {\n        if (!onlyRenderVisible) {\n            return s.edges.map((edge) => edge.id);\n        }\n        const visibleEdgeIds = [];\n        if (s.width && s.height) {\n            for (const edge of s.edges) {\n                const sourceNode = s.nodeLookup.get(edge.source);\n                const targetNode = s.nodeLookup.get(edge.target);\n                if (sourceNode &&\n                    targetNode &&\n                    isEdgeVisible({\n                        sourceNode,\n                        targetNode,\n                        width: s.width,\n                        height: s.height,\n                        transform: s.transform,\n                    })) {\n                    visibleEdgeIds.push(edge.id);\n                }\n            }\n        }\n        return visibleEdgeIds;\n    }, [onlyRenderVisible]), shallow);\n    return edgeIds;\n}\n\nconst ArrowSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", fill: \"none\", points: \"-5,-4 0,0 -5,4\" }));\n};\nconst ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            fill: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", points: \"-5,-4 0,0 -5,4 -5,-4\" }));\n};\nconst MarkerSymbols = {\n    [MarkerType.Arrow]: ArrowSymbol,\n    [MarkerType.ArrowClosed]: ArrowClosedSymbol,\n};\nfunction useMarkerSymbol(type) {\n    const store = useStoreApi();\n    const symbol = useMemo(() => {\n        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);\n        if (!symbolExists) {\n            store.getState().onError?.('009', errorMessages['error009'](type));\n            return null;\n        }\n        return MarkerSymbols[type];\n    }, [type]);\n    return symbol;\n}\n\nconst Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse', }) => {\n    const Symbol = useMarkerSymbol(type);\n    if (!Symbol) {\n        return null;\n    }\n    return (jsx(\"marker\", { className: \"react-flow__arrowhead\", id: id, markerWidth: `${width}`, markerHeight: `${height}`, viewBox: \"-10 -10 20 20\", markerUnits: markerUnits, orient: orient, refX: \"0\", refY: \"0\", children: jsx(Symbol, { color: color, strokeWidth: strokeWidth }) }));\n};\n/*\n * when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore\n * when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper\n * that we can then use for creating our unique marker ids\n */\nconst MarkerDefinitions = ({ defaultColor, rfId }) => {\n    const edges = useStore((s) => s.edges);\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    const markers = useMemo(() => {\n        const markers = createMarkerIds(edges, {\n            id: rfId,\n            defaultColor,\n            defaultMarkerStart: defaultEdgeOptions?.markerStart,\n            defaultMarkerEnd: defaultEdgeOptions?.markerEnd,\n        });\n        return markers;\n    }, [edges, defaultEdgeOptions, rfId, defaultColor]);\n    if (!markers.length) {\n        return null;\n    }\n    return (jsx(\"svg\", { className: \"react-flow__marker\", children: jsx(\"defs\", { children: markers.map((marker) => (jsx(Marker, { id: marker.id, type: marker.type, color: marker.color, width: marker.width, height: marker.height, markerUnits: marker.markerUnits, strokeWidth: marker.strokeWidth, orient: marker.orient }, marker.id))) }) }));\n};\nMarkerDefinitions.displayName = 'MarkerDefinitions';\nvar MarkerDefinitions$1 = memo(MarkerDefinitions);\n\nfunction EdgeTextComponent({ x, y, label, labelStyle = {}, labelShowBg = true, labelBgStyle = {}, labelBgPadding = [2, 4], labelBgBorderRadius = 2, children, className, ...rest }) {\n    const [edgeTextBbox, setEdgeTextBbox] = useState({ x: 1, y: 0, width: 0, height: 0 });\n    const edgeTextClasses = cc(['react-flow__edge-textwrapper', className]);\n    const edgeTextRef = useRef(null);\n    useEffect(() => {\n        if (edgeTextRef.current) {\n            const textBbox = edgeTextRef.current.getBBox();\n            setEdgeTextBbox({\n                x: textBbox.x,\n                y: textBbox.y,\n                width: textBbox.width,\n                height: textBbox.height,\n            });\n        }\n    }, [label]);\n    if (typeof label === 'undefined' || !label) {\n        return null;\n    }\n    return (jsxs(\"g\", { transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`, className: edgeTextClasses, visibility: edgeTextBbox.width ? 'visible' : 'hidden', ...rest, children: [labelShowBg && (jsx(\"rect\", { width: edgeTextBbox.width + 2 * labelBgPadding[0], x: -labelBgPadding[0], y: -labelBgPadding[1], height: edgeTextBbox.height + 2 * labelBgPadding[1], className: \"react-flow__edge-textbg\", style: labelBgStyle, rx: labelBgBorderRadius, ry: labelBgBorderRadius })), jsx(\"text\", { className: \"react-flow__edge-text\", y: edgeTextBbox.height / 2, dy: \"0.3em\", ref: edgeTextRef, style: labelStyle, children: label }), children] }));\n}\nEdgeTextComponent.displayName = 'EdgeText';\n/**\n * You can use the `<EdgeText />` component as a helper component to display text\n * within your custom edges.\n *\n *@public\n *\n *@example\n *```jsx\n *import { EdgeText } from '@xyflow/react';\n *\n *export function CustomEdgeLabel({ label }) {\n *  return (\n *    <EdgeText\n *      x={100}\n *      y={100}\n *      label={label}\n *      labelStyle={{ fill: 'white' }}\n *      labelShowBg\n *      labelBgStyle={{ fill: 'red' }}\n *      labelBgPadding={[2, 4]}\n *      labelBgBorderRadius={2}\n *    />\n *  );\n *}\n *```\n */\nconst EdgeText = memo(EdgeTextComponent);\n\n/**\n * The `<BaseEdge />` component gets used internally for all the edges. It can be\n * used inside a custom edge and handles the invisible helper edge and the edge label\n * for you.\n *\n * @public\n * @example\n * ```jsx\n *import { BaseEdge } from '@xyflow/react';\n *\n *export function CustomEdge({ sourceX, sourceY, targetX, targetY, ...props }) {\n *  const [edgePath] = getStraightPath({\n *    sourceX,\n *    sourceY,\n *    targetX,\n *    targetY,\n *  });\n *\n *  return <BaseEdge path={edgePath} {...props} />;\n *}\n *```\n *\n * @remarks If you want to use an edge marker with the [`<BaseEdge />`](/api-reference/components/base-edge) component,\n * you can pass the `markerStart` or `markerEnd` props passed to your custom edge\n * through to the [`<BaseEdge />`](/api-reference/components/base-edge) component.\n * You can see all the props passed to a custom edge by looking at the [`EdgeProps`](/api-reference/types/edge-props) type.\n */\nfunction BaseEdge({ path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, interactionWidth = 20, ...props }) {\n    return (jsxs(Fragment, { children: [jsx(\"path\", { ...props, d: path, fill: \"none\", className: cc(['react-flow__edge-path', props.className]) }), interactionWidth && (jsx(\"path\", { d: path, fill: \"none\", strokeOpacity: 0, strokeWidth: interactionWidth, className: \"react-flow__edge-interaction\" })), label && isNumeric(labelX) && isNumeric(labelY) ? (jsx(EdgeText, { x: labelX, y: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius })) : null] }));\n}\n\nfunction getControl({ pos, x1, y1, x2, y2 }) {\n    if (pos === Position.Left || pos === Position.Right) {\n        return [0.5 * (x1 + x2), y1];\n    }\n    return [x1, 0.5 * (y1 + y2)];\n}\n/**\n * The `getSimpleBezierPath` util returns everything you need to render a simple\n * bezier edge between two nodes.\n * @public\n */\nfunction getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, }) {\n    const [sourceControlX, sourceControlY] = getControl({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n    });\n    const [targetControlX, targetControlY] = getControl({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nfunction createSimpleBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSimpleBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst SimpleBezierEdge = createSimpleBezierEdge({ isInternal: false });\nconst SimpleBezierEdgeInternal = createSimpleBezierEdge({ isInternal: true });\nSimpleBezierEdge.displayName = 'SimpleBezierEdge';\nSimpleBezierEdgeInternal.displayName = 'SimpleBezierEdgeInternal';\n\nfunction createSmoothStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSmoothStepPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            borderRadius: pathOptions?.borderRadius,\n            offset: pathOptions?.offset,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst SmoothStepEdge = createSmoothStepEdge({ isInternal: false });\nconst SmoothStepEdgeInternal = createSmoothStepEdge({ isInternal: true });\nSmoothStepEdge.displayName = 'SmoothStepEdge';\nSmoothStepEdgeInternal.displayName = 'SmoothStepEdgeInternal';\n\nfunction createStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, ...props }) => {\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(SmoothStepEdge, { ...props, id: _id, pathOptions: useMemo(() => ({ borderRadius: 0, offset: props.pathOptions?.offset }), [props.pathOptions?.offset]) }));\n    });\n}\nconst StepEdge = createStepEdge({ isInternal: false });\nconst StepEdgeInternal = createStepEdge({ isInternal: true });\nStepEdge.displayName = 'StepEdge';\nStepEdgeInternal.displayName = 'StepEdgeInternal';\n\nfunction createStraightEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getStraightPath({ sourceX, sourceY, targetX, targetY });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst StraightEdge = createStraightEdge({ isInternal: false });\nconst StraightEdgeInternal = createStraightEdge({ isInternal: true });\nStraightEdge.displayName = 'StraightEdge';\nStraightEdgeInternal.displayName = 'StraightEdgeInternal';\n\nfunction createBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            curvature: pathOptions?.curvature,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst BezierEdge = createBezierEdge({ isInternal: false });\nconst BezierEdgeInternal = createBezierEdge({ isInternal: true });\nBezierEdge.displayName = 'BezierEdge';\nBezierEdgeInternal.displayName = 'BezierEdgeInternal';\n\nconst builtinEdgeTypes = {\n    default: BezierEdgeInternal,\n    straight: StraightEdgeInternal,\n    step: StepEdgeInternal,\n    smoothstep: SmoothStepEdgeInternal,\n    simplebezier: SimpleBezierEdgeInternal,\n};\nconst nullPosition = {\n    sourceX: null,\n    sourceY: null,\n    targetX: null,\n    targetY: null,\n    sourcePosition: null,\n    targetPosition: null,\n};\n\nconst shiftX = (x, shift, position) => {\n    if (position === Position.Left)\n        return x - shift;\n    if (position === Position.Right)\n        return x + shift;\n    return x;\n};\nconst shiftY = (y, shift, position) => {\n    if (position === Position.Top)\n        return y - shift;\n    if (position === Position.Bottom)\n        return y + shift;\n    return y;\n};\nconst EdgeUpdaterClassName = 'react-flow__edgeupdater';\nfunction EdgeAnchor({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type, }) {\n    return (jsx(\"circle\", { onMouseDown: onMouseDown, onMouseEnter: onMouseEnter, onMouseOut: onMouseOut, className: cc([EdgeUpdaterClassName, `${EdgeUpdaterClassName}-${type}`]), cx: shiftX(centerX, radius, position), cy: shiftY(centerY, radius, position), r: radius, stroke: \"transparent\", fill: \"transparent\" }));\n}\n\nfunction EdgeUpdateAnchors({ isReconnectable, reconnectRadius, edge, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, onReconnect, onReconnectStart, onReconnectEnd, setReconnecting, setUpdateHover, }) {\n    const store = useStoreApi();\n    const handleEdgeUpdater = (event, oppositeHandle) => {\n        // avoid triggering edge updater if mouse btn is not left\n        if (event.button !== 0) {\n            return;\n        }\n        const { autoPanOnConnect, domNode, isValidConnection, connectionMode, connectionRadius, lib, onConnectStart, onConnectEnd, cancelConnection, nodeLookup, rfId: flowId, panBy, updateConnection, } = store.getState();\n        const isTarget = oppositeHandle.type === 'target';\n        setReconnecting(true);\n        onReconnectStart?.(event, edge, oppositeHandle.type);\n        const _onReconnectEnd = (evt, connectionState) => {\n            setReconnecting(false);\n            onReconnectEnd?.(evt, edge, oppositeHandle.type, connectionState);\n        };\n        const onConnectEdge = (connection) => onReconnect?.(edge, connection);\n        XYHandle.onPointerDown(event.nativeEvent, {\n            autoPanOnConnect,\n            connectionMode,\n            connectionRadius,\n            domNode,\n            handleId: oppositeHandle.id,\n            nodeId: oppositeHandle.nodeId,\n            nodeLookup,\n            isTarget,\n            edgeUpdaterType: oppositeHandle.type,\n            lib,\n            flowId,\n            cancelConnection,\n            panBy,\n            isValidConnection,\n            onConnect: onConnectEdge,\n            onConnectStart,\n            onConnectEnd,\n            onReconnectEnd: _onReconnectEnd,\n            updateConnection,\n            getTransform: () => store.getState().transform,\n            getFromHandle: () => store.getState().connection.fromHandle,\n        });\n    };\n    const onReconnectSourceMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.target, id: edge.targetHandle ?? null, type: 'target' });\n    const onReconnectTargetMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.source, id: edge.sourceHandle ?? null, type: 'source' });\n    const onReconnectMouseEnter = () => setUpdateHover(true);\n    const onReconnectMouseOut = () => setUpdateHover(false);\n    return (jsxs(Fragment, { children: [(isReconnectable === true || isReconnectable === 'source') && (jsx(EdgeAnchor, { position: sourcePosition, centerX: sourceX, centerY: sourceY, radius: reconnectRadius, onMouseDown: onReconnectSourceMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"source\" })), (isReconnectable === true || isReconnectable === 'target') && (jsx(EdgeAnchor, { position: targetPosition, centerX: targetX, centerY: targetY, radius: reconnectRadius, onMouseDown: onReconnectTargetMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"target\" }))] }));\n}\n\nfunction EdgeWrapper({ id, edgesFocusable, edgesReconnectable, elementsSelectable, onClick, onDoubleClick, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, rfId, edgeTypes, noPanClassName, onError, disableKeyboardA11y, }) {\n    let edge = useStore((s) => s.edgeLookup.get(id));\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    edge = defaultEdgeOptions ? { ...defaultEdgeOptions, ...edge } : edge;\n    let edgeType = edge.type || 'default';\n    let EdgeComponent = edgeTypes?.[edgeType] || builtinEdgeTypes[edgeType];\n    if (EdgeComponent === undefined) {\n        onError?.('011', errorMessages['error011'](edgeType));\n        edgeType = 'default';\n        EdgeComponent = builtinEdgeTypes.default;\n    }\n    const isFocusable = !!(edge.focusable || (edgesFocusable && typeof edge.focusable === 'undefined'));\n    const isReconnectable = typeof onReconnect !== 'undefined' &&\n        (edge.reconnectable || (edgesReconnectable && typeof edge.reconnectable === 'undefined'));\n    const isSelectable = !!(edge.selectable || (elementsSelectable && typeof edge.selectable === 'undefined'));\n    const edgeRef = useRef(null);\n    const [updateHover, setUpdateHover] = useState(false);\n    const [reconnecting, setReconnecting] = useState(false);\n    const store = useStoreApi();\n    const { zIndex, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition } = useStore(useCallback((store) => {\n        const sourceNode = store.nodeLookup.get(edge.source);\n        const targetNode = store.nodeLookup.get(edge.target);\n        if (!sourceNode || !targetNode) {\n            return {\n                zIndex: edge.zIndex,\n                ...nullPosition,\n            };\n        }\n        const edgePosition = getEdgePosition({\n            id,\n            sourceNode,\n            targetNode,\n            sourceHandle: edge.sourceHandle || null,\n            targetHandle: edge.targetHandle || null,\n            connectionMode: store.connectionMode,\n            onError,\n        });\n        const zIndex = getElevatedEdgeZIndex({\n            selected: edge.selected,\n            zIndex: edge.zIndex,\n            sourceNode,\n            targetNode,\n            elevateOnSelect: store.elevateEdgesOnSelect,\n        });\n        return {\n            zIndex,\n            ...(edgePosition || nullPosition),\n        };\n    }, [edge.source, edge.target, edge.sourceHandle, edge.targetHandle, edge.selected, edge.zIndex]), shallow);\n    const markerStartUrl = useMemo(() => (edge.markerStart ? `url('#${getMarkerId(edge.markerStart, rfId)}')` : undefined), [edge.markerStart, rfId]);\n    const markerEndUrl = useMemo(() => (edge.markerEnd ? `url('#${getMarkerId(edge.markerEnd, rfId)}')` : undefined), [edge.markerEnd, rfId]);\n    if (edge.hidden || sourceX === null || sourceY === null || targetX === null || targetY === null) {\n        return null;\n    }\n    const onEdgeClick = (event) => {\n        const { addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();\n        if (isSelectable) {\n            store.setState({ nodesSelectionActive: false });\n            if (edge.selected && multiSelectionActive) {\n                unselectNodesAndEdges({ nodes: [], edges: [edge] });\n                edgeRef.current?.blur();\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n        if (onClick) {\n            onClick(event, edge);\n        }\n    };\n    const onEdgeDoubleClick = onDoubleClick\n        ? (event) => {\n            onDoubleClick(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeContextMenu = onContextMenu\n        ? (event) => {\n            onContextMenu(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseEnter = onMouseEnter\n        ? (event) => {\n            onMouseEnter(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseMove = onMouseMove\n        ? (event) => {\n            onMouseMove(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseLeave = onMouseLeave\n        ? (event) => {\n            onMouseLeave(event, { ...edge });\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && isSelectable) {\n            const { unselectNodesAndEdges, addSelectedEdges } = store.getState();\n            const unselect = event.key === 'Escape';\n            if (unselect) {\n                edgeRef.current?.blur();\n                unselectNodesAndEdges({ edges: [edge] });\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n    };\n    return (jsx(\"svg\", { style: { zIndex }, children: jsxs(\"g\", { className: cc([\n                'react-flow__edge',\n                `react-flow__edge-${edgeType}`,\n                edge.className,\n                noPanClassName,\n                {\n                    selected: edge.selected,\n                    animated: edge.animated,\n                    inactive: !isSelectable && !onClick,\n                    updating: updateHover,\n                    selectable: isSelectable,\n                },\n            ]), onClick: onEdgeClick, onDoubleClick: onEdgeDoubleClick, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : 'img', \"data-id\": id, \"data-testid\": `rf__edge-${id}`, \"aria-label\": edge.ariaLabel === null ? undefined : edge.ariaLabel || `Edge from ${edge.source} to ${edge.target}`, \"aria-describedby\": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined, ref: edgeRef, children: [!reconnecting && (jsx(EdgeComponent, { id: id, source: edge.source, target: edge.target, type: edge.type, selected: edge.selected, animated: edge.animated, selectable: isSelectable, deletable: edge.deletable ?? true, label: edge.label, labelStyle: edge.labelStyle, labelShowBg: edge.labelShowBg, labelBgStyle: edge.labelBgStyle, labelBgPadding: edge.labelBgPadding, labelBgBorderRadius: edge.labelBgBorderRadius, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, data: edge.data, style: edge.style, sourceHandleId: edge.sourceHandle, targetHandleId: edge.targetHandle, markerStart: markerStartUrl, markerEnd: markerEndUrl, pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined, interactionWidth: edge.interactionWidth })), isReconnectable && (jsx(EdgeUpdateAnchors, { edge: edge, isReconnectable: isReconnectable, reconnectRadius: reconnectRadius, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, setUpdateHover: setUpdateHover, setReconnecting: setReconnecting }))] }) }));\n}\n\nconst selector$a = (s) => ({\n    edgesFocusable: s.edgesFocusable,\n    edgesReconnectable: s.edgesReconnectable,\n    elementsSelectable: s.elementsSelectable,\n    connectionMode: s.connectionMode,\n    onError: s.onError,\n});\nfunction EdgeRendererComponent({ defaultMarkerColor, onlyRenderVisibleElements, rfId, edgeTypes, noPanClassName, onReconnect, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, reconnectRadius, onEdgeDoubleClick, onReconnectStart, onReconnectEnd, disableKeyboardA11y, }) {\n    const { edgesFocusable, edgesReconnectable, elementsSelectable, onError } = useStore(selector$a, shallow);\n    const edgeIds = useVisibleEdgeIds(onlyRenderVisibleElements);\n    return (jsxs(\"div\", { className: \"react-flow__edges\", children: [jsx(MarkerDefinitions$1, { defaultColor: defaultMarkerColor, rfId: rfId }), edgeIds.map((id) => {\n                return (jsx(EdgeWrapper, { id: id, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, noPanClassName: noPanClassName, onReconnect: onReconnect, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onClick: onEdgeClick, reconnectRadius: reconnectRadius, onDoubleClick: onEdgeDoubleClick, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, rfId: rfId, onError: onError, edgeTypes: edgeTypes, disableKeyboardA11y: disableKeyboardA11y }, id));\n            })] }));\n}\nEdgeRendererComponent.displayName = 'EdgeRenderer';\nconst EdgeRenderer = memo(EdgeRendererComponent);\n\nconst selector$9 = (s) => `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;\nfunction Viewport({ children }) {\n    const transform = useStore(selector$9);\n    return (jsx(\"div\", { className: \"react-flow__viewport xyflow__viewport react-flow__container\", style: { transform }, children: children }));\n}\n\n/**\n * Hook for calling onInit handler.\n *\n * @internal\n */\nfunction useOnInitHandler(onInit) {\n    const rfInstance = useReactFlow();\n    const isInitialized = useRef(false);\n    useEffect(() => {\n        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {\n            setTimeout(() => onInit(rfInstance), 1);\n            isInitialized.current = true;\n        }\n    }, [onInit, rfInstance.viewportInitialized]);\n}\n\nconst selector$8 = (state) => state.panZoom?.syncViewport;\n/**\n * Hook for syncing the viewport with the panzoom instance.\n *\n * @internal\n * @param viewport\n */\nfunction useViewportSync(viewport) {\n    const syncViewport = useStore(selector$8);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (viewport) {\n            syncViewport?.(viewport);\n            store.setState({ transform: [viewport.x, viewport.y, viewport.zoom] });\n        }\n    }, [viewport, syncViewport]);\n    return null;\n}\n\nfunction storeSelector$1(s) {\n    return s.connection.inProgress\n        ? { ...s.connection, to: pointToRendererPoint(s.connection.to, s.transform) }\n        : { ...s.connection };\n}\nfunction getSelector(connectionSelector) {\n    if (connectionSelector) {\n        const combinedSelector = (s) => {\n            const connection = storeSelector$1(s);\n            return connectionSelector(connection);\n        };\n        return combinedSelector;\n    }\n    return storeSelector$1;\n}\n/**\n * The `useConnection` hook returns the current connection when there is an active\n * connection interaction. If no connection interaction is active, it returns null\n * for every property. A typical use case for this hook is to colorize handles\n * based on a certain condition (e.g. if the connection is valid or not).\n *\n * @public\n * @example\n *\n * ```tsx\n *import { useConnection } from '@xyflow/react';\n *\n *function App() {\n *  const connection = useConnection();\n *\n *  return (\n *    <div> {connection ? `Someone is trying to make a connection from ${connection.fromNode} to this one.` : 'There are currently no incoming connections!'}\n *\n *   </div>\n *   );\n * }\n * ```\n *\n * @returns ConnectionState\n */\nfunction useConnection(connectionSelector) {\n    const combinedSelector = getSelector(connectionSelector);\n    return useStore(combinedSelector, shallow);\n}\n\nconst selector$7 = (s) => ({\n    nodesConnectable: s.nodesConnectable,\n    isValid: s.connection.isValid,\n    inProgress: s.connection.inProgress,\n    width: s.width,\n    height: s.height,\n});\nfunction ConnectionLineWrapper({ containerStyle, style, type, component, }) {\n    const { nodesConnectable, width, height, isValid, inProgress } = useStore(selector$7, shallow);\n    const renderConnection = !!(width && nodesConnectable && inProgress);\n    if (!renderConnection) {\n        return null;\n    }\n    return (jsx(\"svg\", { style: containerStyle, width: width, height: height, className: \"react-flow__connectionline react-flow__container\", children: jsx(\"g\", { className: cc(['react-flow__connection', getConnectionStatus(isValid)]), children: jsx(ConnectionLine, { style: style, type: type, CustomComponent: component, isValid: isValid }) }) }));\n}\nconst ConnectionLine = ({ style, type = ConnectionLineType.Bezier, CustomComponent, isValid, }) => {\n    const { inProgress, from, fromNode, fromHandle, fromPosition, to, toNode, toHandle, toPosition } = useConnection();\n    if (!inProgress) {\n        return;\n    }\n    if (CustomComponent) {\n        return (jsx(CustomComponent, { connectionLineType: type, connectionLineStyle: style, fromNode: fromNode, fromHandle: fromHandle, fromX: from.x, fromY: from.y, toX: to.x, toY: to.y, fromPosition: fromPosition, toPosition: toPosition, connectionStatus: getConnectionStatus(isValid), toNode: toNode, toHandle: toHandle }));\n    }\n    let path = '';\n    const pathParams = {\n        sourceX: from.x,\n        sourceY: from.y,\n        sourcePosition: fromPosition,\n        targetX: to.x,\n        targetY: to.y,\n        targetPosition: toPosition,\n    };\n    switch (type) {\n        case ConnectionLineType.Bezier:\n            [path] = getBezierPath(pathParams);\n            break;\n        case ConnectionLineType.SimpleBezier:\n            [path] = getSimpleBezierPath(pathParams);\n            break;\n        case ConnectionLineType.Step:\n            [path] = getSmoothStepPath({\n                ...pathParams,\n                borderRadius: 0,\n            });\n            break;\n        case ConnectionLineType.SmoothStep:\n            [path] = getSmoothStepPath(pathParams);\n            break;\n        default:\n            [path] = getStraightPath(pathParams);\n    }\n    return jsx(\"path\", { d: path, fill: \"none\", className: \"react-flow__connection-path\", style: style });\n};\nConnectionLine.displayName = 'ConnectionLine';\n\nconst emptyTypes = {};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodeOrEdgeTypesWarning(nodeOrEdgeTypes = emptyTypes) {\n    const typesRef = useRef(nodeOrEdgeTypes);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            const usedKeys = new Set([...Object.keys(typesRef.current), ...Object.keys(nodeOrEdgeTypes)]);\n            for (const key of usedKeys) {\n                if (typesRef.current[key] !== nodeOrEdgeTypes[key]) {\n                    store.getState().onError?.('002', errorMessages['error002']());\n                    break;\n                }\n            }\n            typesRef.current = nodeOrEdgeTypes;\n        }\n    }, [nodeOrEdgeTypes]);\n}\n\nfunction useStylesLoadedWarning() {\n    const store = useStoreApi();\n    const checked = useRef(false);\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            if (!checked.current) {\n                const pane = document.querySelector('.react-flow__pane');\n                if (pane && !(window.getComputedStyle(pane).zIndex === '1')) {\n                    store.getState().onError?.('013', errorMessages['error013']('react'));\n                }\n                checked.current = true;\n            }\n        }\n    }, []);\n}\n\nfunction GraphViewComponent({ nodeTypes, edgeTypes, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance, nodeClickDistance, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, noDragClassName, noWheelClassName, noPanClassName, disableKeyboardA11y, nodeExtent, rfId, viewport, onViewportChange, }) {\n    useNodeOrEdgeTypesWarning(nodeTypes);\n    useNodeOrEdgeTypesWarning(edgeTypes);\n    useStylesLoadedWarning();\n    useOnInitHandler(onInit);\n    useViewportSync(viewport);\n    return (jsx(FlowRenderer, { onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, paneClickDistance: paneClickDistance, deleteKeyCode: deleteKeyCode, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, onSelectionContextMenu: onSelectionContextMenu, preventScrolling: preventScrolling, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, onViewportChange: onViewportChange, isControlledViewport: !!viewport, children: jsxs(Viewport, { children: [jsx(EdgeRenderer, { edgeTypes: edgeTypes, onEdgeClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onlyRenderVisibleElements: onlyRenderVisibleElements, onEdgeContextMenu: onEdgeContextMenu, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, rfId: rfId }), jsx(ConnectionLineWrapper, { style: connectionLineStyle, type: connectionLineType, component: connectionLineComponent, containerStyle: connectionLineContainerStyle }), jsx(\"div\", { className: \"react-flow__edgelabel-renderer\" }), jsx(NodeRenderer, { nodeTypes: nodeTypes, onNodeClick: onNodeClick, onNodeDoubleClick: onNodeDoubleClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, nodeClickDistance: nodeClickDistance, onlyRenderVisibleElements: onlyRenderVisibleElements, noPanClassName: noPanClassName, noDragClassName: noDragClassName, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, rfId: rfId }), jsx(\"div\", { className: \"react-flow__viewport-portal\" })] }) }));\n}\nGraphViewComponent.displayName = 'GraphView';\nconst GraphView = memo(GraphViewComponent);\n\nconst getInitialState = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView, nodeOrigin, nodeExtent, } = {}) => {\n    const nodeLookup = new Map();\n    const parentLookup = new Map();\n    const connectionLookup = new Map();\n    const edgeLookup = new Map();\n    const storeEdges = defaultEdges ?? edges ?? [];\n    const storeNodes = defaultNodes ?? nodes ?? [];\n    const storeNodeOrigin = nodeOrigin ?? [0, 0];\n    const storeNodeExtent = nodeExtent ?? infiniteExtent;\n    updateConnectionLookup(connectionLookup, edgeLookup, storeEdges);\n    adoptUserNodes(storeNodes, nodeLookup, parentLookup, {\n        nodeOrigin: storeNodeOrigin,\n        nodeExtent: storeNodeExtent,\n        elevateNodesOnSelect: false,\n    });\n    let transform = [0, 0, 1];\n    if (fitView && width && height) {\n        const bounds = getInternalNodesBounds(nodeLookup, {\n            filter: (node) => !!((node.width || node.initialWidth) && (node.height || node.initialHeight)),\n        });\n        const { x, y, zoom } = getViewportForBounds(bounds, width, height, 0.5, 2, 0.1);\n        transform = [x, y, zoom];\n    }\n    return {\n        rfId: '1',\n        width: 0,\n        height: 0,\n        transform,\n        nodes: storeNodes,\n        nodeLookup,\n        parentLookup,\n        edges: storeEdges,\n        edgeLookup,\n        connectionLookup,\n        onNodesChange: null,\n        onEdgesChange: null,\n        hasDefaultNodes: defaultNodes !== undefined,\n        hasDefaultEdges: defaultEdges !== undefined,\n        panZoom: null,\n        minZoom: 0.5,\n        maxZoom: 2,\n        translateExtent: infiniteExtent,\n        nodeExtent: storeNodeExtent,\n        nodesSelectionActive: false,\n        userSelectionActive: false,\n        userSelectionRect: null,\n        connectionMode: ConnectionMode.Strict,\n        domNode: null,\n        paneDragging: false,\n        noPanClassName: 'nopan',\n        nodeOrigin: storeNodeOrigin,\n        nodeDragThreshold: 1,\n        snapGrid: [15, 15],\n        snapToGrid: false,\n        nodesDraggable: true,\n        nodesConnectable: true,\n        nodesFocusable: true,\n        edgesFocusable: true,\n        edgesReconnectable: true,\n        elementsSelectable: true,\n        elevateNodesOnSelect: true,\n        elevateEdgesOnSelect: false,\n        fitViewOnInit: false,\n        fitViewDone: false,\n        fitViewOnInitOptions: undefined,\n        selectNodesOnDrag: true,\n        multiSelectionActive: false,\n        connection: { ...initialConnection },\n        connectionClickStartHandle: null,\n        connectOnClick: true,\n        ariaLiveMessage: '',\n        autoPanOnConnect: true,\n        autoPanOnNodeDrag: true,\n        autoPanSpeed: 15,\n        connectionRadius: 20,\n        onError: devWarn,\n        isValidConnection: undefined,\n        onSelectionChangeHandlers: [],\n        lib: 'react',\n        debug: false,\n    };\n};\n\nconst createStore = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView: fitView$1, nodeOrigin, nodeExtent, }) => createWithEqualityFn((set, get) => ({\n    ...getInitialState({ nodes, edges, width, height, fitView: fitView$1, nodeOrigin, nodeExtent, defaultNodes, defaultEdges }),\n    setNodes: (nodes) => {\n        const { nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect } = get();\n        /*\n         * setNodes() is called exclusively in response to user actions:\n         * - either when the `<ReactFlow nodes>` prop is updated in the controlled ReactFlow setup,\n         * - or when the user calls something like `reactFlowInstance.setNodes()` in an uncontrolled ReactFlow setup.\n         *\n         * When this happens, we take the note objects passed by the user and extend them with fields\n         * relevant for internal React Flow operations.\n         */\n        adoptUserNodes(nodes, nodeLookup, parentLookup, {\n            nodeOrigin,\n            nodeExtent,\n            elevateNodesOnSelect,\n            checkEquality: true,\n        });\n        set({ nodes });\n    },\n    setEdges: (edges) => {\n        const { connectionLookup, edgeLookup } = get();\n        updateConnectionLookup(connectionLookup, edgeLookup, edges);\n        set({ edges });\n    },\n    setDefaultNodesAndEdges: (nodes, edges) => {\n        if (nodes) {\n            const { setNodes } = get();\n            setNodes(nodes);\n            set({ hasDefaultNodes: true });\n        }\n        if (edges) {\n            const { setEdges } = get();\n            setEdges(edges);\n            set({ hasDefaultEdges: true });\n        }\n    },\n    /*\n     * Every node gets registerd at a ResizeObserver. Whenever a node\n     * changes its dimensions, this function is called to measure the\n     * new dimensions and update the nodes.\n     */\n    updateNodeInternals: (updates, params = { triggerFitView: true }) => {\n        const { triggerNodeChanges, nodeLookup, parentLookup, fitViewOnInit, fitViewDone, fitViewOnInitOptions, domNode, nodeOrigin, nodeExtent, debug, fitViewSync, } = get();\n        const { changes, updatedInternals } = updateNodeInternals(updates, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent);\n        if (!updatedInternals) {\n            return;\n        }\n        updateAbsolutePositions(nodeLookup, parentLookup, { nodeOrigin, nodeExtent });\n        if (params.triggerFitView) {\n            // we call fitView once initially after all dimensions are set\n            let nextFitViewDone = fitViewDone;\n            if (!fitViewDone && fitViewOnInit) {\n                nextFitViewDone = fitViewSync({\n                    ...fitViewOnInitOptions,\n                    nodes: fitViewOnInitOptions?.nodes,\n                });\n            }\n            /*\n             * here we are cirmumventing the onNodesChange handler\n             * in order to be able to display nodes even if the user\n             * has not provided an onNodesChange handler.\n             * Nodes are only rendered if they have a width and height\n             * attribute which they get from this handler.\n             */\n            set({ fitViewDone: nextFitViewDone });\n        }\n        else {\n            // we always want to trigger useStore calls whenever updateNodeInternals is called\n            set({});\n        }\n        if (changes?.length > 0) {\n            if (debug) {\n                console.log('React Flow: trigger node changes', changes);\n            }\n            triggerNodeChanges?.(changes);\n        }\n    },\n    updateNodePositions: (nodeDragItems, dragging = false) => {\n        const parentExpandChildren = [];\n        const changes = [];\n        const { nodeLookup, triggerNodeChanges } = get();\n        for (const [id, dragItem] of nodeDragItems) {\n            // we are using the nodelookup to be sure to use the current expandParent and parentId value\n            const node = nodeLookup.get(id);\n            const expandParent = !!(node?.expandParent && node?.parentId && dragItem?.position);\n            const change = {\n                id,\n                type: 'position',\n                position: expandParent\n                    ? {\n                        x: Math.max(0, dragItem.position.x),\n                        y: Math.max(0, dragItem.position.y),\n                    }\n                    : dragItem.position,\n                dragging,\n            };\n            if (expandParent && node.parentId) {\n                parentExpandChildren.push({\n                    id,\n                    parentId: node.parentId,\n                    rect: {\n                        ...dragItem.internals.positionAbsolute,\n                        width: dragItem.measured.width ?? 0,\n                        height: dragItem.measured.height ?? 0,\n                    },\n                });\n            }\n            changes.push(change);\n        }\n        if (parentExpandChildren.length > 0) {\n            const { parentLookup, nodeOrigin } = get();\n            const parentExpandChanges = handleExpandParent(parentExpandChildren, nodeLookup, parentLookup, nodeOrigin);\n            changes.push(...parentExpandChanges);\n        }\n        triggerNodeChanges(changes);\n    },\n    triggerNodeChanges: (changes) => {\n        const { onNodesChange, setNodes, nodes, hasDefaultNodes, debug } = get();\n        if (changes?.length) {\n            if (hasDefaultNodes) {\n                const updatedNodes = applyNodeChanges(changes, nodes);\n                setNodes(updatedNodes);\n            }\n            if (debug) {\n                console.log('React Flow: trigger node changes', changes);\n            }\n            onNodesChange?.(changes);\n        }\n    },\n    triggerEdgeChanges: (changes) => {\n        const { onEdgesChange, setEdges, edges, hasDefaultEdges, debug } = get();\n        if (changes?.length) {\n            if (hasDefaultEdges) {\n                const updatedEdges = applyEdgeChanges(changes, edges);\n                setEdges(updatedEdges);\n            }\n            if (debug) {\n                console.log('React Flow: trigger edge changes', changes);\n            }\n            onEdgesChange?.(changes);\n        }\n    },\n    addSelectedNodes: (selectedNodeIds) => {\n        const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n        if (multiSelectionActive) {\n            const nodeChanges = selectedNodeIds.map((nodeId) => createSelectionChange(nodeId, true));\n            triggerNodeChanges(nodeChanges);\n            return;\n        }\n        triggerNodeChanges(getSelectionChanges(nodeLookup, new Set([...selectedNodeIds]), true));\n        triggerEdgeChanges(getSelectionChanges(edgeLookup));\n    },\n    addSelectedEdges: (selectedEdgeIds) => {\n        const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n        if (multiSelectionActive) {\n            const changedEdges = selectedEdgeIds.map((edgeId) => createSelectionChange(edgeId, true));\n            triggerEdgeChanges(changedEdges);\n            return;\n        }\n        triggerEdgeChanges(getSelectionChanges(edgeLookup, new Set([...selectedEdgeIds])));\n        triggerNodeChanges(getSelectionChanges(nodeLookup, new Set(), true));\n    },\n    unselectNodesAndEdges: ({ nodes, edges } = {}) => {\n        const { edges: storeEdges, nodes: storeNodes, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n        const nodesToUnselect = nodes ? nodes : storeNodes;\n        const edgesToUnselect = edges ? edges : storeEdges;\n        const nodeChanges = nodesToUnselect.map((n) => {\n            const internalNode = nodeLookup.get(n.id);\n            if (internalNode) {\n                /*\n                 * we need to unselect the internal node that was selected previously before we\n                 * send the change to the user to prevent it to be selected while dragging the new node\n                 */\n                internalNode.selected = false;\n            }\n            return createSelectionChange(n.id, false);\n        });\n        const edgeChanges = edgesToUnselect.map((edge) => createSelectionChange(edge.id, false));\n        triggerNodeChanges(nodeChanges);\n        triggerEdgeChanges(edgeChanges);\n    },\n    setMinZoom: (minZoom) => {\n        const { panZoom, maxZoom } = get();\n        panZoom?.setScaleExtent([minZoom, maxZoom]);\n        set({ minZoom });\n    },\n    setMaxZoom: (maxZoom) => {\n        const { panZoom, minZoom } = get();\n        panZoom?.setScaleExtent([minZoom, maxZoom]);\n        set({ maxZoom });\n    },\n    setTranslateExtent: (translateExtent) => {\n        get().panZoom?.setTranslateExtent(translateExtent);\n        set({ translateExtent });\n    },\n    setPaneClickDistance: (clickDistance) => {\n        get().panZoom?.setClickDistance(clickDistance);\n    },\n    resetSelectedElements: () => {\n        const { edges, nodes, triggerNodeChanges, triggerEdgeChanges } = get();\n        const nodeChanges = nodes.reduce((res, node) => (node.selected ? [...res, createSelectionChange(node.id, false)] : res), []);\n        const edgeChanges = edges.reduce((res, edge) => (edge.selected ? [...res, createSelectionChange(edge.id, false)] : res), []);\n        triggerNodeChanges(nodeChanges);\n        triggerEdgeChanges(edgeChanges);\n    },\n    setNodeExtent: (nextNodeExtent) => {\n        const { nodes, nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect, nodeExtent } = get();\n        if (nextNodeExtent[0][0] === nodeExtent[0][0] &&\n            nextNodeExtent[0][1] === nodeExtent[0][1] &&\n            nextNodeExtent[1][0] === nodeExtent[1][0] &&\n            nextNodeExtent[1][1] === nodeExtent[1][1]) {\n            return;\n        }\n        adoptUserNodes(nodes, nodeLookup, parentLookup, {\n            nodeOrigin,\n            nodeExtent: nextNodeExtent,\n            elevateNodesOnSelect,\n            checkEquality: false,\n        });\n        set({ nodeExtent: nextNodeExtent });\n    },\n    panBy: (delta) => {\n        const { transform, width, height, panZoom, translateExtent } = get();\n        return panBy({ delta, panZoom, transform, translateExtent, width, height });\n    },\n    fitView: (options) => {\n        const { panZoom, width, height, minZoom, maxZoom, nodeLookup } = get();\n        if (!panZoom) {\n            return Promise.resolve(false);\n        }\n        const fitViewNodes = getFitViewNodes(nodeLookup, options);\n        return fitView({\n            nodes: fitViewNodes,\n            width,\n            height,\n            panZoom,\n            minZoom,\n            maxZoom,\n        }, options);\n    },\n    /*\n     * we can't call an asnychronous function in updateNodeInternals\n     * for that we created this sync version of fitView\n     */\n    fitViewSync: (options) => {\n        const { panZoom, width, height, minZoom, maxZoom, nodeLookup } = get();\n        if (!panZoom) {\n            return false;\n        }\n        const fitViewNodes = getFitViewNodes(nodeLookup, options);\n        fitView({\n            nodes: fitViewNodes,\n            width,\n            height,\n            panZoom,\n            minZoom,\n            maxZoom,\n        }, options);\n        return fitViewNodes.size > 0;\n    },\n    cancelConnection: () => {\n        set({\n            connection: { ...initialConnection },\n        });\n    },\n    updateConnection: (connection) => {\n        set({ connection });\n    },\n    reset: () => set({ ...getInitialState() }),\n}), Object.is);\n\n/**\n * The `<ReactFlowProvider />` component is a [context provider](https://react.dev/learn/passing-data-deeply-with-context#)\n * that makes it possible to access a flow's internal state outside of the\n * [`<ReactFlow />`](/api-reference/react-flow) component. Many of the hooks we\n * provide rely on this component to work.\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow, ReactFlowProvider, useNodes } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlowProvider>\n *      <ReactFlow nodes={...} edges={...} />\n *      <Sidebar />\n *    </ReactFlowProvider>\n *  );\n *}\n *\n *function Sidebar() {\n *  // This hook will only work if the component it's used in is a child of a\n *  // <ReactFlowProvider />.\n *  const nodes = useNodes()\n *\n *  return <aside>do something with nodes</aside>;\n *}\n *```\n *\n * @remarks If you're using a router and want your flow's state to persist across routes,\n * it's vital that you place the `<ReactFlowProvider />` component _outside_ of\n * your router. If you have multiple flows on the same page you will need to use a separate\n * `<ReactFlowProvider />` for each flow.\n */\nfunction ReactFlowProvider({ initialNodes: nodes, initialEdges: edges, defaultNodes, defaultEdges, initialWidth: width, initialHeight: height, fitView, nodeOrigin, nodeExtent, children, }) {\n    const [store] = useState(() => createStore({\n        nodes,\n        edges,\n        defaultNodes,\n        defaultEdges,\n        width,\n        height,\n        fitView,\n        nodeOrigin,\n        nodeExtent,\n    }));\n    return (jsx(Provider$1, { value: store, children: jsx(BatchProvider, { children: children }) }));\n}\n\nfunction Wrapper({ children, nodes, edges, defaultNodes, defaultEdges, width, height, fitView, nodeOrigin, nodeExtent, }) {\n    const isWrapped = useContext(StoreContext);\n    if (isWrapped) {\n        /*\n         * we need to wrap it with a fragment because it's not allowed for children to be a ReactNode\n         * https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051\n         */\n        return jsx(Fragment, { children: children });\n    }\n    return (jsx(ReactFlowProvider, { initialNodes: nodes, initialEdges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, initialWidth: width, initialHeight: height, fitView: fitView, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: children }));\n}\n\nconst wrapperStyle = {\n    width: '100%',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n    zIndex: 0,\n};\nfunction ReactFlow({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes, edgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, onBeforeDelete, connectionMode, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid, snapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag, nodesDraggable, nodesConnectable, nodesFocusable, nodeOrigin = defaultNodeOrigin, edgesFocusable, edgesReconnectable, elementsSelectable = true, defaultViewport: defaultViewport$1 = defaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance = 0, nodeClickDistance = 0, children, onReconnect, onReconnectStart, onReconnectEnd, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView, fitViewOptions, connectOnClick, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect, elevateEdgesOnSelect, disableKeyboardA11y = false, autoPanOnConnect, autoPanOnNodeDrag, autoPanSpeed, connectionRadius, isValidConnection, onError, style, id, nodeDragThreshold, viewport, onViewportChange, width, height, colorMode = 'light', debug, onScroll, ...rest }, ref) {\n    const rfId = id || '1';\n    const colorModeClassName = useColorModeClass(colorMode);\n    // Undo scroll events, preventing viewport from shifting when nodes outside of it are focused\n    const wrapperOnScroll = useCallback((e) => {\n        e.currentTarget.scrollTo({ top: 0, left: 0, behavior: 'instant' });\n        onScroll?.(e);\n    }, [onScroll]);\n    return (jsx(\"div\", { \"data-testid\": \"rf__wrapper\", ...rest, onScroll: wrapperOnScroll, style: { ...style, ...wrapperStyle }, ref: ref, className: cc(['react-flow', className, colorModeClassName]), id: id, children: jsxs(Wrapper, { nodes: nodes, edges: edges, width: width, height: height, fitView: fitView, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: [jsx(GraphView, { onInit: onInit, onNodeClick: onNodeClick, onEdgeClick: onEdgeClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, onNodeDoubleClick: onNodeDoubleClick, nodeTypes: nodeTypes, edgeTypes: edgeTypes, connectionLineType: connectionLineType, connectionLineStyle: connectionLineStyle, connectionLineComponent: connectionLineComponent, connectionLineContainerStyle: connectionLineContainerStyle, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, deleteKeyCode: deleteKeyCode, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, onlyRenderVisibleElements: onlyRenderVisibleElements, defaultViewport: defaultViewport$1, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, preventScrolling: preventScrolling, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneScroll: onPaneScroll, onPaneContextMenu: onPaneContextMenu, paneClickDistance: paneClickDistance, nodeClickDistance: nodeClickDistance, onSelectionContextMenu: onSelectionContextMenu, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onEdgeContextMenu: onEdgeContextMenu, onEdgeDoubleClick: onEdgeDoubleClick, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, rfId: rfId, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, viewport: viewport, onViewportChange: onViewportChange }), jsx(StoreUpdater, { nodes: nodes, edges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, onConnect: onConnect, onConnectStart: onConnectStart, onConnectEnd: onConnectEnd, onClickConnectStart: onClickConnectStart, onClickConnectEnd: onClickConnectEnd, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, elevateNodesOnSelect: elevateNodesOnSelect, elevateEdgesOnSelect: elevateEdgesOnSelect, minZoom: minZoom, maxZoom: maxZoom, nodeExtent: nodeExtent, onNodesChange: onNodesChange, onEdgesChange: onEdgesChange, snapToGrid: snapToGrid, snapGrid: snapGrid, connectionMode: connectionMode, translateExtent: translateExtent, connectOnClick: connectOnClick, defaultEdgeOptions: defaultEdgeOptions, fitView: fitView, fitViewOptions: fitViewOptions, onNodesDelete: onNodesDelete, onEdgesDelete: onEdgesDelete, onDelete: onDelete, onNodeDragStart: onNodeDragStart, onNodeDrag: onNodeDrag, onNodeDragStop: onNodeDragStop, onSelectionDrag: onSelectionDrag, onSelectionDragStart: onSelectionDragStart, onSelectionDragStop: onSelectionDragStop, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, noPanClassName: noPanClassName, nodeOrigin: nodeOrigin, rfId: rfId, autoPanOnConnect: autoPanOnConnect, autoPanOnNodeDrag: autoPanOnNodeDrag, autoPanSpeed: autoPanSpeed, onError: onError, connectionRadius: connectionRadius, isValidConnection: isValidConnection, selectNodesOnDrag: selectNodesOnDrag, nodeDragThreshold: nodeDragThreshold, onBeforeDelete: onBeforeDelete, paneClickDistance: paneClickDistance, debug: debug }), jsx(SelectionListener, { onSelectionChange: onSelectionChange }), children, jsx(Attribution, { proOptions: proOptions, position: attributionPosition }), jsx(A11yDescriptions, { rfId: rfId, disableKeyboardA11y: disableKeyboardA11y })] }) }));\n}\n/**\n * The `<ReactFlow />` component is the heart of your React Flow application.\n * It renders your nodes and edges and handles user interaction\n *\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (<ReactFlow\n *    nodes={...}\n *    edges={...}\n *    onNodesChange={...}\n *    ...\n *  />);\n *}\n *```\n */\nvar index = fixedForwardRef(ReactFlow);\n\nconst selector$6 = (s) => s.domNode?.querySelector('.react-flow__edgelabel-renderer');\n/**\n * Edges are SVG-based. If you want to render more complex labels you can use the\n * `<EdgeLabelRenderer />` component to access a div based renderer. This component\n * is a portal that renders the label in a `<div />` that is positioned on top of\n * the edges. You can see an example usage of the component in the [edge label renderer](/examples/edges/edge-label-renderer) example.\n * @public\n *\n * @example\n *```jsx\n *import React from 'react';\n *import { getBezierPath, EdgeLabelRenderer, BaseEdge } from '@xyflow/react';\n *\n *export function CustomEdge({ id, data, ...props }) {\n *  const [edgePath, labelX, labelY] = getBezierPath(props);\n *\n *  return (\n *    <>\n *      <BaseEdge id={id} path={edgePath} />\n *      <EdgeLabelRenderer>\n *        <div\n *          style={{\n *            position: 'absolute',\n *            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,\n *            background: '#ffcc00',\n *            padding: 10,\n *        }}\n *          className=\"nodrag nopan\"\n *        >\n *         {data.label}\n *        </div>\n *      </EdgeLabelRenderer>\n *    </>\n *  );\n *};\n *```\n *\n * @remarks The `<EdgeLabelRenderer />` has no pointer events by default. If you want to\n * add mouse interactions you need to set the style `pointerEvents: all` and add\n * the `nopan` class on the label or the element you want to interact with.\n */\nfunction EdgeLabelRenderer({ children }) {\n    const edgeLabelRenderer = useStore(selector$6);\n    if (!edgeLabelRenderer) {\n        return null;\n    }\n    return createPortal(children, edgeLabelRenderer);\n}\n\nconst selector$5 = (s) => s.domNode?.querySelector('.react-flow__viewport-portal');\n/**\n * The `<ViewportPortal />` component can be used to add components to the same viewport\n * of the flow where nodes and edges are rendered. This is useful when you want to render\n * your own components that are adhere to the same coordinate system as the nodes & edges\n * and are also affected by zooming and panning\n * @public\n * @example\n *\n * ```jsx\n *import React from 'react';\n *import { ViewportPortal } from '@xyflow/react';\n *\n *export default function () {\n *  return (\n *    <ViewportPortal>\n *      <div\n *        style={{ transform: 'translate(100px, 100px)', position: 'absolute' }}\n *      >\n *        This div is positioned at [100, 100] on the flow.\n *      </div>\n *    </ViewportPortal>\n *  );\n *}\n *```\n */\nfunction ViewportPortal({ children }) {\n    const viewPortalDiv = useStore(selector$5);\n    if (!viewPortalDiv) {\n        return null;\n    }\n    return createPortal(children, viewPortalDiv);\n}\n\n/**\n * When you programmatically add or remove handles to a node or update a node's\n *handle position, you need to let React Flow know about it using this hook. This\n *will update the internal dimensions of the node and properly reposition handles\n *on the canvas if necessary.\n *\n * @public\n * @returns function for updating node internals\n *\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { Handle, useUpdateNodeInternals } from '@xyflow/react';\n *\n *export default function RandomHandleNode({ id }) {\n *  const updateNodeInternals = useUpdateNodeInternals();\n *  const [handleCount, setHandleCount] = useState(0);\n *  const randomizeHandleCount = useCallback(() => {\n *   setHandleCount(Math.floor(Math.random() * 10));\n *    updateNodeInternals(id);\n *  }, [id, updateNodeInternals]);\n *\n *  return (\n *    <>\n *      {Array.from({ length: handleCount }).map((_, index) => (\n *        <Handle\n *          key={index}\n *          type=\"target\"\n *          position=\"left\"\n *          id={`handle-${index}`}\n *        />\n *      ))}\n *\n *      <div>\n *        <button onClick={randomizeHandleCount}>Randomize handle count</button>\n *        <p>There are {handleCount} handles on this node.</p>\n *      </div>\n *    </>\n *  );\n *}\n *```\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useUpdateNodeInternals() {\n    const store = useStoreApi();\n    return useCallback((id) => {\n        const { domNode, updateNodeInternals } = store.getState();\n        const updateIds = Array.isArray(id) ? id : [id];\n        const updates = new Map();\n        updateIds.forEach((updateId) => {\n            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id=\"${updateId}\"]`);\n            if (nodeElement) {\n                updates.set(updateId, { id: updateId, nodeElement, force: true });\n            }\n        });\n        requestAnimationFrame(() => updateNodeInternals(updates, { triggerFitView: false }));\n    }, []);\n}\n\nconst nodesSelector = (state) => state.nodes;\n/**\n * This hook returns an array of the current nodes. Components that use this hook\n * will re-render **whenever any node changes**, including when a node is selected\n * or moved.\n *\n * @public\n * @returns An array of nodes\n *\n * @example\n * ```jsx\n *import { useNodes } from '@xyflow/react';\n *\n *export default function() {\n *  const nodes = useNodes();\n *\n *  return <div>There are currently {nodes.length} nodes!</div>;\n *}\n *```\n */\nfunction useNodes() {\n    const nodes = useStore(nodesSelector, shallow);\n    return nodes;\n}\n\nconst edgesSelector = (state) => state.edges;\n/**\n * This hook returns an array of the current edges. Components that use this hook\n * will re-render **whenever any edge changes**.\n *\n * @public\n * @returns An array of edges\n *\n * @example\n * ```tsx\n *import { useEdges } from '@xyflow/react';\n *\n *export default function () {\n *  const edges = useEdges();\n *\n *  return <div>There are currently {edges.length} edges!</div>;\n *}\n *```\n */\nfunction useEdges() {\n    const edges = useStore(edgesSelector, shallow);\n    return edges;\n}\n\nconst viewportSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n});\n/**\n * The `useViewport` hook is a convenient way to read the current state of the\n *{@link Viewport} in a component. Components that use this hook\n *will re-render **whenever the viewport changes**.\n *\n * @public\n * @returns The current viewport\n *\n * @example\n *\n *```jsx\n *import { useViewport } from '@xyflow/react';\n *\n *export default function ViewportDisplay() {\n *  const { x, y, zoom } = useViewport();\n *\n *  return (\n *    <div>\n *      <p>\n *        The viewport is currently at ({x}, {y}) and zoomed to {zoom}.\n *      </p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useViewport() {\n    const viewport = useStore(viewportSelector, shallow);\n    return viewport;\n}\n\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @param initialNodes\n * @returns an array [nodes, setNodes, onNodesChange]\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useNodesState(initialNodes) {\n    const [nodes, setNodes] = useState(initialNodes);\n    const onNodesChange = useCallback((changes) => setNodes((nds) => applyNodeChanges(changes, nds)), []);\n    return [nodes, setNodes, onNodesChange];\n}\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @param initialEdges\n * @returns an array [edges, setEdges, onEdgesChange]\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useEdgesState(initialEdges) {\n    const [edges, setEdges] = useState(initialEdges);\n    const onEdgesChange = useCallback((changes) => setEdges((eds) => applyEdgeChanges(changes, eds)), []);\n    return [edges, setEdges, onEdgesChange];\n}\n\n/**\n * The `useOnViewportChange` hook lets you listen for changes to the viewport such\n *as panning and zooming. You can provide a callback for each phase of a viewport\n *change: `onStart`, `onChange`, and `onEnd`.\n *\n * @public\n * @param params.onStart - gets called when the viewport starts changing\n * @param params.onChange - gets called when the viewport changes\n * @param params.onEnd - gets called when the viewport stops changing\n *\n * @example\n * ```jsx\n *import { useCallback } from 'react';\n *import { useOnViewportChange } from '@xyflow/react';\n *\n *function ViewportChangeLogger() {\n *  useOnViewportChange({\n *    onStart: (viewport: Viewport) => console.log('start', viewport),\n *    onChange: (viewport: Viewport) => console.log('change', viewport),\n *    onEnd: (viewport: Viewport) => console.log('end', viewport),\n *  });\n *\n *  return null;\n *}\n *```\n */\nfunction useOnViewportChange({ onStart, onChange, onEnd }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        store.setState({ onViewportChangeStart: onStart });\n    }, [onStart]);\n    useEffect(() => {\n        store.setState({ onViewportChange: onChange });\n    }, [onChange]);\n    useEffect(() => {\n        store.setState({ onViewportChangeEnd: onEnd });\n    }, [onEnd]);\n}\n\n/**\n * This hook lets you listen for changes to both node and edge selection. As the\n *name implies, the callback you provide will be called whenever the selection of\n *_either_ nodes or edges changes.\n *\n * @public\n * @param params.onChange - The handler to register\n *\n * @example\n * ```jsx\n *import { useState } from 'react';\n *import { ReactFlow, useOnSelectionChange } from '@xyflow/react';\n *\n *function SelectionDisplay() {\n *  const [selectedNodes, setSelectedNodes] = useState([]);\n *  const [selectedEdges, setSelectedEdges] = useState([]);\n *\n *  // the passed handler has to be memoized, otherwise the hook will not work correctly\n *  const onChange = useCallback(({ nodes, edges }) => {\n *    setSelectedNodes(nodes.map((node) => node.id));\n *    setSelectedEdges(edges.map((edge) => edge.id));\n *  }, []);\n *\n *  useOnSelectionChange({\n *    onChange,\n *  });\n *\n *  return (\n *    <div>\n *      <p>Selected nodes: {selectedNodes.join(', ')}</p>\n *      <p>Selected edges: {selectedEdges.join(', ')}</p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks You need to memoize the passed `onChange` handler, otherwise the hook will not work correctly.\n */\nfunction useOnSelectionChange({ onChange, }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const nextOnSelectionChangeHandlers = [...store.getState().onSelectionChangeHandlers, onChange];\n        store.setState({ onSelectionChangeHandlers: nextOnSelectionChangeHandlers });\n        return () => {\n            const nextHandlers = store.getState().onSelectionChangeHandlers.filter((fn) => fn !== onChange);\n            store.setState({ onSelectionChangeHandlers: nextHandlers });\n        };\n    }, [onChange]);\n}\n\nconst selector$4 = (options) => (s) => {\n    if (s.nodeLookup.size === 0) {\n        return false;\n    }\n    for (const [, { hidden, internals }] of s.nodeLookup) {\n        if (options.includeHiddenNodes || !hidden) {\n            if (internals.handleBounds === undefined || !nodeHasDimensions(internals.userNode)) {\n                return false;\n            }\n        }\n    }\n    return true;\n};\n/**\n * This hook tells you whether all the nodes in a flow have been measured and given\n *a width and height. When you add a node to the flow, this hook will return\n *`false` and then `true` again once the node has been measured.\n *\n * @public\n * @param options.includeHiddenNodes - defaults to false\n * @returns boolean indicating whether all nodes are initialized\n *\n * @example\n * ```jsx\n *import { useReactFlow, useNodesInitialized } from '@xyflow/react';\n *import { useEffect, useState } from 'react';\n *\n *const options = {\n *  includeHiddenNodes: false,\n *};\n *\n *export default function useLayout() {\n *  const { getNodes } = useReactFlow();\n *  const nodesInitialized = useNodesInitialized(options);\n *  const [layoutedNodes, setLayoutedNodes] = useState(getNodes());\n *\n *  useEffect(() => {\n *    if (nodesInitialized) {\n *      setLayoutedNodes(yourLayoutingFunction(getNodes()));\n *    }\n *  }, [nodesInitialized]);\n *\n *  return layoutedNodes;\n *}\n *```\n */\nfunction useNodesInitialized(options = {\n    includeHiddenNodes: false,\n}) {\n    const initialized = useStore(selector$4(options));\n    return initialized;\n}\n\n/**\n * Hook to check if a <Handle /> is connected to another <Handle /> and get the connections.\n *\n * @public\n * @deprecated Use `useNodeConnections` instead.\n * @param param.type - handle type 'source' or 'target'\n * @param param.nodeId - node id - if not provided, the node id from the NodeIdContext is used\n * @param param.id - the handle id (this is only needed if the node has multiple handles of the same type)\n * @param param.onConnect - gets called when a connection is established\n * @param param.onDisconnect - gets called when a connection is removed\n * @returns an array with handle connections\n */\nfunction useHandleConnections({ type, id, nodeId, onConnect, onDisconnect, }) {\n    console.warn('[DEPRECATED] `useHandleConnections` is deprecated. Instead use `useNodeConnections` https://reactflow.dev/api-reference/hooks/useNodeConnections');\n    const _nodeId = useNodeId();\n    const currentNodeId = nodeId ?? _nodeId;\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}-${type}${id ? `-${id}` : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo dicuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\nconst error014 = errorMessages['error014']();\n/**\n * This hook returns an array of connections on a specific node, handle type ('source', 'target') or handle ID.\n *\n * @public\n * @param param.id - node id - optional if called inside a custom node\n * @param param.handleType - filter by handle type 'source' or 'target'\n * @param param.handleId - filter by handle id (this is only needed if the node has multiple handles of the same type)\n * @param param.onConnect - gets called when a connection is established\n * @param param.onDisconnect - gets called when a connection is removed\n * @returns an array with connections\n *\n * @example\n * ```jsx\n *import { useNodeConnections } from '@xyflow/react';\n *\n *export default function () {\n *  const connections = useNodeConnections({\n *    handleType: 'target',\n *    handleId: 'my-handle',\n *  });\n *\n *  return (\n *    <div>There are currently {connections.length} incoming connections!</div>\n *  );\n *}\n *```\n */\nfunction useNodeConnections({ id, handleType, handleId, onConnect, onDisconnect, } = {}) {\n    const nodeId = useNodeId();\n    const currentNodeId = id ?? nodeId;\n    if (!currentNodeId) {\n        throw new Error(error014);\n    }\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}${handleType ? (handleId ? `-${handleType}-${handleId}` : `-${handleType}`) : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo dicuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodesData(nodeIds) {\n    const nodesData = useStore(useCallback((s) => {\n        const data = [];\n        const isArrayOfIds = Array.isArray(nodeIds);\n        const _nodeIds = isArrayOfIds ? nodeIds : [nodeIds];\n        for (const nodeId of _nodeIds) {\n            const node = s.nodeLookup.get(nodeId);\n            if (node) {\n                data.push({\n                    id: node.id,\n                    type: node.type,\n                    data: node.data,\n                });\n            }\n        }\n        return isArrayOfIds ? data : data[0] ?? null;\n    }, [nodeIds]), shallowNodeData);\n    return nodesData;\n}\n\n/**\n * This hook returns the internal representation of a specific node.\n * Components that use this hook will re-render **whenever the node changes**,\n * including when a node is selected or moved.\n *\n * @public\n * @param id - id of the node\n * @returns array with visible node ids\n *\n * @example\n * ```tsx\n *import { useInternalNode } from '@xyflow/react';\n *\n *export default function () {\n *  const internalNode = useInternalNode('node-1');\n *  const absolutePosition = internalNode.internals.positionAbsolute;\n *\n *  return (\n *    <div>\n *      The absolute position of the node is at:\n *      <p>x: {absolutePosition.x}</p>\n *      <p>y: {absolutePosition.y}</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useInternalNode(id) {\n    const node = useStore(useCallback((s) => s.nodeLookup.get(id), [id]), shallow);\n    return node;\n}\n\nfunction LinePattern({ dimensions, lineWidth, variant, className }) {\n    return (jsx(\"path\", { strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}`, className: cc(['react-flow__background-pattern', variant, className]) }));\n}\nfunction DotPattern({ radius, className }) {\n    return (jsx(\"circle\", { cx: radius, cy: radius, r: radius, className: cc(['react-flow__background-pattern', 'dots', className]) }));\n}\n\n/**\n * The three variants are exported as an enum for convenience. You can either import\n * the enum and use it like `BackgroundVariant.Lines` or you can use the raw string\n * value directly.\n * @public\n */\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector$3 = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction BackgroundComponent({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 0, color, bgColor, style, className, patternClassName, }) {\n    const ref = useRef(null);\n    const { transform, patternId } = useStore(selector$3, shallow);\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const offsetXY = Array.isArray(offset) ? offset : [offset, offset];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const scaledOffset = [\n        offsetXY[0] * transform[2] || 1 + patternDimensions[0] / 2,\n        offsetXY[1] * transform[2] || 1 + patternDimensions[1] / 2,\n    ];\n    const _patternId = `${patternId}${id ? id : ''}`;\n    return (jsxs(\"svg\", { className: cc(['react-flow__background', className]), style: {\n            ...style,\n            ...containerStyle,\n            '--xy-background-color-props': bgColor,\n            '--xy-background-pattern-color-props': color,\n        }, ref: ref, \"data-testid\": \"rf__background\", children: [jsx(\"pattern\", { id: _patternId, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${scaledOffset[0]},-${scaledOffset[1]})`, children: isDots ? (jsx(DotPattern, { radius: scaledSize / 2, className: patternClassName })) : (jsx(LinePattern, { dimensions: patternDimensions, lineWidth: lineWidth, variant: variant, className: patternClassName })) }), jsx(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${_patternId})` })] }));\n}\nBackgroundComponent.displayName = 'Background';\n/**\n * The `<Background />` component makes it convenient to render different types of backgrounds common in node-based UIs. It comes with three variants: lines, dots and cross.\n *\n * @example\n *\n * A simple example of how to use the Background component.\n *\n * ```tsx\n * import { useState } from 'react';\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background color=\"#ccc\" variant={BackgroundVariant.Dots} />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @example\n *\n * In this example you can see how to combine multiple backgrounds\n *\n * ```tsx\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n * import '@xyflow/react/dist/style.css';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background\n *         id=\"1\"\n *         gap={10}\n *         color=\"#f1f1f1\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *       <Background\n *         id=\"2\"\n *         gap={100}\n *         color=\"#ccc\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @remarks\n *\n * When combining multiple <Background /> components it’s important to give each of them a unique id prop!\n *\n */\nconst Background = memo(BackgroundComponent);\n\nfunction PlusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 32\", children: jsx(\"path\", { d: \"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z\" }) }));\n}\n\nfunction MinusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 5\", children: jsx(\"path\", { d: \"M0 0h32v4.2H0z\" }) }));\n}\n\nfunction FitViewIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 30\", children: jsx(\"path\", { d: \"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z\" }) }));\n}\n\nfunction LockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z\" }) }));\n}\n\nfunction UnlockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z\" }) }));\n}\n\n/**\n * You can add buttons to the control panel by using the `<ControlButton />` component\n * and pass it as a child to the [`<Controls />`](/api-reference/components/controls) component.\n *\n * @public\n * @example\n *```jsx\n *import { MagicWand } from '@radix-ui/react-icons'\n *import { ReactFlow, Controls, ControlButton } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls>\n *        <ControlButton onClick={() => alert('Something magical just happened. ✨')}>\n *          <MagicWand />\n *        </ControlButton>\n *      </Controls>\n *    </ReactFlow>\n *  )\n *}\n *```\n */\nfunction ControlButton({ children, className, ...rest }) {\n    return (jsx(\"button\", { type: \"button\", className: cc(['react-flow__controls-button', className]), ...rest, children: children }));\n}\n\nconst selector$2 = (s) => ({\n    isInteractive: s.nodesDraggable || s.nodesConnectable || s.elementsSelectable,\n    minZoomReached: s.transform[2] <= s.minZoom,\n    maxZoomReached: s.transform[2] >= s.maxZoom,\n});\nfunction ControlsComponent({ style, showZoom = true, showFitView = true, showInteractive = true, fitViewOptions, onZoomIn, onZoomOut, onFitView, onInteractiveChange, className, children, position = 'bottom-left', orientation = 'vertical', 'aria-label': ariaLabel = 'React Flow controls', }) {\n    const store = useStoreApi();\n    const { isInteractive, minZoomReached, maxZoomReached } = useStore(selector$2, shallow);\n    const { zoomIn, zoomOut, fitView } = useReactFlow();\n    const onZoomInHandler = () => {\n        zoomIn();\n        onZoomIn?.();\n    };\n    const onZoomOutHandler = () => {\n        zoomOut();\n        onZoomOut?.();\n    };\n    const onFitViewHandler = () => {\n        fitView(fitViewOptions);\n        onFitView?.();\n    };\n    const onToggleInteractivity = () => {\n        store.setState({\n            nodesDraggable: !isInteractive,\n            nodesConnectable: !isInteractive,\n            elementsSelectable: !isInteractive,\n        });\n        onInteractiveChange?.(!isInteractive);\n    };\n    const orientationClass = orientation === 'horizontal' ? 'horizontal' : 'vertical';\n    return (jsxs(Panel, { className: cc(['react-flow__controls', orientationClass, className]), position: position, style: style, \"data-testid\": \"rf__controls\", \"aria-label\": ariaLabel, children: [showZoom && (jsxs(Fragment, { children: [jsx(ControlButton, { onClick: onZoomInHandler, className: \"react-flow__controls-zoomin\", title: \"zoom in\", \"aria-label\": \"zoom in\", disabled: maxZoomReached, children: jsx(PlusIcon, {}) }), jsx(ControlButton, { onClick: onZoomOutHandler, className: \"react-flow__controls-zoomout\", title: \"zoom out\", \"aria-label\": \"zoom out\", disabled: minZoomReached, children: jsx(MinusIcon, {}) })] })), showFitView && (jsx(ControlButton, { className: \"react-flow__controls-fitview\", onClick: onFitViewHandler, title: \"fit view\", \"aria-label\": \"fit view\", children: jsx(FitViewIcon, {}) })), showInteractive && (jsx(ControlButton, { className: \"react-flow__controls-interactive\", onClick: onToggleInteractivity, title: \"toggle interactivity\", \"aria-label\": \"toggle interactivity\", children: isInteractive ? jsx(UnlockIcon, {}) : jsx(LockIcon, {}) })), children] }));\n}\nControlsComponent.displayName = 'Controls';\n/**\n * The `<Controls />` component renders a small panel that contains convenient\n * buttons to zoom in, zoom out, fit the view, and lock the viewport.\n *\n * @public\n * @example\n *```tsx\n *import { ReactFlow, Controls } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls />\n *    </ReactFlow>\n *  )\n *}\n *```\n *\n * @remarks To extend or customise the controls, you can use the [`<ControlButton />`](/api-reference/components/control-button) component\n *\n */\nconst Controls = memo(ControlsComponent);\n\nfunction MiniMapNodeComponent({ id, x, y, width, height, style, color, strokeColor, strokeWidth, className, borderRadius, shapeRendering, selected, onClick, }) {\n    const { background, backgroundColor } = style || {};\n    const fill = (color || background || backgroundColor);\n    return (jsx(\"rect\", { className: cc(['react-flow__minimap-node', { selected }, className]), x: x, y: y, rx: borderRadius, ry: borderRadius, width: width, height: height, style: {\n            fill,\n            stroke: strokeColor,\n            strokeWidth,\n        }, shapeRendering: shapeRendering, onClick: onClick ? (event) => onClick(event, id) : undefined }));\n}\nconst MiniMapNode = memo(MiniMapNodeComponent);\n\nconst selectorNodeIds = (s) => s.nodes.map((node) => node.id);\nconst getAttrFunction = (func) => func instanceof Function ? func : () => func;\nfunction MiniMapNodes({ nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent: NodeComponent = MiniMapNode, onClick, }) {\n    const nodeIds = useStore(selectorNodeIds, shallow);\n    const nodeColorFunc = getAttrFunction(nodeColor);\n    const nodeStrokeColorFunc = getAttrFunction(nodeStrokeColor);\n    const nodeClassNameFunc = getAttrFunction(nodeClassName);\n    const shapeRendering = typeof window === 'undefined' || !!window.chrome ? 'crispEdges' : 'geometricPrecision';\n    return (jsx(Fragment, { children: nodeIds.map((nodeId) => (\n        /*\n         * The split of responsibilities between MiniMapNodes and\n         * NodeComponentWrapper may appear weird. However, it’s designed to\n         * minimize the cost of updates when individual nodes change.\n         *\n         * For more details, see a similar commit in `NodeRenderer/index.tsx`.\n         */\n        jsx(NodeComponentWrapper, { id: nodeId, nodeColorFunc: nodeColorFunc, nodeStrokeColorFunc: nodeStrokeColorFunc, nodeClassNameFunc: nodeClassNameFunc, nodeBorderRadius: nodeBorderRadius, nodeStrokeWidth: nodeStrokeWidth, NodeComponent: NodeComponent, onClick: onClick, shapeRendering: shapeRendering }, nodeId))) }));\n}\nfunction NodeComponentWrapperInner({ id, nodeColorFunc, nodeStrokeColorFunc, nodeClassNameFunc, nodeBorderRadius, nodeStrokeWidth, shapeRendering, NodeComponent, onClick, }) {\n    const { node, x, y, width, height } = useStore((s) => {\n        const node = s.nodeLookup.get(id);\n        const { x, y } = node.internals.positionAbsolute;\n        const { width, height } = getNodeDimensions(node);\n        return {\n            node,\n            x,\n            y,\n            width,\n            height,\n        };\n    }, shallow);\n    if (!node || node.hidden || !nodeHasDimensions(node)) {\n        return null;\n    }\n    return (jsx(NodeComponent, { x: x, y: y, width: width, height: height, style: node.style, selected: !!node.selected, className: nodeClassNameFunc(node), color: nodeColorFunc(node), borderRadius: nodeBorderRadius, strokeColor: nodeStrokeColorFunc(node), strokeWidth: nodeStrokeWidth, shapeRendering: shapeRendering, onClick: onClick, id: node.id }));\n}\nconst NodeComponentWrapper = memo(NodeComponentWrapperInner);\nvar MiniMapNodes$1 = memo(MiniMapNodes);\n\nconst defaultWidth = 200;\nconst defaultHeight = 150;\nconst selector$1 = (s) => {\n    const viewBB = {\n        x: -s.transform[0] / s.transform[2],\n        y: -s.transform[1] / s.transform[2],\n        width: s.width / s.transform[2],\n        height: s.height / s.transform[2],\n    };\n    return {\n        viewBB,\n        boundingRect: s.nodeLookup.size > 0 ? getBoundsOfRects(getInternalNodesBounds(s.nodeLookup), viewBB) : viewBB,\n        rfId: s.rfId,\n        panZoom: s.panZoom,\n        translateExtent: s.translateExtent,\n        flowWidth: s.width,\n        flowHeight: s.height,\n    };\n};\nconst ARIA_LABEL_KEY = 'react-flow__minimap-desc';\nfunction MiniMapComponent({ style, className, nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent, bgColor, maskColor, maskStrokeColor, maskStrokeWidth, position = 'bottom-right', onClick, onNodeClick, pannable = false, zoomable = false, ariaLabel = 'React Flow mini map', inversePan, zoomStep = 10, offsetScale = 5, }) {\n    const store = useStoreApi();\n    const svg = useRef(null);\n    const { boundingRect, viewBB, rfId, panZoom, translateExtent, flowWidth, flowHeight } = useStore(selector$1, shallow);\n    const elementWidth = style?.width ?? defaultWidth;\n    const elementHeight = style?.height ?? defaultHeight;\n    const scaledWidth = boundingRect.width / elementWidth;\n    const scaledHeight = boundingRect.height / elementHeight;\n    const viewScale = Math.max(scaledWidth, scaledHeight);\n    const viewWidth = viewScale * elementWidth;\n    const viewHeight = viewScale * elementHeight;\n    const offset = offsetScale * viewScale;\n    const x = boundingRect.x - (viewWidth - boundingRect.width) / 2 - offset;\n    const y = boundingRect.y - (viewHeight - boundingRect.height) / 2 - offset;\n    const width = viewWidth + offset * 2;\n    const height = viewHeight + offset * 2;\n    const labelledBy = `${ARIA_LABEL_KEY}-${rfId}`;\n    const viewScaleRef = useRef(0);\n    const minimapInstance = useRef();\n    viewScaleRef.current = viewScale;\n    useEffect(() => {\n        if (svg.current && panZoom) {\n            minimapInstance.current = XYMinimap({\n                domNode: svg.current,\n                panZoom,\n                getTransform: () => store.getState().transform,\n                getViewScale: () => viewScaleRef.current,\n            });\n            return () => {\n                minimapInstance.current?.destroy();\n            };\n        }\n    }, [panZoom]);\n    useEffect(() => {\n        minimapInstance.current?.update({\n            translateExtent,\n            width: flowWidth,\n            height: flowHeight,\n            inversePan,\n            pannable,\n            zoomStep,\n            zoomable,\n        });\n    }, [pannable, zoomable, inversePan, zoomStep, translateExtent, flowWidth, flowHeight]);\n    const onSvgClick = onClick\n        ? (event) => {\n            const [x, y] = minimapInstance.current?.pointer(event) || [0, 0];\n            onClick(event, { x, y });\n        }\n        : undefined;\n    const onSvgNodeClick = onNodeClick\n        ? useCallback((event, nodeId) => {\n            const node = store.getState().nodeLookup.get(nodeId);\n            onNodeClick(event, node);\n        }, [])\n        : undefined;\n    return (jsx(Panel, { position: position, style: {\n            ...style,\n            '--xy-minimap-background-color-props': typeof bgColor === 'string' ? bgColor : undefined,\n            '--xy-minimap-mask-background-color-props': typeof maskColor === 'string' ? maskColor : undefined,\n            '--xy-minimap-mask-stroke-color-props': typeof maskStrokeColor === 'string' ? maskStrokeColor : undefined,\n            '--xy-minimap-mask-stroke-width-props': typeof maskStrokeWidth === 'number' ? maskStrokeWidth * viewScale : undefined,\n            '--xy-minimap-node-background-color-props': typeof nodeColor === 'string' ? nodeColor : undefined,\n            '--xy-minimap-node-stroke-color-props': typeof nodeStrokeColor === 'string' ? nodeStrokeColor : undefined,\n            '--xy-minimap-node-stroke-width-props': typeof nodeStrokeWidth === 'string' ? nodeStrokeWidth : undefined,\n        }, className: cc(['react-flow__minimap', className]), \"data-testid\": \"rf__minimap\", children: jsxs(\"svg\", { width: elementWidth, height: elementHeight, viewBox: `${x} ${y} ${width} ${height}`, className: \"react-flow__minimap-svg\", role: \"img\", \"aria-labelledby\": labelledBy, ref: svg, onClick: onSvgClick, children: [ariaLabel && jsx(\"title\", { id: labelledBy, children: ariaLabel }), jsx(MiniMapNodes$1, { onClick: onSvgNodeClick, nodeColor: nodeColor, nodeStrokeColor: nodeStrokeColor, nodeBorderRadius: nodeBorderRadius, nodeClassName: nodeClassName, nodeStrokeWidth: nodeStrokeWidth, nodeComponent: nodeComponent }), jsx(\"path\", { className: \"react-flow__minimap-mask\", d: `M${x - offset},${y - offset}h${width + offset * 2}v${height + offset * 2}h${-width - offset * 2}z\n        M${viewBB.x},${viewBB.y}h${viewBB.width}v${viewBB.height}h${-viewBB.width}z`, fillRule: \"evenodd\", pointerEvents: \"none\" })] }) }));\n}\nMiniMapComponent.displayName = 'MiniMap';\n/**\n * The `<MiniMap />` component can be used to render an overview of your flow. It\n * renders each node as an SVG element and visualizes where the current viewport is\n * in relation to the rest of the flow.\n *\n * @public\n * @example\n *\n * ```jsx\n *import { ReactFlow, MiniMap } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]]} edges={[...]]}>\n *      <MiniMap nodeStrokeWidth={3} />\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst MiniMap = memo(MiniMapComponent);\n\nfunction ResizeControl({ nodeId, position, variant = ResizeControlVariant.Handle, className, style = {}, children, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    const contextNodeId = useNodeId();\n    const id = typeof nodeId === 'string' ? nodeId : contextNodeId;\n    const store = useStoreApi();\n    const resizeControlRef = useRef(null);\n    const defaultPosition = variant === ResizeControlVariant.Line ? 'right' : 'bottom-right';\n    const controlPosition = position ?? defaultPosition;\n    const resizer = useRef(null);\n    useEffect(() => {\n        if (!resizeControlRef.current || !id) {\n            return;\n        }\n        if (!resizer.current) {\n            resizer.current = XYResizer({\n                domNode: resizeControlRef.current,\n                nodeId: id,\n                getStoreItems: () => {\n                    const { nodeLookup, transform, snapGrid, snapToGrid, nodeOrigin, domNode } = store.getState();\n                    return {\n                        nodeLookup,\n                        transform,\n                        snapGrid,\n                        snapToGrid,\n                        nodeOrigin,\n                        paneDomNode: domNode,\n                    };\n                },\n                onChange: (change, childChanges) => {\n                    const { triggerNodeChanges, nodeLookup, parentLookup, nodeOrigin } = store.getState();\n                    const changes = [];\n                    const nextPosition = { x: change.x, y: change.y };\n                    const node = nodeLookup.get(id);\n                    if (node && node.expandParent && node.parentId) {\n                        const origin = node.origin ?? nodeOrigin;\n                        const width = change.width ?? node.measured.width ?? 0;\n                        const height = change.height ?? node.measured.height ?? 0;\n                        const child = {\n                            id: node.id,\n                            parentId: node.parentId,\n                            rect: {\n                                width,\n                                height,\n                                ...evaluateAbsolutePosition({\n                                    x: change.x ?? node.position.x,\n                                    y: change.y ?? node.position.y,\n                                }, { width, height }, node.parentId, nodeLookup, origin),\n                            },\n                        };\n                        const parentExpandChanges = handleExpandParent([child], nodeLookup, parentLookup, nodeOrigin);\n                        changes.push(...parentExpandChanges);\n                        /*\n                         * when the parent was expanded by the child node, its position will be clamped at\n                         * 0,0 when node origin is 0,0 and to width, height if it's 1,1\n                         */\n                        nextPosition.x = change.x ? Math.max(origin[0] * width, change.x) : undefined;\n                        nextPosition.y = change.y ? Math.max(origin[1] * height, change.y) : undefined;\n                    }\n                    if (nextPosition.x !== undefined && nextPosition.y !== undefined) {\n                        const positionChange = {\n                            id,\n                            type: 'position',\n                            position: { ...nextPosition },\n                        };\n                        changes.push(positionChange);\n                    }\n                    if (change.width !== undefined && change.height !== undefined) {\n                        const dimensionChange = {\n                            id,\n                            type: 'dimensions',\n                            resizing: true,\n                            setAttributes: true,\n                            dimensions: {\n                                width: change.width,\n                                height: change.height,\n                            },\n                        };\n                        changes.push(dimensionChange);\n                    }\n                    for (const childChange of childChanges) {\n                        const positionChange = {\n                            ...childChange,\n                            type: 'position',\n                        };\n                        changes.push(positionChange);\n                    }\n                    triggerNodeChanges(changes);\n                },\n                onEnd: () => {\n                    const dimensionChange = {\n                        id: id,\n                        type: 'dimensions',\n                        resizing: false,\n                    };\n                    store.getState().triggerNodeChanges([dimensionChange]);\n                },\n            });\n        }\n        resizer.current.update({\n            controlPosition,\n            boundaries: {\n                minWidth,\n                minHeight,\n                maxWidth,\n                maxHeight,\n            },\n            keepAspectRatio,\n            onResizeStart,\n            onResize,\n            onResizeEnd,\n            shouldResize,\n        });\n        return () => {\n            resizer.current?.destroy();\n        };\n    }, [\n        controlPosition,\n        minWidth,\n        minHeight,\n        maxWidth,\n        maxHeight,\n        keepAspectRatio,\n        onResizeStart,\n        onResize,\n        onResizeEnd,\n        shouldResize,\n    ]);\n    const positionClassNames = controlPosition.split('-');\n    const colorStyleProp = variant === ResizeControlVariant.Line ? 'borderColor' : 'backgroundColor';\n    const controlStyle = color ? { ...style, [colorStyleProp]: color } : style;\n    return (jsx(\"div\", { className: cc(['react-flow__resize-control', 'nodrag', ...positionClassNames, variant, className]), ref: resizeControlRef, style: controlStyle, children: children }));\n}\n/**\n * To create your own resizing UI, you can use the `NodeResizeControl` component where you can pass children (such as icons).\n * @public\n *\n */\nconst NodeResizeControl = memo(ResizeControl);\n\n/**\n * The `<NodeResizer />` component can be used to add a resize functionality to your\n * nodes. It renders draggable controls around the node to resize in all directions.\n * @public\n *\n * @example\n *```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeResizer } from '@xyflow/react';\n *\n *function ResizableNode({ data }) {\n *  return (\n *    <>\n *      <NodeResizer minWidth={100} minHeight={30} />\n *      <Handle type=\"target\" position={Position.Left} />\n *      <div style={{ padding: 10 }}>{data.label}</div>\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(ResizableNode);\n *```\n */\nfunction NodeResizer({ nodeId, isVisible = true, handleClassName, handleStyle, lineClassName, lineStyle, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    if (!isVisible) {\n        return null;\n    }\n    return (jsxs(Fragment, { children: [XY_RESIZER_LINE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: lineClassName, style: lineStyle, nodeId: nodeId, position: position, variant: ResizeControlVariant.Line, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position))), XY_RESIZER_HANDLE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: handleClassName, style: handleStyle, nodeId: nodeId, position: position, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position)))] }));\n}\n\nconst selector = (state) => state.domNode?.querySelector('.react-flow__renderer');\nfunction NodeToolbarPortal({ children }) {\n    const wrapperRef = useStore(selector);\n    if (!wrapperRef) {\n        return null;\n    }\n    return createPortal(children, wrapperRef);\n}\n\nconst nodeEqualityFn = (a, b) => a?.internals.positionAbsolute.x !== b?.internals.positionAbsolute.x ||\n    a?.internals.positionAbsolute.y !== b?.internals.positionAbsolute.y ||\n    a?.measured.width !== b?.measured.width ||\n    a?.measured.height !== b?.measured.height ||\n    a?.selected !== b?.selected ||\n    a?.internals.z !== b?.internals.z;\nconst nodesEqualityFn = (a, b) => {\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const [key, node] of a) {\n        if (nodeEqualityFn(node, b.get(key))) {\n            return false;\n        }\n    }\n    return true;\n};\nconst storeSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n    selectedNodesCount: state.nodes.filter((node) => node.selected).length,\n});\n/**\n * This component can render a toolbar or tooltip to one side of a custom node. This\n * toolbar doesn't scale with the viewport so that the content is always visible.\n *\n * @public\n * @example\n * ```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeToolbar } from '@xyflow/react';\n *\n *function CustomNode({ data }) {\n *  return (\n *    <>\n *      <NodeToolbar isVisible={data.toolbarVisible} position={data.toolbarPosition}>\n *        <button>delete</button>\n *        <button>copy</button>\n *        <button>expand</button>\n *      </NodeToolbar>\n *\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(CustomNode);\n *```\n * @remarks By default, the toolbar is only visible when a node is selected. If multiple\n * nodes are selected it will not be visible to prevent overlapping toolbars or\n * clutter. You can override this behavior by setting the `isVisible` prop to `true`.\n */\nfunction NodeToolbar({ nodeId, children, className, style, isVisible, position = Position.Top, offset = 10, align = 'center', ...rest }) {\n    const contextNodeId = useNodeId();\n    const nodesSelector = useCallback((state) => {\n        const nodeIds = Array.isArray(nodeId) ? nodeId : [nodeId || contextNodeId || ''];\n        const internalNodes = nodeIds.reduce((res, id) => {\n            const node = state.nodeLookup.get(id);\n            if (node) {\n                res.set(node.id, node);\n            }\n            return res;\n        }, new Map());\n        return internalNodes;\n    }, [nodeId, contextNodeId]);\n    const nodes = useStore(nodesSelector, nodesEqualityFn);\n    const { x, y, zoom, selectedNodesCount } = useStore(storeSelector, shallow);\n    // if isVisible is not set, we show the toolbar only if its node is selected and no other node is selected\n    const isActive = typeof isVisible === 'boolean'\n        ? isVisible\n        : nodes.size === 1 && nodes.values().next().value?.selected && selectedNodesCount === 1;\n    if (!isActive || !nodes.size) {\n        return null;\n    }\n    const nodeRect = getInternalNodesBounds(nodes);\n    const nodesArray = Array.from(nodes.values());\n    const zIndex = Math.max(...nodesArray.map((node) => node.internals.z + 1));\n    const wrapperStyle = {\n        position: 'absolute',\n        transform: getNodeToolbarTransform(nodeRect, { x, y, zoom }, position, offset, align),\n        zIndex,\n        ...style,\n    };\n    return (jsx(NodeToolbarPortal, { children: jsx(\"div\", { style: wrapperStyle, className: cc(['react-flow__node-toolbar', className]), ...rest, \"data-id\": nodesArray.reduce((acc, node) => `${acc}${node.id} `, '').trim(), children: children }) }));\n}\n\nexport { Background, BackgroundVariant, BaseEdge, BezierEdge, ControlButton, Controls, EdgeLabelRenderer, EdgeText, Handle, MiniMap, NodeResizeControl, NodeResizer, NodeToolbar, Panel, index as ReactFlow, ReactFlowProvider, SimpleBezierEdge, SmoothStepEdge, StepEdge, StraightEdge, ViewportPortal, applyEdgeChanges, applyNodeChanges, getSimpleBezierPath, isEdge, isNode, useConnection, useEdges, useEdgesState, useHandleConnections, useInternalNode, useKeyPress, useNodeConnections, useNodeId, useNodes, useNodesData, useNodesInitialized, useNodesState, useOnSelectionChange, useOnViewportChange, useReactFlow, useStore, useStoreApi, useUpdateNodeInternals, useViewport };\n"], "names": ["StoreContext", "createContext", "Provider$1", "Provider", "zustandErrorMessage", "useStore", "selector", "equalityFn", "store", "useContext", "Error", "useStoreApi", "useMemo", "getState", "setState", "subscribe", "style", "display", "ariaLiveStyle", "position", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "ARIA_NODE_DESC_KEY", "ARIA_EDGE_DESC_KEY", "selector$o", "s", "ariaLiveMessage", "AriaLiveMessage", "rfId", "jsx", "id", "children", "A11yDescriptions", "disableKeyboardA11y", "jsxs", "Fragment", "selector$n", "userSelectionActive", "Panel", "forwardRef", "className", "rest", "ref", "pointerEvents", "positionClasses", "split", "Attribution", "proOptions", "hideAttribution", "href", "target", "rel", "displayName", "selector$m", "selectedNodes", "<PERSON><PERSON><PERSON>", "node", "nodeLookup", "selected", "push", "internals", "userNode", "edge", "edgeLookup", "selectId", "obj", "areEqual", "a", "b", "map", "SelectionListenerInner", "onSelectionChange", "useEffect", "params", "nodes", "edges", "onSelectionChangeHandlers", "for<PERSON>ach", "fn", "changeSelector", "SelectionListener", "storeHasSelectionChangeHandlers", "defaultNodeOrigin", "defaultViewport", "x", "y", "zoom", "fieldsToTrack", "selector$l", "setNodes", "set<PERSON><PERSON>", "setMinZoom", "setMaxZoom", "setTranslateExtent", "setNodeExtent", "reset", "setDefaultNodesAndEdges", "setPaneClickDistance", "initPrevValues", "translateExtent", "node<PERSON><PERSON><PERSON>", "minZoom", "max<PERSON><PERSON>", "elementsSelectable", "noPanClassName", "paneClickDistance", "StoreUpdater", "props", "defaultNodes", "defaultEdges", "<PERSON><PERSON><PERSON>s", "current", "useRef", "fieldName", "fieldValue", "fitViewOnInit", "fitViewOnInitOptions", "getMediaQuery", "window", "matchMedia", "defaultDoc", "document", "useKeyPress", "keyCode", "options", "actInsideInputWithModifier", "keyPressed", "setKeyPressed", "useState", "modifierPressed", "pressedKeys", "Set", "keyCodes", "keysToWatch", "keys", "Array", "isArray", "filter", "kc", "replace", "<PERSON><PERSON><PERSON>", "reduce", "res", "item", "concat", "downHandler", "event", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "keyOrCode", "useKeyOrCode", "code", "add", "isMatchingKey", "preventDefault", "up<PERSON><PERSON><PERSON>", "clear", "delete", "key", "re<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "isUp", "length", "size", "some", "every", "k", "has", "eventCode", "includes", "useViewportHelper", "zoomIn", "panZoom", "scaleBy", "duration", "Promise", "resolve", "zoomOut", "zoomTo", "zoomLevel", "scaleTo", "getZoom", "transform", "setViewport", "async", "viewport", "tX", "tY", "tZoom", "getViewport", "<PERSON><PERSON><PERSON><PERSON>", "domNode", "fitViewNodes", "setCenter", "nextZoom", "centerX", "centerY", "fitBounds", "bounds", "screenToFlowPosition", "clientPosition", "snapGrid", "snapToGrid", "domX", "domY", "getBoundingClientRect", "correctedPosition", "_snapGrid", "_snapToGrid", "flowToScreenPosition", "flowPosition", "rendererPosition", "applyChanges", "changes", "elements", "updatedElements", "changesMap", "Map", "addItemChanges", "change", "type", "set", "elementChanges", "get", "element", "updatedElement", "applyChange", "undefined", "index", "splice", "dragging", "dimensions", "measured", "setAttributes", "resizing", "applyNodeChanges", "applyEdgeChanges", "createSelectionChange", "getSelectionChanges", "items", "selectedIds", "mutateItem", "willBeSelected", "getElements<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup", "itemsLookup", "entries", "lookupItem", "storeItem", "elementToRemoveChange", "isNode", "isEdge", "fixedForwardRef", "render", "useIsomorphicLayoutEffect", "useLayoutEffect", "useQueue", "runQueue", "serial", "setSerial", "BigInt", "queue", "cb", "createQueue", "n", "queueItems", "BatchContext", "BatchProvider", "nodeQueue", "useCallback", "hasDefaultNodes", "onNodesChange", "next", "payload", "edgeQueue", "hasDefaultEdges", "onEdgesChange", "value", "selector$k", "useReactFlow", "viewportHelper", "batchContext", "useBatchContext", "viewportInitialized", "<PERSON><PERSON><PERSON><PERSON>", "getInternalNode", "getNodeRect", "nodeToUse", "parentId", "nodeWithPosition", "updateNode", "nodeUpdate", "prevNodes", "nextNode", "updateEdge", "edgeUpdate", "prevEdges", "nextEdge", "getNodes", "getNode", "get<PERSON>dges", "e", "getEdge", "addNodes", "newNodes", "addEdges", "newEdges", "toObject", "deleteElements", "nodesToRemove", "edgesToRemove", "onNodesDelete", "onEdgesDelete", "triggerNodeChanges", "triggerEdgeChanges", "onDelete", "onBeforeDelete", "matchingNodes", "matching<PERSON><PERSON>", "hasMatchingEdges", "hasMatchingNodes", "edgeChanges", "nodeChanges", "deletedNodes", "deleted<PERSON>dges", "getIntersectingNodes", "nodeOrRect", "partially", "isRect", "nodeRect", "hasNodesOption", "internalNode", "positionAbsolute", "currNodeRect", "overlappingArea", "isNodeIntersecting", "area", "updateNodeData", "dataUpdate", "nextData", "data", "updateEdgeData", "getNodesBounds", "getHandleConnections", "nodeId", "from", "connectionLookup", "values", "getNodeConnections", "handleId", "deleteKeyOptions", "win$1", "containerStyle", "top", "left", "selector$j", "lib", "ZoomPane", "onPaneContextMenu", "zoomOnScroll", "zoomOnPinch", "panOnScroll", "panOnScrollSpeed", "panOnScrollMode", "Free", "zoomOnDoubleClick", "panOnDrag", "zoomActivationKeyCode", "preventScrolling", "noWheelClassName", "onViewportChange", "isControlledViewport", "zoomPane", "zoomActivationKeyPressed", "updateDimensions", "onError", "resizeObserver", "ResizeObserver", "observe", "unobserve", "useResizeHandler", "onTransformChange", "onDraggingChange", "paneDragging", "onPanZoomStart", "vp", "onViewportChangeStart", "onMoveStart", "onPanZoom", "onMove", "onPanZoomEnd", "onViewportChangeEnd", "onMoveEnd", "closest", "destroy", "update", "selector$i", "userSelectionRect", "UserSelection", "wrapHandler", "handler", "containerRef", "selector$h", "Pane", "isSelecting", "selectionKeyPressed", "selectionMode", "Full", "selectionOnDrag", "onSelectionStart", "onSelectionEnd", "onPaneClick", "onPaneScroll", "onPaneMouseEnter", "onPaneMouseMove", "onPaneMouseLeave", "hasActiveSelection", "container", "containerBounds", "selectedNodeIds", "selectedEdgeIds", "selectionInProgress", "selectionStarted", "onClick", "resetSelectedElements", "nodesSelectionActive", "onWheel", "draggable", "selection", "onContextMenu", "onPointerEnter", "onPointerDown", "button", "setPointerCapture", "pointerId", "nativeEvent", "startX", "startY", "onPointerMove", "defaultEdgeOptions", "mouseX", "mouseY", "nextUserSelectRect", "Math", "abs", "prevSelectedNodeIds", "prevSelectedEdgeIds", "Partial", "edgesSelectable", "selectable", "connections", "edgeId", "onPointerUp", "releasePointerCapture", "onPointerLeave", "handleNodeClick", "unselect", "nodeRef", "addSelectedNodes", "unselectNodesAndEdges", "multiSelectionActive", "requestAnimationFrame", "blur", "useDrag", "disabled", "noDragClassName", "handleSelector", "isSelectable", "nodeClickDistance", "setDragging", "xyDrag", "getStoreItems", "onNodeMouseDown", "onDragStart", "onDragStop", "useMoveSelectedNodes", "nodeExtent", "nodesDraggable", "updateNodePositions", "nodeUpdates", "isSelected", "selectedAndDraggable", "xVelo", "<PERSON><PERSON><PERSON>", "xDiff", "direction", "factor", "yDiff", "nextPosition", "NodeIdContext", "Consumer", "useNodeId", "selector$g", "connectOnClick", "<PERSON><PERSON>", "memo", "Top", "isValidConnection", "isConnectable", "isConnectableStart", "isConnectableEnd", "onConnect", "onMouseDown", "onTouchStart", "<PERSON><PERSON><PERSON><PERSON>", "connectingFrom", "connectingTo", "clickConnecting", "isPossibleEndHandle", "connectionInProcess", "clickConnectionInProcess", "valid", "state", "connectionClickStartHandle", "clickHandle", "connectionMode", "connection", "fromHandle", "toHandle", "<PERSON><PERSON><PERSON><PERSON>", "Strict", "connectingSelector", "onConnectExtended", "onConnectAction", "edgeParams", "isMouseTriggered", "currentStore", "autoPanOnConnect", "connectionRadius", "flowId", "panBy", "cancelConnection", "onConnectStart", "onConnectEnd", "updateConnection", "getTransform", "getFromHandle", "autoPanSpeed", "source", "connectable", "connectablestart", "connectableend", "clickconnecting", "connectingfrom", "connectingto", "connectionindicator", "onClickConnectStart", "onClickConnectEnd", "isValidConnectionStore", "connectionState", "handleType", "doc", "isValidConnectionHandler", "handle", "fromNodeId", "fromHandleId", "fromType", "connectionClone", "structuredClone", "inProgress", "toPosition", "arrowKeyDiffs", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "builtinNodeTypes", "input", "sourcePosition", "Bottom", "label", "default", "targetPosition", "output", "group", "selector$f", "transformString", "NodesSelection", "onSelectionContextMenu", "moveSelectedNodes", "focus", "preventScroll", "tabIndex", "onKeyDown", "Object", "prototype", "hasOwnProperty", "call", "win", "selector$e", "FlowRendererComponent", "deleteKeyCode", "selectionKeyCode", "multiSelectionKeyCode", "panActivationKeyCode", "_panOnScroll", "_panOnDrag", "panActivationKeyPressed", "_selectionOnDrag", "deleteKeyPressed", "multiSelectionKeyPressed", "useGlobalKeyHandler", "<PERSON><PERSON><PERSON><PERSON>", "useVisibleNodeIds", "onlyRenderVisible", "selector$d", "selector$c", "updateNodeInternals", "NodeWrapper", "onMouseEnter", "onMouseMove", "onMouseLeave", "onDoubleClick", "nodesConnectable", "nodesFocusable", "nodeTypes", "isParent", "parentLookup", "nodeType", "NodeComponent", "isDraggable", "isFocusable", "focusable", "hasDimensions", "observedNode", "prevSourcePosition", "prevTargetPosition", "prevType", "isInitialized", "handleBounds", "hidden", "typeChanged", "sourcePosChanged", "targetPosChanged", "nodeElement", "force", "useNodeObserver", "dragHandle", "nodeDimensions", "inlineDimensions", "initialWidth", "initialHeight", "getNodeInlineStyleDimensions", "hasPointerEvents", "onMouseEnterHandler", "onMouseMoveHandler", "onMouseLeaveHandler", "onContextMenuHandler", "onDoubleClickHandler", "parent", "zIndex", "z", "visibility", "selectNodesOnDrag", "nodeDragThreshold", "toLowerCase", "role", "aria<PERSON><PERSON><PERSON>", "positionAbsoluteX", "positionAbsoluteY", "deletable", "selector$b", "NodeRendererComponent", "nodeIds", "onlyRenderVisibleElements", "updates", "entry", "getAttribute", "disconnect", "useResizeObserver", "onNodeClick", "onNodeMouseEnter", "onNodeMouseMove", "onNodeMouseLeave", "onNodeContextMenu", "onNodeDoubleClick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkerSymbols", "Arrow", "color", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "fill", "points", "ArrowClosed", "<PERSON><PERSON>", "markerUnits", "orient", "Symbol", "useMarkerSymbol", "marker<PERSON>id<PERSON>", "markerHeight", "viewBox", "refX", "refY", "MarkerDefinitions", "defaultColor", "markers", "defaultMarkerStart", "markerStart", "defaultMarkerEnd", "markerEnd", "marker", "MarkerDefinitions$1", "EdgeTextComponent", "labelStyle", "labelShowBg", "labelBgStyle", "labelBgPadding", "labelBgBorderRadius", "edgeTextBbox", "setEdgeTextBbox", "edgeTextClasses", "edgeTextRef", "textBbox", "getBBox", "rx", "ry", "dy", "EdgeText", "BaseEdge", "path", "labelX", "labelY", "interactionWidth", "d", "strokeOpacity", "getControl", "pos", "x1", "y1", "x2", "y2", "Left", "Right", "getSimpleBezierPath", "sourceX", "sourceY", "targetX", "targetY", "sourceControlX", "sourceControlY", "targetControlX", "targetControlY", "offsetX", "offsetY", "createSimpleBezierEdge", "_id", "isInternal", "SimpleBezierEdge", "SimpleBezierEdgeInternal", "createSmoothStepEdge", "pathOptions", "borderRadius", "offset", "SmoothStepEdge", "SmoothStepEdgeInternal", "createStepEdge", "Step<PERSON><PERSON>", "StepEdgeInternal", "createStraightEdge", "StraightEdge", "StraightEdgeInternal", "createBezierEdge", "curvature", "<PERSON><PERSON><PERSON><PERSON>", "BezierEdgeInternal", "builtinEdgeTypes", "straight", "step", "smoothstep", "simplebezier", "nullPosition", "shiftX", "shift", "shiftY", "EdgeUpdaterClassName", "EdgeAnchor", "radius", "onMouseOut", "cx", "cy", "r", "EdgeUpdateAnchors", "isReconnectable", "reconnectRadius", "onReconnect", "onReconnectStart", "onReconnectEnd", "setReconnecting", "setUpdateHover", "handleEdgeUpdater", "<PERSON><PERSON><PERSON><PERSON>", "edgeUpdaterType", "evt", "onReconnectMouseEnter", "onReconnectMouseOut", "targetHandle", "sourceHandle", "EdgeWrapper", "edgesFocusable", "edgesReconnectable", "edgeTypes", "edgeType", "EdgeComponent", "reconnectable", "edgeRef", "updateHover", "reconnecting", "sourceNode", "targetNode", "edgePosition", "elevateOnSelect", "elevateEdgesOnSelect", "markerStartUrl", "markerEndUrl", "onEdgeDoubleClick", "onEdgeContextMenu", "onEdgeMouseEnter", "onEdgeMouseMove", "onEdgeMouseLeave", "animated", "inactive", "updating", "addSelectedEdges", "sourceHandleId", "targetHandleId", "selector$a", "EdgeRendererComponent", "defaultMarkerColor", "onEdgeClick", "edgeIds", "visibleEdgeIds", "<PERSON><PERSON><PERSON><PERSON>", "selector$9", "Viewport", "selector$8", "syncViewport", "storeSelector$1", "to", "useConnection", "connectionSelector", "combinedSelector", "getSelector", "selector$7", "ConnectionLineWrapper", "component", "ConnectionLine", "CustomComponent", "<PERSON><PERSON>", "fromNode", "fromPosition", "toNode", "connectionLineType", "connectionLineStyle", "fromX", "fromY", "toX", "toY", "connectionStatus", "pathParams", "SimpleBezier", "Step", "SmoothStep", "emptyTypes", "useNodeOrEdgeTypesWarning", "nodeOrEdgeTypes", "GraphViewComponent", "onInit", "connectionLineComponent", "connectionLineContainerStyle", "rfInstance", "setTimeout", "useOnInitHandler", "useViewportSync", "GraphView", "getInitialState", "storeEdges", "storeNodes", "storeNodeOrigin", "storeNodeExtent", "elevateNodesOnSelect", "fitViewDone", "autoPanOnNodeDrag", "debug", "createStore", "fitView$1", "checkEquality", "triggerFitView", "fitViewSync", "updatedInternals", "nextFitViewDone", "console", "log", "nodeDragItems", "parentExpandChildren", "dragItem", "expandParent", "max", "rect", "parentExpandChanges", "edgesToUnselect", "setScaleExtent", "clickDistance", "setClickDistance", "nextNodeExtent", "delta", "is", "ReactFlowProvider", "initialNodes", "initialEdges", "Wrapper", "wrapperStyle", "onNodeDragStart", "onNodeDrag", "onNodeDragStop", "onSelectionDragStart", "onSelectionDrag", "onSelectionDragStop", "defaultViewport$1", "fitViewOptions", "attributionPosition", "colorMode", "onScroll", "colorModeClassName", "colorModeClass", "setColorModeClass", "mediaQuery", "updateColorModeClass", "matches", "useColorModeClass", "wrapperOnScroll", "currentTarget", "scrollTo", "behavior", "selector$6", "querySelector", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createPortal", "useNodesState", "nds", "useEdgesState", "eds", "LinePattern", "lineWidth", "variant", "DotPattern", "<PERSON><PERSON><PERSON><PERSON>", "defaultSize", "Dots", "Lines", "Cross", "selector$3", "patternId", "BackgroundComponent", "gap", "bgColor", "patternClassName", "patternSize", "isDots", "isCross", "gapXY", "scaledGap", "scaledSize", "offsetXY", "patternDimensions", "scaledOffset", "_patternId", "patternUnits", "patternTransform", "Background", "PlusIcon", "xmlns", "MinusIcon", "FitViewIcon", "LockIcon", "UnlockIcon", "ControlButton", "selector$2", "isInteractive", "<PERSON><PERSON><PERSON>Reached", "max<PERSON><PERSON>Reached", "ControlsComponent", "showZoom", "showFitView", "showInteractive", "onZoomIn", "onZoomOut", "onFitView", "onInteractiveChange", "orientation", "orientationClass", "title", "MiniMapNode", "strokeColor", "shapeRendering", "background", "backgroundColor", "selectorNodeIds", "getAttrFunction", "func", "Function", "NodeComponentWrapper", "nodeColorFunc", "nodeStrokeColorFunc", "nodeClassNameFunc", "nodeBorderRadius", "nodeStrokeWidth", "MiniMapNodes$1", "nodeStrokeColor", "nodeColor", "nodeClassName", "nodeComponent", "chrome", "selector$1", "viewBB", "boundingRect", "flowWidth", "flowHeight", "MiniMapComponent", "maskColor", "maskStrokeColor", "maskStrokeWidth", "pannable", "zoomable", "inversePan", "zoomStep", "offsetScale", "svg", "elementWidth", "elementHeight", "scaledWidth", "scaledHeight", "viewScale", "viewWidth", "viewHeight", "labelledBy", "viewScaleRef", "minimapInstance", "getViewScale", "onSvgClick", "pointer", "onSvgNodeClick", "fillRule", "MiniMap", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "Number", "MAX_VALUE", "maxHeight", "keepAspectRatio", "shouldResize", "onResizeStart", "onResize", "onResizeEnd", "contextNodeId", "resizeControlRef", "defaultPosition", "Line", "controlPosition", "resizer", "paneDomNode", "onChange", "child<PERSON><PERSON><PERSON>", "origin", "child", "positionChange", "dimensionChange", "<PERSON><PERSON><PERSON><PERSON>", "onEnd", "boundaries", "positionClassNames", "colorStyleProp", "controlStyle"], "sourceRoot": ""}