/*! For license information please see component---src-pages-build-tsx-34d8f410f6df7e60cea6.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[477],{2:function(e,t,n){var r=n(2199),a=n(4664),o=n(5950);e.exports=function(e){return r(e,o,a)}},79:function(e,t,n){var r=n(3702),a=n(80),o=n(4739),i=n(8655),c=n(1175);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=a,l.prototype.get=o,l.prototype.has=i,l.prototype.set=c,e.exports=l},80:function(e,t,n){var r=n(6025),a=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0)&&(n==t.length-1?t.pop():a.call(t,n,1),--this.size,!0)}},270:function(e,t,n){var r=n(7068),a=n(346);e.exports=function e(t,n,o,i,c){return t===n||(null==t||null==n||!a(t)&&!a(n)?t!=t&&n!=n:r(t,n,o,i,e,c))}},289:function(e,t,n){var r=n(2651);e.exports=function(e){return r(this,e).get(e)}},294:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},317:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}},346:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},361:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var r=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&t.test(e))&&e>-1&&e%1==0&&e<n}},392:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},659:function(e,t,n){var r=n(1873),a=Object.prototype,o=a.hasOwnProperty,i=a.toString,c=r?r.toStringTag:void 0;e.exports=function(e){var t=o.call(e,c),n=e[c];try{e[c]=void 0;var r=!0}catch(l){}var a=i.call(e);return r&&(t?e[c]=n:delete e[c]),a}},689:function(e,t,n){var r=n(2),a=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,o,i,c){var l=1&n,s=r(e),d=s.length;if(d!=r(t).length&&!l)return!1;for(var u=d;u--;){var m=s[u];if(!(l?m in t:a.call(t,m)))return!1}var p=c.get(e),f=c.get(t);if(p&&f)return p==t&&f==e;var g=!0;c.set(e,t),c.set(t,e);for(var v=l;++u<d;){var h=e[m=s[u]],y=t[m];if(o)var b=l?o(y,h,m,t,e,c):o(h,y,m,e,t,c);if(!(void 0===b?h===y||i(h,y,n,o,c):b)){g=!1;break}v||(v="constructor"==m)}if(g&&!v){var x=e.constructor,E=t.constructor;x==E||!("constructor"in e)||!("constructor"in t)||"function"==typeof x&&x instanceof x&&"function"==typeof E&&E instanceof E||(g=!1)}return c.delete(e),c.delete(t),g}},695:function(e,t,n){var r=n(8096),a=n(2428),o=n(6449),i=n(3656),c=n(361),l=n(7167),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=o(e),d=!n&&a(e),u=!n&&!d&&i(e),m=!n&&!d&&!u&&l(e),p=n||d||u||m,f=p?r(e.length,String):[],g=f.length;for(var v in e)!t&&!s.call(e,v)||p&&("length"==v||u&&("offset"==v||"parent"==v)||m&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,g))||f.push(v);return f}},938:function(e){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},945:function(e,t,n){var r=n(79),a=n(8223),o=n(3661);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var i=n.__data__;if(!a||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new o(i)}return n.set(e,t),this.size=n.size,this}},1042:function(e,t,n){var r=n(6110)(Object,"create");e.exports=r},1175:function(e,t,n){var r=n(6025);e.exports=function(e,t){var n=this.__data__,a=r(n,e);return a<0?(++this.size,n.push([e,t])):n[a][1]=t,this}},1380:function(e){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},1420:function(e,t,n){var r=n(79);e.exports=function(){this.__data__=new r,this.size=0}},1459:function(e){e.exports=function(e){return this.__data__.has(e)}},1549:function(e,t,n){var r=n(2032),a=n(3862),o=n(6721),i=n(2749),c=n(5749);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=a,l.prototype.get=o,l.prototype.has=i,l.prototype.set=c,e.exports=l},1873:function(e,t,n){var r=n(9325).Symbol;e.exports=r},1882:function(e,t,n){var r=n(2552),a=n(3805);e.exports=function(e){if(!a(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},1955:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return ya}});var r=n(6540),a=n(5312),o=n(436),i=n(9036),c=n(8458),l=n(7677),s=n(2744),d=n(2197),u=n(955),m=n(2941),p=n(5319),f=n(9910),g=n(697),v=n(9644),h=n(85),y=n(4060),b=n(6148),x=n(7213),E=n(2708),w=n(2640),N=n(5404),A=n(2206),C=n(2571),S=n(4810),k=n(180);const _=e=>{var t,n,a;let{isOpen:o,teams:c,currentTeam:l,onToggle:d,onSelectTeam:_,onCreateTeam:O,onEditTeam:I,onDeleteTeam:T,isLoading:R=!1,selectedGallery:D,setSelectedGallery:j}=e;const{0:M,1:$}=(0,r.useState)("recent"),[z,P]=i.Ay.useMessage(),{0:H,1:L}=(0,r.useState)(!1),{0:B,1:F}=(0,r.useState)([]),{user:G}=(0,r.useContext)(s.v);r.useEffect((()=>{(async()=>{if(null!=G&&G.id){L(!0);try{const e=new C.Z,t=await e.listGalleries(G.id);F(t);const n=(0,k.Lg)(`selectedGalleryId_${G.id}`);if(n&&t.length>0){const e=t.find((e=>e.id===n));e?j(e):!D&&t.length>0&&j(t[0])}else!D&&t.length>0&&j(t[0])}catch(e){console.error("Error fetching galleries:",e)}finally{L(!1)}}})()}),[null==G?void 0:G.id]);const V=()=>{var e,t;if(null==D||null===(e=D.config.components)||void 0===e||null===(t=e.teams)||void 0===t||!t.length)return;const n=Object.assign({},{component:D.config.components.teams[0]});n.component.label="default_team"+(new Date).getTime().toString().slice(0,2),O(n),$("recent"),z.success(`"${n.component.label}" added to Recents`)};return o?r.createElement("div",{className:"h-full border-r border-secondary"},P,r.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement("span",{className:"text-primary font-medium"},"Teams"),r.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},c.length)),r.createElement(u.A,{title:"Close Sidebar"},r.createElement("button",{onClick:d,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},r.createElement(v.A,{strokeWidth:1.5,className:"h-6 w-6"})))),r.createElement("div",{className:"my-4 flex text-sm"},r.createElement("div",{className:"mr-2 w-full"},r.createElement(u.A,{title:"Create a new team"},r.createElement(m.Ay,{type:"primary",className:"w-full",icon:r.createElement(g.A,{className:"w-4 h-4"}),onClick:V,disabled:!(null!=D&&null!==(t=D.config.components)&&void 0!==t&&null!==(n=t.teams)&&void 0!==n&&n.length)},"New Team")))),r.createElement("div",{className:"flex border-b border-secondary"},r.createElement("button",{style:{width:"110px"},className:"flex items-center  px-2 py-1 text-sm font-medium "+("recent"===M?"text-accent border-b-2 border-accent":"text-secondary hover:text-primary"),onClick:()=>$("recent")},!R&&r.createElement(r.Fragment,null," ",r.createElement(h.A,{className:"w-4 h-4 mr-1.5"})," Recents"," ",r.createElement("span",{className:"ml-1 text-xs"},"(",c.length,")")),R&&"recent"===M&&r.createElement(r.Fragment,null,"Loading ",r.createElement(y.A,{className:"w-4 h-4 ml-2 animate-spin"}))),r.createElement("button",{className:"flex items-center px-4 py-2 text-sm font-medium "+("gallery"===M?"text-accent border-b-2 border-accent":"text-secondary hover:text-primary"),onClick:()=>$("gallery")},r.createElement(b.A,{className:"w-4 h-4 mr-1.5"}),"From Gallery",H&&"gallery"===M&&r.createElement(y.A,{className:"w-4 h-4 ml-2 animate-spin"}))),r.createElement("div",{className:"scroll overflow-y-auto h-[calc(100%-200px)]"},"recent"===M&&r.createElement("div",{className:"pt-2"},!R&&0===c.length&&r.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},r.createElement(x.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No recent teams found"),c.length>0&&r.createElement("div",{className:R?"pointer-events-none":""},c.map((e=>{var t;return r.createElement("div",{key:e.id,className:"relative border-secondary"},r.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n                        w-1 bg-opacity-80 rounded "+((null==l?void 0:l.id)===e.id?"bg-accent":"bg-tertiary")}),r.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary "+((null==l?void 0:l.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>_(e)},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"font-medium truncate"},null===(t=e.component)||void 0===t?void 0:t.label),r.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity"},r.createElement(u.A,{title:"Delete team"},r.createElement(m.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",danger:!0,icon:r.createElement(E.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),e.id&&T(e.id)}})))),r.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},r.createElement("span",{className:"bg-secondary/20 truncate rounded"},e.component.component_type),r.createElement("div",{className:"flex items-center gap-1"},r.createElement(w.A,{className:"w-3 h-3"}),r.createElement("span",null,e.component.config.participants.length," ",1===e.component.config.participants.length?"agent":"agents"))),e.updated_at&&r.createElement("div",{className:"mt-1 flex items-center gap-1 text-xs text-secondary"},r.createElement("span",null,(0,A.vq)(e.updated_at)))))})))),"gallery"===M&&r.createElement("div",{className:"p-2"},r.createElement("div",{className:"my-2 mb-3 text-xs"}," ","Select a"," ",r.createElement(S.Link,{to:"/gallery",className:"text-accent"},r.createElement("span",{className:"font-medium"},"gallery"))," ","to view its components as templates"),r.createElement(p.A,{className:"w-full mb-4",placeholder:"Select gallery",value:null==D?void 0:D.id,onChange:e=>{const t=B.find((t=>t.id===e));t&&(j(t),null!=G&&G.id&&(0,k.ZB)(`selectedGalleryId_${G.id}`,e))},options:B.map((e=>({value:e.id,label:e.config.name}))),loading:H}),null==D||null===(a=D.config.components)||void 0===a?void 0:a.teams.map((e=>r.createElement("div",{key:e.label+e.component_type,className:"relative border-secondary"},r.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n                  w-1 bg-opacity-80 rounded bg-tertiary"}),r.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"font-medium truncate"},e.label),r.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity"},r.createElement(u.A,{title:"Use as template"},r.createElement(m.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",icon:r.createElement(N.A,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation();const n={component:{...e,label:`${e.label}_${((new Date).getTime()+"").substring(0,5)}`}};O(n),$("recent"),i.Ay.success(`"${n.component.label}" added to Recents`)}})))),r.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},r.createElement("span",{className:"bg-secondary/20 truncate rounded"},e.component_type),r.createElement("div",{className:"flex items-center gap-1"},r.createElement(w.A,{className:"w-3 h-3"}),r.createElement("span",null,e.config.participants.length," ",1===e.config.participants.length?"agent":"agents"))))))),!D&&r.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},r.createElement(x.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"Select a gallery to view templates")))):r.createElement("div",{className:"h-full border-r border-secondary"},r.createElement("div",{className:"p-2 -ml-2"},r.createElement(u.A,{title:`Teams (${c.length})`},r.createElement("button",{onClick:d,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},r.createElement(f.A,{strokeWidth:1.5,className:"h-6 w-6"})))),r.createElement("div",{className:"mt-4 px-2 -ml-1"},r.createElement(u.A,{title:"Create new team"},r.createElement(m.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:()=>V(),icon:r.createElement(g.A,{className:"w-4 h-4"})}))))};var O=n(961);const I="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function T(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function R(e){return"nodeType"in e}function D(e){var t,n;return e?T(e)?e:R(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function j(e){const{Document:t}=D(e);return e instanceof t}function M(e){return!T(e)&&e instanceof D(e).HTMLElement}function $(e){return e instanceof D(e).SVGElement}function z(e){return e?T(e)?e.document:R(e)?j(e)?e:M(e)||$(e)?e.ownerDocument:document:document:document}const P=I?r.useLayoutEffect:r.useEffect;function H(e){const t=(0,r.useRef)(e);return P((()=>{t.current=e})),(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function L(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return P((()=>{n.current!==e&&(n.current=e)}),t),n}function B(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function F(e){const t=H(e),n=(0,r.useRef)(null),a=(0,r.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,a]}function G(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e}),[e]),t.current}let V={};function U(e,t){return(0,r.useMemo)((()=>{if(t)return t;const n=null==V[e]?0:V[e]+1;return V[e]=n,e+"-"+n}),[e,t])}function X(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[a,o]of r){const n=t[a];null!=n&&(t[a]=n+e*o)}return t}),{...t})}}const W=X(1),Y=X(-1);function K(e){if(!e)return!1;const{KeyboardEvent:t}=D(e.target);return t&&e instanceof t}function J(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=D(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const Z=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Z.Translate.toString(e),Z.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),q="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function Q(e){return e.matches(q)?e:e.querySelector(q)}const ee={display:"none"};function te(e){let{id:t,value:n}=e;return r.createElement("div",{id:t,style:ee},n)}function ne(e){let{id:t,announcement:n,ariaLiveType:a="assertive"}=e;return r.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":a,"aria-atomic":!0},n)}const re=(0,r.createContext)(null);const ae={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},oe={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function ie(e){let{announcements:t=oe,container:n,hiddenTextDescribedById:a,screenReaderInstructions:o=ae}=e;const{announce:i,announcement:c}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),l=U("DndLiveRegion"),[s,d]=(0,r.useState)(!1);if((0,r.useEffect)((()=>{d(!0)}),[]),function(e){const t=(0,r.useContext)(re);(0,r.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}})),[i,t])),!s)return null;const u=r.createElement(r.Fragment,null,r.createElement(te,{id:a,value:o.draggable}),r.createElement(ne,{id:l,announcement:c}));return n?(0,O.createPortal)(u,n):u}var ce;function le(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(ce||(ce={}));const se=Object.freeze({x:0,y:0});function de(e,t){const n=J(e);if(!n)return"0 0";return(n.x-t.left)/t.width*100+"% "+(n.y-t.top)/t.height*100+"%"}function ue(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function me(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),o=Math.min(t.top+t.height,e.top+e.height),i=a-r,c=o-n;if(r<a&&n<o){const n=t.width*t.height,r=e.width*e.height,a=i*c;return Number((a/(n+r-a)).toFixed(4))}return 0}const pe=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const a=[];for(const o of r){const{id:e}=o,r=n.get(e);if(r){const n=me(r,t);n>0&&a.push({id:e,data:{droppableContainer:o,value:n}})}}return a.sort(ue)};function fe(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:se}function ge(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const ve=ge(1);function he(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}const ye={ignoreTransform:!1};function be(e,t){void 0===t&&(t=ye);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=D(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=he(t);if(!r)return e;const{scaleX:a,scaleY:o,x:i,y:c}=r,l=e.left-i-(1-a)*parseFloat(n),s=e.top-c-(1-o)*parseFloat(n.slice(n.indexOf(" ")+1)),d=a?e.width/a:e.width,u=o?e.height/o:e.height;return{width:d,height:u,top:s,right:l+d,bottom:s+u,left:l}}(n,t,r))}const{top:r,left:a,width:o,height:i,bottom:c,right:l}=n;return{top:r,left:a,width:o,height:i,bottom:c,right:l}}function xe(e){return be(e,{ignoreTransform:!0})}function Ee(e,t){const n=[];return e?function r(a){if(null!=t&&n.length>=t)return n;if(!a)return n;if(j(a)&&null!=a.scrollingElement&&!n.includes(a.scrollingElement))return n.push(a.scrollingElement),n;if(!M(a)||$(a))return n;if(n.includes(a))return n;const o=D(e).getComputedStyle(a);return a!==e&&function(e,t){void 0===t&&(t=D(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(a,o)&&n.push(a),function(e,t){return void 0===t&&(t=D(e).getComputedStyle(e)),"fixed"===t.position}(a,o)?n:r(a.parentNode)}(e):n}function we(e){const[t]=Ee(e,1);return null!=t?t:null}function Ne(e){return I&&e?T(e)?e:R(e)?j(e)||e===z(e).scrollingElement?window:M(e)?e:null:null:null}function Ae(e){return T(e)?e.scrollX:e.scrollLeft}function Ce(e){return T(e)?e.scrollY:e.scrollTop}function Se(e){return{x:Ae(e),y:Ce(e)}}var ke;function _e(e){return!(!I||!e)&&e===document.scrollingElement}function Oe(e){const t={x:0,y:0},n=_e(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(ke||(ke={}));const Ie={x:.2,y:.2};function Te(e,t,n,r,a){let{top:o,left:i,right:c,bottom:l}=n;void 0===r&&(r=10),void 0===a&&(a=Ie);const{isTop:s,isBottom:d,isLeft:u,isRight:m}=Oe(e),p={x:0,y:0},f={x:0,y:0},g=t.height*a.y,v=t.width*a.x;return!s&&o<=t.top+g?(p.y=ke.Backward,f.y=r*Math.abs((t.top+g-o)/g)):!d&&l>=t.bottom-g&&(p.y=ke.Forward,f.y=r*Math.abs((t.bottom-g-l)/g)),!m&&c>=t.right-v?(p.x=ke.Forward,f.x=r*Math.abs((t.right-v-c)/v)):!u&&i<=t.left+v&&(p.x=ke.Backward,f.x=r*Math.abs((t.left+v-i)/v)),{direction:p,speed:f}}function Re(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:a}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:a,width:e.clientWidth,height:e.clientHeight}}function De(e){return e.reduce(((e,t)=>W(e,Se(t))),se)}function je(e,t){if(void 0===t&&(t=be),!e)return;const{top:n,left:r,bottom:a,right:o}=t(e);we(e)&&(a<=0||o<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Me=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+Ae(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+Ce(t)),0)}]];class $e{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=Ee(t),r=De(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[a,o,i]of Me)for(const e of o)Object.defineProperty(this,e,{get:()=>{const t=i(n),o=r[a]-t;return this.rect[e]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ze{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Pe(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var He,Le;function Be(e){e.preventDefault()}function Fe(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(He||(He={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(Le||(Le={}));const Ge={start:[Le.Space,Le.Enter],cancel:[Le.Esc],end:[Le.Space,Le.Enter,Le.Tab]},Ve=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case Le.Right:return{...n,x:n.x+25};case Le.Left:return{...n,x:n.x-25};case Le.Down:return{...n,y:n.y+25};case Le.Up:return{...n,y:n.y-25}}};class Ue{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new ze(z(t)),this.windowListeners=new ze(D(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(He.Resize,this.handleCancel),this.windowListeners.add(He.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(He.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&je(n),t(se)}handleKeyDown(e){if(K(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:a=Ge,coordinateGetter:o=Ve,scrollBehavior:i="smooth"}=r,{code:c}=e;if(a.end.includes(c))return void this.handleEnd(e);if(a.cancel.includes(c))return void this.handleCancel(e);const{collisionRect:l}=n.current,s=l?{x:l.left,y:l.top}:se;this.referenceCoordinates||(this.referenceCoordinates=s);const d=o(e,{active:t,context:n.current,currentCoordinates:s});if(d){const t=Y(d,s),r={x:0,y:0},{scrollableAncestors:a}=n.current;for(const n of a){const a=e.code,{isTop:o,isRight:c,isLeft:l,isBottom:s,maxScroll:u,minScroll:m}=Oe(n),p=Re(n),f={x:Math.min(a===Le.Right?p.right-p.width/2:p.right,Math.max(a===Le.Right?p.left:p.left+p.width/2,d.x)),y:Math.min(a===Le.Down?p.bottom-p.height/2:p.bottom,Math.max(a===Le.Down?p.top:p.top+p.height/2,d.y))},g=a===Le.Right&&!c||a===Le.Left&&!l,v=a===Le.Down&&!s||a===Le.Up&&!o;if(g&&f.x!==d.x){const e=n.scrollLeft+t.x,o=a===Le.Right&&e<=u.x||a===Le.Left&&e>=m.x;if(o&&!t.y)return void n.scrollTo({left:e,behavior:i});r.x=o?n.scrollLeft-e:a===Le.Right?n.scrollLeft-u.x:n.scrollLeft-m.x,r.x&&n.scrollBy({left:-r.x,behavior:i});break}if(v&&f.y!==d.y){const e=n.scrollTop+t.y,o=a===Le.Down&&e<=u.y||a===Le.Up&&e>=m.y;if(o&&!t.x)return void n.scrollTo({top:e,behavior:i});r.y=o?n.scrollTop-e:a===Le.Down?n.scrollTop-u.y:n.scrollTop-m.y,r.y&&n.scrollBy({top:-r.y,behavior:i});break}}this.handleMove(e,W(Y(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function Xe(e){return Boolean(e&&"distance"in e)}function We(e){return Boolean(e&&"delay"in e)}Ue.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Ge,onActivation:a}=t,{active:o}=n;const{code:i}=e.nativeEvent;if(r.start.includes(i)){const t=o.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class Ye{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=D(e);return e instanceof t?e:z(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:a}=e,{target:o}=a;this.props=e,this.events=t,this.document=z(o),this.documentListeners=new ze(this.document),this.listeners=new ze(n),this.windowListeners=new ze(D(o)),this.initialCoordinates=null!=(r=J(a))?r:se,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(He.Resize,this.handleCancel),this.windowListeners.add(He.DragStart,Be),this.windowListeners.add(He.VisibilityChange,this.handleCancel),this.windowListeners.add(He.ContextMenu,Be),this.documentListeners.add(He.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(We(t))return this.timeoutId=setTimeout(this.handleStart,t.delay),void this.handlePending(t);if(Xe(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(He.Click,Fe,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(He.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:a}=this,{onMove:o,options:{activationConstraint:i}}=a;if(!r)return;const c=null!=(t=J(e))?t:se,l=Y(r,c);if(!n&&i){if(Xe(i)){if(null!=i.tolerance&&Pe(l,i.tolerance))return this.handleCancel();if(Pe(l,i.distance))return this.handleStart()}return We(i)&&Pe(l,i.tolerance)?this.handleCancel():void this.handlePending(i,l)}e.cancelable&&e.preventDefault(),o(c)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===Le.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Ke={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Je extends Ye{constructor(e){const{event:t}=e,n=z(t.target);super(e,Ke,n)}}Je.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button)&&(null==r||r({event:n}),!0)}}];const Ze={move:{name:"mousemove"},end:{name:"mouseup"}};var qe;!function(e){e[e.RightClick=2]="RightClick"}(qe||(qe={}));(class extends Ye{constructor(e){super(e,Ze,z(e.event.target))}}).activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==qe.RightClick&&(null==r||r({event:n}),!0)}}];const Qe={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};var et,tt;function nt(e){let{acceleration:t,activator:n=et.Pointer,canScroll:a,draggingRect:o,enabled:i,interval:c=5,order:l=tt.TreeOrder,pointerCoordinates:s,scrollableAncestors:d,scrollableAncestorRects:u,delta:m,threshold:p}=e;const f=function(e){let{delta:t,disabled:n}=e;const r=G(t);return B((e=>{if(n||!r||!e)return rt;const a={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[ke.Backward]:e.x[ke.Backward]||-1===a.x,[ke.Forward]:e.x[ke.Forward]||1===a.x},y:{[ke.Backward]:e.y[ke.Backward]||-1===a.y,[ke.Forward]:e.y[ke.Forward]||1===a.y}}}),[n,t,r])}({delta:m,disabled:!i}),[g,v]=function(){const e=(0,r.useRef)(null);return[(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,r.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}(),h=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),b=(0,r.useMemo)((()=>{switch(n){case et.Pointer:return s?{top:s.y,bottom:s.y,left:s.x,right:s.x}:null;case et.DraggableRect:return o}}),[n,o,s]),x=(0,r.useRef)(null),E=(0,r.useCallback)((()=>{const e=x.current;if(!e)return;const t=h.current.x*y.current.x,n=h.current.y*y.current.y;e.scrollBy(t,n)}),[]),w=(0,r.useMemo)((()=>l===tt.TreeOrder?[...d].reverse():d),[l,d]);(0,r.useEffect)((()=>{if(i&&d.length&&b){for(const e of w){if(!1===(null==a?void 0:a(e)))continue;const n=d.indexOf(e),r=u[n];if(!r)continue;const{direction:o,speed:i}=Te(e,r,b,t,p);for(const e of["x","y"])f[e][o[e]]||(i[e]=0,o[e]=0);if(i.x>0||i.y>0)return v(),x.current=e,g(E,c),h.current=i,void(y.current=o)}h.current={x:0,y:0},y.current={x:0,y:0},v()}else v()}),[t,E,a,v,i,c,JSON.stringify(b),JSON.stringify(f),g,d,w,u,JSON.stringify(p)])}(class extends Ye{constructor(e){super(e,Qe)}static setup(){return window.addEventListener(Qe.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Qe.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:a}=n;return!(a.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(et||(et={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(tt||(tt={}));const rt={x:{[ke.Backward]:!1,[ke.Forward]:!1},y:{[ke.Backward]:!1,[ke.Forward]:!1}};var at,ot;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(at||(at={})),function(e){e.Optimized="optimized"}(ot||(ot={}));const it=new Map;function ct(e,t){return B((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function lt(e){let{callback:t,disabled:n}=e;const a=H(t),o=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(a)}),[n]);return(0,r.useEffect)((()=>()=>null==o?void 0:o.disconnect()),[o]),o}function st(e){return new $e(be(e),e)}function dt(e,t,n){void 0===t&&(t=st);const[a,o]=(0,r.useState)(null);function i(){o((r=>{if(!e)return null;var a;if(!1===e.isConnected)return null!=(a=null!=r?r:n)?a:null;const o=t(e);return JSON.stringify(r)===JSON.stringify(o)?r:o}))}const c=function(e){let{callback:t,disabled:n}=e;const a=H(t),o=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(a)}),[a,n]);return(0,r.useEffect)((()=>()=>null==o?void 0:o.disconnect()),[o]),o}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),l=lt({callback:i});return P((()=>{i(),e?(null==l||l.observe(e),null==c||c.observe(document.body,{childList:!0,subtree:!0})):(null==l||l.disconnect(),null==c||c.disconnect())}),[e]),a}const ut=[];function mt(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{n.current=null}),t),(0,r.useEffect)((()=>{const t=e!==se;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?Y(e,n.current):se}function pt(e){return(0,r.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const ft=[];function gt(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return M(t)?t:e}const vt=[{sensor:Je,options:{}},{sensor:Ue,options:{}}],ht={current:{}},yt={draggable:{measure:xe},droppable:{measure:xe,strategy:at.WhileDragging,frequency:ot.Optimized},dragOverlay:{measure:be}};class bt extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const xt={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new bt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:le},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:yt,measureDroppableContainers:le,windowRect:null,measuringScheduled:!1},Et={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:le,draggableNodes:new Map,over:null,measureDroppableContainers:le},wt=(0,r.createContext)(Et),Nt=(0,r.createContext)(xt);function At(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new bt}}}function Ct(e,t){switch(t.type){case ce.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case ce.DragMove:return null==e.draggable.active?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case ce.DragEnd:case ce.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case ce.RegisterDroppable:{const{element:n}=t,{id:r}=n,a=new bt(e.droppable.containers);return a.set(r,n),{...e,droppable:{...e.droppable,containers:a}}}case ce.SetDroppableDisabled:{const{id:n,key:r,disabled:a}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new bt(e.droppable.containers);return i.set(n,{...o,disabled:a}),{...e,droppable:{...e.droppable,containers:i}}}case ce.UnregisterDroppable:{const{id:n,key:r}=t,a=e.droppable.containers.get(n);if(!a||r!==a.key)return e;const o=new bt(e.droppable.containers);return o.delete(n),{...e,droppable:{...e.droppable,containers:o}}}default:return e}}function St(e){let{disabled:t}=e;const{active:n,activatorEvent:a,draggableNodes:o}=(0,r.useContext)(wt),i=G(a),c=G(null==n?void 0:n.id);return(0,r.useEffect)((()=>{if(!t&&!a&&i&&null!=c){if(!K(i))return;if(document.activeElement===i.target)return;const e=o.get(c);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=Q(e);if(t){t.focus();break}}}))}}),[a,t,o,c,i]),null}function kt(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}const _t=(0,r.createContext)({...se,scaleX:1,scaleY:1});var Ot;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ot||(Ot={}));const It=(0,r.memo)((function(e){var t,n,a,o;let{id:i,accessibility:c,autoScroll:l=!0,children:s,sensors:d=vt,collisionDetection:u=pe,measuring:m,modifiers:p,...f}=e;const g=(0,r.useReducer)(Ct,void 0,At),[v,h]=g,[y,b]=function(){const[e]=(0,r.useState)((()=>new Set)),t=(0,r.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[e]),t]}(),[x,E]=(0,r.useState)(Ot.Uninitialized),w=x===Ot.Initialized,{draggable:{active:N,nodes:A,translate:C},droppable:{containers:S}}=v,k=null!=N?A.get(N):null,_=(0,r.useRef)({initial:null,translated:null}),T=(0,r.useMemo)((()=>{var e;return null!=N?{id:N,data:null!=(e=null==k?void 0:k.data)?e:ht,rect:_}:null}),[N,k]),R=(0,r.useRef)(null),[j,$]=(0,r.useState)(null),[z,H]=(0,r.useState)(null),G=L(f,Object.values(f)),V=U("DndDescribedBy",i),X=(0,r.useMemo)((()=>S.getEnabled()),[S]),Y=(K=m,(0,r.useMemo)((()=>({draggable:{...yt.draggable,...null==K?void 0:K.draggable},droppable:{...yt.droppable,...null==K?void 0:K.droppable},dragOverlay:{...yt.dragOverlay,...null==K?void 0:K.dragOverlay}})),[null==K?void 0:K.draggable,null==K?void 0:K.droppable,null==K?void 0:K.dragOverlay]));var K;const{droppableRects:Z,measureDroppableContainers:q,measuringScheduled:Q}=function(e,t){let{dragging:n,dependencies:a,config:o}=t;const[i,c]=(0,r.useState)(null),{frequency:l,measure:s,strategy:d}=o,u=(0,r.useRef)(e),m=function(){switch(d){case at.Always:return!1;case at.BeforeDragging:return n;default:return!n}}(),p=L(m),f=(0,r.useCallback)((function(e){void 0===e&&(e=[]),p.current||c((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[p]),g=(0,r.useRef)(null),v=B((t=>{if(m&&!n)return it;if(!t||t===it||u.current!==e||null!=i){const t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new $e(s(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t}),[e,i,n,m,s]);return(0,r.useEffect)((()=>{u.current=e}),[e]),(0,r.useEffect)((()=>{m||f()}),[n,m]),(0,r.useEffect)((()=>{i&&i.length>0&&c(null)}),[JSON.stringify(i)]),(0,r.useEffect)((()=>{m||"number"!=typeof l||null!==g.current||(g.current=setTimeout((()=>{f(),g.current=null}),l))}),[l,m,f,...a]),{droppableRects:v,measureDroppableContainers:f,measuringScheduled:null!=i}}(X,{dragging:w,dependencies:[C.x,C.y],config:Y.droppable}),ee=function(e,t){const n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return B((e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(A,N),te=(0,r.useMemo)((()=>z?J(z):null),[z]),ne=function(){const e=!1===(null==j?void 0:j.autoScrollEnabled),t="object"==typeof l?!1===l.enabled:!1===l,n=w&&!e&&!t;if("object"==typeof l)return{...l,enabled:n};return{enabled:n}}(),ae=function(e,t){return ct(e,t)}(ee,Y.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:a,config:o=!0}=e;const i=(0,r.useRef)(!1),{x:c,y:l}="boolean"==typeof o?{x:o,y:o}:o;P((()=>{if(!c&&!l||!t)return void(i.current=!1);if(i.current||!a)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const r=fe(n(e),a);if(c||(r.x=0),l||(r.y=0),i.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){const t=we(e);t&&t.scrollBy({top:r.y,left:r.x})}}),[t,c,l,a,n])}({activeNode:null!=N?A.get(N):null,config:ne.layoutShiftCompensation,initialRect:ae,measure:Y.draggable.measure});const oe=dt(ee,Y.draggable.measure,ae),le=dt(ee?ee.parentElement:null),de=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:Z,draggableNodes:A,draggingNode:null,draggingNodeRect:null,droppableContainers:S,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ue=S.getNodeFor(null==(t=de.current.over)?void 0:t.id),me=function(e){let{measure:t}=e;const[n,a]=(0,r.useState)(null),o=lt({callback:(0,r.useCallback)((e=>{for(const{target:n}of e)if(M(n)){a((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}),[t])}),i=(0,r.useCallback)((e=>{const n=gt(e);null==o||o.disconnect(),n&&(null==o||o.observe(n)),a(n?t(n):null)}),[t,o]),[c,l]=F(i);return(0,r.useMemo)((()=>({nodeRef:c,rect:n,setRef:l})),[n,c,l])}({measure:Y.dragOverlay.measure}),ge=null!=(n=me.nodeRef.current)?n:ee,he=w?null!=(a=me.rect)?a:oe:null,ye=Boolean(me.nodeRef.current&&me.rect),xe=fe(Ae=ye?null:oe,ct(Ae));var Ae;const Ce=pt(ge?D(ge):null),ke=function(e){const t=(0,r.useRef)(e),n=B((n=>e?n&&n!==ut&&e&&t.current&&e.parentNode===t.current.parentNode?n:Ee(e):ut),[e]);return(0,r.useEffect)((()=>{t.current=e}),[e]),n}(w?null!=ue?ue:ee:null),Oe=function(e,t){void 0===t&&(t=be);const[n]=e,a=pt(n?D(n):null),[o,i]=(0,r.useState)(ft);function c(){i((()=>e.length?e.map((e=>_e(e)?a:new $e(t(e),e))):ft))}const l=lt({callback:c});return P((()=>{null==l||l.disconnect(),c(),e.forEach((e=>null==l?void 0:l.observe(e)))}),[e]),o}(ke),Ie=kt(p,{transform:{x:C.x-xe.x,y:C.y-xe.y,scaleX:1,scaleY:1},activatorEvent:z,active:T,activeNodeRect:oe,containerNodeRect:le,draggingNodeRect:he,over:de.current.over,overlayNodeRect:me.rect,scrollableAncestors:ke,scrollableAncestorRects:Oe,windowRect:Ce}),Te=te?W(te,C):null,Re=function(e){const[t,n]=(0,r.useState)(null),a=(0,r.useRef)(e),o=(0,r.useCallback)((e=>{const t=Ne(e.target);t&&n((e=>e?(e.set(t,Se(t)),new Map(e)):null))}),[]);return(0,r.useEffect)((()=>{const t=a.current;if(e!==t){r(t);const i=e.map((e=>{const t=Ne(e);return t?(t.addEventListener("scroll",o,{passive:!0}),[t,Se(t)]):null})).filter((e=>null!=e));n(i.length?new Map(i):null),a.current=e}return()=>{r(e),r(t)};function r(e){e.forEach((e=>{const t=Ne(e);null==t||t.removeEventListener("scroll",o)}))}}),[o,e]),(0,r.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>W(e,t)),se):De(e):se),[e,t])}(ke),je=mt(Re),Me=mt(Re,[oe]),ze=W(Ie,je),Pe=he?ve(he,Ie):null,He=T&&Pe?u({active:T,collisionRect:Pe,droppableRects:Z,droppableContainers:X,pointerCoordinates:Te}):null,Le=function(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}(He,"id"),[Be,Fe]=(0,r.useState)(null),Ge=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(ye?Ie:W(Ie,Me),null!=(o=null==Be?void 0:Be.rect)?o:null,oe),Ve=(0,r.useRef)(null),Ue=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==R.current)return;const a=A.get(R.current);if(!a)return;const o=e.nativeEvent,i=new n({active:R.current,activeNode:a,event:o,options:r,context:de,onAbort(e){if(!A.get(e))return;const{onDragAbort:t}=G.current,n={id:e};null==t||t(n),y({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!A.get(e))return;const{onDragPending:a}=G.current,o={id:e,constraint:t,initialCoordinates:n,offset:r};null==a||a(o),y({type:"onDragPending",event:o})},onStart(e){const t=R.current;if(null==t)return;const n=A.get(t);if(!n)return;const{onDragStart:r}=G.current,a={activatorEvent:o,active:{id:t,data:n.data,rect:_}};(0,O.unstable_batchedUpdates)((()=>{null==r||r(a),E(Ot.Initializing),h({type:ce.DragStart,initialCoordinates:e,active:t}),y({type:"onDragStart",event:a}),$(Ve.current),H(o)}))},onMove(e){h({type:ce.DragMove,coordinates:e})},onEnd:c(ce.DragEnd),onCancel:c(ce.DragCancel)});function c(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:a}=de.current;let i=null;if(t&&a){const{cancelDrop:c}=G.current;if(i={activatorEvent:o,active:t,collisions:n,delta:a,over:r},e===ce.DragEnd&&"function"==typeof c){await Promise.resolve(c(i))&&(e=ce.DragCancel)}}R.current=null,(0,O.unstable_batchedUpdates)((()=>{h({type:e}),E(Ot.Uninitialized),Fe(null),$(null),H(null),Ve.current=null;const t=e===ce.DragEnd?"onDragEnd":"onDragCancel";if(i){const e=G.current[t];null==e||e(i),y({type:t,event:i})}}))}}Ve.current=i}),[A]),Xe=(0,r.useCallback)(((e,t)=>(n,r)=>{const a=n.nativeEvent,o=A.get(r);if(null!==R.current||!o||a.dndKit||a.defaultPrevented)return;const i={active:o};!0===e(n,t.options,i)&&(a.dndKit={capturedBy:t.sensor},R.current=r,Ue(n,t))}),[A,Ue]),We=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(d,Xe);!function(e){(0,r.useEffect)((()=>{if(!I)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(d),P((()=>{oe&&x===Ot.Initializing&&E(Ot.Initialized)}),[oe,x]),(0,r.useEffect)((()=>{const{onDragMove:e}=G.current,{active:t,activatorEvent:n,collisions:r,over:a}=de.current;if(!t||!n)return;const o={active:t,activatorEvent:n,collisions:r,delta:{x:ze.x,y:ze.y},over:a};(0,O.unstable_batchedUpdates)((()=>{null==e||e(o),y({type:"onDragMove",event:o})}))}),[ze.x,ze.y]),(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:a}=de.current;if(!e||null==R.current||!t||!a)return;const{onDragOver:o}=G.current,i=r.get(Le),c=i&&i.rect.current?{id:i.id,rect:i.rect.current,data:i.data,disabled:i.disabled}:null,l={active:e,activatorEvent:t,collisions:n,delta:{x:a.x,y:a.y},over:c};(0,O.unstable_batchedUpdates)((()=>{Fe(c),null==o||o(l),y({type:"onDragOver",event:l})}))}),[Le]),P((()=>{de.current={activatorEvent:z,active:T,activeNode:ee,collisionRect:Pe,collisions:He,droppableRects:Z,draggableNodes:A,draggingNode:ge,draggingNodeRect:he,droppableContainers:S,over:Be,scrollableAncestors:ke,scrollAdjustedTranslate:ze},_.current={initial:he,translated:Pe}}),[T,ee,He,Pe,A,ge,he,Z,S,Be,ke,ze]),nt({...ne,delta:C,draggingRect:Pe,pointerCoordinates:Te,scrollableAncestors:ke,scrollableAncestorRects:Oe});const Ye=(0,r.useMemo)((()=>({active:T,activeNode:ee,activeNodeRect:oe,activatorEvent:z,collisions:He,containerNodeRect:le,dragOverlay:me,draggableNodes:A,droppableContainers:S,droppableRects:Z,over:Be,measureDroppableContainers:q,scrollableAncestors:ke,scrollableAncestorRects:Oe,measuringConfiguration:Y,measuringScheduled:Q,windowRect:Ce})),[T,ee,oe,z,He,le,me,A,S,Z,Be,q,ke,Oe,Y,Q,Ce]),Ke=(0,r.useMemo)((()=>({activatorEvent:z,activators:We,active:T,activeNodeRect:oe,ariaDescribedById:{draggable:V},dispatch:h,draggableNodes:A,over:Be,measureDroppableContainers:q})),[z,We,T,oe,h,V,A,Be,q]);return r.createElement(re.Provider,{value:b},r.createElement(wt.Provider,{value:Ke},r.createElement(Nt.Provider,{value:Ye},r.createElement(_t.Provider,{value:Ge},s)),r.createElement(St,{disabled:!1===(null==c?void 0:c.restoreFocus)})),r.createElement(ie,{...c,hiddenTextDescribedById:V}))})),Tt=(0,r.createContext)(null),Rt="button";function Dt(e){let{id:t,data:n,disabled:a=!1,attributes:o}=e;const i=U("Draggable"),{activators:c,activatorEvent:l,active:s,activeNodeRect:d,ariaDescribedById:u,draggableNodes:m,over:p}=(0,r.useContext)(wt),{role:f=Rt,roleDescription:g="draggable",tabIndex:v=0}=null!=o?o:{},h=(null==s?void 0:s.id)===t,y=(0,r.useContext)(h?_t:Tt),[b,x]=F(),[E,w]=F(),N=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:a}=n;return e[r]=e=>{a(e,t)},e}),{})),[e,t])}(c,t),A=L(n);P((()=>(m.set(t,{id:t,key:i,node:b,activatorNode:E,data:A}),()=>{const e=m.get(t);e&&e.key===i&&m.delete(t)})),[m,t]);return{active:s,activatorEvent:l,activeNodeRect:d,attributes:(0,r.useMemo)((()=>({role:f,tabIndex:v,"aria-disabled":a,"aria-pressed":!(!h||f!==Rt)||void 0,"aria-roledescription":g,"aria-describedby":u.draggable})),[a,f,v,h,g,u.draggable]),isDragging:h,listeners:a?void 0:N,node:b,over:p,setNodeRef:x,setActivatorNodeRef:w,transform:y}}const jt={timeout:25};function Mt(e){let{animation:t,children:n}=e;const[a,o]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),l=G(n);return n||a||!l||o(l),P((()=>{if(!i)return;const e=null==a?void 0:a.key,n=null==a?void 0:a.props.id;null!=e&&null!=n?Promise.resolve(t(n,i)).then((()=>{o(null)})):o(null)}),[t,a,i]),r.createElement(r.Fragment,null,n,a?(0,r.cloneElement)(a,{ref:c}):null)}const $t={x:0,y:0,scaleX:1,scaleY:1};function zt(e){let{children:t}=e;return r.createElement(wt.Provider,{value:Et},r.createElement(_t.Provider,{value:$t},t))}const Pt={position:"fixed",touchAction:"none"},Ht=e=>K(e)?"transform 250ms ease":void 0,Lt=(0,r.forwardRef)(((e,t)=>{let{as:n,activatorEvent:a,adjustScale:o,children:i,className:c,rect:l,style:s,transform:d,transition:u=Ht}=e;if(!l)return null;const m=o?d:{...d,scaleX:1,scaleY:1},p={...Pt,width:l.width,height:l.height,top:l.top,left:l.left,transform:Z.Transform.toString(m),transformOrigin:o&&a?de(a,l):void 0,transition:"function"==typeof u?u(a):u,...s};return r.createElement(n,{className:c,style:p,ref:t},i)})),Bt=e=>t=>{let{active:n,dragOverlay:r}=t;const a={},{styles:o,className:i}=e;if(null!=o&&o.active)for(const[e,c]of Object.entries(o.active))void 0!==c&&(a[e]=n.node.style.getPropertyValue(e),n.node.style.setProperty(e,c));if(null!=o&&o.dragOverlay)for(const[e,c]of Object.entries(o.dragOverlay))void 0!==c&&r.node.style.setProperty(e,c);return null!=i&&i.active&&n.node.classList.add(i.active),null!=i&&i.dragOverlay&&r.node.classList.add(i.dragOverlay),function(){for(const[e,t]of Object.entries(a))n.node.style.setProperty(e,t);null!=i&&i.active&&n.node.classList.remove(i.active)}},Ft={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:Z.Transform.toString(t)},{transform:Z.Transform.toString(n)}]},sideEffects:Bt({styles:{active:{opacity:"0"}}})};function Gt(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:a}=e;return H(((e,o)=>{if(null===t)return;const i=n.get(e);if(!i)return;const c=i.node.current;if(!c)return;const l=gt(o);if(!l)return;const{transform:s}=D(o).getComputedStyle(o),d=he(s);if(!d)return;const u="function"==typeof t?t:function(e){const{duration:t,easing:n,sideEffects:r,keyframes:a}={...Ft,...e};return e=>{let{active:o,dragOverlay:i,transform:c,...l}=e;if(!t)return;const s={x:i.rect.left-o.rect.left,y:i.rect.top-o.rect.top},d={scaleX:1!==c.scaleX?o.rect.width*c.scaleX/i.rect.width:1,scaleY:1!==c.scaleY?o.rect.height*c.scaleY/i.rect.height:1},u={x:c.x-s.x,y:c.y-s.y,...d},m=a({...l,active:o,dragOverlay:i,transform:{initial:c,final:u}}),[p]=m,f=m[m.length-1];if(JSON.stringify(p)===JSON.stringify(f))return;const g=null==r?void 0:r({active:o,dragOverlay:i,...l}),v=i.node.animate(m,{duration:t,easing:n,fill:"forwards"});return new Promise((e=>{v.onfinish=()=>{null==g||g(),e()}}))}}(t);return je(c,a.draggable.measure),u({active:{id:e,data:i.data,node:c,rect:a.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:o,rect:a.dragOverlay.measure(l)},droppableContainers:r,measuringConfiguration:a,transform:d})}))}let Vt=0;function Ut(e){return(0,r.useMemo)((()=>{if(null!=e)return Vt++,Vt}),[e])}const Xt=r.memo((e=>{let{adjustScale:t=!1,children:n,dropAnimation:a,style:o,transition:i,modifiers:c,wrapperElement:l="div",className:s,zIndex:d=999}=e;const{activatorEvent:u,active:m,activeNodeRect:p,containerNodeRect:f,draggableNodes:g,droppableContainers:v,dragOverlay:h,over:y,measuringConfiguration:b,scrollableAncestors:x,scrollableAncestorRects:E,windowRect:w}=(0,r.useContext)(Nt),N=(0,r.useContext)(_t),A=Ut(null==m?void 0:m.id),C=kt(c,{activatorEvent:u,active:m,activeNodeRect:p,containerNodeRect:f,draggingNodeRect:h.rect,over:y,overlayNodeRect:h.rect,scrollableAncestors:x,scrollableAncestorRects:E,transform:N,windowRect:w}),S=ct(p),k=Gt({config:a,draggableNodes:g,droppableContainers:v,measuringConfiguration:b}),_=S?h.setRef:void 0;return r.createElement(zt,null,r.createElement(Mt,{animation:k},m&&A?r.createElement(Lt,{key:A,id:m.id,ref:_,as:l,activatorEvent:u,adjustScale:t,className:s,transition:i,rect:S,style:{zIndex:d,...o},transform:C},n):null))}));var Wt=n(1133),Yt=n(2275),Kt=(n(8280),n(6942)),Jt=n.n(Kt),Zt=n(9853),qt=n(2279),Qt=n(4129),en=n(2546),tn=n(8414);var nn=n(4440),rn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};function an(e){let{suffixCls:t,tagName:n,displayName:a}=e;return e=>r.forwardRef(((a,o)=>r.createElement(e,Object.assign({ref:o,suffixCls:t,tagName:n},a))))}const on=r.forwardRef(((e,t)=>{const{prefixCls:n,suffixCls:a,className:o,tagName:i}=e,c=rn(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:l}=r.useContext(qt.QO),s=l("layout",n),[d,u,m]=(0,nn.Ay)(s),p=a?`${s}-${a}`:s;return d(r.createElement(i,Object.assign({className:Jt()(n||p,o,u,m),ref:t},c)))})),cn=r.forwardRef(((e,t)=>{const{direction:n}=r.useContext(qt.QO),[a,i]=r.useState([]),{prefixCls:c,className:l,rootClassName:s,children:d,hasSider:u,tagName:m,style:p}=e,f=rn(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),g=(0,Zt.A)(f,["suffixCls"]),{getPrefixCls:v,className:h,style:y}=(0,qt.TP)("layout"),b=v("layout",c),x=function(e,t,n){return"boolean"==typeof n?n:!!e.length||(0,en.A)(t).some((e=>e.type===tn.A))}(a,d,u),[E,w,N]=(0,nn.Ay)(b),A=Jt()(b,{[`${b}-has-sider`]:x,[`${b}-rtl`]:"rtl"===n},h,l,s,w,N),C=r.useMemo((()=>({siderHook:{addSider:e=>{i((t=>[].concat((0,o.A)(t),[e])))},removeSider:e=>{i((t=>t.filter((t=>t!==e))))}}})),[]);return E(r.createElement(Qt.M.Provider,{value:C},r.createElement(m,Object.assign({ref:t,className:A,style:Object.assign(Object.assign({},y),p)},g),d)))})),ln=an({tagName:"div",displayName:"Layout"})(cn),sn=an({suffixCls:"header",tagName:"header",displayName:"Header"})(on),dn=an({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(on),un=an({suffixCls:"content",tagName:"main",displayName:"Content"})(on);const mn=ln;mn.Header=sn,mn.Footer=dn,mn.Content=un,mn.Sider=tn.A,mn._InternalSiderContext=tn.P;var pn=mn,fn=n(2609),gn=n(6143),vn=n(1788);const hn=(0,vn.A)("Cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]]),yn=(0,vn.A)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);var bn=n(8309),xn=n(8852),En=n(4471),wn=n(7799);const Nn=(0,vn.A)("ListCheck",[["path",{d:"M11 18H3",key:"n3j2dh"}],["path",{d:"m15 18 2 2 4-4",key:"1szwhi"}],["path",{d:"M16 12H3",key:"1a2rj7"}],["path",{d:"M16 6H3",key:"1wxfjs"}]]);var An=n(6808),Cn=n(2404),Sn=n.n(Cn),kn=n(1511);let _n=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"");var On=n(4279);const In={X_POSITION:100,MIN_Y_POSITION:200},Tn={START_X:600,START_Y:200,X_STAGGER:0,MIN_Y_STAGGER:50},Rn={WIDTH:272,MIN_HEIGHT:100,PADDING:20},Dn={BASE:80,DESCRIPTION:60,MODEL_SECTION:100,TOOL_SECTION:80,TOOL_ITEM:40,AGENT_SECTION:80,AGENT_ITEM:40,TERMINATION_SECTION:80},jn=e=>{var t;let n=Dn.BASE;switch(e.description&&(n+=Dn.DESCRIPTION),e.component_type){case"team":const a=e;null!==(t=a.config.participants)&&void 0!==t&&t.length&&(n+=Dn.AGENT_SECTION,n+=a.config.participants.length*Dn.AGENT_ITEM),a.config.termination_condition&&(n+=Dn.TERMINATION_SECTION);break;case"agent":var r;if((0,On.O6)(e))n+=200,null!==(r=e.config.tools)&&void 0!==r&&r.length&&(n+=Dn.TOOL_SECTION,n+=e.config.tools.length*Dn.TOOL_ITEM);(0,On.Zj)(e)&&(n+=100),(0,On.fk)(e)&&(n+=-100)}return Math.max(n,Rn.MIN_HEIGHT)},Mn=(e,t)=>{const n=t.map((e=>jn(e.data.component)+50)).reduce(((e,t)=>e+t+Tn.MIN_Y_STAGGER),0);return{x:Tn.START_X+e*Tn.X_STAGGER,y:Tn.START_Y+n}},$n=e=>{if(0===e.length)return{x:In.X_POSITION,y:In.MIN_Y_POSITION};const t=e.reduce(((e,t)=>e+t.position.y),0)/e.length,n=Math.max(In.MIN_Y_POSITION,t);return{x:In.X_POSITION,y:n}},zn=(e,t,n)=>({id:_n(),position:e,type:t.component_type,data:{label:n||t.label||t.component_type,component:t,type:t.component_type,dimensions:{width:Rn.WIDTH,height:jn(t)}}}),Pn=(e,t)=>{const n=e.find((e=>"team"===e.data.type));if(!n)return{nodes:e,edges:t};const r=e.filter((e=>"team"!==e.data.type)),a=r.map(((e,t)=>({...e,position:Mn(t,r.slice(0,t)),data:{...e.data,dimensions:{width:Rn.WIDTH,height:jn(e.data.component)}}})));return{nodes:[{...n,position:$n(a),data:{...n.data,dimensions:{width:Rn.WIDTH,height:jn(n.data.component)}}}].concat((0,o.A)(a)),edges:t}},Hn=(e,t)=>{let n=e.replace(/[^a-zA-Z0-9_$]/g,"_").replace(/^([^a-zA-Z_$])/,"_$1");if(!t.includes(n))return n;let r=1;for(;t.includes(`${n}_${r}`);)r++;return`${n}_${r}`},Ln=(0,kn.v)(((e,t)=>({nodes:[],edges:[],selectedNodeId:null,history:[],currentHistoryIndex:-1,originalComponent:null,addNode:(t,n,r)=>{e((e=>{const a=JSON.parse(JSON.stringify(n));let i=(0,o.A)(e.nodes),c=(0,o.A)(e.edges);if(r){const t=e.nodes.find((e=>e.id===r));if(!t)return e;if((0,On.nL)(a)){if((0,On.VZ)(t.data.component)&&(0,On.HX)(t.data.component))return t.data.component.config.model_client=a,{nodes:i,edges:c,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1};if((0,On.fF)(t.data.component)&&((0,On.O6)(t.data.component)||(0,On.Zj)(t.data.component)))return t.data.component.config.model_client=a,{nodes:i,edges:c,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}else if((0,On.gG)(a)){if((0,On.fF)(t.data.component)&&(0,On.O6)(t.data.component)){t.data.component.config.tools||(t.data.component.config.tools=[]);const n=Hn(a.config.name||a.label||"tool",t.data.component.config.tools.map((e=>e.config.name||e.label||"tool")));return a.config.name=n,t.data.component.config.tools.push(a),{nodes:i,edges:c,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}}else if((0,On.U9)(a)&&(console.log("Termination component added",a),(0,On.VZ)(t.data.component)))return i=e.nodes.map((e=>e.id===r?{...e,data:{...e.data,component:{...e.data.component,config:{...e.data.component.config,termination_condition:a}}}}:e)),{nodes:i,edges:c,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}if((0,On.VZ)(a)){const e={id:_n(),position:t,type:a.component_type,data:{label:a.label||"Team",component:a,type:a.component_type}};i.push(e)}else if((0,On.fF)(a)){const e=i.find((e=>(0,On.VZ)(e.data.component)));if(e){if((0,On.O6)(a)&&(0,On.VZ)(e.data.component)){const t=(e.data.component.config.participants||[]).map((e=>e.config.name));a.config.name=Hn(a.config.name,t)}const n={id:_n(),position:t,type:a.component_type,data:{label:a.label||a.config.name,component:a,type:a.component_type}};i.push(n),c.push({id:_n(),source:e.id,target:n.id,sourceHandle:`${e.id}-agent-output-handle`,targetHandle:`${n.id}-agent-input-handle`,type:"agent-connection"}),(0,On.VZ)(e.data.component)&&(e.data.component.config.participants||(e.data.component.config.participants=[]),e.data.component.config.participants.push(n.data.component))}}const{nodes:l,edges:s}=Pn(i,c);return{nodes:l,edges:s,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:l,edges:s}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}))},updateNode:(t,n)=>{e((e=>{const r=e.nodes.map((r=>{if(r.id!==t){return(0,On.VZ)(r.data.component)&&e.edges.some((e=>"agent-connection"===e.type&&e.target===t&&e.source===r.id))&&(0,On.VZ)(r.data.component)?{...r,data:{...r.data,component:{...r.data.component,config:{...r.data.component.config,participants:r.data.component.config.participants.map((r=>{var a;return r===(null===(a=e.nodes.find((e=>e.id===t)))||void 0===a?void 0:a.data.component)?n.component:r}))}}}}:r}const a=n.component||r.data.component;return{...r,data:{...r.data,...n,component:a}}}));return{nodes:r,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:r,edges:e.edges}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}))},removeNode:t=>{e((e=>{const n=new Set,r=new Map,a=t=>{const o=e.nodes.find((e=>e.id===t));if(!o)return;n.add(t);const i=e.edges.filter((e=>e.source===t||e.target===t));if((0,On.VZ)(o.data.component))i.filter((e=>"agent-connection"===e.type)).forEach((e=>a(e.target)));else if((0,On.fF)(o.data.component)){const t=i.find((e=>"agent-connection"===e.type));if(t){const n=e.nodes.find((e=>e.id===t.source));if(n&&(0,On.VZ)(n.data.component)){const e={...n,data:{...n.data,component:{...n.data.component,config:{...n.data.component.config,participants:n.data.component.config.participants.filter((e=>!Sn()(e,o.data.component)))}}}};r.set(n.id,e)}}}};a(t);const i=e.nodes.filter((e=>!n.has(e.id))).map((e=>r.get(e.id)||e)),c=e.edges.filter((e=>!n.has(e.source)&&!n.has(e.target)));return{nodes:i,edges:c,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}))},addEdge:t=>{e((e=>({edges:[].concat((0,o.A)(e.edges),[t]),history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:[].concat((0,o.A)(e.edges),[t])}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1})))},removeEdge:t=>{e((e=>({edges:e.edges.filter((e=>e.id!==t)),history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:e.edges.filter((e=>e.id!==t))}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1})))},setSelectedNode:t=>{e({selectedNodeId:t})},undo:()=>{e((e=>{if(e.currentHistoryIndex<=0)return e;const t=e.history[e.currentHistoryIndex-1];return{...e,nodes:t.nodes,edges:t.edges,currentHistoryIndex:e.currentHistoryIndex-1}}))},redo:()=>{e((e=>{if(e.currentHistoryIndex>=e.history.length-1)return e;const t=e.history[e.currentHistoryIndex+1];return{...e,nodes:t.nodes,edges:t.edges,currentHistoryIndex:e.currentHistoryIndex+1}}))},syncToJson:()=>{const e=t(),n=e.nodes.filter((e=>"team"===e.data.component.component_type));if(0===n.length)return null;return((e,t,n)=>{if(!(0,On.VZ)(e.data.component))return null;const r={...e.data.component},a=n.filter((t=>t.source===e.id&&"agent-connection"===t.type));return r.config.participants=a.map((e=>{const n=t.find((t=>t.id===e.target));return n&&(0,On.fF)(n.data.component)?n.data.component:null})).filter((e=>null!==e)),r})(n[0],e.nodes,e.edges)},layoutNodes:()=>{const{nodes:n,edges:r}=t(),{nodes:a,edges:i}=Pn(n,r);e({nodes:a,edges:i,history:[].concat((0,o.A)(t().history.slice(0,t().currentHistoryIndex+1)),[{nodes:a,edges:i}]).slice(-50),currentHistoryIndex:t().currentHistoryIndex+1})},loadFromJson:function(n,r){void 0===r&&(r=!0);const{nodes:a,edges:i}=(e=>{const t=[],n=[],r=[];e.config.participants.forEach(((e,t)=>{const n=Mn(t,r),a=zn(n,e);r.push(a)}));const a=zn($n(r),e);return t.push.apply(t,[a].concat(r)),r.forEach((e=>{var t,r;n.push({id:`e${t=a.id}-${r=e.id}`,source:t,target:r,sourceHandle:`${t}-agent-output-handle`,targetHandle:`${r}-agent-input-handle`,type:"agent-connection"})})),{nodes:t,edges:n}})(n),{nodes:c,edges:l}=Pn(a,i);if(r)e({nodes:c,edges:l,originalComponent:n,history:[{nodes:c,edges:l}],currentHistoryIndex:0,selectedNodeId:null});else{const n=t();Sn()(c,n.nodes)&&Sn()(l,n.edges)||e((e=>({nodes:c,edges:l,history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:c,edges:l}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1})))}return{nodes:c,edges:l}},resetHistory:()=>{e((e=>({history:[{nodes:e.nodes,edges:e.edges}],currentHistoryIndex:0})))},addToHistory:()=>{e((e=>({history:[].concat((0,o.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:e.edges}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1})))}})));var Bn=n(9957),Fn=n(8e3),Gn=n(8168),Vn=n(5544),Un=n(2284),Xn=n(2533),Wn=n(8210),Yn=n(3986),Kn=n(9379),Jn=n(4467),Zn=n(754),qn=n(6928),Qn=r.forwardRef((function(e,t){var n=e.prefixCls,a=e.forceRender,o=e.className,i=e.style,c=e.children,l=e.isActive,s=e.role,d=e.classNames,u=e.styles,m=r.useState(l||a),p=(0,Vn.A)(m,2),f=p[0],g=p[1];return r.useEffect((function(){(a||l)&&g(!0)}),[a,l]),f?r.createElement("div",{ref:t,className:Jt()("".concat(n,"-content"),(0,Jn.A)((0,Jn.A)({},"".concat(n,"-content-active"),l),"".concat(n,"-content-inactive"),!l),o),style:i,role:s},r.createElement("div",{className:Jt()("".concat(n,"-content-box"),null==d?void 0:d.body),style:null==u?void 0:u.body},c)):null}));Qn.displayName="PanelContent";var er=Qn,tr=["showArrow","headerClass","isActive","onItemClick","forceRender","className","classNames","styles","prefixCls","collapsible","accordion","panelKey","extra","header","expandIcon","openMotion","destroyInactivePanel","children"],nr=r.forwardRef((function(e,t){var n=e.showArrow,a=void 0===n||n,o=e.headerClass,i=e.isActive,c=e.onItemClick,l=e.forceRender,s=e.className,d=e.classNames,u=void 0===d?{}:d,m=e.styles,p=void 0===m?{}:m,f=e.prefixCls,g=e.collapsible,v=e.accordion,h=e.panelKey,y=e.extra,b=e.header,x=e.expandIcon,E=e.openMotion,w=e.destroyInactivePanel,N=e.children,A=(0,Yn.A)(e,tr),C="disabled"===g,S=null!=y&&"boolean"!=typeof y,k=(0,Jn.A)((0,Jn.A)((0,Jn.A)({onClick:function(){null==c||c(h)},onKeyDown:function(e){"Enter"!==e.key&&e.keyCode!==qn.A.ENTER&&e.which!==qn.A.ENTER||null==c||c(h)},role:v?"tab":"button"},"aria-expanded",i),"aria-disabled",C),"tabIndex",C?-1:0),_="function"==typeof x?x(e):r.createElement("i",{className:"arrow"}),O=_&&r.createElement("div",(0,Gn.A)({className:"".concat(f,"-expand-icon")},["header","icon"].includes(g)?k:{}),_),I=Jt()("".concat(f,"-item"),(0,Jn.A)((0,Jn.A)({},"".concat(f,"-item-active"),i),"".concat(f,"-item-disabled"),C),s),T=Jt()(o,"".concat(f,"-header"),(0,Jn.A)({},"".concat(f,"-collapsible-").concat(g),!!g),u.header),R=(0,Kn.A)({className:T,style:p.header},["header","icon"].includes(g)?{}:k);return r.createElement("div",(0,Gn.A)({},A,{ref:t,className:I}),r.createElement("div",R,a&&O,r.createElement("span",(0,Gn.A)({className:"".concat(f,"-header-text")},"header"===g?k:{}),b),S&&r.createElement("div",{className:"".concat(f,"-extra")},y)),r.createElement(Zn.Ay,(0,Gn.A)({visible:i,leavedClassName:"".concat(f,"-content-hidden")},E,{forceRender:l,removeOnLeave:w}),(function(e,t){var n=e.className,a=e.style;return r.createElement(er,{ref:t,prefixCls:f,className:n,classNames:u,style:a,styles:p,isActive:i,forceRender:l,role:v?"tabpanel":void 0},N)})))})),rr=["children","label","key","collapsible","onItemClick","destroyInactivePanel"];var ar=function(e,t,n){return Array.isArray(e)?function(e,t){var n=t.prefixCls,a=t.accordion,o=t.collapsible,i=t.destroyInactivePanel,c=t.onItemClick,l=t.activeKey,s=t.openMotion,d=t.expandIcon;return e.map((function(e,t){var u=e.children,m=e.label,p=e.key,f=e.collapsible,g=e.onItemClick,v=e.destroyInactivePanel,h=(0,Yn.A)(e,rr),y=String(null!=p?p:t),b=null!=f?f:o,x=null!=v?v:i,E=!1;return E=a?l[0]===y:l.indexOf(y)>-1,r.createElement(nr,(0,Gn.A)({},h,{prefixCls:n,key:y,panelKey:y,isActive:E,accordion:a,openMotion:s,expandIcon:d,header:m,collapsible:b,onItemClick:function(e){"disabled"!==b&&(c(e),null==g||g(e))},destroyInactivePanel:x}),u)}))}(e,n):(0,en.A)(t).map((function(e,t){return function(e,t,n){if(!e)return null;var a=n.prefixCls,o=n.accordion,i=n.collapsible,c=n.destroyInactivePanel,l=n.onItemClick,s=n.activeKey,d=n.openMotion,u=n.expandIcon,m=e.key||String(t),p=e.props,f=p.header,g=p.headerClass,v=p.destroyInactivePanel,h=p.collapsible,y=p.onItemClick,b=!1;b=o?s[0]===m:s.indexOf(m)>-1;var x=null!=h?h:i,E={key:m,panelKey:m,header:f,headerClass:g,isActive:b,prefixCls:a,destroyInactivePanel:null!=v?v:c,openMotion:d,accordion:o,children:e.props.children,onItemClick:function(e){"disabled"!==x&&(l(e),null==y||y(e))},expandIcon:u,collapsible:x};return"string"==typeof e.type?e:(Object.keys(E).forEach((function(e){void 0===E[e]&&delete E[e]})),r.cloneElement(e,E))}(e,t,n)}))},or=n(2065);function ir(e){var t=e;if(!Array.isArray(t)){var n=(0,Un.A)(t);t="number"===n||"string"===n?[t]:[]}return t.map((function(e){return String(e)}))}var cr=r.forwardRef((function(e,t){var n=e.prefixCls,a=void 0===n?"rc-collapse":n,i=e.destroyInactivePanel,c=void 0!==i&&i,l=e.style,s=e.accordion,d=e.className,u=e.children,m=e.collapsible,p=e.openMotion,f=e.expandIcon,g=e.activeKey,v=e.defaultActiveKey,h=e.onChange,y=e.items,b=Jt()(a,d),x=(0,Xn.A)([],{value:g,onChange:function(e){return null==h?void 0:h(e)},defaultValue:v,postState:ir}),E=(0,Vn.A)(x,2),w=E[0],N=E[1];(0,Wn.Ay)(!u,"[rc-collapse] `children` will be removed in next major version. Please use `items` instead.");var A=ar(y,u,{prefixCls:a,accordion:s,openMotion:p,expandIcon:f,collapsible:m,destroyInactivePanel:c,onItemClick:function(e){return N((function(){return s?w[0]===e?[]:[e]:w.indexOf(e)>-1?w.filter((function(t){return t!==e})):[].concat((0,o.A)(w),[e])}))},activeKey:w});return r.createElement("div",(0,Gn.A)({ref:t,className:b,style:l,role:s?"tablist":void 0},(0,or.A)(e,{aria:!0,data:!0})),A)})),lr=Object.assign(cr,{Panel:nr}),sr=lr,dr=(lr.Panel,n(3723)),ur=n(682),mr=n(829);var pr=r.forwardRef(((e,t)=>{const{getPrefixCls:n}=r.useContext(qt.QO),{prefixCls:a,className:o,showArrow:i=!0}=e,c=n("collapse",a),l=Jt()({[`${c}-no-arrow`]:!i},o);return r.createElement(sr.Panel,Object.assign({ref:t},e,{prefixCls:c,className:l}))})),fr=n(2187),gr=n(5905),vr=n(977),hr=n(7358),yr=n(4277);const br=e=>{const{componentCls:t,contentBg:n,padding:r,headerBg:a,headerPadding:o,collapseHeaderPaddingSM:i,collapseHeaderPaddingLG:c,collapsePanelBorderRadius:l,lineWidth:s,lineType:d,colorBorder:u,colorText:m,colorTextHeading:p,colorTextDisabled:f,fontSizeLG:g,lineHeight:v,lineHeightLG:h,marginSM:y,paddingSM:b,paddingLG:x,paddingXS:E,motionDurationSlow:w,fontSizeIcon:N,contentPadding:A,fontHeight:C,fontHeightLG:S}=e,k=`${(0,fr.zA)(s)} ${d} ${u}`;return{[t]:Object.assign(Object.assign({},(0,gr.dF)(e)),{backgroundColor:a,border:k,borderRadius:l,"&-rtl":{direction:"rtl"},[`& > ${t}-item`]:{borderBottom:k,"&:first-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`${(0,fr.zA)(l)} ${(0,fr.zA)(l)} 0 0`}},"&:last-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`0 0 ${(0,fr.zA)(l)} ${(0,fr.zA)(l)}`}},[`> ${t}-header`]:Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:o,color:p,lineHeight:v,cursor:"pointer",transition:`all ${w}, visibility 0s`},(0,gr.K8)(e)),{[`> ${t}-header-text`]:{flex:"auto"},[`${t}-expand-icon`]:{height:C,display:"flex",alignItems:"center",paddingInlineEnd:y},[`${t}-arrow`]:Object.assign(Object.assign({},(0,gr.Nk)()),{fontSize:N,transition:`transform ${w}`,svg:{transition:`transform ${w}`}}),[`${t}-header-text`]:{marginInlineEnd:"auto"}}),[`${t}-collapsible-header`]:{cursor:"default",[`${t}-header-text`]:{flex:"none",cursor:"pointer"}},[`${t}-collapsible-icon`]:{cursor:"unset",[`${t}-expand-icon`]:{cursor:"pointer"}}},[`${t}-content`]:{color:m,backgroundColor:n,borderTop:k,[`& > ${t}-content-box`]:{padding:A},"&-hidden":{display:"none"}},"&-small":{[`> ${t}-item`]:{[`> ${t}-header`]:{padding:i,paddingInlineStart:E,[`> ${t}-expand-icon`]:{marginInlineStart:e.calc(b).sub(E).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:b}}},"&-large":{[`> ${t}-item`]:{fontSize:g,lineHeight:h,[`> ${t}-header`]:{padding:c,paddingInlineStart:r,[`> ${t}-expand-icon`]:{height:S,marginInlineStart:e.calc(x).sub(r).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:x}}},[`${t}-item:last-child`]:{borderBottom:0,[`> ${t}-content`]:{borderRadius:`0 0 ${(0,fr.zA)(l)} ${(0,fr.zA)(l)}`}},[`& ${t}-item-disabled > ${t}-header`]:{"\n          &,\n          & > .arrow\n        ":{color:f,cursor:"not-allowed"}},[`&${t}-icon-position-end`]:{[`& > ${t}-item`]:{[`> ${t}-header`]:{[`${t}-expand-icon`]:{order:1,paddingInlineEnd:0,paddingInlineStart:y}}}}})}},xr=e=>{const{componentCls:t}=e,n=`> ${t}-item > ${t}-header ${t}-arrow`;return{[`${t}-rtl`]:{[n]:{transform:"rotate(180deg)"}}}},Er=e=>{const{componentCls:t,headerBg:n,paddingXXS:r,colorBorder:a}=e;return{[`${t}-borderless`]:{backgroundColor:n,border:0,[`> ${t}-item`]:{borderBottom:`1px solid ${a}`},[`\n        > ${t}-item:last-child,\n        > ${t}-item:last-child ${t}-header\n      `]:{borderRadius:0},[`> ${t}-item:last-child`]:{borderBottom:0},[`> ${t}-item > ${t}-content`]:{backgroundColor:"transparent",borderTop:0},[`> ${t}-item > ${t}-content > ${t}-content-box`]:{paddingTop:r}}}},wr=e=>{const{componentCls:t,paddingSM:n}=e;return{[`${t}-ghost`]:{backgroundColor:"transparent",border:0,[`> ${t}-item`]:{borderBottom:0,[`> ${t}-content`]:{backgroundColor:"transparent",border:0,[`> ${t}-content-box`]:{paddingBlock:n}}}}}};var Nr=(0,hr.OF)("Collapse",(e=>{const t=(0,yr.oX)(e,{collapseHeaderPaddingSM:`${(0,fr.zA)(e.paddingXS)} ${(0,fr.zA)(e.paddingSM)}`,collapseHeaderPaddingLG:`${(0,fr.zA)(e.padding)} ${(0,fr.zA)(e.paddingLG)}`,collapsePanelBorderRadius:e.borderRadiusLG});return[br(t),Er(t),wr(t),xr(t),(0,vr.A)(t)]}),(e=>({headerPadding:`${e.paddingSM}px ${e.padding}px`,headerBg:e.colorFillAlter,contentPadding:`${e.padding}px 16px`,contentBg:e.colorBgContainer})));const Ar=r.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:a,expandIcon:o,className:i,style:c}=(0,qt.TP)("collapse"),{prefixCls:l,className:s,rootClassName:d,style:u,bordered:m=!0,ghost:p,size:f,expandIconPosition:g="start",children:v,expandIcon:h}=e,y=(0,mr.A)((e=>{var t;return null!==(t=null!=f?f:e)&&void 0!==t?t:"middle"})),b=n("collapse",l),x=n(),[E,w,N]=Nr(b);const A=r.useMemo((()=>"left"===g?"start":"right"===g?"end":g),[g]),C=null!=h?h:o,S=r.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t="function"==typeof C?C(e):r.createElement(Fn.A,{rotate:e.isActive?"rtl"===a?-90:90:void 0,"aria-label":e.isActive?"expanded":"collapsed"});return(0,ur.Ob)(t,(()=>{var e;return{className:Jt()(null===(e=null==t?void 0:t.props)||void 0===e?void 0:e.className,`${b}-arrow`)}}))}),[C,b]),k=Jt()(`${b}-icon-position-${A}`,{[`${b}-borderless`]:!m,[`${b}-rtl`]:"rtl"===a,[`${b}-ghost`]:!!p,[`${b}-${y}`]:"middle"!==y},i,s,d,w,N),_=Object.assign(Object.assign({},(0,dr.A)(x)),{motionAppear:!1,leavedClassName:`${b}-content-hidden`}),O=r.useMemo((()=>v?(0,en.A)(v).map(((e,t)=>{var n,r;const a=e.props;if(null==a?void 0:a.disabled){const o=null!==(n=e.key)&&void 0!==n?n:String(t),i=Object.assign(Object.assign({},(0,Zt.A)(e.props,["disabled"])),{key:o,collapsible:null!==(r=a.collapsible)&&void 0!==r?r:"disabled"});return(0,ur.Ob)(e,i)}return e})):null),[v]);return E(r.createElement(sr,Object.assign({ref:t,openMotion:_},(0,Zt.A)(e,["rootClassName"]),{expandIcon:S,prefixCls:b,className:k,style:Object.assign(Object.assign({},c),u)}),O))}));var Cr=Object.assign(Ar,{Panel:pr});const Sr=(0,vn.A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);var kr=n(7073),_r=n(6816),Or=n(5680),Ir=n(3324),Tr=n(942),Rr=n(5107);const Dr=e=>{let{id:t,type:n,config:a,label:o,icon:i}=e;const{attributes:c,listeners:l,setNodeRef:s,transform:d,isDragging:u}=Dt({id:t,data:{current:{type:n,config:a,label:o}}}),m={transform:Z.Transform.toString(d),opacity:u?.8:void 0};return r.createElement("div",Object.assign({ref:s,style:m},c,l,{className:"p-2 text-primary mb-2 border  rounded cursor-move  bg-secondary transition-colors"}),r.createElement("div",{className:"flex items-center gap-2"},r.createElement(Sr,{className:"w-4 h-4 inline-block"}),i,r.createElement("span",{className:" text-sm"},o)))},jr=e=>{let{defaultGallery:t}=e;const[n,a]=r.useState(""),[o,i]=r.useState(!1),c=r.useMemo((()=>[{title:"Agents",type:"agent",items:t.config.components.agents.map((e=>({label:e.label,config:e}))),icon:r.createElement(w.A,{className:"w-4 h-4"})},{title:"Models",type:"model",items:t.config.components.models.map((e=>({label:`${e.label||e.config.model}`,config:e}))),icon:r.createElement(kr.A,{className:"w-4 h-4"})},{title:"Tools",type:"tool",items:t.config.components.tools.map((e=>{var t;return{label:(null===(t=e.config)||void 0===t?void 0:t.name)||e.label,config:e}})),icon:r.createElement(_r.A,{className:"w-4 h-4"})},{title:"Terminations",type:"termination",items:t.config.components.terminations.map((e=>({label:`${e.label}`,config:e}))),icon:r.createElement(Or.A,{className:"w-4 h-4"})}]),[t]).map((e=>{const t=e.items.filter((e=>{var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes(n.toLowerCase())}));return{key:e.title,label:r.createElement("div",{className:"flex items-center gap-2 font-medium"},e.icon,r.createElement("span",null,e.title),r.createElement("span",{className:"text-xs text-gray-500"},"(",t.length,")")),children:r.createElement("div",{className:"space-y-2"},t.map(((t,n)=>r.createElement(Dr,{key:n,id:`${e.title.toLowerCase()}-${n}`,type:e.type,config:t.config,label:t.label||"",icon:e.icon}))))}}));return o?r.createElement("div",{onClick:()=>i(!1),className:"absolute group top-4 left-4 bg-primary shadow-md rounded px-4 pr-2 py-2 cursor-pointer transition-all duration-300 z-50 flex items-center gap-2"},r.createElement("span",null,"Show Component Library"),r.createElement("button",{onClick:()=>i(!1),className:"p-1 group-hover:bg-tertiary rounded transition-colors",title:"Maximize Library"},r.createElement(Ir.A,{className:"w-4 h-4"}))):r.createElement(tn.A,{width:300,className:"bg-primary border z-10 mr-2 border-r border-secondary"},r.createElement("div",{className:"rounded p-2 pt-2"},r.createElement("div",{className:"flex justify-between items-center mb-2"},r.createElement("div",{className:"text-normal"},"Component Library"),r.createElement("button",{onClick:()=>i(!0),className:"p-1 hover:bg-tertiary rounded transition-colors",title:"Minimize Library"},r.createElement(Tr.A,{className:"w-4 h-4"}))),r.createElement("div",{className:"mb-4 text-secondary"},"Drag a component to add it to the team"),r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(Bn.A,{placeholder:"Search components...",onChange:e=>a(e.target.value),className:"flex-1 p-2"})),r.createElement(Cr,{accordion:!0,items:c,defaultActiveKey:["Agents"],bordered:!1,expandIcon:e=>{let{isActive:t}=e;return r.createElement(Rr.A,{strokeWidth:1,className:(t?"transform rotate-180":"")+" w-4 h-4"})}})))};var Mr=n(6274),$r=n(8188);const zr={team:Mr.A,agent:w.A,tool:_r.A,model:kr.A,termination:Or.A},Pr=(0,r.memo)((e=>{var t,n,a;let{accepts:o,children:i,className:c,id:l}=e;const{isOver:s,setNodeRef:d,active:u}=function(e){let{data:t,disabled:n=!1,id:a,resizeObserverConfig:o}=e;const i=U("Droppable"),{active:c,dispatch:l,over:s,measureDroppableContainers:d}=(0,r.useContext)(wt),u=(0,r.useRef)({disabled:n}),m=(0,r.useRef)(!1),p=(0,r.useRef)(null),f=(0,r.useRef)(null),{disabled:g,updateMeasurementsFor:v,timeout:h}={...jt,...o},y=L(null!=v?v:a),b=lt({callback:(0,r.useCallback)((()=>{m.current?(null!=f.current&&clearTimeout(f.current),f.current=setTimeout((()=>{d(Array.isArray(y.current)?y.current:[y.current]),f.current=null}),h)):m.current=!0}),[h]),disabled:g||!c}),x=(0,r.useCallback)(((e,t)=>{b&&(t&&(b.unobserve(t),m.current=!1),e&&b.observe(e))}),[b]),[E,w]=F(x),N=L(t);return(0,r.useEffect)((()=>{b&&E.current&&(b.disconnect(),m.current=!1,b.observe(E.current))}),[E,b]),(0,r.useEffect)((()=>(l({type:ce.RegisterDroppable,element:{id:a,key:i,disabled:n,node:E,rect:p,data:N}}),()=>l({type:ce.UnregisterDroppable,key:i,id:a}))),[a]),(0,r.useEffect)((()=>{n!==u.current.disabled&&(l({type:ce.SetDroppableDisabled,id:a,key:i,disabled:n}),u.current.disabled=n)}),[a,i,n,l]),{active:c,rect:p,isOver:(null==s?void 0:s.id)===a,node:E,over:s,setNodeRef:w}}({id:l,data:{accepts:o}}),m=s&&(null==u||null===(t=u.data)||void 0===t||null===(n=t.current)||void 0===n||null===(a=n.current)||void 0===a?void 0:a.type)&&o.includes(u.data.current.current.type);return r.createElement("div",{ref:d,className:`droppable-zone p-2 ${m?"can-drop":""} ${c||""}`},i)}));Pr.displayName="DroppableZone";const Hr=(0,r.memo)((e=>{let{id:t,data:n,selected:a,dragHandle:o,icon:i,children:c,headerContent:l,descriptionContent:s,className:d,onEditClick:u}=e;const m=Ln((e=>e.removeNode)),p=Ln((e=>e.setSelectedNode)),f="team"!==n.type;return r.createElement("div",{ref:o,className:`\n        bg-white text-primary relative rounded-lg shadow-lg w-72 \n        ${a?"ring-2 ring-accent":""}\n        ${d||""} \n        transition-all duration-200\n      `},r.createElement("div",{className:"border-b p-3 bg-gray-50 rounded-t-lg"},r.createElement("div",{className:"flex items-center justify-between min-w-0"},r.createElement("div",{className:"flex items-center gap-2 min-w-0 flex-1"},r.createElement(i,{className:"flex-shrink-0 w-5 h-5 text-gray-600"}),r.createElement("span",{className:"font-medium text-gray-800 truncate"},n.component.label)),r.createElement("div",{className:"flex items-center gap-2 flex-shrink-0"},r.createElement("span",{className:"text-xs px-2 py-1 bg-gray-200 rounded text-gray-700"},n.component.component_type),r.createElement("button",{onClick:e=>{e.stopPropagation(),p(t)},className:"p-1 hover:bg-secondary rounded"},r.createElement($r.A,{className:"w-4 h-4 text-accent"})),f&&r.createElement(r.Fragment,null,r.createElement("button",{onClick:e=>{console.log("remove node",t),e.stopPropagation(),t&&m(t)},className:"p-1 hover:bg-red-100 rounded"},r.createElement(E.A,{className:"w-4 h-4 text-red-500"}))))),l),r.createElement("div",{className:"px-3 py-2 border-b text-sm text-gray-600"},s),r.createElement("div",{className:"p-3 space-y-2"},c))}));Hr.displayName="BaseNode";const Lr=e=>{let{title:t,children:n}=e;return r.createElement("div",{className:"space-y-1 relative"},r.createElement("h4",{className:"text-xs font-medium text-gray-500 uppercase"},t),r.createElement("div",{className:"bg-gray-50 rounded p-2"},n))},Br=e=>{let{connected:t,label:n}=e;return r.createElement("span",{className:`\n      text-xs px-2 py-1 rounded-full\n      ${t?"bg-green-100 text-green-700":"bg-gray-100 text-gray-600"}\n    `},n)},Fr=(0,r.memo)((e=>{var t,n;const a=e.data.component,o=(0,On.HX)(a)&&!!a.config.model_client,i=(null===(t=a.config.participants)||void 0===t?void 0:t.length)||0;return r.createElement(Hr,Object.assign({},e,{icon:zr.team,headerContent:r.createElement("div",{className:"flex gap-2 mt-2"},r.createElement(Br,{connected:o,label:"Model"}),r.createElement(Br,{connected:i>0,label:`${i} Agent${i>1?"s":""}`})),descriptionContent:r.createElement("div",null,r.createElement("div",null,r.createElement(A.PA,{content:a.description||a.label||"",textThreshold:150,showFullscreen:!1})),(0,On.HX)(a)&&a.config.selector_prompt&&r.createElement("div",{className:"mt-1 text-xs"},"Selector:"," ",r.createElement(A.PA,{content:a.config.selector_prompt,textThreshold:150,showFullscreen:!1})))}),(0,On.HX)(a)&&r.createElement(Lr,{title:"Model"},r.createElement("div",{className:"relative"},o&&r.createElement("div",{className:"text-sm"},a.config.model_client.config.model),r.createElement(Pr,{id:`${e.id}@@@model-zone`,accepts:["model"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop model here")))),r.createElement(Lr,{title:r.createElement("div",null,"Agents"," ",r.createElement("span",{className:"text-xs text-accent"},"(",i,")"))},r.createElement(Wt.h7,{type:"source",position:Yt.yX.Right,id:`${e.id}-agent-output-handle`,className:"my-right-handle"}),r.createElement("div",{className:"space-y-1"},null===(n=a.config.participants)||void 0===n?void 0:n.map(((e,t)=>r.createElement("div",{key:t,className:"relative text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(kr.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,e.config.name)))),r.createElement(Pr,{id:`${e.id}@@@agent-zone`,accepts:["agent"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop agents here")))),r.createElement(Lr,{title:"Terminations"},r.createElement("div",{className:"space-y-1"},a.config.termination_condition&&r.createElement("div",{className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(Or.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,a.config.termination_condition.label||a.config.termination_condition.component_type)),r.createElement(Pr,{id:`${e.id}@@@termination-zone`,accepts:["termination"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop termination here")))))}));Fr.displayName="TeamNode";const Gr=(0,r.memo)((e=>{var t,n,a,o;const i=e.data.component,c=(0,On.O6)(i)&&!!i.config.model_client,l=(0,On.O6)(i)&&(null===(t=i.config.tools)||void 0===t?void 0:t.length)||0;return r.createElement(Hr,Object.assign({},e,{icon:zr.agent,headerContent:r.createElement("div",{className:"flex gap-2 mt-2"},(0,On.O6)(i)&&r.createElement(r.Fragment,null,r.createElement(Br,{connected:c,label:"Model"}),r.createElement(Br,{connected:l>0,label:`${l} Tools`}))),descriptionContent:r.createElement("div",null,r.createElement("div",{className:"break-words truncate mb-1"}," ",i.config.name),r.createElement("div",{className:"break-words"}," ",i.description))}),r.createElement(Wt.h7,{type:"target",position:Yt.yX.Left,id:`${e.id}-agent-input-handle`,className:"my-left-handle z-100"}),((0,On.O6)(i)||(0,On.Zj)(i))&&r.createElement(r.Fragment,null,r.createElement(Lr,{title:"Model"},r.createElement("div",{className:"relative"},(null===(n=i.config)||void 0===n?void 0:n.model_client)&&r.createElement("div",{className:"text-sm"},null===(a=i.config)||void 0===a||null===(o=a.model_client.config)||void 0===o?void 0:o.model),r.createElement(Pr,{id:`${e.id}@@@model-zone`,accepts:["model"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop model here")))),(0,On.O6)(i)&&r.createElement(Lr,{title:"Tools"},r.createElement("div",{className:"space-y-1"},i.config.tools&&l>0&&r.createElement("div",{className:"space-y-1"},i.config.tools.map(((e,t)=>r.createElement("div",{key:t,className:"relative text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(_r.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,e.config.name))))),r.createElement(Pr,{id:`${e.id}@@@tool-zone`,accepts:["tool"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop tools here"))))))}));Gr.displayName="AgentNode";const Vr={team:Fr,agent:Gr},Ur={"model-connection":{stroke:"rgb(220,220,220)"},"tool-connection":{stroke:"rgb(220,220,220)"},"agent-connection":{stroke:"rgb(220,220,220)"},"termination-connection":{stroke:"rgb(220,220,220)"}},Xr=e=>{let{type:t,data:n,deletable:a,...o}=e;const[i]=(0,Yt.Fp)(o),c=t||"model-connection",{style:l,...s}=o,{sourceX:d,sourceY:u,sourcePosition:m,targetPosition:p,sourceHandleId:f,targetHandleId:g,pathOptions:v,selectable:h,...y}=s;return r.createElement(Wt.tE,Object.assign({path:i,style:{...Ur[c],strokeWidth:2}},y))},Wr={"model-connection":Xr,"tool-connection":Xr,"agent-connection":Xr,"termination-connection":Xr};var Yr=n(8603);const Kr=(0,vn.A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);var Jr=n(468),Zr=n(6043);const qr=(0,vn.A)("Undo2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]]),Qr=(0,vn.A)("Redo2",[["path",{d:"m15 14 5-5-5-5",key:"12vg1m"}],["path",{d:"M20 9H9.5A5.5 5.5 0 0 0 4 14.5A5.5 5.5 0 0 0 9.5 20H13",key:"6uklza"}]]);var ea=n(4718);var ta=e=>{let{isJsonMode:t,isFullscreen:n,showGrid:a,canUndo:o,canRedo:i,isDirty:c,onToggleView:l,onUndo:s,onRedo:d,onSave:p,onToggleGrid:f,onToggleFullscreen:g,onAutoLayout:v,onToggleMiniMap:h}=e;const y=[{key:"autoLayout",label:"Auto Layout",icon:r.createElement(Kr,{size:16}),onClick:v},{key:"grid",label:"Show Grid",icon:r.createElement(Jr.A,{size:16}),onClick:f},{key:"minimap",label:"Show Mini Map",icon:r.createElement(Zr.A,{size:16}),onClick:h}];return r.createElement("div",{className:(n?"fixed top-6 right-6":"absolute top-2 right-2")+" bg-secondary hover:bg-secondary rounded shadow-sm min-w-[200px] z-[60]"},r.createElement("div",{className:"p-1 flex items-center gap-1"},!t&&r.createElement(r.Fragment,null,r.createElement(u.A,{title:"Undo"},r.createElement(m.Ay,{type:"text",icon:r.createElement(qr,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:s,disabled:!o})),r.createElement(u.A,{title:"Redo"},r.createElement(m.Ay,{type:"text",icon:r.createElement(Qr,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:d,disabled:!i})),r.createElement(u.A,{title:n?"Exit Fullscreen":"Enter Fullscreen"},r.createElement(m.Ay,{type:"text",icon:n?r.createElement(Tr.A,{size:18}):r.createElement(Ir.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:g}))),r.createElement(u.A,{title:"Save Changes"},r.createElement(m.Ay,{type:"text",icon:r.createElement("div",{className:"relative"},r.createElement(xn.A,{size:18}),c&&r.createElement("div",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:p})),r.createElement(u.A,{title:t?"Switch to Visual":"Switch to JSON"},r.createElement(m.Ay,{type:"text",icon:t?r.createElement(hn,{size:18}):r.createElement(yn,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:l})),!t&&r.createElement(Yr.A,{menu:{items:y},trigger:["click"],overlayStyle:{zIndex:1001},placement:"bottomRight"},r.createElement(m.Ay,{type:"text",icon:r.createElement(ea.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",title:"More Options"}))))},na=n(9872),ra=n(181),aa=n.n(ra),oa=n(1196),ia=n(208),ca=n(9850);var la=e=>{let{isVisble:t,onClose:n,team:a}=e;const{0:o,1:c}=(0,r.useState)(null),{user:l}=(0,r.useContext)(s.v),{0:d,1:u}=(0,r.useState)(!1),{0:m,1:p}=(0,r.useState)(!0),[f,g]=i.Ay.useMessage();(0,r.useEffect)((()=>{t&&null!=a&&a.id&&!o&&(u(!0),(async(e,t)=>{if(null!=l&&l.id)try{const n=`Test Session ${t.substring(0,20)} - ${(new Date).toLocaleString()} `,r=await ca.j.createSession({name:n,team_id:e},l.id);c(r)}catch(n){f.error("Error creating session")}})(a.id,a.component.label||a.component.component_type).finally((()=>{u(!1)})))}),[t,null==a?void 0:a.id]);return r.createElement("div",null,g,r.createElement(gn.A,{title:r.createElement("span",null,"Test Team: ",a.component.label),size:"large",placement:"right",onClose:async()=>{null!=o&&o.id&&m&&await(async e=>{if(null!=l&&l.id)try{await ca.j.deleteSession(e,l.id),c(null)}catch(t){f.error("Error deleting session")}})(o.id),n()},open:t,extra:r.createElement(oa.A,{checked:m,onChange:e=>p(e.target.checked)},"Delete session on close")},d&&r.createElement("p",null,"Creating a test session..."),o&&r.createElement(ia.A,{session:o,showCompareButton:!1})))},sa=n(8697),da=n(418);const ua=e=>{let{validation:t,onClose:n}=e;return r.createElement("div",{style:{zIndex:1e3},className:"fixed inset-0 bg-black/80  flex items-center justify-center transition-opacity duration-300",onClick:n},r.createElement("div",{className:"relative bg-primary w-full h-full md:w-4/5 md:h-4/5 md:rounded-lg p-8 overflow-auto",style:{opacity:.95},onClick:e=>e.stopPropagation()},r.createElement(u.A,{title:"Close"},r.createElement("button",{onClick:n,className:"absolute top-4 right-4 p-2 rounded-full bg-tertiary  hover:bg-secondary text-primary transition-colors"},r.createElement(sa.A,{size:24}))),r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(wn.A,{size:20,className:"text-red-500"}),r.createElement("h3",{className:"text-lg font-medium"},"Validation Issues"),r.createElement("h4",{className:"text-sm text-secondary"},t.errors.length," errors • ",t.warnings.length," ","warnings")),t.errors.length>0&&r.createElement("div",{className:"space-y-2"},r.createElement("h4",{className:"text-sm font-medium"},"Errors"),t.errors.map(((e,t)=>r.createElement("div",{key:t,className:"p-4 bg-tertiary rounded-lg"},r.createElement("div",{className:"flex gap-3"},r.createElement(wn.A,{className:"h-4 w-4 text-red-500 shrink-0 mt-1"}),r.createElement("div",null,r.createElement("div",{className:"text-xs font-medium uppercase text-secondary mb-1"},e.field),r.createElement("div",{className:"text-sm"},e.error),e.suggestion&&r.createElement("div",{className:"text-sm mt-2 text-secondary"},"Suggestion: ",e.suggestion))))))),t.warnings.length>0&&r.createElement("div",{className:"space-y-2 mt-6"},r.createElement("h4",{className:"text-sm font-medium"},"Warnings"),t.warnings.map(((e,t)=>r.createElement("div",{key:t,className:"p-4 bg-tertiary rounded-lg"},r.createElement("div",{className:"flex gap-3"},r.createElement(da.A,{className:"h-4 w-4 text-yellow-500 shrink-0 mt-1"}),r.createElement("div",null,r.createElement("div",{className:"text-xs font-medium uppercase text-secondary mb-1"},e.field),r.createElement("div",{className:"text-sm"},e.error),e.suggestion&&r.createElement("div",{className:"text-sm mt-2 text-secondary"},"Suggestion: ",e.suggestion))))))))))},ma=e=>{let{validation:t}=e;const[n,a]=r.useState(!1);return r.createElement(r.Fragment,null,r.createElement("div",{className:"flex items-center gap-2 py-2   px-3 bg-secondary rounded  text-sm text-secondary hover:text-primary transition-colors group cursor-pointer",onClick:()=>a(!0)},r.createElement(wn.A,{size:14,className:"text-red-500"}),r.createElement("span",{className:"flex-1"},t.errors.length," errors • ",t.warnings.length," ","warnings"),r.createElement(da.A,{size:14,className:"group-hover:text-accent"})),n&&r.createElement(ua,{validation:t,onClose:()=>a(!1)}))};var pa=n(7199);const{Sider:fa,Content:ga}=pn,va=e=>{var t;let{team:n,onChange:a,onDirtyStateChange:o,selectedGallery:c}=e;const[l,s,p]=(0,Wt.ck)([]),[f,g,v]=(0,Wt.fM)([]),{0:h,1:y}=(0,r.useState)(!1),{0:b,1:x}=(0,r.useState)(!1),{0:E,1:w}=(0,r.useState)(!0),{0:N,1:A}=(0,r.useState)(!0),C=(0,r.useRef)(null),[S,k]=i.Ay.useMessage(),{0:_,1:O}=(0,r.useState)(null),{0:I,1:T}=(0,r.useState)(null),{0:R,1:D}=(0,r.useState)(!1),{0:j,1:M}=(0,r.useState)(!1),{undo:$,redo:z,loadFromJson:P,syncToJson:H,addNode:L,layoutNodes:B,resetHistory:F,history:G,updateNode:V,selectedNodeId:U,setSelectedNode:X}=Ln(),W=Ln((e=>e.currentHistoryIndex)),Y=W>0,K=W>0,J=W<G.length-1,Z=(0,r.useCallback)((e=>g((t=>(0,Yt.rN)(e,t)))),[g]),q=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}((Q=Je,ee={activationConstraint:{distance:8}},(0,r.useMemo)((()=>({sensor:Q,options:null!=ee?ee:{}})),[Q,ee])));var Q,ee;r.useEffect((()=>{null==o||o(Y)}),[Y,o]),r.useEffect((()=>{if(Y){const e=e=>{e.preventDefault(),e.returnValue=""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)}}),[Y]),r.useEffect((()=>{if(null!=n&&n.component){const{nodes:e,edges:t}=P(n.component);s(e),g(t)}return ne(),()=>{T(null)}}),[n,s,g]);const te=(0,r.useCallback)(aa()((e=>{try{const t=JSON.parse(e);P(t,!1),Ln.getState().addToHistory()}catch(t){console.error("Invalid JSON:",t)}}),1e3),[P]);(0,r.useEffect)((()=>()=>{te.cancel(),T(null)}),[te]);const ne=(0,r.useCallback)((async()=>{const e=H();if(!e)throw new Error("Unable to generate valid configuration");try{D(!0);const t=await d.gw.validateComponent(e);T(t)}catch(t){console.error("Validation error:",t),S.error("Validation failed")}finally{D(!1)}}),[H]),re=(0,r.useCallback)((async()=>{try{const e=H();if(!e)throw new Error("Unable to generate valid configuration");if(a){const t=n?{...n,component:e,created_at:void 0,updated_at:void 0}:{component:e};await a(t),F()}}catch(e){S.error(e instanceof Error?e.message:"Failed to save team configuration")}}),[H,a,F]),ae=(0,r.useCallback)((()=>{x((e=>!e))}),[]);r.useEffect((()=>{if(!b)return;const e=e=>{"Escape"===e.key&&x(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[b]),r.useEffect((()=>Ln.subscribe((e=>{s(e.nodes),g(e.edges)}))),[s,g]);const oe=(e,t)=>{var n;return(null===(n={model:["team","agent"],tool:["agent"],agent:["team"],team:[],termination:["team"]}[e])||void 0===n?void 0:n.includes(t))||!1},ie=I&&I.is_valid;return r.createElement("div",null,k,r.createElement("div",{className:"flex gap-2 text-xs rounded border-dashed border p-2 mb-2 items-center"},r.createElement("div",{className:"flex-1"},r.createElement(fn.A,{onChange:()=>{y(!h)},className:"mr-2",defaultChecked:!h,checkedChildren:r.createElement("div",{className:" text-xs"},r.createElement(hn,{className:"w-3 h-3 inline-block mt-1 mr-1"})),unCheckedChildren:r.createElement("div",{className:" text-xs"},r.createElement(yn,{className:"w-3 h-3 mt-1 inline-block mr-1"}))}),h?"View JSON":r.createElement(r.Fragment,null,"Visual Builder")," "),r.createElement("div",{className:"flex items-center"},I&&!I.is_valid&&r.createElement("div",{className:"inline-block mr-2"}," ",r.createElement(ma,{validation:I})),r.createElement(u.A,{title:"Download Team"},r.createElement(m.Ay,{type:"text",icon:r.createElement(bn.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:()=>{const e=JSON.stringify(H(),null,2),t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="team-config.json",r.click(),URL.revokeObjectURL(n)}})),r.createElement(u.A,{title:"Save Changes"},r.createElement(m.Ay,{type:"text",icon:r.createElement("div",{className:"relative"},r.createElement(xn.A,{size:18}),Y&&r.createElement("div",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:re})),r.createElement(u.A,{title:r.createElement("div",null,"Validate Team",I&&r.createElement("div",{className:"text-xs text-center my-1"},ie?r.createElement("span",null,r.createElement(En.A,{className:"w-3 h-3 text-green-500 inline-block mr-1"}),"success"):r.createElement("div",{className:""},r.createElement(wn.A,{className:"w-3 h-3 text-red-500 inline-block mr-1"}),"errors")))},r.createElement(m.Ay,{type:"text",loading:R,icon:r.createElement("div",{className:"relative"},r.createElement(Nn,{size:18}),I&&r.createElement("div",{className:` ${ie?"bg-green-500":"bg-red-500"} absolute top-0 right-0 w-2 h-2  rounded-full`})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:ne})),r.createElement(u.A,{title:"Run Team"},r.createElement(m.Ay,{type:"primary",icon:r.createElement(An.A,{size:18}),className:"p-1.5 ml-2 px-2.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:()=>{M(!0)}},"Run")))),r.createElement(It,{sensors:q,onDragEnd:e=>{var t,n;const{active:r,over:a}=e;if(!a||null===(t=r.data)||void 0===t||null===(n=t.current)||void 0===n||!n.current)return;const o=r.data.current.current,i=a.id,[c]=i.split("@@@"),s=l.find((e=>e.id===c));if(!s)return;if(!oe(o.type,s.data.component.component_type))return;const d={x:e.delta.x,y:e.delta.y};L(d,o.config,c),O(null)},onDragOver:e=>{const{active:t,over:n}=e;if(null==n||!n.id||!t.data.current)return;const r=t.data.current.type,a=l.find((e=>e.id===n.id));if(!a)return;const o=oe(r,a.data.component.component_type);a.className=o?"drop-target-valid":"drop-target-invalid"},onDragStart:e=>{const{active:t}=e;t.data.current&&O(t.data.current)}},r.createElement(pn,{className:" relative bg-primary  h-[calc(100vh-239px)] rounded"},!h&&c&&r.createElement(jr,{defaultGallery:c}),r.createElement(pn,{className:"bg-primary rounded"},r.createElement(ga,{className:"relative rounded bg-tertiary  "},r.createElement("div",{className:"w-full h-full transition-all duration-200 "+(b?"fixed inset-4 z-50 shadow bg-tertiary  backdrop-blur-sm":"")},h?r.createElement(na.T,{value:JSON.stringify(H(),null,2),onChange:te,editorRef:C,language:"json",minimap:!1}):r.createElement(Wt.Gc,{nodes:l,edges:f,onNodesChange:p,onEdgesChange:v,onConnect:Z,nodeTypes:Vr,edgeTypes:Wr,onDrop:e=>e.preventDefault(),onDragOver:e=>e.preventDefault(),className:"rounded",fitView:!0,fitViewOptions:{padding:10}},E&&r.createElement(Wt.VS,null),N&&r.createElement(Wt.of,null))),b&&r.createElement("div",{className:"fixed inset-0 -z-10 bg-background bg-opacity-80 backdrop-blur-sm",onClick:ae}),r.createElement(ta,{isJsonMode:h,isFullscreen:b,showGrid:E,onToggleMiniMap:()=>A(!N),canUndo:K,canRedo:J,isDirty:Y,onToggleView:()=>y(!h),onUndo:$,onRedo:z,onSave:re,onToggleGrid:()=>w(!E),onToggleFullscreen:ae,onAutoLayout:B}))),U&&r.createElement(gn.A,{title:"Edit Component",placement:"right",size:"large",onClose:()=>X(null),open:!!U,className:"component-editor-drawer"},(null===(t=l.find((e=>e.id===U)))||void 0===t?void 0:t.data.component)&&r.createElement(pa.A,{component:l.find((e=>e.id===U)).data.component,onChange:e=>{U&&(V(U,{component:e}),re())},onClose:()=>X(null),navigationDepth:!0}))),r.createElement(Xt,{dropAnimation:{duration:250,easing:"cubic-bezier(0.18, 0.67, 0.6, 1.22)"}},_?r.createElement("div",{className:"p-2 text-primary h-full     rounded    "},r.createElement("div",{className:"flex items-center gap-2"},_.icon,r.createElement("span",{className:"text-sm"},_.label))):null)),j&&r.createElement(la,{isVisble:j,team:n,onClose:()=>{M(!1)}}))};var ha=()=>{var e;const{0:t,1:n}=(0,r.useState)(!1),{0:a,1:u}=(0,r.useState)([]),{0:m,1:p}=(0,r.useState)(null),{0:f,1:g}=(0,r.useState)((()=>{if("undefined"!=typeof window){const e=localStorage.getItem("teamSidebar");return null===e||JSON.parse(e)}})),{0:v,1:h}=(0,r.useState)(null),{user:y}=(0,r.useContext)(s.v),[b,x]=i.Ay.useMessage(),{0:E,1:w}=(0,r.useState)(!1);(0,r.useEffect)((()=>{"undefined"!=typeof window&&localStorage.setItem("teamSidebar",JSON.stringify(f))}),[f]);const N=(0,r.useCallback)((async()=>{if(null!=y&&y.id)try{n(!0);const e=await d.CG.listTeams(y.id);u(e),!m&&e.length>0&&p(e[0])}catch(e){console.error("Error fetching teams:",e)}finally{n(!1)}}),[null==y?void 0:y.id,m]);(0,r.useEffect)((()=>{N()}),[N]),(0,r.useEffect)((()=>{const e=new URLSearchParams(window.location.search).get("teamId");e&&!m&&A({id:parseInt(e)})}),[]);const A=async e=>{null!=y&&y.id&&e.id&&(E?c.A.confirm({title:"Unsaved Changes",content:"You have unsaved changes. Do you want to discard them?",okText:"Discard",cancelText:"Go Back",onOk:()=>{C(e.id)}}):await C(e.id))},C=async e=>{if(e&&null!=y&&y.id){n(!0);try{const t=await d.CG.getTeam(e,y.id);p(t),window.history.pushState({},"",`?teamId=${e}`)}catch(t){console.error("Error loading team:",t),b.error("Failed to load team")}finally{n(!1)}}},S=async e=>{if(null!=y&&y.id)try{const t={...e,created_at:void 0,updated_at:void 0},n=await d.CG.createTeam(t,y.id);b.success(`Team ${e.id?"updated":"created"} successfully`),e.id?(u(a.map((e=>e.id===n.id?n:e))),(null==m?void 0:m.id)===n.id&&p(n)):(u([n].concat((0,o.A)(a))),p(n))}catch(t){throw t}};return r.createElement("div",{className:"relative flex h-full w-full"},x,r.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(f?"w-64":"w-12")},r.createElement(_,{isOpen:f,teams:a,currentTeam:m,onToggle:()=>g(!f),onSelectTeam:A,onCreateTeam:e=>{p(e),S(e)},onEditTeam:p,onDeleteTeam:async e=>{if(null!=y&&y.id)try{await d.CG.deleteTeam(e,y.id),u(a.filter((t=>t.id!==e))),(null==m?void 0:m.id)===e&&p(null),b.success("Team deleted")}catch(t){console.error("Error deleting team:",t),b.error("Error deleting team")}},isLoading:t,setSelectedGallery:h,selectedGallery:v})),r.createElement("div",{className:"flex-1 transition-all -mr-6 duration-200 "+(f?"ml-64":"ml-12")},r.createElement("div",{className:"p-4 pt-2"},r.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},r.createElement("span",{className:"text-primary font-medium"},"Teams"),m&&r.createElement(r.Fragment,null,r.createElement(l.A,{className:"w-4 h-4 text-secondary"}),r.createElement("span",{className:"text-secondary"},null===(e=m.component)||void 0===e?void 0:e.label,m.id?"":r.createElement("span",{className:"text-xs text-orange-500"}," (New)")))),m?r.createElement(va,{team:m,onChange:S,onDirtyStateChange:w,selectedGallery:v}):r.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"Select a team from the sidebar or create a new one"))))};var ya=e=>{let{data:t}=e;return r.createElement(a.A,{meta:t.site.siteMetadata,title:"Home",link:"/build"},r.createElement("main",{style:{height:"100%"},className:" h-full "},r.createElement(ha,null)))}},1986:function(e,t,n){var r=n(1873),a=n(7828),o=n(5288),i=n(5911),c=n(317),l=n(4247),s=r?r.prototype:void 0,d=s?s.valueOf:void 0;e.exports=function(e,t,n,r,s,u,m){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!u(new a(e),new a(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=c;case"[object Set]":var f=1&r;if(p||(p=l),e.size!=t.size&&!f)return!1;var g=m.get(e);if(g)return g==t;r|=2,m.set(e,t);var v=i(p(e),p(t),r,s,u,m);return m.delete(e),v;case"[object Symbol]":if(d)return d.call(e)==d.call(t)}return!1}},2032:function(e,t,n){var r=n(1042);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},2199:function(e,t,n){var r=n(4528),a=n(6449);e.exports=function(e,t,n){var o=t(e);return a(e)?o:r(o,n(e))}},2404:function(e,t,n){var r=n(270);e.exports=function(e,t){return r(e,t)}},2428:function(e,t,n){var r=n(7534),a=n(346),o=Object.prototype,i=o.hasOwnProperty,c=o.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return a(e)&&i.call(e,"callee")&&!c.call(e,"callee")};e.exports=l},2552:function(e,t,n){var r=n(1873),a=n(659),o=n(9350),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?a(e):o(e)}},2651:function(e,t,n){var r=n(4218);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},2749:function(e,t,n){var r=n(1042),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:a.call(t,e)}},2804:function(e,t,n){var r=n(6110)(n(9325),"Promise");e.exports=r},2949:function(e,t,n){var r=n(2651);e.exports=function(e,t){var n=r(this,e),a=n.size;return n.set(e,t),this.size+=n.size==a?0:1,this}},3040:function(e,t,n){var r=n(1549),a=n(79),o=n(8223);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(o||a),string:new r}}},3345:function(e){e.exports=function(){return[]}},3605:function(e){e.exports=function(e){return this.__data__.get(e)}},3650:function(e,t,n){var r=n(4335)(Object.keys,Object);e.exports=r},3656:function(e,t,n){e=n.nmd(e);var r=n(9325),a=n(9935),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,c=i&&i.exports===o?r.Buffer:void 0,l=(c?c.isBuffer:void 0)||a;e.exports=l},3661:function(e,t,n){var r=n(3040),a=n(7670),o=n(289),i=n(4509),c=n(2949);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=a,l.prototype.get=o,l.prototype.has=i,l.prototype.set=c,e.exports=l},3702:function(e){e.exports=function(){this.__data__=[],this.size=0}},3805:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3862:function(e){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},4218:function(e){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},4247:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}},4248:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},4335:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},4509:function(e,t,n){var r=n(2651);e.exports=function(e){return r(this,e).has(e)}},4528:function(e){e.exports=function(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e}},4664:function(e,t,n){var r=n(9770),a=n(3345),o=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,c=i?function(e){return null==e?[]:(e=Object(e),r(i(e),(function(t){return o.call(e,t)})))}:a;e.exports=c},4739:function(e,t,n){var r=n(6025);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},4840:function(e,t,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},4894:function(e,t,n){var r=n(1882),a=n(294);e.exports=function(e){return null!=e&&a(e.length)&&!r(e)}},4901:function(e,t,n){var r=n(2552),a=n(294),o=n(346),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return o(e)&&a(e.length)&&!!i[r(e)]}},5083:function(e,t,n){var r=n(1882),a=n(7296),o=n(3805),i=n(7473),c=/^\[object .+?Constructor\]$/,l=Function.prototype,s=Object.prototype,d=l.toString,u=s.hasOwnProperty,m=RegExp("^"+d.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!o(e)||a(e))&&(r(e)?m:c).test(i(e))}},5288:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},5404:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5481:function(e,t,n){var r=n(9325)["__core-js_shared__"];e.exports=r},5527:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},5580:function(e,t,n){var r=n(6110)(n(9325),"DataView");e.exports=r},5749:function(e,t,n){var r=n(1042);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},5861:function(e,t,n){var r=n(5580),a=n(8223),o=n(2804),i=n(6545),c=n(8303),l=n(2552),s=n(7473),d="[object Map]",u="[object Promise]",m="[object Set]",p="[object WeakMap]",f="[object DataView]",g=s(r),v=s(a),h=s(o),y=s(i),b=s(c),x=l;(r&&x(new r(new ArrayBuffer(1)))!=f||a&&x(new a)!=d||o&&x(o.resolve())!=u||i&&x(new i)!=m||c&&x(new c)!=p)&&(x=function(e){var t=l(e),n="[object Object]"==t?e.constructor:void 0,r=n?s(n):"";if(r)switch(r){case g:return f;case v:return d;case h:return u;case y:return m;case b:return p}return t}),e.exports=x},5911:function(e,t,n){var r=n(8859),a=n(4248),o=n(9219);e.exports=function(e,t,n,i,c,l){var s=1&n,d=e.length,u=t.length;if(d!=u&&!(s&&u>d))return!1;var m=l.get(e),p=l.get(t);if(m&&p)return m==t&&p==e;var f=-1,g=!0,v=2&n?new r:void 0;for(l.set(e,t),l.set(t,e);++f<d;){var h=e[f],y=t[f];if(i)var b=s?i(y,h,f,t,e,l):i(h,y,f,e,t,l);if(void 0!==b){if(b)continue;g=!1;break}if(v){if(!a(t,(function(e,t){if(!o(v,t)&&(h===e||c(h,e,n,i,l)))return v.push(t)}))){g=!1;break}}else if(h!==y&&!c(h,y,n,i,l)){g=!1;break}}return l.delete(e),l.delete(t),g}},5950:function(e,t,n){var r=n(695),a=n(8984),o=n(4894);e.exports=function(e){return o(e)?r(e):a(e)}},6009:function(e,t,n){e=n.nmd(e);var r=n(4840),a=t&&!t.nodeType&&t,o=a&&e&&!e.nodeType&&e,i=o&&o.exports===a&&r.process,c=function(){try{var e=o&&o.require&&o.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(t){}}();e.exports=c},6025:function(e,t,n){var r=n(5288);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},6110:function(e,t,n){var r=n(5083),a=n(392);e.exports=function(e,t){var n=a(e,t);return r(n)?n:void 0}},6274:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},6449:function(e){var t=Array.isArray;e.exports=t},6545:function(e,t,n){var r=n(6110)(n(9325),"Set");e.exports=r},6721:function(e,t,n){var r=n(1042),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return a.call(t,e)?t[e]:void 0}},6816:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},7068:function(e,t,n){var r=n(7217),a=n(5911),o=n(1986),i=n(689),c=n(5861),l=n(6449),s=n(3656),d=n(7167),u="[object Arguments]",m="[object Array]",p="[object Object]",f=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,g,v,h){var y=l(e),b=l(t),x=y?m:c(e),E=b?m:c(t),w=(x=x==u?p:x)==p,N=(E=E==u?p:E)==p,A=x==E;if(A&&s(e)){if(!s(t))return!1;y=!0,w=!1}if(A&&!w)return h||(h=new r),y||d(e)?a(e,t,n,g,v,h):o(e,t,x,n,g,v,h);if(!(1&n)){var C=w&&f.call(e,"__wrapped__"),S=N&&f.call(t,"__wrapped__");if(C||S){var k=C?e.value():e,_=S?t.value():t;return h||(h=new r),v(k,_,n,g,h)}}return!!A&&(h||(h=new r),i(e,t,n,g,v,h))}},7167:function(e,t,n){var r=n(4901),a=n(7301),o=n(6009),i=o&&o.isTypedArray,c=i?a(i):r;e.exports=c},7217:function(e,t,n){var r=n(79),a=n(1420),o=n(938),i=n(3605),c=n(9817),l=n(945);function s(e){var t=this.__data__=new r(e);this.size=t.size}s.prototype.clear=a,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=c,s.prototype.set=l,e.exports=s},7296:function(e,t,n){var r,a=n(5481),o=(r=/[^.]+$/.exec(a&&a.keys&&a.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!o&&o in e}},7301:function(e){e.exports=function(e){return function(t){return e(t)}}},7473:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(n){}try{return e+""}catch(n){}}return""}},7534:function(e,t,n){var r=n(2552),a=n(346);e.exports=function(e){return a(e)&&"[object Arguments]"==r(e)}},7670:function(e,t,n){var r=n(2651);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},7828:function(e,t,n){var r=n(9325).Uint8Array;e.exports=r},8096:function(e){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},8223:function(e,t,n){var r=n(6110)(n(9325),"Map");e.exports=r},8303:function(e,t,n){var r=n(6110)(n(9325),"WeakMap");e.exports=r},8309:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},8655:function(e,t,n){var r=n(6025);e.exports=function(e){return r(this.__data__,e)>-1}},8859:function(e,t,n){var r=n(3661),a=n(1380),o=n(1459);function i(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}i.prototype.add=i.prototype.push=a,i.prototype.has=o,e.exports=i},8984:function(e,t,n){var r=n(5527),a=n(3650),o=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return a(e);var t=[];for(var n in Object(e))o.call(e,n)&&"constructor"!=n&&t.push(n);return t}},9219:function(e){e.exports=function(e,t){return e.has(t)}},9325:function(e,t,n){var r=n(4840),a="object"==typeof self&&self&&self.Object===Object&&self,o=r||a||Function("return this")();e.exports=o},9350:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},9770:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,a=0,o=[];++n<r;){var i=e[n];t(i,n,e)&&(o[a++]=i)}return o}},9817:function(e){e.exports=function(e){return this.__data__.has(e)}},9935:function(e){e.exports=function(){return!1}}}]);
//# sourceMappingURL=component---src-pages-build-tsx-34d8f410f6df7e60cea6.js.map