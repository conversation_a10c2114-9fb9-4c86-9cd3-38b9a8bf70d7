"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[626],{9679:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var i=a(6540),n=a(226),r=a(4810),l=a(3041),c=a(9036),s=a(4716),o=a(2702),d=a(2941),m=a(8168),u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"}}]},name:"github",theme:"outlined"},g=a(7064),f=function(e,t){return i.createElement(g.A,(0,m.A)({},e,{ref:t,icon:u}))};var h=i.forwardRef(f),p=a(5312),v=a(6647);const{Title:w,Text:E}=l.A;var b=e=>{let{data:t}=e;const{isAuthenticated:a,isLoading:l,login:m,authType:u}=(0,n.A)(),{0:g,1:f}=(0,i.useState)(!1);(0,i.useEffect)((()=>{a&&!l&&(0,r.navigate)("/")}),[a,l]),(0,i.useEffect)((()=>{"none"!==u||l||(0,r.navigate)("/")}),[u,l]);return l?i.createElement(p.A,{meta:t.site.siteMetadata,title:"Login",link:"/login"},i.createElement("div",{className:"flex items-center justify-center h-screen"},i.createElement(s.A,{size:"large",tip:"Loading..."}))):i.createElement(p.A,{meta:t.site.siteMetadata,title:"Login",link:"/login",showHeader:!0,restricted:!1},i.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-164px)]"},i.createElement("div",{className:"w-full rounded bg-secondary max-w-md p-8 sxhadow-sm"},i.createElement("div",{className:"text-center mb-8"},i.createElement("div",{className:"mb-3"},i.createElement(v.A,{icon:"app",size:12})),i.createElement("div",{className:"text-2xl mb-1 font-semibold text-primary"},"Sign in to ",t.site.siteMetadata.title),i.createElement("div",{className:"text-secondary text-sm"}," ","Build and prototype multi-agent applications")),i.createElement(o.A,{direction:"vertical",className:"w-full"},i.createElement(d.Ay,{type:"primary",size:"large",icon:i.createElement(h,null),onClick:async()=>{try{f(!0);const e=await m();if(!e)return c.Ay.error("Failed to get login URL"),void f(!1);const t=600,a=700,i=window.screen.width/2-t/2,n=window.screen.height/2-a/2,r=window.open(e,"github-auth",`width=${t},height=${a},top=${n},left=${i}`);if(!r||r.closed||void 0===r.closed)return c.Ay.error("Popup was blocked by browser. Please allow popups for this site."),void f(!1);const l=setInterval((()=>{r.closed&&(clearInterval(l),f(!1))}),1e3)}catch(e){console.error("Login error:",e),c.Ay.error("Failed to initiate login"),f(!1)}},loading:g,block:!0},g?"Connecting to GitHub...":"Sign in with GitHub")))))}}}]);
//# sourceMappingURL=component---src-pages-login-tsx-e7b19ce014e96a866900.js.map