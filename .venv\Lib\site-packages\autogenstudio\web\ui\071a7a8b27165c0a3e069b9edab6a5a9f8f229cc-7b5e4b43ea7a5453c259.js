"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[166],{9872:function(e,t,r){r.d(t,{T:function(){return ue}});var n=r(6540);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e){return function t(){for(var r=this,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return t.apply(r,[].concat(o,n))}}}function g(e){return{}.toString.call(e).includes("Object")}function p(e){return"function"==typeof e}var h=d((function(e,t){throw new Error(e[t]||e.default)}))({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),m={changes:function(e,t){return g(t)||h("changeType"),Object.keys(t).some((function(t){return r=e,n=t,!Object.prototype.hasOwnProperty.call(r,n);var r,n}))&&h("changeField"),t},selector:function(e){p(e)||h("selectorType")},handler:function(e){p(e)||g(e)||h("handlerType"),g(e)&&Object.values(e).some((function(e){return!p(e)}))&&h("handlersType")},initial:function(e){var t;e||h("initialIsRequired"),g(e)||h("initialType"),t=e,Object.keys(t).length||h("initialContent")}};function y(e,t){return p(t)?t(e.current):t}function v(e,t){return e.current=f(f({},e.current),t),t}function b(e,t,r){return p(t)?t(e.current):Object.keys(r).forEach((function(r){var n;return null===(n=t[r])||void 0===n?void 0:n.call(t,e.current[r])})),r}var w={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};m.initial(e),m.handler(t);var r={current:e},n=d(b)(r,t),o=d(v)(r),i=d(m.changes)(e),c=d(y)(r);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return m.selector(e),e(r.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}(n,o,i,c)(e)}]}},O=w,j={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};var M=function(e){return function t(){for(var r=this,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return t.apply(r,[].concat(o,n))}}};var E=function(e){return{}.toString.call(e).includes("Object")};var P={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},R=M((function(e,t){throw new Error(e[t]||e.default)}))(P),k={config:function(e){return e||R("configIsRequired"),E(e)||R("configType"),e.urls?(console.warn(P.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},S=k,C=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}};var T=function e(t,r){return Object.keys(r).forEach((function(n){r[n]instanceof Object&&t[n]&&Object.assign(r[n],e(t[n],r[n]))})),c(c({},t),r)},I={type:"cancelation",msg:"operation is manually canceled"};var x,V,A=function(e){var t=!1,r=new Promise((function(r,n){e.then((function(e){return t?n(I):r(e)})),e.catch(n)}));return r.cancel=function(){return t=!0},r},L=O.create({config:j,isInitialized:!1,resolve:null,reject:null,monaco:null}),D=(V=2,function(e){if(Array.isArray(e))return e}(x=L)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var c,u=e[Symbol.iterator]();!(n=(c=u.next()).done)&&(r.push(c.value),!t||r.length!==t);n=!0);}catch(a){o=!0,i=a}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(x,V)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}}(x,V)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),N=D[0],q=D[1];function z(e){return document.body.appendChild(e)}function F(e){var t,r,n=N((function(e){return{config:e.config,reject:e.reject}})),o=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return o.onload=function(){return e()},o.onerror=n.reject,o}function _(){var e=N((function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}})),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],(function(t){U(t),e.resolve(t)}),(function(t){e.reject(t)}))}function U(e){N().monaco||q({monaco:e})}var W=new Promise((function(e,t){return q({resolve:e,reject:t})})),B={config:function(e){var t=S.config(e),r=t.monaco,n=u(t,["monaco"]);q((function(e){return{config:T(e.config,n),monaco:r}}))},init:function(){var e=N((function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}}));if(!e.isInitialized){if(q({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),A(W);if(window.monaco&&window.monaco.editor)return U(window.monaco),e.resolve(window.monaco),A(W);C(z,F)(_)}return A(W)},__getMonacoInstance:function(){return N((function(e){return e.monaco}))}},$=B,Y={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},G={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var H=function({children:e}){return n.createElement("div",{style:G.container},e)};var J=function({width:e,height:t,isEditorReady:r,loading:o,_ref:i,className:c,wrapperProps:u}){return n.createElement("section",{style:{...Y.wrapper,width:e,height:t},...u},!r&&n.createElement(H,null,o),n.createElement("div",{ref:i,style:{...Y.fullWidth,...!r&&Y.hide},className:c}))},K=(0,n.memo)(J);var Q=function(e){(0,n.useEffect)(e,[])};var X=function(e,t,r=!0){let o=(0,n.useRef)(!0);(0,n.useEffect)(o.current||!r?()=>{o.current=!1}:e,t)};function Z(){}function ee(e,t,r,n){return function(e,t){return e.editor.getModel(te(e,t))}(e,n)||function(e,t,r,n){return e.editor.createModel(t,r,n?te(e,n):void 0)}(e,t,r,n)}function te(e,t){return e.Uri.parse(t)}var re=function({original:e,modified:t,language:r,originalLanguage:o,modifiedLanguage:i,originalModelPath:c,modifiedModelPath:u,keepCurrentOriginalModel:a=!1,keepCurrentModifiedModel:l=!1,theme:s="light",loading:f="Loading...",options:d={},height:g="100%",width:p="100%",className:h,wrapperProps:m={},beforeMount:y=Z,onMount:v=Z}){let[b,w]=(0,n.useState)(!1),[O,j]=(0,n.useState)(!0),M=(0,n.useRef)(null),E=(0,n.useRef)(null),P=(0,n.useRef)(null),R=(0,n.useRef)(v),k=(0,n.useRef)(y),S=(0,n.useRef)(!1);Q((()=>{let e=$.init();return e.then((e=>(E.current=e)&&j(!1))).catch((e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e))),()=>M.current?function(){let e=M.current?.getModel();a||e?.original?.dispose(),l||e?.modified?.dispose(),M.current?.dispose()}():e.cancel()})),X((()=>{if(M.current&&E.current){let t=M.current.getOriginalEditor(),n=ee(E.current,e||"",o||r||"text",c||"");n!==t.getModel()&&t.setModel(n)}}),[c],b),X((()=>{if(M.current&&E.current){let e=M.current.getModifiedEditor(),n=ee(E.current,t||"",i||r||"text",u||"");n!==e.getModel()&&e.setModel(n)}}),[u],b),X((()=>{let e=M.current.getModifiedEditor();e.getOption(E.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())}),[t],b),X((()=>{M.current?.getModel()?.original.setValue(e||"")}),[e],b),X((()=>{let{original:e,modified:t}=M.current.getModel();E.current.editor.setModelLanguage(e,o||r||"text"),E.current.editor.setModelLanguage(t,i||r||"text")}),[r,o,i],b),X((()=>{E.current?.editor.setTheme(s)}),[s],b),X((()=>{M.current?.updateOptions(d)}),[d],b);let C=(0,n.useCallback)((()=>{if(!E.current)return;k.current(E.current);let n=ee(E.current,e||"",o||r||"text",c||""),a=ee(E.current,t||"",i||r||"text",u||"");M.current?.setModel({original:n,modified:a})}),[r,t,i,e,o,c,u]),T=(0,n.useCallback)((()=>{!S.current&&P.current&&(M.current=E.current.editor.createDiffEditor(P.current,{automaticLayout:!0,...d}),C(),E.current?.editor.setTheme(s),w(!0),S.current=!0)}),[d,s,C]);return(0,n.useEffect)((()=>{b&&R.current(M.current,E.current)}),[b]),(0,n.useEffect)((()=>{!O&&!b&&T()}),[O,b,T]),n.createElement(K,{width:p,height:g,isEditorReady:b,loading:f,_ref:P,className:h,wrapperProps:m})};(0,n.memo)(re);var ne=function(e){let t=(0,n.useRef)();return(0,n.useEffect)((()=>{t.current=e}),[e]),t.current},oe=new Map;var ie=function({defaultValue:e,defaultLanguage:t,defaultPath:r,value:o,language:i,path:c,theme:u="light",line:a,loading:l="Loading...",options:s={},overrideServices:f={},saveViewState:d=!0,keepCurrentModel:g=!1,width:p="100%",height:h="100%",className:m,wrapperProps:y={},beforeMount:v=Z,onMount:b=Z,onChange:w,onValidate:O=Z}){let[j,M]=(0,n.useState)(!1),[E,P]=(0,n.useState)(!0),R=(0,n.useRef)(null),k=(0,n.useRef)(null),S=(0,n.useRef)(null),C=(0,n.useRef)(b),T=(0,n.useRef)(v),I=(0,n.useRef)(),x=(0,n.useRef)(o),V=ne(c),A=(0,n.useRef)(!1),L=(0,n.useRef)(!1);Q((()=>{let e=$.init();return e.then((e=>(R.current=e)&&P(!1))).catch((e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e))),()=>k.current?(I.current?.dispose(),g?d&&oe.set(c,k.current.saveViewState()):k.current.getModel()?.dispose(),void k.current.dispose()):e.cancel()})),X((()=>{let n=ee(R.current,e||o||"",t||i||"",c||r||"");n!==k.current?.getModel()&&(d&&oe.set(V,k.current?.saveViewState()),k.current?.setModel(n),d&&k.current?.restoreViewState(oe.get(c)))}),[c],j),X((()=>{k.current?.updateOptions(s)}),[s],j),X((()=>{!k.current||void 0===o||(k.current.getOption(R.current.editor.EditorOption.readOnly)?k.current.setValue(o):o!==k.current.getValue()&&(L.current=!0,k.current.executeEdits("",[{range:k.current.getModel().getFullModelRange(),text:o,forceMoveMarkers:!0}]),k.current.pushUndoStop(),L.current=!1))}),[o],j),X((()=>{let e=k.current?.getModel();e&&i&&R.current?.editor.setModelLanguage(e,i)}),[i],j),X((()=>{void 0!==a&&k.current?.revealLine(a)}),[a],j),X((()=>{R.current?.editor.setTheme(u)}),[u],j);let D=(0,n.useCallback)((()=>{if(S.current&&R.current&&!A.current){T.current(R.current);let n=c||r,l=ee(R.current,o||e||"",t||i||"",n||"");k.current=R.current?.editor.create(S.current,{model:l,automaticLayout:!0,...s},f),d&&k.current.restoreViewState(oe.get(n)),R.current.editor.setTheme(u),void 0!==a&&k.current.revealLine(a),M(!0),A.current=!0}}),[e,t,r,o,i,c,s,f,d,u,a]);return(0,n.useEffect)((()=>{j&&C.current(k.current,R.current)}),[j]),(0,n.useEffect)((()=>{!E&&!j&&D()}),[E,j,D]),x.current=o,(0,n.useEffect)((()=>{j&&w&&(I.current?.dispose(),I.current=k.current?.onDidChangeModelContent((e=>{L.current||w(k.current.getValue(),e)})))}),[j,w]),(0,n.useEffect)((()=>{if(j){let e=R.current.editor.onDidChangeMarkers((e=>{let t=k.current.getModel()?.uri;if(t&&e.find((e=>e.path===t.path))){let e=R.current.editor.getModelMarkers({resource:t});O?.(e)}}));return()=>{e?.dispose()}}return()=>{}}),[j,O]),n.createElement(K,{width:p,height:h,isEditorReady:j,loading:l,_ref:S,className:m,wrapperProps:y})},ce=(0,n.memo)(ie);const ue=e=>{let{value:t,editorRef:r,language:o,onChange:i,minimap:c=!0,className:u}=e;const{0:a,1:l}=(0,n.useState)(!1);return n.createElement("div",{id:"monaco-editor",className:`h-full rounded ${u}`},n.createElement(ce,{height:"100%",className:"h-full rounded",defaultLanguage:o,defaultValue:t,value:t,onChange:e=>{i&&e&&i(e)},onMount:(e,t)=>{r.current=e,l(!0)},theme:"vs-dark",options:{wordWrap:"on",wrappingIndent:"indent",wrappingStrategy:"advanced",minimap:{enabled:c}}}))}}}]);
//# sourceMappingURL=071a7a8b27165c0a3e069b9edab6a5a9f8f229cc-7b5e4b43ea7a5453c259.js.map