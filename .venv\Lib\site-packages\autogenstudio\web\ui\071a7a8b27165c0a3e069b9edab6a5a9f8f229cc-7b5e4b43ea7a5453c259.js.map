{"version": 3, "file": "071a7a8b27165c0a3e069b9edab6a5a9f8f229cc-7b5e4b43ea7a5453c259.js", "mappings": "oKAAA,SAASA,EAAgBC,EAAKC,EAAKC,GAYjC,OAXID,KAAOD,EACTG,OAAOC,eAAeJ,EAAKC,EAAK,CAC9BC,MAAOA,EACPG,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZP,EAAIC,GAAOC,EAGNF,CACT,CAEA,SAASQ,EAAQC,EAAQC,GACvB,IAAIC,EAAOR,OAAOQ,KAAKF,GAEvB,GAAIN,OAAOS,sBAAuB,CAChC,IAAIC,EAAUV,OAAOS,sBAAsBH,GACvCC,IAAgBG,EAAUA,EAAQC,QAAO,SAAUC,GACrD,OAAOZ,OAAOa,yBAAyBP,EAAQM,GAAKV,UACtD,KACAM,EAAKM,KAAKC,MAAMP,EAAME,EACxB,CAEA,OAAOF,CACT,CAEA,SAASQ,EAAeC,GACtB,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAAyB,MAAhBF,UAAUD,GAAaC,UAAUD,GAAK,CAAC,EAEhDA,EAAI,EACNb,EAAQL,OAAOqB,IAAS,GAAMC,SAAQ,SAAUxB,GAC9CF,EAAgBqB,EAAQnB,EAAKuB,EAAOvB,GACtC,IACSE,OAAOuB,0BAChBvB,OAAOwB,iBAAiBP,EAAQjB,OAAOuB,0BAA0BF,IAEjEhB,EAAQL,OAAOqB,IAASC,SAAQ,SAAUxB,GACxCE,OAAOC,eAAegB,EAAQnB,EAAKE,OAAOa,yBAAyBQ,EAAQvB,GAC7E,GAEJ,CAEA,OAAOmB,CACT,CAiBA,SAASQ,EAAyBJ,EAAQK,GACxC,GAAc,MAAVL,EAAgB,MAAO,CAAC,EAE5B,IAEIvB,EAAKoB,EAFLD,EAlBN,SAAuCI,EAAQK,GAC7C,GAAc,MAAVL,EAAgB,MAAO,CAAC,EAC5B,IAEIvB,EAAKoB,EAFLD,EAAS,CAAC,EACVU,EAAa3B,OAAOQ,KAAKa,GAG7B,IAAKH,EAAI,EAAGA,EAAIS,EAAWP,OAAQF,IACjCpB,EAAM6B,EAAWT,GACbQ,EAASE,QAAQ9B,IAAQ,IAC7BmB,EAAOnB,GAAOuB,EAAOvB,IAGvB,OAAOmB,CACT,CAKeY,CAA8BR,EAAQK,GAInD,GAAI1B,OAAOS,sBAAuB,CAChC,IAAIqB,EAAmB9B,OAAOS,sBAAsBY,GAEpD,IAAKH,EAAI,EAAGA,EAAIY,EAAiBV,OAAQF,IACvCpB,EAAMgC,EAAiBZ,GACnBQ,EAASE,QAAQ9B,IAAQ,GACxBE,OAAO+B,UAAUC,qBAAqBC,KAAKZ,EAAQvB,KACxDmB,EAAOnB,GAAOuB,EAAOvB,GAEzB,CAEA,OAAOmB,CACT,CA8CA,SAASiB,EAAkBC,EAAKC,IACnB,MAAPA,GAAeA,EAAMD,EAAIf,UAAQgB,EAAMD,EAAIf,QAE/C,IAAK,IAAIF,EAAI,EAAGmB,EAAO,IAAIC,MAAMF,GAAMlB,EAAIkB,EAAKlB,IAAKmB,EAAKnB,GAAKiB,EAAIjB,GAEnE,OAAOmB,CACT,CCvIA,SAAS,EAAgBxC,EAAKC,EAAKC,GAYjC,OAXID,KAAOD,EACTG,OAAOC,eAAeJ,EAAKC,EAAK,CAC9BC,MAAOA,EACPG,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZP,EAAIC,GAAOC,EAGNF,CACT,CAEA,SAAS,EAAQS,EAAQC,GACvB,IAAIC,EAAOR,OAAOQ,KAAKF,GAEvB,GAAIN,OAAOS,sBAAuB,CAChC,IAAIC,EAAUV,OAAOS,sBAAsBH,GACvCC,IAAgBG,EAAUA,EAAQC,QAAO,SAAUC,GACrD,OAAOZ,OAAOa,yBAAyBP,EAAQM,GAAKV,UACtD,KACAM,EAAKM,KAAKC,MAAMP,EAAME,EACxB,CAEA,OAAOF,CACT,CAEA,SAAS,EAAeS,GACtB,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAAyB,MAAhBF,UAAUD,GAAaC,UAAUD,GAAK,CAAC,EAEhDA,EAAI,EACN,EAAQlB,OAAOqB,IAAS,GAAMC,SAAQ,SAAUxB,GAC9C,EAAgBmB,EAAQnB,EAAKuB,EAAOvB,GACtC,IACSE,OAAOuB,0BAChBvB,OAAOwB,iBAAiBP,EAAQjB,OAAOuB,0BAA0BF,IAEjE,EAAQrB,OAAOqB,IAASC,SAAQ,SAAUxB,GACxCE,OAAOC,eAAegB,EAAQnB,EAAKE,OAAOa,yBAAyBQ,EAAQvB,GAC7E,GAEJ,CAEA,OAAOmB,CACT,CAcA,SAASsB,EAAMC,GACb,OAAO,SAASC,IAGd,IAFA,IAAIC,EAAQC,KAEHC,EAAQzB,UAAUC,OAAQyB,EAAO,IAAIP,MAAMM,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACpFD,EAAKC,GAAS3B,UAAU2B,GAG1B,OAAOD,EAAKzB,QAAUoB,EAAGpB,OAASoB,EAAGzB,MAAM4B,KAAME,GAAQ,WACvD,IAAK,IAAIE,EAAQ5B,UAAUC,OAAQ4B,EAAW,IAAIV,MAAMS,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFD,EAASC,GAAS9B,UAAU8B,GAG9B,OAAOR,EAAQ1B,MAAM2B,EAAO,GAAGQ,OAAOL,EAAMG,GAC9C,CACF,CACF,CAEA,SAASG,EAASpD,GAChB,MAAO,CAAC,EAAEqD,SAASnB,KAAKlC,GAAOsD,SAAS,SAC1C,CAMA,SAASC,EAAWvD,GAClB,MAAwB,mBAAVA,CAChB,CAmCA,IAWIwD,EAAehB,GAfnB,SAAoBiB,EAAeC,GACjC,MAAM,IAAIC,MAAMF,EAAcC,IAASD,EAAuB,QAChE,GAamBjB,CAXC,CAClBoB,kBAAmB,4BACnBC,YAAa,oCACbC,eAAgB,6CAChBC,YAAa,4CACbC,aAAc,qCACdC,aAAc,gCACdC,WAAY,gDACZC,YAAa,iGACb,QAAW,sDAGTC,EAAa,CACfC,QA1CF,SAAyBC,EAASD,GAKhC,OAJKjB,EAASiB,IAAUb,EAAa,cACjCvD,OAAOQ,KAAK4D,GAASE,MAAK,SAAUC,GACtC,OAPoBjE,EAOG+D,EAPKG,EAOID,GAN3BvE,OAAO+B,UAAU0C,eAAexC,KAAK3B,EAAQkE,GADtD,IAAwBlE,EAAQkE,CAQ9B,KAAIjB,EAAa,eACVa,CACT,EAqCEM,SAnCF,SAA0BA,GACnBpB,EAAWoB,IAAWnB,EAAa,eAC1C,EAkCEoB,QAhCF,SAAyBA,GACjBrB,EAAWqB,IAAYxB,EAASwB,IAAWpB,EAAa,eAC1DJ,EAASwB,IAAY3E,OAAO4E,OAAOD,GAASL,MAAK,SAAUO,GAC7D,OAAQvB,EAAWuB,EACrB,KAAItB,EAAa,eACnB,EA4BEc,QA1BF,SAAyBA,GA/BzB,IAAiBxE,EAgCVwE,GAASd,EAAa,qBACtBJ,EAASkB,IAAUd,EAAa,eAjCtB1D,EAkCHwE,EAjCJrE,OAAOQ,KAAKX,GAAKuB,QAiCHmC,EAAa,iBACrC,GAoDA,SAASuB,EAAeC,EAAOC,GAC7B,OAAO1B,EAAW0B,GAAiBA,EAAcD,EAAME,SAAWD,CACpE,CAEA,SAASE,EAAYH,EAAOX,GAE1B,OADAW,EAAME,QAAU,EAAe,EAAe,CAAC,EAAGF,EAAME,SAAUb,GAC3DA,CACT,CAEA,SAASe,EAAeJ,EAAOJ,EAASP,GAMtC,OALAd,EAAWqB,GAAWA,EAAQI,EAAME,SAAWjF,OAAOQ,KAAK4D,GAAS9C,SAAQ,SAAUiD,GACpF,IAAIa,EAEJ,OAA6C,QAArCA,EAAiBT,EAAQJ,UAAuC,IAAnBa,OAA4B,EAASA,EAAenD,KAAK0C,EAASI,EAAME,QAAQV,GACvI,IACOH,CACT,CAEA,IAAIiB,EAAQ,CACVC,OA9CF,SAAgBjB,GACd,IAAIM,EAAUxD,UAAUC,OAAS,QAAsBmE,IAAjBpE,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnFgD,EAAWE,QAAQA,GACnBF,EAAWQ,QAAQA,GACnB,IAAII,EAAQ,CACVE,QAASZ,GAEPmB,EAAYjD,EAAM4C,EAAN5C,CAAsBwC,EAAOJ,GACzCc,EAASlD,EAAM2C,EAAN3C,CAAmBwC,GAC5BW,EAAWnD,EAAM4B,EAAWC,QAAjB7B,CAA0B8B,GACrCsB,EAAapD,EAAMuC,EAANvC,CAAsBwC,GAcvC,MAAO,CAZP,WACE,IAAIL,EAAWvD,UAAUC,OAAS,QAAsBmE,IAAjBpE,UAAU,GAAmBA,UAAU,GAAK,SAAU4D,GAC3F,OAAOA,CACT,EAEA,OADAZ,EAAWO,SAASA,GACbA,EAASK,EAAME,QACxB,EAEA,SAAkBD,IAlHpB,WACE,IAAK,IAAIY,EAAOzE,UAAUC,OAAQyE,EAAM,IAAIvD,MAAMsD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC9ED,EAAIC,GAAQ3E,UAAU2E,GAGxB,OAAO,SAAUC,GACf,OAAOF,EAAIG,aAAY,SAAUC,EAAGC,GAClC,OAAOA,EAAED,EACX,GAAGF,EACL,CACF,CAyGII,CAAQX,EAAWC,EAAQC,EAAUC,EAArCQ,CAAiDnB,EACnD,EAGF,GAwBA,IC1LA,EANa,CACXoB,MAAO,CACLC,GAAI,6DCgBR,MAlBA,SAAe7D,GACb,OAAO,SAASC,IAGd,IAFA,IAAIC,EAAQC,KAEHiD,EAAOzE,UAAUC,OAAQyB,EAAO,IAAIP,MAAMsD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/EjD,EAAKiD,GAAQ3E,UAAU2E,GAGzB,OAAOjD,EAAKzB,QAAUoB,EAAGpB,OAASoB,EAAGzB,MAAM4B,KAAME,GAAQ,WACvD,IAAK,IAAID,EAAQzB,UAAUC,OAAQ4B,EAAW,IAAIV,MAAMM,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFE,EAASF,GAAS3B,UAAU2B,GAG9B,OAAOL,EAAQ1B,MAAM2B,EAAO,GAAGQ,OAAOL,EAAMG,GAC9C,CACF,CACF,ECZA,MAJA,SAAkBjD,GAChB,MAAO,CAAC,EAAEqD,SAASnB,KAAKlC,GAAOsD,SAAS,SAC1C,ECmCA,IAAI,EAAgB,CAClBiD,iBAAkB,uCAClBC,WAAY,+CACZ,QAAW,8DACXC,YAAa,iTAEX,EAAe,GAVnB,SAAoBhD,EAAeC,GACjC,MAAM,IAAIC,MAAMF,EAAcC,IAASD,EAAuB,QAChE,GAQmB,CAAkB,GACjC,EAAa,CACfiD,OApCF,SAAwBA,GAItB,OAHKA,GAAQ,EAAa,oBACrB,EAASA,IAAS,EAAa,cAEhCA,EAAOC,MAiBXC,QAAQC,KAAK,EAAcJ,aAflB,CACLJ,MAAO,CACLC,GAAII,EAAOC,KAAKG,cAKfJ,CACT,GAyBA,ICpCA,EAZc,WACZ,IAAK,IAAIb,EAAOzE,UAAUC,OAAQyE,EAAM,IAAIvD,MAAMsD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC9ED,EAAIC,GAAQ3E,UAAU2E,GAGxB,OAAO,SAAUC,GACf,OAAOF,EAAIG,aAAY,SAAUC,EAAGC,GAClC,OAAOA,EAAED,EACX,GAAGF,EACL,CACF,ECGA,MAXA,SAASe,EAAM7F,EAAQI,GAQrB,OAPArB,OAAOQ,KAAKa,GAAQC,SAAQ,SAAUxB,GAChCuB,EAAOvB,aAAgBE,QACrBiB,EAAOnB,IACTE,OAAO+G,OAAO1F,EAAOvB,GAAMgH,EAAM7F,EAAOnB,GAAMuB,EAAOvB,IAG3D,IACOkB,EAAeA,EAAe,CAAC,EAAGC,GAASI,EACpD,ECVI2F,EAAsB,CACxBvD,KAAM,cACNwD,IAAK,kCAgBP,IRkEwB9E,EAAKjB,EQlE7B,EAbA,SAAwBgG,GACtB,IAAIC,GAAe,EACfC,EAAiB,IAAIC,SAAQ,SAAUC,EAASC,GAClDL,EAAQM,MAAK,SAAUC,GACrB,OAAON,EAAeI,EAAOP,GAAuBM,EAAQG,EAC9D,IACAP,EAAe,MAAEK,EACnB,IACA,OAAOH,EAAeM,OAAS,WAC7B,OAAOP,GAAe,CACxB,EAAGC,CACL,ECPIO,EAAgB,EAAMrC,OAAO,CAC/BmB,OAAQ,EACRmB,eAAe,EACfN,QAAS,KACTC,OAAQ,KACRM,OAAQ,OAENC,GToEyB5G,ESpEsB,ETwEnD,SAAyBiB,GACvB,GAAIG,MAAMyF,QAAQ5F,GAAM,OAAOA,CACjC,CALS6F,CADe7F,ESpEYwF,IT4EpC,SAA+BxF,EAAKjB,GAClC,GAAsB,oBAAX+G,QAA4BA,OAAOC,YAAYlI,OAAOmC,GAAjE,CACA,IAAIgG,EAAO,GACPC,GAAK,EACLC,GAAK,EACLC,OAAK/C,EAET,IACE,IAAK,IAAiCgD,EAA7BC,EAAKrG,EAAI8F,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGC,QAAQC,QAChEP,EAAKrH,KAAKyH,EAAGxI,QAETmB,GAAKiH,EAAK/G,SAAWF,GAH8CkH,GAAK,GAKhF,CAAE,MAAOO,GACPN,GAAK,EACLC,EAAKK,CACP,CAAE,QACA,IACOP,GAAsB,MAAhBI,EAAW,QAAWA,EAAW,QAC9C,CAAE,QACA,GAAIH,EAAI,MAAMC,CAChB,CACF,CAEA,OAAOH,CAvBuE,CAwBhF,CAhCiCS,CAAsBzG,EAAKjB,IAkC5D,SAAqC2H,EAAGC,GACtC,GAAKD,EAAL,CACA,GAAiB,iBAANA,EAAgB,OAAO3G,EAAkB2G,EAAGC,GACvD,IAAIC,EAAI/I,OAAO+B,UAAUqB,SAASnB,KAAK4G,GAAGG,MAAM,GAAI,GAEpD,MADU,WAAND,GAAkBF,EAAEI,cAAaF,EAAIF,EAAEI,YAAYC,MAC7C,QAANH,GAAqB,QAANA,EAAoBzG,MAAM6G,KAAKN,GACxC,cAANE,GAAqB,2CAA2CK,KAAKL,GAAW7G,EAAkB2G,EAAGC,QAAzG,CALc,CAMhB,CAzCkEO,CAA4BlH,EAAKjB,IAmDnG,WACE,MAAM,IAAIoI,UAAU,4IACtB,CArDyGC,ISpErGC,EAAW1B,EAAe,GAC1B2B,EAAW3B,EAAe,GAiE9B,SAAS4B,EAAcC,GACrB,OAAOC,SAASC,KAAKC,YAAYH,EACnC,CAkBA,SAASI,EAAsBC,GAC7B,IAXoBC,EAChBN,EAUA5E,EAAQyE,GAAS,SAAUU,GAG7B,MAAO,CACLzD,OAHWyD,EAAMzD,OAIjBc,OAHW2C,EAAM3C,OAKrB,IACI4C,GAnBgBF,EAmBY,GAAG/G,OAAO6B,EAAM0B,OAAOL,MAAMC,GAAI,cAlB7DsD,EAASC,SAASQ,cAAc,UAC7BH,IAAQN,EAAOM,IAAMA,GAAMN,GAwBlC,OALAQ,EAAaE,OAAS,WACpB,OAAOL,GACT,EAEAG,EAAaG,QAAUvF,EAAMwC,OACtB4C,CACT,CAMA,SAASH,IACP,IAAIjF,EAAQyE,GAAS,SAAUe,GAI7B,MAAO,CACL9D,OAJW8D,EAAM9D,OAKjBa,QAJYiD,EAAMjD,QAKlBC,OAJWgD,EAAMhD,OAMrB,IACIiD,EAAUC,OAAOD,QAErBA,EAAQ/D,OAAO1B,EAAM0B,QAErB+D,EAAQ,CAAC,0BAA0B,SAAU3C,GAC3C6C,EAAoB7C,GACpB9C,EAAMuC,QAAQO,EAChB,IAAG,SAAU8C,GACX5F,EAAMwC,OAAOoD,EACf,GACF,CAMA,SAASD,EAAoB7C,GACtB2B,IAAW3B,QACd4B,EAAS,CACP5B,OAAQA,GAGd,CAeA,IAAI+C,EAAiB,IAAIvD,SAAQ,SAAUC,EAASC,GAClD,OAAOkC,EAAS,CACdnC,QAASA,EACTC,OAAQA,GAEZ,IACIsD,EAAS,CACXpE,OA5JF,SAAgBqE,GACd,IAAIC,EAAqB,EAAWtE,OAAOqE,GACvCjD,EAASkD,EAAmBlD,OAC5BpB,EAAShF,EAAyBsJ,EAAoB,CAAC,WAE3DtB,GAAS,SAAU1E,GACjB,MAAO,CACL0B,OAAQ,EAAM1B,EAAM0B,OAAQA,GAC5BoB,OAAQA,EAEZ,GACF,EAkJEmD,KA3IF,WACE,IAAIjG,EAAQyE,GAAS,SAAUyB,GAI7B,MAAO,CACLpD,OAJWoD,EAAKpD,OAKhBD,cAJkBqD,EAAKrD,cAKvBN,QAJY2D,EAAK3D,QAMrB,IAEA,IAAKvC,EAAM6C,cAAe,CAKxB,GAJA6B,EAAS,CACP7B,eAAe,IAGb7C,EAAM8C,OAER,OADA9C,EAAMuC,QAAQvC,EAAM8C,QACb,EAAe+C,GAGxB,GAAIH,OAAO5C,QAAU4C,OAAO5C,OAAOqD,OAGjC,OAFAR,EAAoBD,OAAO5C,QAC3B9C,EAAMuC,QAAQmD,OAAO5C,QACd,EAAe+C,GAGxB,EAAQlB,EAAeK,EAAvB,CAA8CC,EAChD,CAEA,OAAO,EAAeY,EACxB,EA4GEO,oBAhBF,WACE,OAAO3B,GAAS,SAAU4B,GAExB,OADaA,EAAMvD,MAErB,GACF,GAcA,IC3L0WwD,EAAlH,CAACC,QAAQ,CAACC,QAAQ,OAAOC,SAAS,WAAWC,UAAU,WAAWC,UAAU,CAACC,MAAM,QAAQC,KAAK,CAACL,QAAQ,SAA+IM,EAApG,CAACC,UAAU,CAACP,QAAQ,OAAOQ,OAAO,OAAOJ,MAAM,OAAOK,eAAe,SAASC,WAAW,WAA+F,IAAaC,EAA5F,UAAaC,SAASC,IAAI,OAAO,gBAAiB,MAAM,CAACC,MAAMR,EAAEC,WAAWM,EAAE,EAA2S,IAAIE,EAA7R,UAAaX,MAAMS,EAAEL,OAAOQ,EAAEC,cAAczD,EAAE0D,QAAQC,EAAEzB,KAAK0B,EAAEC,UAAUC,EAAEC,aAAaC,IAAI,OAAO,gBAAgB,UAAU,CAACV,MAAM,IAAIhB,EAAEC,QAAQK,MAAMS,EAAEL,OAAOQ,MAAMQ,IAAIhE,GAAG,gBAAgBmD,EAAE,KAAKQ,GAAG,gBAAgB,MAAM,CAACM,IAAIL,EAAEN,MAAM,IAAIhB,EAAEK,cAAc3C,GAAGsC,EAAEO,MAAMgB,UAAUC,IAAI,EAAeI,GAAE,UAAGX,GAA+D,IAAIY,EAA5B,SAAYd,IAAG,eAAGA,EAAE,GAAG,EAAsI,IAAIe,EAAhF,SAAYf,EAAEG,EAAExD,GAAE,GAAI,IAAI2D,GAAE,aAAG,IAAI,eAAGA,EAAEzH,UAAU8D,EAAE,KAAK2D,EAAEzH,SAAQ,CAAC,EAAGmH,EAAEG,EAAE,EAAU,SAASa,IAAI,CAAC,SAASC,GAAEjB,EAAEG,EAAExD,EAAE2D,GAAG,OAA4B,SAAYN,EAAEG,GAAG,OAAOH,EAAElB,OAAOoC,SAASC,GAAGnB,EAAEG,GAAG,CAAvEiB,CAAGpB,EAAEM,IAAmE,SAAYN,EAAEG,EAAExD,EAAE2D,GAAG,OAAON,EAAElB,OAAOuC,YAAYlB,EAAExD,EAAE2D,EAAEa,GAAGnB,EAAEM,QAAG,EAAO,CAArIgB,CAAGtB,EAAEG,EAAExD,EAAE2D,EAAE,CAA2H,SAASa,GAAGnB,EAAEG,GAAG,OAAOH,EAAEuB,IAAIC,MAAMrB,EAAE,CAAqlE,IAAIsB,GAAxlE,UAAaC,SAAS1B,EAAE2B,SAASxB,EAAEyB,SAASjF,EAAEkF,iBAAiBvB,EAAEwB,iBAAiBvB,EAAEwB,kBAAkBtB,EAAEuB,kBAAkBrB,EAAEsB,yBAAyBC,GAAE,EAAGC,yBAAyBC,GAAE,EAAGC,MAAM1I,EAAE,QAAQ0G,QAAQiC,EAAE,aAAaC,QAAQ1I,EAAE,CAAC,EAAE8F,OAAO6C,EAAE,OAAOjD,MAAMkD,EAAE,OAAOjC,UAAUkC,EAAEhC,aAAaiC,EAAE,CAAC,EAAEC,YAAYC,EAAE7B,EAAE8B,QAAQC,EAAE/B,IAAI,IAAIgC,EAAEC,IAAG,eAAG,IAAKC,EAAEC,IAAG,eAAG,GAAIC,GAAE,YAAE,MAAMC,GAAE,YAAE,MAAMC,GAAE,YAAE,MAAMC,GAAE,YAAER,GAAGtG,GAAE,YAAEoG,GAAGW,GAAE,aAAE,GAAI1C,GAAE,KAAK,IAAIhM,EAAE,EAAG8J,OAAO,OAAO9J,EAAEsG,MAAKtB,IAAIuJ,EAAExK,QAAQiB,IAAIqJ,GAAE,KAAKM,OAAM3J,GAAa,gBAAVA,GAAGzC,MAAsBkD,QAAQgE,MAAM,gCAAgCzE,KAAI,IAAIsJ,EAAEvK,QAAw0C,WAAa,IAAI/D,EAAEsO,EAAEvK,SAASqI,WAAWgB,GAAGpN,GAAG4M,UAAUgC,UAAUtB,GAAGtN,GAAG6M,UAAU+B,UAAUN,EAAEvK,SAAS6K,SAAS,CAAj7CC,GAAI7O,EAAEwG,QAAO,IAAIyF,GAAE,KAAK,GAAGqC,EAAEvK,SAASwK,EAAExK,QAAQ,CAAC,IAAI/D,EAAEsO,EAAEvK,QAAQ+K,oBAAoB9J,EAAEmH,GAAEoC,EAAExK,QAAQmH,GAAG,GAAGM,GAAG3D,GAAG,OAAO8D,GAAG,IAAI3G,IAAIhF,EAAEoM,YAAYpM,EAAE+O,SAAS/J,EAAE,IAAG,CAAC2G,GAAGuC,GAAGjC,GAAE,KAAK,GAAGqC,EAAEvK,SAASwK,EAAExK,QAAQ,CAAC,IAAI/D,EAAEsO,EAAEvK,QAAQiL,oBAAoBhK,EAAEmH,GAAEoC,EAAExK,QAAQsH,GAAG,GAAGI,GAAG5D,GAAG,OAAOgE,GAAG,IAAI7G,IAAIhF,EAAEoM,YAAYpM,EAAE+O,SAAS/J,EAAE,IAAG,CAAC6G,GAAGqC,GAAGjC,GAAE,KAAK,IAAIjM,EAAEsO,EAAEvK,QAAQiL,oBAAoBhP,EAAEiP,UAAUV,EAAExK,QAAQiG,OAAOkF,aAAaC,UAAUnP,EAAEoP,SAAS/D,GAAG,IAAIA,IAAIrL,EAAEqP,aAAarP,EAAEsP,aAAa,GAAG,CAAC,CAACC,MAAMvP,EAAEoM,WAAWoD,oBAAoBC,KAAKpE,GAAG,GAAGqE,kBAAiB,KAAM1P,EAAE2P,eAAc,GAAG,CAACtE,GAAG6C,GAAGjC,GAAE,KAAKqC,EAAEvK,SAASqI,YAAYQ,SAASwC,SAASlE,GAAG,GAAE,GAAG,CAACA,GAAGgD,GAAGjC,GAAE,KAAK,IAAIW,SAAS5M,EAAE6M,SAAS7H,GAAGsJ,EAAEvK,QAAQqI,WAAWmC,EAAExK,QAAQiG,OAAO4F,iBAAiB5P,EAAEwL,GAAG3D,GAAG,QAAQ0G,EAAExK,QAAQiG,OAAO4F,iBAAiB5K,EAAEyG,GAAG5D,GAAG,OAAM,GAAG,CAACA,EAAE2D,EAAEC,GAAGyC,GAAGjC,GAAE,KAAKsC,EAAExK,SAASiG,OAAO6F,SAAShL,EAAC,GAAG,CAACA,GAAGqJ,GAAGjC,GAAE,KAAKqC,EAAEvK,SAAS+L,cAAc/K,EAAC,GAAG,CAACA,GAAGmJ,GAAG,IAAI6B,GAAE,kBAAG,KAAK,IAAIxB,EAAExK,QAAQ,OAAO4D,EAAE5D,QAAQwK,EAAExK,SAAS,IAAI/D,EAAEmM,GAAEoC,EAAExK,QAAQmH,GAAG,GAAGM,GAAG3D,GAAG,OAAO8D,GAAG,IAAI3G,EAAEmH,GAAEoC,EAAExK,QAAQsH,GAAG,GAAGI,GAAG5D,GAAG,OAAOgE,GAAG,IAAIyC,EAAEvK,SAASgL,SAAS,CAACnC,SAAS5M,EAAE6M,SAAS7H,GAAE,GAAG,CAAC6C,EAAEwD,EAAEI,EAAEP,EAAEM,EAAEG,EAAEE,IAAImE,GAAE,kBAAG,MAAMtB,EAAE3K,SAASyK,EAAEzK,UAAUuK,EAAEvK,QAAQwK,EAAExK,QAAQiG,OAAOiG,iBAAiBzB,EAAEzK,QAAQ,CAACmM,iBAAgB,KAAMnL,IAAIgL,IAAIxB,EAAExK,SAASiG,OAAO6F,SAAShL,GAAGsJ,GAAE,GAAIO,EAAE3K,SAAQ,EAAE,GAAG,CAACgB,EAAEF,EAAEkL,IAAoM,OAAhM,gBAAG,KAAK7B,GAAGO,EAAE1K,QAAQuK,EAAEvK,QAAQwK,EAAExK,QAAO,GAAG,CAACmK,KAAI,gBAAG,MAAME,IAAIF,GAAG8B,GAAE,GAAG,CAAC5B,EAAEF,EAAE8B,IAA6H,gBAAiBjE,EAAE,CAACtB,MAAMkD,EAAE9C,OAAO6C,EAAEpC,cAAc4C,EAAE3C,QAAQiC,EAAEzD,KAAKyE,EAAE9C,UAAUkC,EAAEhC,aAAaiC,GAAG,GAAkB,UAAGlB,IAAwe,IAAIwD,GAAzE,SAAYjF,GAAG,IAAIG,GAAE,cAAK,OAAO,gBAAG,KAAKA,EAAEtH,QAAQmH,IAAG,CAACA,IAAIG,EAAEtH,OAAO,EAAeqM,GAAE,IAAIC,IAAy1E,IAAIC,GAAz1E,UAAaC,aAAarF,EAAEsF,gBAAgBnF,EAAEoF,YAAY5I,EAAEhJ,MAAM2M,EAAEsB,SAASrB,EAAEiF,KAAK/E,EAAE4B,MAAM1B,EAAE,QAAQ8E,KAAKvD,EAAE7B,QAAQ+B,EAAE,aAAaG,QAAQ5I,EAAE,CAAC,EAAE+L,iBAAiBpD,EAAE,CAAC,EAAEqD,cAAc9L,GAAE,EAAG+L,iBAAiBpD,GAAE,EAAGjD,MAAMkD,EAAE,OAAO9C,OAAO+C,EAAE,OAAOlC,UAAUmC,EAAEjC,aAAamC,EAAE,CAAC,EAAED,YAAYG,EAAE/B,EAAE8B,QAAQE,EAAEhC,EAAE6E,SAAS5C,EAAE6C,WAAW5C,EAAElC,IAAI,IAAImC,EAAEC,IAAG,eAAG,IAAKC,EAAEC,IAAG,eAAG,GAAIC,GAAE,YAAE,MAAM9G,GAAE,YAAE,MAAM+G,GAAE,YAAE,MAAMqB,GAAE,YAAE7B,GAAG8B,GAAE,YAAE/B,GAAGY,GAAE,cAAI7O,GAAE,YAAEwL,GAAGxG,EAAEmL,GAAGxE,GAAGsF,GAAE,aAAE,GAAIC,GAAE,aAAE,GAAIlF,GAAE,KAAK,IAAImF,EAAE,EAAGrH,OAAO,OAAOqH,EAAE7K,MAAK8K,IAAI3C,EAAE1K,QAAQqN,IAAI5C,GAAE,KAAKG,OAAMyC,GAAa,gBAAVA,GAAG7O,MAAsBkD,QAAQgE,MAAM,gCAAgC2H,KAAI,IAAIzJ,EAAE5D,SAA+iD8K,EAAE9K,SAAS6K,UAAUlB,EAAE3I,GAAGqL,GAAEiB,IAAI1F,EAAEhE,EAAE5D,QAAQ8M,iBAAiBlJ,EAAE5D,QAAQqI,YAAYwC,eAAUjH,EAAE5D,QAAQ6K,WAAzoDuC,EAAE3K,QAAO,IAAIyF,GAAE,KAAK,IAAIkF,EAAEhF,GAAEsC,EAAE1K,QAAQmH,GAAGM,GAAG,GAAGH,GAAGI,GAAG,GAAGE,GAAG9D,GAAG,IAAIsJ,IAAIxJ,EAAE5D,SAASqI,aAAarH,GAAGqL,GAAEiB,IAAIrM,EAAE2C,EAAE5D,SAAS8M,iBAAiBlJ,EAAE5D,SAASgL,SAASoC,GAAGpM,GAAG4C,EAAE5D,SAASuN,iBAAiBlB,GAAEmB,IAAI5F,IAAG,GAAG,CAACA,GAAG0C,GAAGpC,GAAE,KAAKtE,EAAE5D,SAAS+L,cAAcjL,EAAC,GAAG,CAACA,GAAGwJ,GAAGpC,GAAE,MAAMtE,EAAE5D,cAAa,IAAJyH,IAAa7D,EAAE5D,QAAQkL,UAAUR,EAAE1K,QAAQiG,OAAOkF,aAAaC,UAAUxH,EAAE5D,QAAQqL,SAAS5D,GAAGA,IAAI7D,EAAE5D,QAAQsL,aAAa6B,EAAEnN,SAAQ,EAAG4D,EAAE5D,QAAQuL,aAAa,GAAG,CAAC,CAACC,MAAM5H,EAAE5D,QAAQqI,WAAWoD,oBAAoBC,KAAKjE,EAAEkE,kBAAiB,KAAM/H,EAAE5D,QAAQ4L,eAAeuB,EAAEnN,SAAQ,GAAG,GAAG,CAACyH,GAAG6C,GAAGpC,GAAE,KAAK,IAAIkF,EAAExJ,EAAE5D,SAASqI,WAAW+E,GAAG1F,GAAGgD,EAAE1K,SAASiG,OAAO4F,iBAAiBuB,EAAE1F,EAAC,GAAG,CAACA,GAAG4C,GAAGpC,GAAE,UAAS,IAAJmB,GAAYzF,EAAE5D,SAASyN,WAAWpE,EAAC,GAAG,CAACA,GAAGiB,GAAGpC,GAAE,KAAKwC,EAAE1K,SAASiG,OAAO6F,SAAShE,EAAC,GAAG,CAACA,GAAGwC,GAAG,IAAIoD,GAAE,kBAAG,KAAK,GAAM/C,EAAE3K,SAAU0K,EAAE1K,UAAWkN,EAAElN,QAAQ,CAACiM,EAAEjM,QAAQ0K,EAAE1K,SAAS,IAAIoN,EAAExF,GAAG9D,EAAEuJ,EAAEjF,GAAEsC,EAAE1K,QAAQyH,GAAGN,GAAG,GAAGG,GAAGI,GAAG,GAAG0F,GAAG,IAAIxJ,EAAE5D,QAAQ0K,EAAE1K,SAASiG,OAAO5F,OAAOsK,EAAE3K,QAAQ,CAAC2N,MAAMN,EAAElB,iBAAgB,KAAMrL,GAAG2I,GAAGzI,GAAG4C,EAAE5D,QAAQuN,iBAAiBlB,GAAEmB,IAAIJ,IAAI1C,EAAE1K,QAAQiG,OAAO6F,SAAShE,QAAO,IAAJuB,GAAYzF,EAAE5D,QAAQyN,WAAWpE,GAAGkB,GAAE,GAAI2C,EAAElN,SAAQ,CAAE,IAAG,CAACmH,EAAEG,EAAExD,EAAE2D,EAAEC,EAAEE,EAAE9G,EAAE2I,EAAEzI,EAAE8G,EAAEuB,IAAykB,OAArkB,gBAAE,KAAKiB,GAAG0B,EAAEhM,QAAQ4D,EAAE5D,QAAQ0K,EAAE1K,QAAO,GAAG,CAACsK,KAAI,gBAAE,MAAME,IAAIF,GAAGoD,GAAE,GAAG,CAAClD,EAAEF,EAAEoD,IAAIzR,EAAE+D,QAAQyH,GAAE,gBAAE,KAAK6C,GAAGF,IAAIU,EAAE9K,SAAS6K,UAAUC,EAAE9K,QAAQ4D,EAAE5D,SAAS4N,yBAAwBR,IAAID,EAAEnN,SAASoK,EAAExG,EAAE5D,QAAQsL,WAAW8B,EAAC,IAAG,GAAG,CAAC9C,EAAEF,KAAI,gBAAE,KAAK,GAAGE,EAAE,CAAC,IAAI8C,EAAE1C,EAAE1K,QAAQiG,OAAO4H,oBAAmBR,IAAI,IAAIS,EAAElK,EAAE5D,QAAQqI,YAAY0F,IAAI,GAAGD,GAAGT,EAAEW,MAAKC,GAAGA,EAAEtB,OAAOmB,EAAEnB,OAAM,CAAC,IAAIsB,EAAEvD,EAAE1K,QAAQiG,OAAOiI,gBAAgB,CAACC,SAASL,IAAIzD,IAAI4D,EAAE,KAAI,MAAM,KAAKb,GAAGvC,SAAQ,CAAE,CAAC,MAAM,MAAK,GAAG,CAACP,EAAED,IAA0I,gBAAiBrC,EAAE,CAACtB,MAAMkD,EAAE9C,OAAO+C,EAAEtC,cAAc+C,EAAE9C,QAAQ+B,EAAEvD,KAAK2E,EAAEhD,UAAUmC,EAAEjC,aAAamC,GAAG,EAA6BoE,IAAX,UAAG7B,ICGvvM,MAAM8B,GAAerI,IAcrB,IAdsB,MAC3BlL,EAAK,UACLwT,EAAS,SACTvF,EAAQ,SACRiE,EAAQ,QACRuB,GAAU,EAAI,UACd5G,GAQD3B,EACC,MAAM,EAACuB,EAAc,EAACiH,IAAoBC,EAAAA,EAAAA,WAAS,GAKnD,OACEC,EAAAA,cAAA,OAAKC,GAAG,gBAAgBhH,UAAW,kBAAkBA,KACnD+G,EAAAA,cAACE,GAAM,CACL9H,OAAO,OACPa,UAAU,iBACV8E,gBAAiB1D,EACjByD,aAAc1R,EACdA,MAAOA,EACPkS,SAAWlS,IACLkS,GAAYlS,GACdkS,EAASlS,EACX,EAEFmP,QAjBmB4E,CAAC5I,EAAarD,KACrC0L,EAAUtO,QAAUiG,EACpBuI,GAAiB,EAAK,EAgBlBhF,MAAM,UACNE,QAAS,CACPoF,SAAU,KACVC,eAAgB,SAChBC,iBAAkB,WAClBT,QAAS,CACPU,QAASV,MAIX,C", "sources": ["webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "webpack://autogentstudio/./node_modules/state-local/lib/es/state-local.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/config/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/validators/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/loader/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/react/dist/index.mjs", "webpack://autogentstudio/./src/components/views/monaco.tsx"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "import _t from\"@monaco-editor/loader\";import{memo as Te}from\"react\";import ke,{useState as re,useRef as S,useCallback as oe,useEffect as ne}from\"react\";import Se from\"@monaco-editor/loader\";import{memo as ye}from\"react\";import K from\"react\";var le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;import me from\"react\";var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return me.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return K.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&K.createElement($,null,t),K.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=ye(ee);import{useEffect as xe}from\"react\";function Ce(e){xe(e,[])}var k=Ce;import{useEffect as ge,useRef as Re}from\"react\";function he(e,r,n=!0){let t=Re(!0);ge(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=re(!1),[T,s]=re(!0),u=S(null),c=S(null),w=S(null),d=S(q),o=S(A),b=S(!1);k(()=>{let i=Se.init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=oe(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=oe(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);ne(()=>{M&&d.current(u.current,c.current)},[M]),ne(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return ke.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=Te(ie);import{useState as Ie}from\"react\";import ce from\"@monaco-editor/loader\";function Pe(){let[e,r]=Ie(ce.__getMonacoInstance());return k(()=>{let n;return e||(n=ce.init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;import{memo as ze}from\"react\";import We,{useState as ue,useEffect as W,useRef as C,useCallback as _e}from\"react\";import Ne from\"@monaco-editor/loader\";import{useEffect as Ue,useRef as ve}from\"react\";function He(e){let r=ve();return Ue(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=ue(!1),[c,w]=ue(!0),d=C(null),o=C(null),b=C(null),L=C(M),U=C(q),I=C(),i=C(t),f=se(m),Q=C(!1),B=C(!1);k(()=>{let p=Ne.init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=_e(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);W(()=>{s&&L.current(o.current,d.current)},[s]),W(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,W(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),W(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return We.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=ze(fe);var Ft=de;export{we as DiffEditor,de as Editor,Ft as default,_t as loader,Le as useMonaco};\n//# sourceMappingURL=index.mjs.map", "import React, { useState } from \"react\";\nimport Editor from \"@monaco-editor/react\";\n\nexport const MonacoEditor = ({\n  value,\n  editorRef,\n  language,\n  onChange,\n  minimap = true,\n  className,\n}: {\n  value: string;\n  onChange?: (value: string) => void;\n  editorRef: any;\n  language: string;\n  minimap?: boolean;\n  className?: string;\n}) => {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const onEditorDidMount = (editor: any, monaco: any) => {\n    editorRef.current = editor;\n    setIsEditorReady(true);\n  };\n  return (\n    <div id=\"monaco-editor\" className={`h-full rounded ${className}`}>\n      <Editor\n        height=\"100%\"\n        className=\"h-full rounded\"\n        defaultLanguage={language}\n        defaultValue={value}\n        value={value}\n        onChange={(value: string | undefined) => {\n          if (onChange && value) {\n            onChange(value);\n          }\n        }}\n        onMount={onEditorDidMount}\n        theme=\"vs-dark\"\n        options={{\n          wordWrap: \"on\",\n          wrappingIndent: \"indent\",\n          wrappingStrategy: \"advanced\",\n          minimap: {\n            enabled: minimap,\n          },\n        }}\n      />\n    </div>\n  );\n};\n"], "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_objectWithoutProperties", "excluded", "sourceKeys", "indexOf", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "prototype", "propertyIsEnumerable", "call", "_arrayLikeToArray", "arr", "len", "arr2", "Array", "curry", "fn", "curried", "_this", "this", "_len2", "args", "_key2", "_len3", "nextArgs", "_key3", "concat", "isObject", "toString", "includes", "isFunction", "<PERSON><PERSON><PERSON><PERSON>", "errorMessages", "type", "Error", "initialIsRequired", "initialType", "initialContent", "handlerType", "handlersType", "selectorType", "changeType", "changeField", "validators", "changes", "initial", "some", "field", "property", "hasOwnProperty", "selector", "handler", "values", "_handler", "extractChanges", "state", "<PERSON><PERSON><PERSON><PERSON>", "current", "updateState", "didStateUpdate", "_handler$field", "index", "create", "undefined", "didUpdate", "update", "validate", "getChanges", "_len", "fns", "_key", "x", "reduceRight", "y", "f", "compose", "paths", "vs", "configIsRequired", "configType", "deprecation", "config", "urls", "console", "warn", "monacoBase", "merge", "assign", "CANCELATION_MESSAGE", "msg", "promise", "hasCanceled_", "wrappedPromise", "Promise", "resolve", "reject", "then", "val", "cancel", "_state$create", "isInitialized", "monaco", "_state$create2", "isArray", "_arrayWithHoles", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "_s", "_i", "next", "done", "err", "_iterableToArrayLimit", "o", "minLen", "n", "slice", "constructor", "name", "from", "test", "_unsupportedIterableToArray", "TypeError", "_nonIterableRest", "getState", "setState", "injectScripts", "script", "document", "body", "append<PERSON><PERSON><PERSON>", "getMonacoLoaderScript", "configure<PERSON><PERSON><PERSON>", "src", "_ref2", "loaderScript", "createElement", "onload", "onerror", "_ref3", "require", "window", "storeMonacoInstance", "error", "wrapperPromise", "loader", "globalConfig", "_validators$config", "init", "_ref", "editor", "__getMonacoInstance", "_ref4", "v", "wrapper", "display", "position", "textAlign", "fullWidth", "width", "hide", "Y", "container", "height", "justifyContent", "alignItems", "$", "children", "e", "style", "ee", "r", "isEditorReady", "loading", "t", "a", "className", "m", "wrapperProps", "E", "ref", "H", "k", "l", "D", "h", "getModel", "te", "De", "createModel", "be", "<PERSON><PERSON>", "parse", "ie", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "g", "keepCurrentModifiedModel", "N", "theme", "P", "options", "V", "z", "F", "j", "beforeMount", "A", "onMount", "q", "M", "O", "T", "s", "u", "c", "w", "d", "b", "catch", "dispose", "I", "getOriginalEditor", "setModel", "getModifiedEditor", "getOption", "EditorOption", "readOnly", "setValue", "getValue", "executeEdits", "range", "getFullModelRange", "text", "forceMoveMarkers", "pushUndoStop", "setModelLanguage", "setTheme", "updateOptions", "L", "U", "createDiffEditor", "automaticLayout", "se", "_", "Map", "fe", "defaultValue", "defaultLanguage", "defaultPath", "path", "line", "overrideServices", "saveViewState", "keepCurrentModel", "onChange", "onValidate", "Q", "B", "p", "R", "set", "restoreViewState", "get", "revealLine", "X", "model", "onDidChangeModelContent", "onDidChangeMarkers", "G", "uri", "find", "J", "getModelMarkers", "resource", "Ft", "MonacoEditor", "editor<PERSON><PERSON>", "minimap", "setIsEditorReady", "useState", "React", "id", "Editor", "onEditorDidMount", "wordWrap", "wrappingIndent", "wrappingStrategy", "enabled"], "sourceRoot": ""}